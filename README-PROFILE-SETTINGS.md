# Profile and Settings Implementation Guide

This guide explains how to set up and use the real profile and settings functionality in the Quamin Agricare application.

## Overview

The Profile and Settings pages have been updated to use real data persistence through a backend API. This implementation includes:

1. Backend API endpoints for user profile and settings
2. Updated User model with settings fields
3. Frontend services to interact with the API
4. Real-time data fetching and updating in the UI

## Backend Setup

### 1. User Model

The User model has been updated to include settings fields:

```javascript
// backend/models/User.js
const UserSchema = new mongoose.Schema({
  // ... existing fields
  settings: {
    notificationsEnabled: {
      type: Boolean,
      default: true
    },
    emailNotifications: {
      type: Boolean,
      default: true
    },
    smsNotifications: {
      type: <PERSON>olean,
      default: false
    },
    language: {
      type: String,
      default: 'en-IN'
    },
    theme: {
      type: String,
      default: 'light'
    },
    dataPrivacy: {
      type: String,
      enum: ['private', 'shared', 'public'],
      default: 'private'
    }
  }
  // ... other fields
});
```

### 2. API Routes

New API endpoints have been added for user profile and settings:

```javascript
// backend/routes/userRoutes.js
// GET /api/users/profile - Get user profile
// PUT /api/users/profile - Update user profile
// PUT /api/users/password - Update user password
// GET /api/users/settings - Get user settings
// PUT /api/users/settings - Update user settings
```

### 3. Server Configuration

The server has been updated to include the user routes:

```javascript
// backend/server.js
const userRoutes = require('./routes/userRoutes');
// ...
app.use("/api/users", userRoutes);
```

## Frontend Implementation

### 1. API Services

A new service has been created to interact with the user API:

```javascript
// frontend/src/services/userService.js
// getUserProfile() - Get user profile
// updateUserProfile() - Update user profile
// changePassword() - Change user password
// getUserSettings() - Get user settings
// updateUserSettings() - Update user settings
```

### 2. Profile Component

The Profile component has been updated to use real data:

- Fetches user profile on component mount
- Updates user profile through the API
- Shows loading and error states

### 3. Settings Component

The Settings component has been updated to use real data:

- Fetches user settings on component mount
- Updates user settings through the API
- Changes password through the API
- Shows loading and error states

## How to Use

### Profile Page

1. Navigate to `/profile` to view and edit your profile
2. The form will be pre-filled with your current profile data
3. Make changes and click "Update Profile" to save
4. You'll see a success message when the profile is updated

### Settings Page

1. Navigate to `/settings` to view and edit your settings
2. Toggle notification preferences
3. Change language and theme
4. Update privacy settings
5. Change your password
6. Click "Save Settings" to save your preferences

## Testing

To test the functionality:

1. Make sure the backend server is running
2. Log in to the application
3. Navigate to the Profile or Settings page
4. Make changes and save
5. Refresh the page to verify that changes persist

## Troubleshooting

If you encounter issues:

1. Check the browser console for errors
2. Verify that the backend server is running
3. Check that the API endpoints are accessible
4. Verify that you're logged in with a valid token

## Next Steps

Future improvements could include:

1. Adding profile picture upload functionality
2. Implementing two-factor authentication
3. Adding email verification for password changes
4. Creating an activity log for account changes
