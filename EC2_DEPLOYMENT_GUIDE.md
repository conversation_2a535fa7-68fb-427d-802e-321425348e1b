# AgriCare EC2 Deployment Guide

This guide provides detailed instructions for deploying the AgriCare application on an Amazon EC2 free tier instance.

## Prerequisites

1. AWS account with access to EC2
2. AWS CLI installed and configured
3. Basic knowledge of Linux commands
4. SSH client

## Deployment Steps

### 1. Install AWS CLI (if not already installed)

**For macOS:**
```bash
brew install awscli
```

**For Linux:**
```bash
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
```

**For Windows:**
Download and run the installer from: https://aws.amazon.com/cli/

### 2. Configure AWS CLI

```bash
aws configure
```

Enter your AWS Access Key ID, Secret Access Key, default region (e.g., us-east-1), and output format (json).

### 3. Run the Deployment Script

```bash
./deploy-to-ec2.sh
```

This script will:
- Create a key pair (or use an existing one)
- Create a security group with necessary inbound rules
- Launch an EC2 t2.micro instance (free tier eligible)
- Install Node.js, MongoDB, Git, and other dependencies
- Provide instructions for completing the deployment

### 4. Follow the Generated Instructions

After the script completes, follow the instructions in the `deploy-instructions.txt` file to:
1. Connect to your EC2 instance
2. Clone the repository
3. Set up environment variables
4. Install dependencies
5. Start the application with PM2
6. Configure Nginx (optional)

## Environment Variables

Create the following `.env` files on your EC2 instance:

### Backend (.env)

```
NODE_ENV=production
PORT=8000
MONGODB_URI=mongodb://localhost:27017/agricare
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d
SMS_PROVIDER=console
```

### Frontend (.env.production)

```
VITE_API_BASE_URL=http://your_ec2_public_ip:8000/api
```

## Accessing Your Application

After deployment, you can access your application at:
- Backend API: http://your_ec2_public_ip:8000
- Frontend: http://your_ec2_public_ip:5173

If you set up Nginx, you can access it at:
- http://your_ec2_public_ip

## Troubleshooting

### MongoDB Connection Issues

If MongoDB fails to start:
```bash
sudo systemctl status mongod
sudo systemctl restart mongod
```

### Application Not Starting

Check PM2 logs:
```bash
pm2 logs
```

### Security Group Issues

Make sure your security group allows traffic on ports 22, 80, 443, 8000, and 5173.

## Maintenance

### Updating Your Application

```bash
cd /home/<USER>/agricare
git pull
cd backend
npm install
pm2 restart agricare-backend
cd ../frontend
npm install
npm run build
pm2 restart agricare-frontend
```

### Monitoring

```bash
pm2 monit
```

### Backup MongoDB Data

```bash
mongodump --out /home/<USER>/backup/$(date +%Y-%m-%d)
```

## Cost Management

The t2.micro instance is eligible for the AWS free tier for 12 months. After that, you'll be charged for usage. To avoid unexpected charges:

1. Set up billing alerts in AWS
2. Stop the instance when not in use
3. Terminate the instance when no longer needed

To stop the instance:
```bash
aws ec2 stop-instances --instance-ids your_instance_id
```

To terminate the instance:
```bash
aws ec2 terminate-instances --instance-ids your_instance_id
```
