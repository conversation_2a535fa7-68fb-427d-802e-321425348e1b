const mongoose = require("mongoose");
require("dotenv").config();

const connectDB = async () => {
  try {
    // Azure Cosmos DB specific options
    const options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      retryWrites: false, // Required for Azure Cosmos DB
      serverSelectionTimeoutMS: 10000, // Increased timeout for cloud connection
      socketTimeoutMS: 45000,
      maxPoolSize: 10, // Connection pool size
      minPoolSize: 1,
      maxIdleTimeMS: 120000, // Match Azure's maxIdleTimeMS
      ssl: true, // Always use SSL for Azure
      sslValidate: true,
      // Azure Cosmos DB specific settings
      replicaSet: 'globaldb',
      appName: '@qdb@'
    };

    const connectionString = process.env.MONGODB_URI || process.env.AZURE_COSMOS_DB_URI;
    
    if (!connectionString) {
      throw new Error("No database connection string provided. Please set MONGODB_URI or AZURE_COSMOS_DB_URI in your .env file");
    }

    const conn = await mongoose.connect(connectionString, options);
    
    console.log(`✅ Azure Cosmos DB Connected: ${conn.connection.host}`);
    console.log(`📊 Database: ${conn.connection.name}`);
    
    // Test the connection
    await mongoose.connection.db.admin().ping();
    console.log("✅ Database connection test successful");
    
  } catch (error) {
    console.error(`❌ Database Connection Error: ${error.message}`);
    console.error("Please check your connection string and network connectivity");
    process.exit(1);
  }
};

module.exports = connectDB;
