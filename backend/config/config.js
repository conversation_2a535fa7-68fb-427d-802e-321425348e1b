/**
 * Application configuration
 */
module.exports = {
  // MongoDB configuration
  mongodb: {
    uri: process.env.MONGODB_URI,
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      retryWrites: false,
    },
  },

  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET || "agricare-secret-key",
    expiresIn: process.env.JWT_EXPIRES_IN || "7d",
  },

  // Azure OpenAI configuration
  azure: {
    openai: {
      apiKey: process.env.AZURE_OPENAI_API_KEY,
      endpoint: process.env.AZURE_OPENAI_ENDPOINT,
      deploymentName: process.env.AZURE_OPENAI_DEPLOYMENT_NAME || "gpt-4o",
      apiVersion: process.env.AZURE_OPENAI_API_VERSION || "2024-02-15-preview",
    },

    // Azure AI Search configuration
    searchEndpoint: process.env.AZURE_SEARCH_ENDPOINT,
    searchApiKey: process.env.AZURE_SEARCH_API_KEY,
    searchIndexName: process.env.AZURE_SEARCH_INDEX_NAME || "agricare-index",
    searchRegion: process.env.AZURE_SEARCH_REGION || "southindia",

    // Azure Computer Vision configuration
    computerVision: {
      endpoint: process.env.AZURE_COMPUTER_VISION_ENDPOINT,
      apiKey: process.env.AZURE_COMPUTER_VISION_API_KEY,
    },
  },

  // Weather API configuration
  weather: {
    apiKey: process.env.WEATHER_API_KEY,
    endpoint: process.env.WEATHER_API_ENDPOINT,
  },

  // Firebase configuration
  firebase: {
    apiKey: process.env.FIREBASE_API_KEY,
    authDomain: process.env.FIREBASE_AUTH_DOMAIN,
    projectId: process.env.FIREBASE_PROJECT_ID,
    storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.FIREBASE_APP_ID,
  },

  // Application settings
  app: {
    port: process.env.PORT || 8000,
    environment: process.env.NODE_ENV || "development",
    corsOrigins: [
      "http://localhost:3000",
      "http://localhost:5173",
      "http://localhost:5174",
      "http://127.0.0.1:5173",
      "http://127.0.0.1:5174",
    ],
  },
};
