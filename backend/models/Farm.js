const mongoose = require('mongoose');

const farmSchema = new mongoose.Schema({
  farmerId: {
    type: String,
    required: true,
    index: true
  },
  farmerName: {
    type: String,
    required: true
  },
  state: {
    type: String,
    required: true
  },
  district: {
    type: String,
    required: true
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number],  // [longitude, latitude]
      required: true,
      validate: {
        validator: function(v) {
          return v.length === 2 && 
                 v[0] >= -180 && v[0] <= 180 && // longitude
                 v[1] >= -90 && v[1] <= 90;     // latitude
        },
        message: 'Invalid coordinates'
      }
    }
  },
  farmSize: {
    type: String,
    required: true
  },
  cropType: {
    type: String,
    default: 'Not specified'
  },
  irrigationStatus: {
    type: String,
    enum: ['Well', 'Canal', 'River', 'Rain-fed', 'Drip Irrigation', 'Not specified'],
    default: 'Not specified'
  },
  services: {
    cropRecommendation: {
      isSubscribed: { type: Boolean, default: false },
      isActive: { type: Boolean, default: false },
      activatedAt: Date,
      lastUsed: Date
    },
    weatherInsights: {
      isSubscribed: { type: Boolean, default: false },
      isActive: { type: Boolean, default: false },
      activatedAt: Date,
      lastUsed: Date
    },
    soilAnalysis: {
      isSubscribed: { type: Boolean, default: false },
      isActive: { type: Boolean, default: false },
      activatedAt: Date,
      lastReport: {
        nitrogen: Number,
        phosphorus: Number,
        potassium: Number,
        ph: Number,
        lastUpdated: Date
      }
    },
    pestDetection: {
      isSubscribed: { type: Boolean, default: false },
      isActive: { type: Boolean, default: false },
      activatedAt: Date,
      lastDetection: {
        pestType: String,
        severity: String,
        detectedAt: Date
      }
    }
  },
  sensorData: {
    nitrogen: { type: String, default: 'N/A' },
    phosphorus: { type: String, default: 'N/A' },
    potassium: { type: String, default: 'N/A' },
    ph: { type: String, default: 'N/A' },
    moisture: { type: String, default: 'N/A' },
    lastUpdated: Date
  },
  weatherData: {
    temperature: { type: String, default: 'N/A' },
    humidity: { type: String, default: 'N/A' },
    rainfall: { type: String, default: 'N/A' },
    lastUpdated: Date
  },
  tasks: [{
    title: String,
    description: String,
    status: {
      type: String,
      enum: ['pending', 'in-progress', 'completed'],
      default: 'pending'
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    dueDate: Date,
    createdAt: Date
  }],
  alerts: [{
    type: {
      type: String,
      enum: ['info', 'warning', 'critical'],
      default: 'info'
    },
    message: String,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create a 2dsphere index for geospatial queries
farmSchema.index({ location: '2dsphere' });

// Update the updatedAt timestamp before saving
farmSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Farm', farmSchema); 