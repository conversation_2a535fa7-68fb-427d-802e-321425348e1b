const mongoose = require('mongoose');

const aiConversationSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
  },
  userRole: {
    type: String,
    enum: ['TM', 'Farmer', 'HR'],
    required: true,
  },
  conversation: [{
    role: {
      type: String,
      enum: ['user', 'assistant'],
      required: true,
    },
    content: {
      type: String,
      required: true,
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
  }],
  farmData: {
    weather: {
      temperature: Number,
      humidity: Number,
      rainfall: Number,
    },
    soil: {
      moisture: Number,
      ph: Number,
      nitrogen: Number,
      phosphorus: Number,
      potassium: Number,
    },
    alerts: [{
      type: String,
      message: String,
      timestamp: Date,
    }],
  },
  recommendations: [{
    category: {
      type: String,
      enum: ['soil', 'crop', 'pest', 'weather'],
      required: true,
    },
    content: String,
    timestamp: {
      type: Date,
      default: Date.now,
    },
    status: {
      type: String,
      enum: ['pending', 'implemented', 'ignored'],
      default: 'pending',
    },
  }],
}, {
  timestamps: true,
});

// Add indexes for better query performance
aiConversationSchema.index({ userId: 1, createdAt: -1 });
aiConversationSchema.index({ 'recommendations.category': 1 });

module.exports = mongoose.model('AIConversation', aiConversationSchema); 