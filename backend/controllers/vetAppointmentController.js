const VetAppointment = require('../models/VetAppointment');
const User = require('../models/User');

// Get all appointments
exports.getAllAppointments = async (req, res) => {
  try {
    const appointments = await VetAppointment.find()
      .populate('farmerId', 'name email phone')
      .populate('vetId', 'name email specialization');
    
    res.status(200).json({
      success: true,
      count: appointments.length,
      data: appointments
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Get appointments for a specific farmer
exports.getFarmerAppointments = async (req, res) => {
  try {
    const appointments = await VetAppointment.find({ farmerId: req.params.farmerId })
      .populate('vetId', 'name email specialization');
    
    res.status(200).json({
      success: true,
      count: appointments.length,
      data: appointments
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Get appointments for a specific veterinarian
exports.getVetAppointments = async (req, res) => {
  try {
    const appointments = await VetAppointment.find({ vetId: req.params.vetId })
      .populate('farmerId', 'name email phone');
    
    res.status(200).json({
      success: true,
      count: appointments.length,
      data: appointments
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Create a new appointment
exports.createAppointment = async (req, res) => {
  try {
    const appointment = await VetAppointment.create(req.body);
    
    res.status(201).json({
      success: true,
      data: appointment
    });
  } catch (error) {
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        error: messages
      });
    } else {
      return res.status(500).json({
        success: false,
        error: 'Server Error'
      });
    }
  }
};

// Update appointment status
exports.updateAppointmentStatus = async (req, res) => {
  try {
    const { status, treatmentNotes } = req.body;
    
    const appointment = await VetAppointment.findById(req.params.id);
    
    if (!appointment) {
      return res.status(404).json({
        success: false,
        error: 'Appointment not found'
      });
    }
    
    // Update fields
    appointment.status = status || appointment.status;
    if (treatmentNotes) {
      appointment.treatmentNotes = treatmentNotes;
    }
    
    await appointment.save();
    
    res.status(200).json({
      success: true,
      data: appointment
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Delete appointment
exports.deleteAppointment = async (req, res) => {
  try {
    const appointment = await VetAppointment.findById(req.params.id);
    
    if (!appointment) {
      return res.status(404).json({
        success: false,
        error: 'Appointment not found'
      });
    }
    
    await appointment.remove();
    
    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

// Get available veterinarians
exports.getAvailableVets = async (req, res) => {
  try {
    // In a real app, you would filter users with vet role
    const vets = await User.find({ role: 'vet' }).select('name email specialization experience');
    
    res.status(200).json({
      success: true,
      count: vets.length,
      data: vets
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};
