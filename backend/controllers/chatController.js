const ChatHistory = require("../models/ChatHistory");
const User = require("../models/user.model");
const { OpenAIClient, AzureKeyCredential } = require("@azure/openai");
const fs = require("fs");
const path = require("path");
const axios = require("axios");
const FormData = require("form-data");

// Initialize Azure OpenAI client
const client = new OpenAIClient(
  process.env.AZURE_OPENAI_ENDPOINT,
  new AzureKeyCredential(process.env.AZURE_OPENAI_API_KEY)
);

console.log(
  "Azure OpenAI client initialized with endpoint:",
  process.env.AZURE_OPENAI_ENDPOINT
);

// Initialize Azure Speech Service for STT
const speechKey = process.env.AZURE_SPEECH_KEY;
const speechRegion = process.env.AZURE_SPEECH_REGION;

// Helper function to convert image to base64
const imageToBase64 = async (imagePath) => {
  try {
    const imageBuffer = fs.readFileSync(imagePath);
    return imageBuffer.toString("base64");
  } catch (error) {
    console.error("Error converting image to base64:", error);
    throw error;
  }
};

// Helper function to download image from URL
const downloadImage = async (url, outputPath) => {
  try {
    const response = await axios({
      url,
      method: "GET",
      responseType: "stream",
    });

    const writer = fs.createWriteStream(outputPath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on("finish", resolve);
      writer.on("error", reject);
    });
  } catch (error) {
    console.error("Error downloading image:", error);
    throw error;
  }
};

//Delete chat history for a user
exports.deleteChatByDate = async (req, res) => {
  try {
    const userId = req.user?.id;
    const { date } = req.params;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated!",
      });
    }

    if (!date) {
      return res.status(400).json({
        success: false,
        message: "Date parameter is missing!",
      });
    }

    // Get the start and end of the given date
    const startOfDay = new Date(date);
    const endOfDay = new Date(date);
    endOfDay.setUTCHours(23, 59, 59, 999);

    const result = await ChatHistory.deleteMany({
      userId,
      chatDate: { $gte: startOfDay, $lte: endOfDay },
    });

    return res.status(200).json({
      success: true,
      message: `Deleted ${result.deletedCount} chat(s) for date ${date}`,
    });
  } catch (error) {
    console.error("Error deleting chat history:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to delete chat history. Please try again.",
    });
  }
};

// Get chat history for a user (most recent conversations first)
exports.getChatHistory = async (req, res) => {
  try {
    // Get user ID from authenticated user
    console.log("Request user object:", req.user);
    console.log("User ID from req.user.id:", req.user?.id);
    console.log("User ID from req.user._id:", req.user?._id);
    const userId = req.user?.id || req.user?._id;
    console.log("Final user ID being used:", userId);

    // Get chat history from the last two weeks
    const twoWeeksAgo = new Date();
    twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);

    // Try to find chat history for the user, grouped by chatDate
    let chatHistory = await ChatHistory.find({
      userId,
      chatDate: { $gte: twoWeeksAgo },
    })
      .sort({ chatDate: -1 })
      .limit(30);

    // Group chats by date for the response
    const chatsByDate = {};

    if (chatHistory.length > 0) {
      chatHistory.forEach((chat) => {
        const dateKey = new Date(chat.chatDate).toISOString().split("T")[0]; // YYYY-MM-DD format

        if (!chatsByDate[dateKey]) {
          chatsByDate[dateKey] = [];
        }

        chatsByDate[dateKey].push(chat);
      });
    }

    // If no chat history found, create mock data
    if (chatHistory.length === 0) {
      console.log("No chat history found, creating mock data");
      // chatHistory = [
      //   {
      //     _id: 'mock-chat-1',
      //     userId: userId,
      //     userName: 'Mock User',
      //     userRole: 'Farmer',
      //     messages: [
      //       {
      //         text: 'Hello, I need help with my wheat crop',
      //         sender: 'user',
      //         timestamp: new Date(Date.now() - 3600000),
      //         language: 'en'
      //       },
      //       {
      //         text: 'I\'m your Quamin assistant. How can I help you today?',
      //         sender: 'bot',
      //         timestamp: new Date(Date.now() - 3500000),
      //         language: 'en',
      //         followupQuestions: [
      //           'How do I analyze my soil health?',
      //           'What crops are best for my region?',
      //           'How can I improve crop yield?'
      //         ]
      //       }
      //     ],
      //     contextData: { contextId: Date.now().toString() },
      //     createdAt: new Date(Date.now() - 86400000),
      //     updatedAt: new Date(Date.now() - 3500000)
      //   }
      // ];
    }

    res.status(200).json({
      success: true,
      data: { byDate: chatsByDate },
    });
  } catch (error) {
    console.error("Error fetching chat history:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch chat history",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Search chat history
exports.searchChatHistory = async (req, res) => {
  try {
    // TEMPORARY: Use a mock user ID if no authenticated user
    const userId = req.user?.id || "mock-user-id";
    console.log("Searching chat history for user:", userId);
    const { query, startDate, endDate } = req.query;

    // Build search criteria
    const searchCriteria = { userId };

    // Add date range if provided
    if (startDate || endDate) {
      searchCriteria.chatDate = {};
      if (startDate) searchCriteria.chatDate.$gte = new Date(startDate);
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999); // Set to end of day
        searchCriteria.chatDate.$lte = endDateTime;
      }
    }

    // Add text search if provided
    let chatHistory;
    if (query) {
      chatHistory = await ChatHistory.find({
        ...searchCriteria,
        "messages.text": { $regex: query, $options: "i" },
      }).sort({ chatDate: -1 });
    } else {
      chatHistory = await ChatHistory.find(searchCriteria)
        .sort({ chatDate: -1 })
        .limit(30);
    }

    // Group chats by date for the response
    const chatsByDate = {};

    if (chatHistory.length > 0) {
      chatHistory.forEach((chat) => {
        const dateKey = new Date(chat.chatDate || chat.createdAt)
          .toISOString()
          .split("T")[0]; // YYYY-MM-DD format

        if (!chatsByDate[dateKey]) {
          chatsByDate[dateKey] = [];
        }

        chatsByDate[dateKey].push(chat);
      });

      // Sort chats within each date by updatedAt
      Object.keys(chatsByDate).forEach((dateKey) => {
        chatsByDate[dateKey].sort(
          (a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)
        );
      });
    }

    res.status(200).json({
      success: true,
      data: { byDate: chatsByDate },
    });
  } catch (error) {
    console.error("Error searching chat history:", error);
    res.status(500).json({
      success: false,
      message: "Failed to search chat history",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Convert speech to text using Azure Speech Service
exports.speechToText = async (req, res) => {
  try {
    // TEMPORARY: Log the request
    console.log("Processing speech-to-text request");
    const audioFile = req.file;
    const language = req.body.language || "en-US";

    if (!audioFile) {
      console.log("No audio file provided, returning mock response");
      // Return a mock response for testing
      return res.status(200).json({
        success: true,
        data: {
          text: language.startsWith("en")
            ? "What is the weather forecast for tomorrow?"
            : "कल का मौसम कैसा रहेगा?",
        },
      });
    }

    // Map language codes to Azure Speech Service language codes
    const languageMap = {
      "en": "en-US",
      "hi": "hi-IN",
      "te": "te-IN",
      "ta": "ta-IN",
      "kn": "kn-IN",
      "ml": "ml-IN",
      "pa": "pa-IN",
      "bn": "bn-IN",
      "gu": "gu-IN",
      "mr": "mr-IN",
    };

    const speechLanguage = languageMap[language] || "en-US";

    // For testing, return a mock response
    console.log("Using mock speech-to-text response");

    // Generate mock text based on the language
    let mockText = "";
    if (language === "en") {
      mockText = "What is the weather forecast for tomorrow?";
    } else if (language === "hi") {
      mockText = "कल का मौसम कैसा रहेगा?";
    } else {
      mockText = "What is the weather forecast for tomorrow?";
    }

    // Clean up the temporary file
    fs.unlinkSync(audioFile.path);

    return res.status(200).json({
      success: true,
      data: {
        text: mockText,
      },
    });
  } catch (error) {
    console.error("Error in speech to text conversion:", error);
    res.status(500).json({
      success: false,
      message: "Failed to convert speech to text",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Process a chat message with Azure OpenAI
exports.processMessage = async (req, res) => {
  try {
    // TEMPORARY: Use a mock user ID if no authenticated user
    const userId = req.user?.id || "mock-user-id";
    console.log("Processing message for user:", userId);
    const { text, language, contextId } = req.body;

    if (!text) {
      return res.status(400).json({
        success: false,
        message: "Message text is required",
      });
    }

    // Get today's date at midnight (to use as a key for today's chat)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    today.setMinutes(today.getMinutes() + 330); // Add 5 hours and 30 minutes for Indian timezone

    // Find chat history for this specific context/session
    let todayChat = await ChatHistory.findOne({
      userId,
      "contextData.contextId": contextId
    }).sort({ updatedAt: -1 });

    // Get user context from chat history (for AI context)
    const previousChats = await ChatHistory.find({ userId })
      .sort({ updatedAt: -1 })
      .limit(2);

    // Build conversation history for context
    let conversationHistory = [];
    if (previousChats.length > 0) {
      const recentMessages = previousChats[0].messages.slice(-5); // Get last 5 messages for context
      conversationHistory = recentMessages.map((msg) => ({
        role: msg.sender === "user" ? "user" : "assistant",
        content: msg.text,
      }));
    }

    // Prepare system prompt based on user's context
    let systemPrompt = `You are an agricultural expert AI assistant named Quamin SmartBot.
    You specialize in providing advice on farming, crops, soil health, and agricultural best practices.
    Be concise, helpful, and provide practical advice that farmers can implement.
    The user's preferred language is: ${language}. Respond in this language.`;

    // Add context from previous analyses if available
    if (
      previousChats.length > 0 &&
      previousChats[0].contextData &&
      previousChats[0].contextData.analyses
    ) {
      const previousAnalyses = previousChats[0].contextData.analyses;
      if (previousAnalyses.length > 0) {
        systemPrompt += `\n\nRecent crop analyses: ${JSON.stringify(
          previousAnalyses.slice(-1)
        )}`;
      }
    }

    // Call Azure OpenAI
    const messages = [
      { role: "system", content: systemPrompt },
      ...conversationHistory,
      { role: "user", content: text },
    ];

    console.log(
      "Calling Azure OpenAI for chat processing with deployment name:",
      process.env.AZURE_OPENAI_DEPLOYMENT_NAME
    );
    let result;
    try {
      result = await client.getChatCompletions(
        process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
        messages,
        { maxTokens: 500 }
      );
    } catch (error) {
      console.error("Error calling Azure OpenAI:", error);
      // Return a mock response for testing
      return res.status(200).json({
        success: true,
        data: {
          response: `I'm your agricultural assistant. Based on your question about "${text}", here's my advice: For most crops, ensure proper irrigation, regular monitoring for pests and diseases, and timely application of appropriate fertilizers. Would you like more specific information about a particular crop or farming practice?`,
          followupQuestions: [
            "What crops are you currently growing?",
            "Do you have any specific issues with your crops?",
            "Would you like information about sustainable farming practices?",
          ],
        },
      });
    }

    const botResponse = result.choices[0].message.content;

    // Generate followup questions based on the conversation
    const followupPrompt = `Based on the conversation so far, generate 3 relevant follow-up questions that the farmer might want to ask next. Return ONLY a JSON array of strings with no additional text. Example: ["Question 1?", "Question 2?", "Question 3?"]`;

    console.log(
      "Calling Azure OpenAI for followup questions with deployment name:",
      process.env.AZURE_OPENAI_DEPLOYMENT_NAME
    );
    let followupResult;
    try {
      followupResult = await client.getChatCompletions(
        process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
        [
          ...messages,
          { role: "assistant", content: botResponse },
          { role: "user", content: followupPrompt },
        ],
        { maxTokens: 200 }
      );
    } catch (error) {
      console.error("Error generating followup questions:", error);
      console.log("Executing error path for followup questions");
      // Use default followup questions
      followupQuestions = [
        "What crops are you currently growing?",
        "Do you have any specific issues with your crops?",
        "Would you like information about sustainable farming practices?",
      ];

      // Save user message and bot response
      const userMessage = {
        text,
        sender: "user",
        timestamp: new Date(),
        language,
      };

      const botMessage = {
        text: botResponse,
        sender: "bot",
        timestamp: new Date(),
        language,
        followupQuestions,
      };

      // If we didn't find a chat for today, create a new one
      if (!todayChat) {
        console.log("No chat found for today (error path), creating new chat");
        // Get user details
        const user = await User.findById(userId);
        console.log("User found (error path):", user ? user.name : "User not found");

        todayChat = new ChatHistory({
          userId,
          userName: user?.name || "User",
          userRole: user?.role || "Farmer",
          messages: [],
          contextData: { contextId: contextId || Date.now().toString() },
          chatDate: today,
        });
        console.log("Created new ChatHistory document (error path)");
      } else {
        console.log("Found existing chat for today (error path)");
      }

      // Add messages to chat history
      console.log("Adding messages to chat history (error path)");
      todayChat.messages.push(userMessage);
      todayChat.messages.push(botMessage);
      
      try {
        await todayChat.save();
        console.log(
          `Successfully saved messages to chat history (error path) for ${today.toISOString().split("T")[0]}`
        );
      } catch (saveError) {
        console.error("Error saving chat history (error path):", saveError);
        throw saveError;
      }

      // Return the response
      return res.status(200).json({
        success: true,
        data: {
          response: botResponse,
          followupQuestions,
        },
      });
    }

    let followupQuestions = [];
    try {
      const followupContent = followupResult.choices[0].message.content;
      const jsonMatch = followupContent.match(/\[.*\]/s);
      if (jsonMatch) {
        followupQuestions = JSON.parse(jsonMatch[0]);
      } else {
        // Fallback questions if parsing fails
        followupQuestions = [
          "Can you tell me more about your farm?",
          "What crops are you currently growing?",
          "Have you noticed any issues with your crops recently?",
        ];
      }
    } catch (error) {
      console.error("Error parsing followup questions:", error);
      console.log("Executing normal path for followup questions");
      followupQuestions = [
        "Can you tell me more about your farm?",
        "What crops are you currently growing?",
        "Have you noticed any issues with your crops recently?",
      ];
    }

    // Save user message and bot response
    const userMessage = {
      text,
      sender: "user",
      timestamp: new Date(),
      language,
    };

    const botMessage = {
      text: botResponse,
      sender: "bot",
      timestamp: new Date(),
      language,
      followupQuestions,
    };

    // If we didn't find a chat for today, create a new one
    if (!todayChat) {
      console.log("No chat found for today, creating new chat");
      // Get user details
      const user = await User.findById(userId);
      console.log("User found:", user ? user.name : "User not found");

      todayChat = new ChatHistory({
        userId,
        userName: user?.name || "User",
        userRole: user?.role || "Farmer",
        messages: [],
        contextData: { contextId: contextId || Date.now().toString() },
        chatDate: today,
      });
      console.log("Created new ChatHistory document");
    } else {
      console.log("Found existing chat for today");
    }

    // Add messages to chat history
    console.log("Adding messages to chat history");
    todayChat.messages.push(userMessage);
    todayChat.messages.push(botMessage);
    
    try {
      const savedDoc = await todayChat.save();
      console.log(
        `Successfully saved messages to chat history for ${today.toISOString().split("T")[0]}`
      );
      console.log("Saved document ID:", savedDoc._id);
      console.log("Saved document:", savedDoc);
    } catch (saveError) {
      console.error("Error saving chat history:", saveError);
      console.error("Save error details:", {
        message: saveError.message,
        code: saveError.code,
        name: saveError.name,
        stack: saveError.stack
      });
      throw saveError;
    }

    res.status(200).json({
      success: true,
      data: {
        response: botResponse,
        followupQuestions,
      },
    });
  } catch (error) {
    console.error("Error processing message:", error);
    res.status(500).json({
      success: false,
      message: "Failed to process message",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Save a new message to chat history
exports.saveMessage = async (req, res) => {
  try {
    // TEMPORARY: Use a mock user ID if no authenticated user
    const userId = req.user?.id || "mock-user-id";
    console.log("Saving message for user:", userId);
    const {
      text,
      sender,
      language,
      attachments,
      followupQuestions,
      contextId,
    } = req.body;

    // Validate required fields
    if (!text || !sender) {
      return res.status(400).json({
        success: false,
        message: "Text and sender are required fields",
      });
    }

    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      console.log("User not found");
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Get today's date at midnight
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    today.setMinutes(today.getMinutes() + 330); // Add 5 hours and 30 minutes for Indian timezone

    // Find chat history for today
    let chatHistory = await ChatHistory.findOne({
      userId,
      chatDate: {
        $gte: today,
        $lt: new Date(today.getTime() + 24 * 60 * 60 * 1000),
      },
    }).sort({ updatedAt: -1 });

    // If no chat history exists for today, create a new one
    if (!chatHistory) {
      chatHistory = new ChatHistory({
        userId,
        userName: user.name,
        userRole: user.role,
        messages: [],
        contextData: { contextId: contextId || Date.now().toString() },
        chatDate: today,
      });
    }

    // Add the new message
    const newMessage = {
      text,
      sender,
      timestamp: new Date(),
      language: language || user.preferredLanguage || "en",
      attachments: attachments || [],
      followupQuestions: followupQuestions || [],
      contextId,
    };

    chatHistory.messages.push(newMessage);
    
    console.log("About to save chatHistory for regular message");
    console.log("ChatHistory document:", {
      _id: chatHistory._id,
      userId: chatHistory.userId,
      contextId: chatHistory.contextData?.contextId,
      messagesCount: chatHistory.messages?.length
    });
    
    try {
      const savedDoc = await chatHistory.save();
      console.log("Successfully saved chatHistory for regular message:", savedDoc._id);
      console.log("Saved document messages count:", savedDoc.messages?.length);
    } catch (saveError) {
      console.error("Error saving chatHistory for regular message:", saveError);
      console.error("Save error details:", {
        message: saveError.message,
        code: saveError.code,
        name: saveError.name
      });
      throw saveError; // Re-throw to trigger the outer catch block
    }

    res.status(201).json({
      success: true,
      data: newMessage,
    });
  } catch (error) {
    console.error("Error saving message:", error);
    res.status(500).json({
      success: false,
      message: "Failed to save message",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Get image analysis history for a user
exports.getImageAnalysisHistory = async (req, res) => {
  try {
    // Get user ID from authenticated user
    console.log("Request user object for analysis:", req.user);
    console.log("User ID from req.user.id:", req.user?.id);
    console.log("User ID from req.user._id:", req.user?._id);
    const userId = req.user?.id || req.user?._id;
    console.log("Final user ID being used for analysis:", userId);

    // Get query parameters for pagination and date filtering
    const { page = 1, limit = 20, startDate, endDate } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);

    // Build query criteria
    const criteria = { userId };

    // Add date range if provided
    if (startDate || endDate) {
      criteria.createdAt = {};
      if (startDate) criteria.createdAt.$gte = new Date(startDate);
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999); // Set to end of day
        criteria.createdAt.$lte = endDateTime;
      }
    }

    // Find all chat histories that contain image analyses
    const chatHistories = await ChatHistory.find(criteria)
      .sort({ createdAt: -1 })
      .lean(); // Use lean() for better performance

    // Extract all image analyses from chat histories
    let allAnalyses = [];

    chatHistories.forEach((chat) => {
      // Extract from contextData.analyses
      if (
        chat.contextData &&
        chat.contextData.analyses &&
        Array.isArray(chat.contextData.analyses)
      ) {
        chat.contextData.analyses.forEach((analysis) => {
          allAnalyses.push({
            ...analysis,
            chatId: chat._id,
            userName: chat.userName,
            userRole: chat.userRole,
            chatDate: chat.chatDate || chat.createdAt,
          });
        });
      }

      // Also extract from message attachments for backward compatibility
      chat.messages.forEach((message) => {
        if (
          message.sender === "bot" &&
          message.attachments &&
          Array.isArray(message.attachments)
        ) {
          message.attachments.forEach((attachment) => {
            if (attachment.type === "image" && attachment.analysisResult) {
              // Check if this analysis is already included (to avoid duplicates)
              const isDuplicate = allAnalyses.some(
                (a) =>
                  a.imageUrl === attachment.url &&
                  a.timestamp &&
                  new Date(a.timestamp).getTime() ===
                    new Date(message.timestamp).getTime()
              );

              if (!isDuplicate) {
                allAnalyses.push({
                  timestamp: message.timestamp,
                  imageUrl: attachment.url,
                  result: attachment.analysisResult,
                  text: message.text,
                  chatId: chat._id,
                  userName: chat.userName,
                  userRole: chat.userRole,
                  chatDate: chat.chatDate || chat.createdAt,
                });
              }
            }
          });
        }
      });
    });

    // Sort analyses by timestamp (newest first)
    allAnalyses.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Apply pagination
    const totalAnalyses = allAnalyses.length;
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedAnalyses = allAnalyses.slice(startIndex, endIndex);

    // Group analyses by date
    const analysesByDate = {};
    paginatedAnalyses.forEach((analysis) => {
      const dateKey = new Date(analysis.timestamp).toISOString().split("T")[0]; // YYYY-MM-DD
      if (!analysesByDate[dateKey]) {
        analysesByDate[dateKey] = [];
      }
      analysesByDate[dateKey].push(analysis);
    });

    // Return the results
    res.status(200).json({
      success: true,
      data: {
        analyses: paginatedAnalyses,
        byDate: analysesByDate,
        pagination: {
          total: totalAnalyses,
          page: pageNum,
          limit: limitNum,
          totalPages: Math.ceil(totalAnalyses / limitNum),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching image analysis history:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch image analysis history",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Process image analysis
exports.processImageAnalysis = async (req, res) => {
  try {
    // Get the authenticated user ID
    const userId = req.user?.id || req.user?._id;
    console.log("Processing image analysis for user:", userId);
    console.log("Request body:", req.body);
    const { imageUrl, contextId } = req.body;
    console.log("Image URL:", imageUrl);
    const userQuery = req.body.text || "Please analyze this crop image";
    const userLanguage = req.body.language || "en";

    if (!imageUrl) {
      return res.status(400).json({
        success: false,
        message: "Image URL is required",
      });
    }

    // Download the image to a temporary location
    const tempDir = path.join(__dirname, "../uploads/temp");
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const imageName = path.basename(imageUrl);
    const tempImagePath = path.join(tempDir, imageName);

    try {
      await downloadImage(imageUrl, tempImagePath);
    } catch (error) {
      console.error("Error downloading image:", error);
      return res.status(500).json({
        success: false,
        message: "Failed to process image",
      });
    }

    // Convert image to base64
    const base64Image = await imageToBase64(tempImagePath);

    // Get user context from chat history
    const previousChats = await ChatHistory.find({ userId })
      .sort({ updatedAt: -1 })
      .limit(1);

    let contextInfo = "";
    if (
      previousChats.length > 0 &&
      previousChats[0].contextData &&
      previousChats[0].contextData.analyses
    ) {
      const previousAnalyses = previousChats[0].contextData.analyses;
      if (previousAnalyses.length > 0) {
        contextInfo = `Previous analyses: ${JSON.stringify(
          previousAnalyses.slice(-2)
        )}`;
      }
    }

    // Prepare the prompt for GPT-4o
    const systemPrompt = `You are an agricultural expert AI assistant specialized in analyzing crop, soil, and seed images.
    Provide detailed analysis including:
    1. Identification of the crop/plant/soil
    2. Health assessment with confidence level
    3. Detection of any issues (diseases, pests, nutrient deficiencies)
    4. Specific recommendations for the farmer
    5. Detailed agricultural practices suitable for the identified crop (cultural, mechanical, biological, and chemical)
    6. Suggest 3 relevant follow-up questions the farmer might want to ask

    Format your response as a structured JSON object with the following fields:
    {
      "analysis": {
        "type": "crop|soil|seed",
        "identifiedAs": "name of crop/soil type",
        "confidence": 0.XX,
        "healthStatus": "good|moderate|poor",
        "issues": ["list of issues"],
        "recommendations": ["list of recommendations"],
        "agriculturalPractices": {
          "cultural": ["practice1", "practice2", "practice3", "practice4"],
          "mechanical": ["practice1", "practice2", "practice3", "practice4"],
          "biological": ["practice1", "practice2", "practice3", "practice4"],
          "chemical": ["practice1", "practice2", "practice3", "practice4"]
        },
        "cropHealthDetails": {
          "growthStage": "specific growth stage description",
          "leafColor": "leaf color assessment and implications",
          "canopyDensity": "canopy density evaluation",
          "stressIndicators": "visible stress indicators",
          "yieldPotential": {
            "currentTrajectory": "yield trajectory assessment",
            "limitingFactors": "factors limiting yield",
            "improvementPotential": "potential for improvement"
          },
          "pestAndDiseaseAssessment": {
            "detectedIssues": ["specific pests/diseases if any"],
            "riskLevel": "low|medium|high",
            "preventiveMeasures": ["prevention strategies"]
          }
        }
      },
      "followupQuestions": ["question1", "question2", "question3"],
      "textResponse": "A natural language summary of the analysis"
    }

    The user's preferred language is: ${userLanguage}. If this is not English, provide the textResponse in that language while keeping the JSON structure in English.
    ${contextInfo}`;

    // Call Azure OpenAI with GPT-4o
    console.log(
      "Calling Azure OpenAI with deployment name:",
      process.env.AZURE_OPENAI_DEPLOYMENT_NAME
    );
    let result;
    try {
      result = await client.getChatCompletions(
        process.env.AZURE_OPENAI_DEPLOYMENT_NAME,
        [
          { role: "system", content: systemPrompt },
          {
            role: "user",
            content: [
              { type: "text", text: userQuery },
              {
                type: "image_url",
                image_url: { url: `data:image/jpeg;base64,${base64Image}` },
              },
            ],
          },
        ],
        { maxTokens: 800 }
      );
    } catch (error) {
      console.error("Error calling Azure OpenAI:", error);
      // Return a mock response for testing
      return res.status(200).json({
        success: true,
        data: {
          message: {
            text: `Analysis Results:\n- Identified: wheat (92% confidence)\n- Health Status: good\n- Issues: Possible nitrogen deficiency\n- Recommendations: \n  * Apply nitrogen-rich fertilizer\n  * Ensure proper irrigation\n  * Monitor for pests`,
            attachment: {
              url: imageUrl,
              type: "image",
            },
          },
          analysisResult: {
            type: "crop",
            healthStatus: "good",
            identifiedAs: "wheat",
            confidence: 0.92,
            issues: ["Possible nitrogen deficiency"],
            recommendations: [
              "Apply nitrogen-rich fertilizer",
              "Ensure proper irrigation",
              "Monitor for pests",
            ],
            agriculturalPractices: {
              cultural: [
                "Crop rotation: Rotate wheat with legumes like soybeans or pulses",
                "Row spacing: Optimal spacing for wheat is typically 15-20 cm between rows",
                "Mulching: Apply organic mulch to conserve moisture and suppress weeds",
                "Intercropping: Consider companion planting with pest-repelling plants"
              ],
              mechanical: [
                "Tillage: Minimum tillage recommended based on current soil conditions",
                "Hand weeding: Regular weeding helps reduce competition for nutrients",
                "Trellising: Not typically required for this crop",
                "Pruning: Limited pruning needed, focus on removing damaged parts"
              ],
              biological: [
                "Beneficial insects: Introduce ladybugs and lacewings to control aphids",
                "Microbial inoculants: Apply rhizobacteria to improve nutrient uptake and root health",
                "Nematode control: Use nematode-suppressive cover crops in rotation",
                "Trap crops: Plant mustard as trap crop for pest diversion"
              ],
              chemical: [
                "Fertilization: Apply balanced NPK with focus on nitrogen",
                "Fungicide: Apply copper-based fungicide at recommended rates as preventive measure",
                "pH management: Maintain soil pH between 6.0-7.0 for optimal nutrient availability",
                "Integrated approach: Combine chemical controls with other management practices for sustainable pest management"
              ]
            }
          },
          followupQuestions: [
            "What fertilizers do you recommend for wheat?",
            "How often should I irrigate my wheat crop?",
            "What are common pests that affect wheat?",
          ],
        },
      });
    }

    // Clean up the temporary file
    fs.unlinkSync(tempImagePath);

    // Parse the response
    let analysisResult;
    let followupQuestions;
    let analysisText;

    try {
      const responseContent = result.choices[0].message.content;
      const jsonMatch = responseContent.match(/\{[\s\S]*\}/); // Extract JSON from response

      if (jsonMatch) {
        const parsedResponse = JSON.parse(jsonMatch[0]);
        analysisResult = parsedResponse.analysis;
        followupQuestions = parsedResponse.followupQuestions;
        analysisText = parsedResponse.textResponse;
      } else {
        // Fallback if JSON parsing fails
        analysisResult = {
          type: "unknown",
          healthStatus: "unknown",
          identifiedAs: "unknown",
          confidence: 0.5,
          issues: ["Could not determine issues"],
          recommendations: ["Please consult with a local agricultural expert"],
        };
        followupQuestions = [
          "Can you try with a clearer image?",
          "What specific concerns do you have about your crop?",
          "When did you first notice these issues?",
        ];
        analysisText = responseContent;
      }
    } catch (error) {
      console.error("Error parsing AI response:", error);
      // Fallback values
      analysisResult = {
        type: "crop",
        healthStatus: "unknown",
        identifiedAs: "unknown",
        confidence: 0.5,
        issues: ["Analysis error"],
        recommendations: ["Please try again with a different image"],
      };
      followupQuestions = [
        "Can you try with a clearer image?",
        "What specific concerns do you have about your crop?",
        "When did you first notice these issues?",
      ];
      analysisText =
        "I encountered an error analyzing this image. Please try again with a clearer image.";
    }

    // Find the chat history for this specific context/session
    let chatHistory = await ChatHistory.findOne({
      userId,
      "contextData.contextId": contextId
    }).sort({ updatedAt: -1 });

    if (!chatHistory) {
      // Create a new chat history with default values
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      today.setMinutes(today.getMinutes() + 330); // Add 5 hours and 30 minutes for Indian timezone

      chatHistory = new ChatHistory({
        userId,
        userName: "User",
        userRole: "Farmer",
        messages: [],
        contextData: { contextId: contextId || Date.now().toString() },
        chatDate: today,
      });

      // Try to get user details if available
      try {
        const User = mongoose.model("User");
        const user = await User.findById(userId);
        if (user) {
          chatHistory.userName = user.name || "User";
          chatHistory.userRole = user.role || "Farmer";
        }
      } catch (error) {
        console.log("User not found, using default values");
      }
    }

    // Add the user message with image first
    const userMessage = {
      text: userQuery,
      sender: "user",
      timestamp: new Date(),
      language: userLanguage,
      attachments: [
        {
          type: "image",
          url: imageUrl,
          name: path.basename(imageUrl),
        },
      ],
    };

    // Add the analysis response message
    const botMessage = {
      text: analysisText,
      sender: "bot",
      timestamp: new Date(),
      language: userLanguage,
      attachments: [
        {
          type: "image",
          url: imageUrl,
          analysisResult,
        },
      ],
      followupQuestions,
    };

    // Add both messages to chat history
    chatHistory.messages.push(userMessage);
    chatHistory.messages.push(botMessage);

    // Update context data with analysis results
    if (!chatHistory.contextData.analyses) {
      chatHistory.contextData.analyses = [];
    }

    chatHistory.contextData.analyses.push({
      timestamp: new Date(),
      imageUrl,
      result: analysisResult,
    });

    console.log("About to save chatHistory for image analysis");
    console.log("ChatHistory document:", {
      _id: chatHistory._id,
      userId: chatHistory.userId,
      contextId: chatHistory.contextData?.contextId,
      messagesCount: chatHistory.messages?.length
    });
    
    try {
      const savedDoc = await chatHistory.save();
      console.log("Successfully saved chatHistory for image analysis:", savedDoc._id);
      console.log("Saved document messages count:", savedDoc.messages?.length);
    } catch (saveError) {
      console.error("Error saving chatHistory for image analysis:", saveError);
      console.error("Save error details:", {
        message: saveError.message,
        code: saveError.code,
        name: saveError.name
      });
      throw saveError; // Re-throw to trigger the outer catch block
    }

    res.status(200).json({
      success: true,
      data: {
        analysisResult,
        followupQuestions,
        message: botMessage,
        userMessage: userMessage,
      },
    });
  } catch (error) {
    console.error("Error processing image analysis:", error);
    res.status(500).json({
      success: false,
      message: "Failed to process image analysis",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Get image analysis history for a user
exports.getImageAnalysisHistory = async (req, res) => {
  try {
    // Get the authenticated user ID
    const userId = req.user?.id || req.user?._id;
    console.log("Getting image analysis history for user:", userId);

    // Pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Find chat history entries that contain image attachments
    const chatHistory = await ChatHistory.find({
      userId,
      "messages.attachments.type": "image",
    }).sort({ updatedAt: -1 });

    // Extract messages with image attachments
    const imageAnalyses = [];

    if (chatHistory.length > 0) {
      chatHistory.forEach((chat) => {
        chat.messages.forEach((message) => {
          if (message.attachments && message.attachments.length > 0) {
            message.attachments.forEach((attachment) => {
              if (attachment.type === "image" && attachment.analysisResult) {
                // Create a unique identifier using timestamp and image URL
                const uniqueId = `${message.timestamp.toISOString()}-${attachment.url}`;
                imageAnalyses.push({
                  id: uniqueId,
                  chatId: chat._id,
                  timestamp: message.timestamp,
                  imageUrl: attachment.url,
                  analysis: attachment.analysisResult,
                  text: message.text,
                });
              }
            });
          }
        });
      });
    }

    // If no image analyses found, return empty array
    if (imageAnalyses.length === 0) {
      console.log("No image analysis history found, returning empty array");

      return res.status(200).json({
        success: true,
        data: {
          analyses: [],
          pagination: {
            total: 0,
            page: 1,
            pages: 0,
            limit: limit,
          },
        },
      });
    }

    // Sort by timestamp (newest first)
    imageAnalyses.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Apply pagination
    const paginatedAnalyses = imageAnalyses.slice(skip, skip + limit);

    res.status(200).json({
      success: true,
      data: {
        analyses: paginatedAnalyses,
        pagination: {
          total: imageAnalyses.length,
          page,
          pages: Math.ceil(imageAnalyses.length / limit),
          limit,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching image analysis history:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch image analysis history",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Delete individual image analysis
exports.deleteImageAnalysis = async (req, res) => {
  try {
    const userId = req.user?.id || req.user?._id;
    const { analysisId } = req.params;
    const decodedAnalysisId = decodeURIComponent(analysisId);

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated!",
      });
    }

    if (!analysisId) {
      return res.status(400).json({
        success: false,
        message: "Analysis ID is required!",
      });
    }

    console.log("Deleting image analysis:", decodedAnalysisId, "for user:", userId);

    // Find all chat histories for the user that contain image analyses
    const chatHistories = await ChatHistory.find({
      userId,
      "messages.attachments.type": "image",
    });

    let deleted = false;
    let chatHistoryId = null;

    // Find the specific analysis and remove it
    for (const chatHistory of chatHistories) {
      const messageIndex = chatHistory.messages.findIndex(message => {
        return message.attachments && message.attachments.some(attachment => {
          if (attachment.type === "image" && attachment.analysisResult) {
            // Create a unique identifier similar to what we use in frontend
            const uniqueId = `${message.timestamp.toISOString()}-${attachment.url}`;
            return uniqueId === decodedAnalysisId;
          }
          return false;
        });
      });

      if (messageIndex !== -1) {
        // Remove the message at this index
        chatHistory.messages.splice(messageIndex, 1);
        await chatHistory.save();
        deleted = true;
        chatHistoryId = chatHistory._id;
        break;
      }
    }

    if (!deleted) {
      return res.status(404).json({
        success: false,
        message: "Analysis not found!",
      });
    }

    console.log("Successfully deleted image analysis:", decodedAnalysisId, "from chat:", chatHistoryId);

    return res.status(200).json({
      success: true,
      message: "Analysis deleted successfully!",
    });
  } catch (error) {
    console.error("Error deleting image analysis:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to delete analysis. Please try again.",
    });
  }
};
