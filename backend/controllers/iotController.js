const IOTSensor = require('../models/IOTSensor');
const { validationResult } = require('express-validator');

let iotWebSocket = null;

// Initialize WebSocket instance
exports.initializeWebSocket = (wsInstance) => {
  iotWebSocket = wsInstance;
};

// Get all sensors
exports.getAllSensors = async (req, res) => {
  try {
    const sensors = await IOTSensor.find({ assignedTo: req.user._id })
      .select('-readings')
      .sort({ lastUpdate: -1 });
    res.json(sensors);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching sensors', error: error.message });
  }
};

// Get single sensor
exports.getSensor = async (req, res) => {
  try {
    const sensor = await IOTSensor.findOne({
      _id: req.params.id,
      assignedTo: req.user._id
    });
    
    if (!sensor) {
      return res.status(404).json({ message: 'Sensor not found' });
    }
    
    res.json(sensor);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching sensor', error: error.message });
  }
};

// Create new sensor
exports.createSensor = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const sensor = new IOTSensor({
      ...req.body,
      assignedTo: req.user._id
    });

    await sensor.save();
    res.status(201).json(sensor);
  } catch (error) {
    res.status(500).json({ message: 'Error creating sensor', error: error.message });
  }
};

// Update sensor
exports.updateSensor = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const sensor = await IOTSensor.findOneAndUpdate(
      { _id: req.params.id, assignedTo: req.user._id },
      req.body,
      { new: true, runValidators: true }
    );

    if (!sensor) {
      return res.status(404).json({ message: 'Sensor not found' });
    }

    res.json(sensor);
  } catch (error) {
    res.status(500).json({ message: 'Error updating sensor', error: error.message });
  }
};

// Delete sensor
exports.deleteSensor = async (req, res) => {
  try {
    const sensor = await IOTSensor.findOneAndDelete({
      _id: req.params.id,
      assignedTo: req.user._id
    });

    if (!sensor) {
      return res.status(404).json({ message: 'Sensor not found' });
    }

    res.json({ message: 'Sensor deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Error deleting sensor', error: error.message });
  }
};

// Update sensor readings
exports.updateReadings = async (req, res) => {
  try {
    const sensor = await IOTSensor.findOne({
      _id: req.params.id,
      assignedTo: req.user._id
    });

    if (!sensor) {
      return res.status(404).json({ message: 'Sensor not found' });
    }

    await sensor.updateReadings(req.body.readings);
    
    // Broadcast update via WebSocket
    if (iotWebSocket) {
      iotWebSocket.broadcastToUser(sensor.assignedTo.toString(), {
        type: 'SENSOR_UPDATE',
        data: {
          sensorId: sensor._id,
          readings: req.body.readings,
          lastUpdate: sensor.lastUpdate
        }
      });
    }

    res.json({ message: 'Readings updated successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Error updating readings', error: error.message });
  }
};

// Update sensor battery level
exports.updateBatteryLevel = async (req, res) => {
  try {
    const sensor = await IOTSensor.findOne({
      _id: req.params.id,
      assignedTo: req.user._id
    });

    if (!sensor) {
      return res.status(404).json({ message: 'Sensor not found' });
    }

    await sensor.updateBatteryLevel(req.body.batteryLevel);
    
    // Broadcast update via WebSocket
    if (iotWebSocket) {
      iotWebSocket.broadcastToUser(sensor.assignedTo.toString(), {
        type: 'BATTERY_UPDATE',
        data: {
          sensorId: sensor._id,
          batteryLevel: sensor.batteryLevel
        }
      });
    }

    res.json({ message: 'Battery level updated successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Error updating battery level', error: error.message });
  }
};

// Get sensor readings history
exports.getReadingsHistory = async (req, res) => {
  try {
    const sensor = await IOTSensor.findOne({
      _id: req.params.id,
      assignedTo: req.user._id
    }).select('readings');

    if (!sensor) {
      return res.status(404).json({ message: 'Sensor not found' });
    }

    res.json(sensor.readings);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching readings history', error: error.message });
  }
};

// Get sensor statistics
exports.getSensorStats = async (req, res) => {
  try {
    const sensor = await IOTSensor.findOne({
      _id: req.params.id,
      assignedTo: req.user._id
    });

    if (!sensor) {
      return res.status(404).json({ message: 'Sensor not found' });
    }

    const stats = {
      totalReadings: sensor.readings.length,
      lastUpdate: sensor.lastUpdate,
      batteryLevel: sensor.batteryLevel,
      status: sensor.status,
      averageReadings: {}
    };

    // Calculate averages for each parameter
    if (sensor.readings.length > 0) {
      const parameters = Object.keys(sensor.readings[0].values);
      parameters.forEach(param => {
        const values = sensor.readings.map(r => r.values[param]).filter(v => v !== undefined);
        if (values.length > 0) {
          stats.averageReadings[param] = values.reduce((a, b) => a + b, 0) / values.length;
        }
      });
    }

    res.json(stats);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching sensor statistics', error: error.message });
  }
}; 