const weatherService = require('../services/weatherService');

const weatherController = async (req, res) => {
  try {
    const { farmerId } = req.query;
    
    if (farmerId) {
      // Get weather for specific farmer
      const weatherData = await weatherService.getFarmerWeather(farmerId);
      res.status(200).json({
        success: true,
        data: weatherData
      });
    } else {
      // Get weather for all farmers
      const allFarmersWeather = await weatherService.getAllFarmersWeather();
      res.status(200).json({
        success: true,
        data: allFarmersWeather
      });
    }
  } catch (error) {
    console.error('Weather controller error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch weather data',
      error: error.message
    });
  }
};

module.exports = { weatherController }; 