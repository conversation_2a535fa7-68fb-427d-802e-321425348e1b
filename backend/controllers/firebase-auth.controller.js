// Firebase Authentication Controller
const admin = require('firebase-admin');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const config = require('../config/config');
const otpService = require('../services/otp.service');

// JWT Secret
const JWT_SECRET = config.jwtSecret;

/**
 * Verify Firebase ID token and create a session
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.verifyIdToken = async (req, res) => {
  try {
    const { idToken } = req.body;
    
    if (!idToken) {
      return res.status(400).json({ success: false, message: 'ID token is required' });
    }
    
    // Verify the ID token
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    const { uid, phone_number, email } = decodedToken;
    
    // Check if user exists in database
    let user = await User.findOne({ firebaseUid: uid });
    
    if (!user) {
      // Create new user if not exists
      user = new User({
        firebaseUid: uid,
        phone: phone_number,
        email: email || '',
        role: 'farmer', // Default role
        isActive: true
      });
      
      await user.save();
    }
    
    // Generate JWT token
    const token = jwt.sign(
      { 
        id: user._id,
        firebaseUid: uid,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: config.jwtExpiration }
    );
    
    // Return user data and token
    res.status(200).json({
      success: true,
      message: 'Authentication successful',
      token,
      user: {
        id: user._id,
        phone: user.phone,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.isActive
      }
    });
  } catch (error) {
    console.error('Firebase authentication error:', error);
    res.status(401).json({ success: false, message: 'Authentication failed', error: error.message });
  }
};

/**
 * Send OTP for phone authentication
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.sendOTP = async (req, res) => {
  try {
    const { phone } = req.body;
    
    if (!phone) {
      return res.status(400).json({ success: false, message: 'Phone number is required' });
    }
    
    // Generate OTP
    const otp = otpService.generateOTP();
    
    // Store OTP
    otpService.storeOTP(phone, otp);
    
    // Send OTP (mock implementation)
    await otpService.sendOTP(phone, otp);
    
    res.status(200).json({
      success: true,
      message: 'OTP sent successfully',
      phone
    });
  } catch (error) {
    console.error('Error sending OTP:', error);
    res.status(500).json({ success: false, message: 'Failed to send OTP', error: error.message });
  }
};

/**
 * Verify OTP for phone authentication
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.verifyOTP = async (req, res) => {
  try {
    const { phone, otp } = req.body;
    
    if (!phone || !otp) {
      return res.status(400).json({ success: false, message: 'Phone number and OTP are required' });
    }
    
    // Verify OTP
    const isValid = otpService.verifyOTP(phone, otp);
    
    if (!isValid) {
      return res.status(401).json({ success: false, message: 'Invalid or expired OTP' });
    }
    
    // Check if user exists
    let user = await User.findOne({ phone });
    
    if (!user) {
      // Create new user if not exists
      user = new User({
        phone,
        role: 'farmer', // Default role
        isActive: true
      });
      
      await user.save();
    }
    
    // Generate JWT token
    const token = jwt.sign(
      { 
        id: user._id,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: config.jwtExpiration }
    );
    
    // Return user data and token
    res.status(200).json({
      success: true,
      message: 'OTP verification successful',
      token,
      user: {
        id: user._id,
        phone: user.phone,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.isActive
      }
    });
  } catch (error) {
    console.error('Error verifying OTP:', error);
    res.status(500).json({ success: false, message: 'Failed to verify OTP', error: error.message });
  }
};

/**
 * Get current user profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCurrentUser = async (req, res) => {
  try {
    const userId = req.user.id;
    
    const user = await User.findById(userId).select('-password');
    
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }
    
    res.status(200).json({
      success: true,
      user: {
        id: user._id,
        phone: user.phone,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.isActive
      }
    });
  } catch (error) {
    console.error('Error getting current user:', error);
    res.status(500).json({ success: false, message: 'Failed to get user profile', error: error.message });
  }
};
