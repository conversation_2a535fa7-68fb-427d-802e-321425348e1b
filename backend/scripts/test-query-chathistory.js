const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

const uri = process.env.AZURE_COSMOS_DB_URI;
const dbName = 'qdb';
const collectionName = 'ChatHistory';
const userIdString = '687e0b68857b993eef00c36b';
const userIdObjectId = new ObjectId(userIdString);

async function testQueryChatHistory() {
  if (!uri) {
    console.error('AZURE_COSMOS_DB_URI not set in .env');
    process.exit(1);
  }
  const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });
  try {
    await client.connect();
    const db = client.db(dbName);
    const collection = db.collection(collectionName);
    const twoWeeksAgo = new Date();
    twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);

    // Try with ObjectId
    try {
      console.log('--- Query with ObjectId ---');
      const resultsObjId = await collection.find({
        userId: userIdObjectId,
        chatDate: { $gte: twoWeeksAgo },
      })
      .sort({ chatDate: -1, updatedAt: -1 })
      .limit(30)
      .toArray();
      console.log('Results (ObjectId):', resultsObjId.length);
      if (resultsObjId.length > 0) console.log('Sample:', resultsObjId[0]);
    } catch (err) {
      console.error('Error with ObjectId query:', err);
    }

    // Try with String
    try {
      console.log('--- Query with String ---');
      const resultsStr = await collection.find({
        userId: userIdString,
        chatDate: { $gte: twoWeeksAgo },
      })
      .sort({ chatDate: -1, updatedAt: -1 })
      .limit(30)
      .toArray();
      console.log('Results (String):', resultsStr.length);
      if (resultsStr.length > 0) console.log('Sample:', resultsStr[0]);
    } catch (err) {
      console.error('Error with String query:', err);
    }

  } catch (err) {
    console.error('Error testing chat history query:', err);
  } finally {
    await client.close();
    console.log('Connection closed');
  }
}

if (require.main === module) {
  testQueryChatHistory();
} 