const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

const uri = process.env.AZURE_COSMOS_DB_URI;
const dbName = 'qdb';
const collectionName = 'ChatHistory';

async function insertTestChatHistory() {
  if (!uri) {
    console.error('AZURE_COSMOS_DB_URI not set in .env');
    process.exit(1);
  }
  const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });
  try {
    await client.connect();
    const db = client.db(dbName);
    const collection = db.collection(collectionName);

    // Try with ObjectId
    const docWithObjectId = {
      userId: new ObjectId('687e0b68857b993eef00c36b'),
      userName: 'Test User',
      userRole: 'Farmer',
      messages: [{ text: 'Hello', sender: 'user', timestamp: new Date(), language: 'en' }],
      contextData: {},
      chatDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
    };

    // Try with String
    const docWithStringId = {
      userId: '687e0b68857b993eef00c36b',
      userName: 'Test User String',
      userRole: 'Farmer',
      messages: [{ text: 'Hello', sender: 'user', timestamp: new Date(), language: 'en' }],
      contextData: {},
      chatDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
    };

    const result1 = await collection.insertOne(docWithObjectId);
    console.log('Inserted with ObjectId:', result1.insertedId);

    const result2 = await collection.insertOne(docWithStringId);
    console.log('Inserted with String:', result2.insertedId);

  } catch (err) {
    console.error('Error inserting test chat history:', err);
  } finally {
    await client.close();
    console.log('Connection closed');
  }
}

if (require.main === module) {
  insertTestChatHistory();
} 