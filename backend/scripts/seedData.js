const mongoose = require('mongoose');
const Farmer = require('../models/Farmer');
const Farm = require('../models/Farm');
require('dotenv').config();

async function seedData() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/agricare');
    console.log('Connected to MongoDB');

    // Create farmer
    const farmer = await Farmer.create({
      name: 'Test Farmer',
      mobile: '9611966747',
      aadharNumber: '************',
      panNumber: '**********',
      state: 'Karnataka',
      district: 'Bangalore Rural',
      farmSize: '5 acres',
      cropType: 'Mixed',
      irrigationStatus: 'Well',
      geoLocation: {
        type: 'Point',
        coordinates: [77.5946, 12.9716] // Bangalore coordinates
      }
    });
    console.log('Created farmer:', farmer);

    // Create farm
    const farm = await Farm.create({
      farmerId: farmer._id,
      farmerName: farmer.name,
      state: farmer.state,
      district: farmer.district,
      location: {
        type: 'Point',
        coordinates: farmer.geoLocation.coordinates
      },
      farmSize: farmer.farmSize,
      cropType: farmer.cropType,
      irrigationStatus: farmer.irrigationStatus
    });
    console.log('Created farm:', farm);

    console.log('Data seeding completed');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding data:', error);
    process.exit(1);
  }
}

seedData(); 