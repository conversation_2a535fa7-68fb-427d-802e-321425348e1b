const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

const uri = process.env.AZURE_COSMOS_DB_URI;
const dbName = 'qdb';
const collectionName = 'ChatHistory';

async function debugChatSave() {
  if (!uri) {
    console.error('AZURE_COSMOS_DB_URI not set in .env');
    process.exit(1);
  }
  const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });
  try {
    await client.connect();
    console.log('Connected to Azure Cosmos DB');
    const db = client.db(dbName);
    const collection = db.collection(collectionName);
    
    console.log('Monitoring ChatHistory collection for new documents...');
    console.log('Send a chat message or image to see what gets saved.');
    
    // Keep checking for new documents
    let lastCount = 0;
    setInterval(async () => {
      try {
        const currentCount = await collection.countDocuments();
        if (currentCount !== lastCount) {
          console.log(`\n📊 Document count changed: ${lastCount} -> ${currentCount}`);
          lastCount = currentCount;
          
          // Get the latest documents
          const latestDocs = await collection.find({}).sort({ _id: -1 }).limit(3).toArray();
          
          latestDocs.forEach((doc, i) => {
            console.log(`\n--- Document ${i + 1} ---`);
            console.log('ID:', doc._id);
            console.log('UserName:', doc.userName);
            console.log('ContextId:', doc.contextData?.contextId);
            console.log('Messages count:', doc.messages?.length || 0);
            
            if (doc.messages && doc.messages.length > 0) {
              doc.messages.forEach((msg, j) => {
                console.log(`  Message ${j + 1}:`, {
                  sender: msg.sender,
                  text: msg.text?.substring(0, 50) + '...',
                  hasAttachments: !!(msg.attachments && msg.attachments.length > 0),
                  hasAttachment: !!msg.attachment,
                  attachmentType: msg.attachments?.[0]?.type || msg.attachment?.type
                });
                
                if (msg.attachments && msg.attachments.length > 0) {
                  console.log('    Attachments:', msg.attachments);
                }
                if (msg.attachment) {
                  console.log('    Attachment:', msg.attachment);
                }
              });
            }
          });
        }
      } catch (err) {
        console.error('Error monitoring:', err);
      }
    }, 2000);
    
  } catch (err) {
    console.error('Error setting up monitoring:', err);
  }
}

if (require.main === module) {
  debugChatSave();
} 