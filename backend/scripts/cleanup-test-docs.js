const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

const uri = process.env.AZURE_COSMOS_DB_URI;
const dbName = 'qdb';
const collectionName = 'ChatHistory';

async function cleanupTestDocs() {
  if (!uri) {
    console.error('AZURE_COSMOS_DB_URI not set in .env');
    process.exit(1);
  }
  const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });
  try {
    await client.connect();
    console.log('Connected to Azure Cosmos DB');
    const db = client.db(dbName);
    const collection = db.collection(collectionName);
    
    // Remove test documents (those with userName containing "Test")
    const result = await collection.deleteMany({
      userName: { $regex: /Test/ }
    });
    console.log(`Deleted ${result.deletedCount} test documents`);
    
    // Check remaining documents
    const remaining = await collection.find({}).toArray();
    console.log(`Remaining documents: ${remaining.length}`);
    remaining.forEach((doc, i) => {
      console.log(`Document ${i + 1}:`, {
        _id: doc._id,
        userName: doc.userName,
        contextId: doc.contextData?.contextId,
        messageCount: doc.messages?.length
      });
    });
    
  } catch (err) {
    console.error('Error cleaning up test docs:', err);
  } finally {
    await client.close();
    console.log('Connection closed');
  }
}

if (require.main === module) {
  cleanupTestDocs();
} 