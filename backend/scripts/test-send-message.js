const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

const uri = process.env.AZURE_COSMOS_DB_URI;
const dbName = 'qdb';
const userId = '687e0b68857b993eef00c36b';

async function testSendMessage() {
  if (!uri) {
    console.error('AZURE_COSMOS_DB_URI not set in .env');
    process.exit(1);
  }
  const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });
  try {
    await client.connect();
    console.log('Connected to Azure Cosmos DB');
    const db = client.db(dbName);
    const chatHistoryCollection = db.collection('ChatHistory');
    
    // Check if there are any chat history documents
    const allChats = await chatHistoryCollection.find({}).toArray();
    console.log('Total chat history documents:', allChats.length);
    
    if (allChats.length > 0) {
      console.log('Sample chat history document:', allChats[0]);
    }
    
    // Check for chats for our specific user
    const userChats = await chatHistoryCollection.find({ 
      userId: new ObjectId(userId) 
    }).toArray();
    console.log('Chats for user:', userChats.length);
    
    if (userChats.length > 0) {
      console.log('User chat document:', userChats[0]);
    }
    
  } catch (err) {
    console.error('Error testing send message:', err);
  } finally {
    await client.close();
    console.log('Connection closed');
  }
}

if (require.main === module) {
  testSendMessage();
} 