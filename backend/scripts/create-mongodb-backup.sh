#!/bin/bash
# MongoDB Backup Script for AgriCare
# This script creates a backup of the MongoDB database and compresses it

# Exit on error
set -e

# Configuration
BACKUP_DIR="../mongodb_backup"
TEMP_DIR="$BACKUP_DIR/temp"
MONGODB_URI="${MONGODB_URI:-mongodb://localhost:27017/agricare}"
DATABASE_NAME="agricare"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILENAME="mongodb_backup.tar.gz"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_FILENAME"
OLD_BACKUP_PATH="$BACKUP_DIR/${BACKUP_FILENAME}.${TIMESTAMP}.bak"

# Create directories if they don't exist
mkdir -p "$BACKUP_DIR"
mkdir -p "$TEMP_DIR"

echo "🔄 Creating MongoDB backup..."
echo "📂 Backup directory: $BACKUP_DIR"
echo "🗄️ Database: $DATABASE_NAME"

# Check if mongodump is installed
if ! command -v mongodump &> /dev/null; then
    echo "❌ mongodump command not found. Please install MongoDB Database Tools."
    exit 1
fi

# Backup existing backup file if it exists
if [ -f "$BACKUP_PATH" ]; then
    echo "🔄 Backing up existing backup file to $OLD_BACKUP_PATH"
    cp "$BACKUP_PATH" "$OLD_BACKUP_PATH"
fi

# Create the backup
echo "🔄 Running mongodump..."
mongodump --uri="$MONGODB_URI" --out="$TEMP_DIR/exports" --db="$DATABASE_NAME"

# Check if backup was successful
if [ $? -ne 0 ]; then
    echo "❌ mongodump failed"
    exit 1
fi

# Compress the backup
echo "🔄 Compressing backup..."
cd "$TEMP_DIR"
tar -czvf "../$BACKUP_FILENAME" exports/

# Check if compression was successful
if [ $? -ne 0 ]; then
    echo "❌ Compression failed"
    exit 1
fi

# Clean up temp directory
echo "🔄 Cleaning up temporary files..."
cd ..
rm -rf "$TEMP_DIR"

echo "✅ MongoDB backup created successfully: $BACKUP_PATH"
echo "📊 Backup size: $(du -h "$BACKUP_PATH" | cut -f1)"

# Verify the backup
echo "🔄 Verifying backup..."
if tar -tzf "$BACKUP_PATH" > /dev/null 2>&1; then
    echo "✅ Backup verification successful"
else
    echo "❌ Backup verification failed"
    exit 1
fi

echo "✅ Backup process completed successfully"
