const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

const uri = process.env.AZURE_COSMOS_DB_URI;
const dbName = 'qdb';
const collectionName = 'User';
const userId = '687e0b68857b993eef00c36b';

const testUser = {
  _id: new ObjectId(userId),
  firebaseUid: 'dev_9561388306',
  phoneNumber: '9561388306',
  name: '<PERSON>',
  email: '<EMAIL>',
  role: 'Farmer',
  preferredLanguage: 'en',
  createdAt: new Date('2025-07-21T09:42:00.714Z'),
  lastLogin: new Date('2025-07-21T09:42:00.714Z'),
  __v: 0
};

async function insertTestUser() {
  if (!uri) {
    console.error('AZURE_COSMOS_DB_URI not set in .env');
    process.exit(1);
  }
  const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });
  try {
    await client.connect();
    console.log('Connected to Azure Cosmos DB');
    const db = client.db(dbName);
    const collection = db.collection(collectionName);
    const existing = await collection.findOne({ _id: new ObjectId(userId) });
    if (existing) {
      console.log('User already exists:', existing);
    } else {
      const result = await collection.insertOne(testUser);
      console.log('Inserted test user:', result.insertedId);
    }
  } catch (err) {
    console.error('Error inserting user:', err);
  } finally {
    await client.close();
    console.log('Connection closed');
  }
}

if (require.main === module) {
  insertTestUser();
} 