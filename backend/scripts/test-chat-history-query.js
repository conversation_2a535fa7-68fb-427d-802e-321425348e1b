const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

const uri = process.env.AZURE_COSMOS_DB_URI;
const dbName = 'qdb';
const collectionName = 'ChatHistory';
const userId = '687e0b68857b993eef00c36b';

async function testChatHistoryQuery() {
  if (!uri) {
    console.error('AZURE_COSMOS_DB_URI not set in .env');
    process.exit(1);
  }
  const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });
  try {
    await client.connect();
    console.log('Connected to Azure Cosmos DB');
    const db = client.db(dbName);
    const collection = db.collection(collectionName);
    
    // Test the exact query from the backend
    const twoWeeksAgo = new Date();
    twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
    
    console.log('Testing query with userId:', userId);
    console.log('Testing query with twoWeeksAgo:', twoWeeksAgo);
    
    const chatHistory = await collection.find({
      userId: new ObjectId(userId),
      chatDate: { $gte: twoWeeksAgo },
    })
    .sort({ chatDate: -1, updatedAt: -1 })
    .limit(30)
    .toArray();
    
    console.log('Query executed successfully!');
    console.log('Number of documents found:', chatHistory.length);
    
    if (chatHistory.length > 0) {
      console.log('Sample document:', chatHistory[0]);
    }
    
  } catch (err) {
    console.error('Error testing query:', err);
  } finally {
    await client.close();
    console.log('Connection closed');
  }
}

if (require.main === module) {
  testChatHistoryQuery();
} 