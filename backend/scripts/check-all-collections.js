const { MongoClient } = require('mongodb');
require('dotenv').config();

const uri = process.env.AZURE_COSMOS_DB_URI;
const dbName = 'qdb';

async function checkAllCollections() {
  if (!uri) {
    console.error('AZURE_COSMOS_DB_URI not set in .env');
    process.exit(1);
  }
  const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });
  try {
    await client.connect();
    console.log('Connected to Azure Cosmos DB');
    const db = client.db(dbName);
    
    // List all collections
    const collections = await db.listCollections().toArray();
    console.log('All collections in database:');
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
    // Check each collection for documents
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      console.log(`Collection '${collection.name}': ${count} documents`);
      
      if (count > 0) {
        const sampleDoc = await db.collection(collection.name).findOne();
        console.log(`Sample document from '${collection.name}':`, sampleDoc);
      }
    }
    
  } catch (err) {
    console.error('Error checking collections:', err);
  } finally {
    await client.close();
    console.log('Connection closed');
  }
}

if (require.main === module) {
  checkAllCollections();
} 