const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

const uri = process.env.AZURE_COSMOS_DB_URI;
const dbName = 'qdb';
const collectionName = 'User';
const userId = '687e0b68857b993eef00c36b'; // Replace with the userId you want to check

async function checkUserExists() {
  if (!uri) {
    console.error('AZURE_COSMOS_DB_URI not set in .env');
    process.exit(1);
  }
  const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });
  try {
    await client.connect();
    console.log('Connected to Azure Cosmos DB');
    const db = client.db(dbName);
    const collection = db.collection(collectionName);
    const user = await collection.findOne({ _id: new ObjectId(userId) });
    if (user) {
      console.log('User found:', user);
    } else {
      console.log('User NOT found with _id:', userId);
    }
  } catch (err) {
    console.error('Error checking user:', err);
  } finally {
    await client.close();
    console.log('Connection closed');
  }
}

if (require.main === module) {
  checkUserExists();
} 