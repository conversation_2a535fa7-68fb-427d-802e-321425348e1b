const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Import all models
const User = require('../models/User');
const Farmer = require('../models/Farmer');
const Farm = require('../models/Farm');
const FarmData = require('../models/FarmData');
const FarmFinancial = require('../models/FarmFinancial');
const Task = require('../models/Task');
const Event = require('../models/Event');
const ChatHistory = require('../models/ChatHistory');
const AIConversation = require('../models/AIConversation');
const Livestock = require('../models/Livestock');
const HealthRecord = require('../models/HealthRecord');
const VetAppointment = require('../models/VetAppointment');
const WeatherData = require('../models/WeatherData');
const IOTSensor = require('../models/IOTSensor');

// Models mapping
const models = {
  users: User,
  farmers: Farmer,
  farms: Farm,
  farmdatas: FarmData,
  farmfinancials: FarmFinancial,
  tasks: Task,
  events: Event,
  chathistories: ChatHistory,
  aiconversations: AIConversation,
  livestock: Livestock,
  healthrecords: HealthRecord,
  vetappointments: VetAppointment,
  weatherdatas: WeatherData,
  iotsensors: IOTSensor
};

// Connect to local MongoDB for reading
const connectToLocalDB = async () => {
  try {
    const localOptions = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      retryWrites: false,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    };

    await mongoose.connect(
      process.env.LOCAL_MONGODB_URI || "mongodb://localhost:27017/agricare",
      localOptions
    );
    console.log("✅ Connected to local MongoDB for data extraction");
  } catch (error) {
    console.error("❌ Failed to connect to local MongoDB:", error.message);
    throw error;
  }
};

// Connect to Azure Cosmos DB
const connectToAzureDB = async () => {
  try {
    const azureOptions = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      retryWrites: false,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      maxPoolSize: 10,
      minPoolSize: 1,
      maxIdleTimeMS: 120000,
      ssl: true,
      sslValidate: true,
      replicaSet: 'globaldb',
      appName: '@qdb@'
    };

    const connectionString = process.env.AZURE_COSMOS_DB_URI;
    if (!connectionString) {
      throw new Error("AZURE_COSMOS_DB_URI not found in environment variables");
    }

    await mongoose.connect(connectionString, azureOptions);
    console.log("✅ Connected to Azure Cosmos DB for data import");
  } catch (error) {
    console.error("❌ Failed to connect to Azure Cosmos DB:", error.message);
    throw error;
  }
};

// Migrate data for a specific collection
const migrateCollection = async (collectionName, Model) => {
  try {
    console.log(`🔄 Migrating ${collectionName}...`);
    
    // Get all documents from local database
    const documents = await Model.find({});
    console.log(`📊 Found ${documents.length} documents in ${collectionName}`);
    
    if (documents.length === 0) {
      console.log(`⚠️  No documents found in ${collectionName}, skipping...`);
      return;
    }
    
    // Clear existing data in Azure (optional - comment out if you want to keep existing data)
    // await Model.deleteMany({});
    // console.log(`🗑️  Cleared existing ${collectionName} data in Azure`);
    
    // Insert documents into Azure
    const result = await Model.insertMany(documents, { 
      ordered: false, // Continue even if some documents fail
      rawResult: true 
    });
    
    console.log(`✅ Successfully migrated ${result.insertedCount} documents to ${collectionName}`);
    
    if (result.writeErrors && result.writeErrors.length > 0) {
      console.log(`⚠️  ${result.writeErrors.length} documents failed to migrate in ${collectionName}`);
    }
    
  } catch (error) {
    console.error(`❌ Error migrating ${collectionName}:`, error.message);
    throw error;
  }
};

// Main migration function
const migrateData = async () => {
  try {
    console.log("🚀 Starting data migration from local MongoDB to Azure Cosmos DB...");
    
    // Connect to local database
    await connectToLocalDB();
    
    // Extract data from local database
    const localData = {};
    for (const [collectionName, Model] of Object.entries(models)) {
      try {
        localData[collectionName] = await Model.find({});
        console.log(`📥 Extracted ${localData[collectionName].length} documents from ${collectionName}`);
      } catch (error) {
        console.error(`❌ Error extracting ${collectionName}:`, error.message);
      }
    }
    
    // Disconnect from local database
    await mongoose.disconnect();
    console.log("✅ Disconnected from local MongoDB");
    
    // Connect to Azure database
    await connectToAzureDB();
    
    // Migrate data to Azure
    for (const [collectionName, Model] of Object.entries(models)) {
      if (localData[collectionName] && localData[collectionName].length > 0) {
        try {
          // Clear existing data first
          await Model.deleteMany({});
          console.log(`🗑️  Cleared existing ${collectionName} data in Azure`);
          
          // Insert new data
          const result = await Model.insertMany(localData[collectionName], { 
            ordered: false,
            rawResult: true 
          });
          
          console.log(`✅ Successfully migrated ${result.insertedCount} documents to ${collectionName}`);
          
          if (result.writeErrors && result.writeErrors.length > 0) {
            console.log(`⚠️  ${result.writeErrors.length} documents failed to migrate in ${collectionName}`);
          }
        } catch (error) {
          console.error(`❌ Error migrating ${collectionName}:`, error.message);
        }
      }
    }
    
    console.log("🎉 Data migration completed successfully!");
    
  } catch (error) {
    console.error("❌ Migration failed:", error.message);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log("✅ Disconnected from Azure Cosmos DB");
  }
};

// Run migration if this script is executed directly
if (require.main === module) {
  migrateData();
}

module.exports = { migrateData }; 