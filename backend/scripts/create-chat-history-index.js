const { MongoClient } = require('mongodb');
require('dotenv').config();

const uri = process.env.AZURE_COSMOS_DB_URI;
const dbName = 'qdb';
const collectionName = 'ChatHistory';

async function createCompoundIndex() {
  if (!uri) {
    console.error('AZURE_COSMOS_DB_URI not set in .env');
    process.exit(1);
  }
  const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });
  try {
    await client.connect();
    console.log('Connected to Azure Cosmos DB');
    const db = client.db(dbName);
    const collection = db.collection(collectionName);
    const result = await collection.createIndex({ chatDate: -1, updatedAt: -1 });
    console.log(`Compound index created: ${result}`);
    const indexes = await collection.indexes();
    console.log('Current indexes:', indexes);
  } catch (err) {
    console.error('Error creating index:', err);
  } finally {
    await client.close();
    console.log('Connection closed');
  }
}

if (require.main === module) {
  createCompoundIndex();
} 