const { MongoClient } = require('mongodb');
require('dotenv').config();

const uri = process.env.AZURE_COSMOS_DB_URI;
const dbName = 'qdb';
const collectionName = 'ChatHistory';

async function checkIndexesAndSampleDocs() {
  if (!uri) {
    console.error('AZURE_COSMOS_DB_URI not set in .env');
    process.exit(1);
  }
  const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });
  try {
    await client.connect();
    console.log('Connected to Azure Cosmos DB');
    const db = client.db(dbName);
    const collection = db.collection(collectionName);
    const indexes = await collection.indexes();
    console.log('Current indexes:', indexes);
    const sampleDocs = await collection.find({}).limit(5).toArray();
    console.log('Sample documents:');
    sampleDocs.forEach((doc, i) => {
      console.log(`Document ${i + 1}:`, {
        _id: doc._id,
        chatDate: doc.chatDate,
        updatedAt: doc.updatedAt,
        userId: doc.userId
      });
    });
  } catch (err) {
    console.error('Error checking indexes or documents:', err);
  } finally {
    await client.close();
    console.log('Connection closed');
  }
}

if (require.main === module) {
  checkIndexesAndSampleDocs();
} 