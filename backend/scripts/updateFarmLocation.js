const mongoose = require('mongoose');
const Farm = require('../models/Farm');
require('dotenv').config();

const MONGODB_URI = 'mongodb://localhost:27017/agricare';

const updateFarmLocation = async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    const result = await Farm.updateOne(
      { farmerId: '67ea3c0701918d4c7822366b' },
      {
        $set: {
          state: 'Karnataka',
          district: 'Bangalore',
          location: {
            type: 'Point',
            coordinates: [77.5946, 12.9716] // Bangalore coordinates
          }
        }
      }
    );

    console.log('Update result:', result);

    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error);
  }
};

updateFarmLocation(); 