const mongoose = require('mongoose');
require('dotenv').config();

const testAzureConnection = async () => {
  try {
    console.log("🔍 Testing Azure Cosmos DB connection...");
    
    // Azure Cosmos DB specific options
    const options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      retryWrites: false,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      maxPoolSize: 10,
      minPoolSize: 1,
      maxIdleTimeMS: 120000,
      ssl: true,
      sslValidate: true,
      replicaSet: 'globaldb',
      appName: '@qdb@'
    };

    const connectionString = process.env.AZURE_COSMOS_DB_URI;
    
    if (!connectionString) {
      console.error("❌ AZURE_COSMOS_DB_URI not found in environment variables");
      console.log("Please add the following to your .env file:");
      console.log("AZURE_COSMOS_DB_URI=**************************************************************************************************************************************************************************************************************************");
      return;
    }

    console.log("📡 Attempting to connect to Azure Cosmos DB...");
    
    const conn = await mongoose.connect(connectionString, options);
    
    console.log(`✅ Successfully connected to Azure Cosmos DB`);
    console.log(`📍 Host: ${conn.connection.host}`);
    console.log(`📊 Database: ${conn.connection.name}`);
    console.log(`🔗 Connection State: ${conn.connection.readyState}`);
    
    // Test database operations
    console.log("\n🧪 Testing database operations...");
    
    // Test ping
    const pingResult = await mongoose.connection.db.admin().ping();
    console.log(`✅ Ping test: ${pingResult.ok ? 'SUCCESS' : 'FAILED'}`);
    
    // Test list collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log(`📚 Found ${collections.length} collections in database`);
    
    if (collections.length > 0) {
      console.log("📋 Collections:");
      collections.forEach(collection => {
        console.log(`   - ${collection.name}`);
      });
    }
    
    // Test basic CRUD operations
    console.log("\n🔧 Testing basic CRUD operations...");
    
    // Create a test collection
    const testCollection = mongoose.connection.db.collection('connection_test');
    
    // Insert test document
    const insertResult = await testCollection.insertOne({
      test: true,
      timestamp: new Date(),
      message: 'Connection test successful'
    });
    console.log(`✅ Insert test: SUCCESS (ID: ${insertResult.insertedId})`);
    
    // Find test document
    const findResult = await testCollection.findOne({ _id: insertResult.insertedId });
    console.log(`✅ Find test: ${findResult ? 'SUCCESS' : 'FAILED'}`);
    
    // Update test document
    const updateResult = await testCollection.updateOne(
      { _id: insertResult.insertedId },
      { $set: { updated: true } }
    );
    console.log(`✅ Update test: ${updateResult.modifiedCount > 0 ? 'SUCCESS' : 'FAILED'}`);
    
    // Delete test document
    const deleteResult = await testCollection.deleteOne({ _id: insertResult.insertedId });
    console.log(`✅ Delete test: ${deleteResult.deletedCount > 0 ? 'SUCCESS' : 'FAILED'}`);
    
    console.log("\n🎉 All connection tests passed! Azure Cosmos DB is ready to use.");
    
  } catch (error) {
    console.error("❌ Connection test failed:", error.message);
    console.error("\n🔧 Troubleshooting tips:");
    console.error("1. Check your internet connection");
    console.error("2. Verify the connection string is correct");
    console.error("3. Ensure the Azure Cosmos DB account is active");
    console.error("4. Check if your IP is whitelisted in Azure");
    console.error("5. Verify the database name and credentials");
  } finally {
    await mongoose.disconnect();
    console.log("✅ Disconnected from Azure Cosmos DB");
  }
};

// Run test if this script is executed directly
if (require.main === module) {
  testAzureConnection();
}

module.exports = { testAzureConnection }; 