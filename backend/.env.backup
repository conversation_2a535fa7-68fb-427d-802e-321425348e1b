# PORT=8000
# AZURE_OPENAI_ENDPOINT="https://image-gpt4o.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview"
# AZURE_OPENAI_API_KEY="5kxS162ETTa3VMG7ou9OueTBgTgklpRtE758oSmJp1DyQMV1o7ZaJQQJ99BBACYeBjFXJ3w3AAABACOG5O2J"
# AZURE_DEPLOYMENT_NAME=gpt-4o  # Name of your deployed model
# AZURE_API_VERSION="2024-08-01-preview"
# AZURE_SPEECH_KEY="BRkk9BHloMtyp0TnyRkTHrhKwvoPT1P4DFeSJ6pP2OJnf1MlfDulJQQJ99BBACGhslBXJ3w3AAAYACOGsXJ4"
# AZURE_SPEECH_REGION=centralindia
# REACT_APP_AZURE_SPEECH_KEY="BRkk9BHloMtyp0TnyRkTHrhKwvoPT1P4DFeSJ6pP2OJnf1MlfDulJQQJ99BBACGhslBXJ3w3AAAYACOGsXJ4"
# REACT_APP_AZURE_SPEECH_ENDPOINT="https://centralindia.api.cognitive.microsoft.com/"

# Environment
NODE_ENV=development

# Server Configuration
PORT=8000

# MongoDB Configuration
MONGODB_URI=**************************************************************************************************************************************************************************************************************************

# Firebase Configuration
FIREBASE_PROJECT_ID=quamin-agricare
GOOGLE_APPLICATION_CREDENTIALS=./config/firebase-credentials.json

# Weather API Configuration
OPENWEATHER_API_KEY=********************************

# Optional APIs (can be configured later)
# WEATHER_API_KEY=********************************

# JWT Configuration
JWT_SECRET=dev_secret_key
JWT_EXPIRES_IN=7d

# Google Translate Configuration (Optional)
GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key

# Add other environment variables below

# Optional: Azure OpenAI Configuration (for production)
AZURE_OPENAI_ENDPOINT="https://image-gpt4o.openai.azure.com"
AZURE_OPENAI_API_KEY="5kxS162ETTa3VMG7ou9OueTBgTgklpRtE758oSmJp1DyQMV1o7ZaJQQJ99BBACYeBjFXJ3w3AAABACOG5O2J"
AZURE_DEPLOYMENT_NAME="gpt-4o"
AZURE_API_VERSION="2024-08-01-preview"

# Azure Speech Configuration
AZURE_SPEECH_KEY="BRkk9BHloMtyp0TnyRkTHrhKwvoPT1P4DFeSJ6pP2OJnf1MlfDulJQQJ99BBACGhslBXJ3w3AAAYACOGsXJ4"
AZURE_SPEECH_REGION="centralindia"
REACT_APP_AZURE_SPEECH_ENDPOINT="https://centralindia.api.cognitive.microsoft.com/"

VITE_AZURE_TRANSLATION_API_KEY=9DGIQrRiywiKFxqm1utRrYcDY7lSMtLDv35VKWGjkoZeztk0yHWIJQQJ99BEACGhslBXJ3w3AAAbACOGB8ph
VITE_AZURE_TRANSLATION_REGION=centralindia