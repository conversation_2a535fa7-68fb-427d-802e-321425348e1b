const fs = require("fs");
const axios = require("axios");
require("dotenv").config({ path: "../.env" });

// ✅ Directly paste your object here
const en = require("../../frontend/src/translations/i18n/locales/en/en.json");
// const en = {
//   back_to_dash_msg: "Back to Dashboard",
//   farm_manage_msg: "Farm Management",
//   add_farm_msg: "Add Farm",
//   livestock_msg: "Livestock Monitoring System",
//   drone_monitoring_msg: "Drone Monitoring & Control",
//   market_analysis_msg: "Market Analysis",
//   farm_profile_msg: "Farm Financial Profile",
//   agri_expert_msg: "AgriXpert Connect",
//   crop_demand_msg: "Crop Demand & Supply Analysis",
// };

// 🔐 Replace with your Azure Translator credentials
const subscriptionKey = process.env.AZURE_TRANSLATION_KEY;
const region = process.env.AZURE_TRANSLATION_REGION;
const endpoint = "https://api.cognitive.microsofttranslator.com";
const targetLanguage = "hi";

async function translateText(textArray) {
  const url = `${endpoint}/translate?api-version=3.0&to=${targetLanguage}`;

  const headers = {
    "Ocp-Apim-Subscription-Key": subscriptionKey,
    "Ocp-Apim-Subscription-Region": region,
    "Content-type": "application/json",
  };

  const body = textArray.map((text) => ({ Text: text }));
  const response = await axios.post(url, body, { headers });
  return response.data.map((item) => item.translations[0].text);
}

(async () => {
  const keys = Object.keys(en);
  const values = Object.values(en);

  try {
    const translations = await translateText(values);

    const hiJson = {};
    keys.forEach((key, index) => {
      hiJson[key] = translations[index];
    });

    // 📁 Output location: adjust to match your frontend
    fs.writeFileSync("./hi2.json", JSON.stringify(hiJson, null, 2), "utf-8");
    console.log("✅ Hindi translation saved to hi2.json");
  } catch (error) {
    console.error(
      "❌ Translation failed:",
      error.response?.data || error.message
    );
  }
})();
