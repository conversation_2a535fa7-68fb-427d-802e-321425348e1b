/**
 * Real Market Data Routes
 * 
 * API routes for real market data.
 */

const express = require('express');
const router = express.Router();
const realMarketDataService = require('../services/realMarketDataService');

/**
 * @route GET /api/real-market/overview
 * @desc Get market overview data
 * @access Public
 */
router.get('/overview', async (req, res) => {
  try {
    const overview = await realMarketDataService.getMarketOverview();
    
    res.json({
      success: true,
      data: overview
    });
  } catch (error) {
    console.error('Error getting market overview:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting market overview',
      error: error.message
    });
  }
});

/**
 * @route GET /api/real-market/states
 * @desc Get available states
 * @access Public
 */
router.get('/states', async (req, res) => {
  try {
    const states = await realMarketDataService.getAvailableStates();
    
    res.json({
      success: true,
      data: states,
      count: states.length
    });
  } catch (error) {
    console.error('Error getting states:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting states',
      error: error.message
    });
  }
});

/**
 * @route GET /api/real-market/mandis
 * @desc Get mandis for a specific state
 * @access Public
 */
router.get('/mandis', async (req, res) => {
  try {
    const { state = 'Maharashtra' } = req.query;
    
    const mandis = await realMarketDataService.getMandisByState(state);
    
    res.json({
      success: true,
      data: mandis,
      count: mandis.length
    });
  } catch (error) {
    console.error('Error getting mandis:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting mandis',
      error: error.message
    });
  }
});

/**
 * @route GET /api/real-market/crops
 * @desc Get available crops
 * @access Public
 */
router.get('/crops', async (req, res) => {
  try {
    const crops = await realMarketDataService.getAvailableCrops();
    
    res.json({
      success: true,
      data: crops,
      count: crops.length
    });
  } catch (error) {
    console.error('Error getting crops:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting crops',
      error: error.message
    });
  }
});

/**
 * @route GET /api/real-market/prices
 * @desc Get mandi prices for a specific crop and state
 * @access Public
 */
router.get('/prices', async (req, res) => {
  try {
    const { crop = 'Wheat', state = 'Maharashtra', mandi = 'Mumbai' } = req.query;
    
    const prices = await realMarketDataService.getMandiPrices(crop, state, mandi);
    
    res.json({
      success: true,
      data: prices
    });
  } catch (error) {
    console.error('Error getting mandi prices:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting mandi prices',
      error: error.message
    });
  }
});

module.exports = router;
