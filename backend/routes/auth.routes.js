const express = require("express");
const router = express.Router();
const { protect } = require("../middleware/auth.middleware");
const {
  sendOTP,
  verifyOTP,
  updateProfile,
  getProfile,
  getFarmerState,
  verifyFirebaseAuth,
} = require("../controllers/auth.controller");
const { verifyIdToken } = require("../controllers/firebase-auth.controller");
const verifyFirebaseToken = require("../middleware/verifyFirebaseToken");
const Farmer = require("../models/Farmer");
const User = require("../models/user.model");
const mongoose = require("mongoose");

// Public routes
router.post("/send-otp", sendOTP);
router.post("/verify-otp", verifyOTP);
router.post("/firebase-auth", verifyIdToken);
router.post("/verify-firebase", verifyFirebaseAuth);

// User profile routes
router.put("/profile", protect, updateProfile);
router.get("/profile", protect, getProfile);
router.get("/farmer-state", protect, getFarmerState);

// Firebase verification endpoint
router.post("/verify-firebase-token", verifyFirebaseToken, async (req, res) => {
  try {
    const { phoneNumber, role } = req.body;
    
    if (!phoneNumber || !role) {
      return res.status(400).json({
        success: false,
        message: "Phone number and role are required"
      });
    }
    
    // Normalize phone number
    const normalizedPhone = phoneNumber.replace(/\D/g, "").slice(-10);
    
    // Verify user exists with the given role
    let userInfo = null;
    
    if (role === "Farmer") {
      const farmer = await Farmer.findOne({ mobile: normalizedPhone });
      if (!farmer) {
        return res.status(404).json({
          success: false,
          message: "No farmer found with this phone number"
        });
      }
      userInfo = {
        id: farmer._id,
        name: farmer.name,
        phoneNumber: normalizedPhone,
        role: "Farmer",
        preferredLanguage: "en",
        state: farmer.state || "Unknown"
      };
    } else if (role === "TM") {
      const tm = await mongoose.model("TM").findOne({ mobile: normalizedPhone });
      if (!tm) {
        return res.status(404).json({
          success: false,
          message: "No territory manager found with this phone number"
        });
      }
      userInfo = {
        id: tm._id,
        name: tm.name,
        phoneNumber: normalizedPhone,
        role: "TM",
        preferredLanguage: "en"
      };
    } else {
      return res.status(400).json({
        success: false,
        message: "Invalid role. Must be 'Farmer' or 'TM'"
      });
    }
    
    // Update or create user in auth system
    let user = await User.findOne({ phoneNumber: normalizedPhone });
    
    if (!user) {
      // Create new user
      user = new User({
        phoneNumber: normalizedPhone,
        name: userInfo.name,
        role: role,
        firebaseUid: req.user.uid || `dev_${normalizedPhone}`,
        lastLogin: new Date()
      });
      await user.save();
    } else {
      // Update existing user
      user.firebaseUid = req.user.uid || `dev_${normalizedPhone}`;
      user.lastLogin = new Date();
      await user.save();
    }
    
    return res.status(200).json({
      success: true,
      user: userInfo
    });
  } catch (error) {
    console.error("Error verifying Firebase token:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to verify user"
    });
  }
});

module.exports = router;
