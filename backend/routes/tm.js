const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');

// TM Schema
const tmSchema = new mongoose.Schema({
    name: { type: String, required: true },
    phoneNumber: { type: String, required: true, unique: true },
    region: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    createdAt: { type: Date, default: Date.now }
});

const TM = mongoose.model('TM', tmSchema);

// Get all TMs
router.get('/list', async (req, res) => {
    try {
        const tms = await TM.find().sort({ createdAt: -1 });
        res.json({
            success: true,
            territoryManagers: tms
        });
    } catch (error) {
        console.error('Error fetching TMs:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch Territory Managers'
        });
    }
});

// Add new TM
router.post('/add', async (req, res) => {
    try {
        const { name, phoneNumber, region } = req.body;

        // Validate input
        if (!name || !phoneNumber || !region) {
            return res.status(400).json({
                success: false,
                message: 'Name, phone number, and region are required'
            });
        }

        // Validate phone number format (10 digits)
        const phoneRegex = /^[0-9]{10}$/;
        if (!phoneRegex.test(phoneNumber)) {
            return res.status(400).json({
                success: false,
                message: 'Phone number must be exactly 10 digits'
            });
        }

        // Check if phone number already exists
        const existingTM = await TM.findOne({ phoneNumber });
        if (existingTM) {
            return res.status(400).json({
                success: false,
                message: 'Phone number already registered'
            });
        }

        // Create new TM
        const tm = new TM({
            name,
            phoneNumber,
            region
        });

        await tm.save();

        res.status(201).json({
            success: true,
            message: 'Territory Manager added successfully',
            territoryManager: tm
        });

    } catch (error) {
        console.error('Error adding TM:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to add Territory Manager'
        });
    }
});

// Update TM
router.put('/update/:id', async (req, res) => {
    try {
        const { name, phoneNumber, region } = req.body;

        // Validate input
        if (!name || !phoneNumber || !region) {
            return res.status(400).json({
                success: false,
                message: 'Please provide name, phone number, and region'
            });
        }

        // Check if TM exists
        const tm = await TM.findById(req.params.id);
        if (!tm) {
            return res.status(404).json({
                success: false,
                message: 'Territory Manager not found'
            });
        }

        // Check if phone number is already in use by another TM
        if (phoneNumber !== tm.phoneNumber) {
            const existingTM = await TM.findOne({ phoneNumber });
            if (existingTM && existingTM._id.toString() !== req.params.id) {
                return res.status(400).json({
                    success: false,
                    message: 'Phone number is already in use by another Territory Manager'
                });
            }
        }

        // Update TM
        tm.name = name;
        tm.phoneNumber = phoneNumber;
        tm.region = region;
        await tm.save();

        res.json({
            success: true,
            message: 'Territory Manager updated successfully',
            territoryManager: tm
        });

    } catch (error) {
        console.error('Error updating TM:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update Territory Manager'
        });
    }
});

// Toggle TM status
router.post('/toggle/:id', async (req, res) => {
    try {
        const tm = await TM.findById(req.params.id);
        if (!tm) {
            return res.status(404).json({
                success: false,
                message: 'Territory Manager not found'
            });
        }

        tm.isActive = !tm.isActive;
        await tm.save();

        res.json({
            success: true,
            message: `Territory Manager ${tm.isActive ? 'activated' : 'deactivated'} successfully`,
            tm
        });

    } catch (error) {
        console.error('Error toggling TM status:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update Territory Manager status'
        });
    }
});

// Helper function to normalize phone number
const normalizePhoneNumber = (phoneNumber) => {
    // Remove any non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');

    // If it starts with country code (e.g., 91 for India), return as is
    if (digits.length > 10) {
        return digits; // Return just the digits for comparison
    }

    // Otherwise, assume Indian number and add 91
    return `91${digits}`;
};

// Verify TM
router.post('/verify', async (req, res) => {
    try {
        const { phoneNumber } = req.body;

        if (!phoneNumber) {
            return res.status(400).json({
                success: false,
                message: 'Phone number is required'
            });
        }

        // Normalize the phone number for consistent comparison
        const normalizedInputNumber = normalizePhoneNumber(phoneNumber);
        console.log('Normalized input phone number:', normalizedInputNumber);

        // Find TM and normalize their stored number for comparison
        const tms = await TM.find();
        const tm = tms.find(t => normalizePhoneNumber(t.phoneNumber) === normalizedInputNumber);
        console.log('Found TM:', tm);

        if (!tm) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized: Not a registered Territory Manager'
            });
        }

        if (!tm.isActive) {
            return res.status(401).json({
                success: false,
                message: 'Unauthorized: Territory Manager account is inactive'
            });
        }

        res.json({
            success: true,
            message: 'Territory Manager verified successfully',
            tm: {
                id: tm._id,
                name: tm.name,
                phoneNumber: tm.phoneNumber
            }
        });

    } catch (error) {
        console.error('Error verifying TM:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to verify Territory Manager'
        });
    }
});

module.exports = router;