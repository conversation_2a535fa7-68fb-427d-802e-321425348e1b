const express = require('express');
const router = express.Router();
const weatherService = require('../services/weatherService');
const auth = require('../middleware/auth');

// Get current weather for a farmer
router.get('/current/:farmerId', auth, async (req, res) => {
  try {
    const weatherData = await weatherService.getCurrentWeather(req.params.farmerId);
    res.json({
      success: true,
      data: weatherData
    });
  } catch (error) {
    console.error('Error in current weather route:', error);

    // Handle specific error cases
    if (error.message.includes('Farmer not found')) {
      return res.status(404).json({
        success: false,
        error: `Farmer not found with the provided ID: ${req.params.farmerId}`
      });
    } else if (error.message.includes('Invalid location data')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid or missing location data for the farmer'
      });
    } else if (error.message.includes('OpenWeather API key')) {
      return res.status(500).json({
        success: false,
        error: 'Weather service configuration error'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to fetch current weather data',
      message: error.message
    });
  }
});

// Get weather forecast for a farmer
router.get('/forecast/:farmerId', auth, async (req, res) => {
  try {
    const forecastData = await weatherService.getForecast(req.params.farmerId);
    res.json({
      success: true,
      data: forecastData
    });
  } catch (error) {
    console.error('Error in forecast route:', error);

    // Handle specific error cases
    if (error.message.includes('Farmer not found')) {
      return res.status(404).json({
        success: false,
        error: `Farmer not found with the provided ID: ${req.params.farmerId}`
      });
    } else if (error.message.includes('Invalid location data')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid or missing location data for the farmer'
      });
    } else if (error.message.includes('OpenWeather API key')) {
      return res.status(500).json({
        success: false,
        error: 'Weather service configuration error'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to fetch weather forecast',
      message: error.message
    });
  }
});

// Get weather bulletin for a farmer
router.get('/bulletin/:farmerId', auth, async (req, res) => {
  try {
    const bulletinText = await weatherService.getWeatherBulletin(req.params.farmerId);
    res.json({
      success: true,
      data: {
        bulletin: bulletinText
      }
    });
  } catch (error) {
    console.error('Error in bulletin route:', error);

    // Handle specific error cases
    if (error.message.includes('Farmer not found')) {
      return res.status(404).json({
        success: false,
        error: `Farmer not found with the provided ID: ${req.params.farmerId}`
      });
    } else if (error.message.includes('Invalid location data')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid or missing location data for the farmer'
      });
    } else if (error.message.includes('OpenWeather API key')) {
      return res.status(500).json({
        success: false,
        error: 'Weather service configuration error'
      });
    } else if (error.message.includes('Failed to fetch weather data')) {
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch required weather data for bulletin generation'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to generate weather bulletin',
      message: error.message
    });
  }
});

module.exports = router;