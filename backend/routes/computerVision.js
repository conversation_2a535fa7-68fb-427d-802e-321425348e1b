const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { ComputerVisionClient } = require('@azure/cognitiveservices-computervision');
const { ApiKeyCredentials } = require('@azure/ms-rest-js');
const config = require('../config/config');

// Configure Azure Computer Vision client
const computerVisionKey = process.env.AZURE_COMPUTER_VISION_KEY || 'd133bd0fbec34843afefea5fcbbb1242';
const computerVisionEndpoint = process.env.AZURE_COMPUTER_VISION_ENDPOINT || 'https://centralindia.api.cognitive.microsoft.com/';

const credentials = new ApiKeyCredentials({ inHeader: { 'Ocp-Apim-Subscription-Key': computerVisionKey } });
const client = new ComputerVisionClient(credentials, computerVisionEndpoint);

// Configure storage for uploaded images
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    const filetypes = /jpeg|jpg|png|gif/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('Only image files are allowed!'));
  }
});

// Analyze image
router.post('/analyze', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, message: 'No image file provided' });
    }

    const imagePath = req.file.path;

    // Read the image file
    const imageBuffer = fs.readFileSync(imagePath);

    // Features to analyze
    const visualFeatures = [
      'Categories',
      'Description',
      'Color',
      'Tags',
      'Objects',
      'Brands'
    ];

    // Analyze the image
    const result = await client.analyzeImage({
      visualFeatures,
      image: imageBuffer
    });

    // Return the analysis results
    res.status(200).json({
      success: true,
      imageUrl: `/uploads/${path.basename(imagePath)}`,
      analysis: result
    });
  } catch (error) {
    console.error('Error analyzing image:', error);
    res.status(500).json({ success: false, message: 'Failed to analyze image', error: error.message });
  }
});

// Detect objects in image
router.post('/detect-objects', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, message: 'No image file provided' });
    }

    const imagePath = req.file.path;

    // Read the image file
    const imageBuffer = fs.readFileSync(imagePath);

    // Detect objects
    const result = await client.detectObjects({
      image: imageBuffer
    });

    // Return the detection results
    res.status(200).json({
      success: true,
      imageUrl: `/uploads/${path.basename(imagePath)}`,
      objects: result.objects
    });
  } catch (error) {
    console.error('Error detecting objects:', error);
    res.status(500).json({ success: false, message: 'Failed to detect objects', error: error.message });
  }
});

// OCR (Read text from image)
router.post('/ocr', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, message: 'No image file provided' });
    }

    const imagePath = req.file.path;

    // Read the image file
    const imageBuffer = fs.readFileSync(imagePath);

    // Read text from image
    const result = await client.read({
      image: imageBuffer
    });

    // The read operation is async, so we need to wait for the result
    let operationLocation = result.operationLocation;
    const operationId = operationLocation.split('/').pop();

    // Poll for the result
    let readResult;
    let attempts = 0;
    const maxAttempts = 10;
    const pollingInterval = 1000; // 1 second

    while (!readResult && attempts < maxAttempts) {
      readResult = await client.getReadResult(operationId);
      if (readResult.status !== 'succeeded' && readResult.status !== 'failed') {
        await new Promise(resolve => setTimeout(resolve, pollingInterval));
        attempts++;
      }
    }

    if (!readResult || readResult.status !== 'succeeded') {
      throw new Error('OCR operation failed or timed out');
    }

    // Extract text from the result
    const textLines = [];
    for (const page of readResult.analyzeResult.readResults) {
      for (const line of page.lines) {
        textLines.push(line.text);
      }
    }

    // Return the OCR results
    res.status(200).json({
      success: true,
      imageUrl: `/uploads/${path.basename(imagePath)}`,
      text: textLines.join(' '),
      lines: textLines
    });
  } catch (error) {
    console.error('Error performing OCR:', error);
    res.status(500).json({ success: false, message: 'Failed to perform OCR', error: error.message });
  }
});

module.exports = router;
