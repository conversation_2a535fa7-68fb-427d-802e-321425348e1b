const express = require('express');
const router = express.Router();
const { 
  getAllAppointments, 
  getFarmerAppointments, 
  getVetAppointments, 
  createAppointment, 
  updateAppointmentStatus, 
  deleteAppointment,
  getAvailableVets
} = require('../controllers/vetAppointmentController');

// Get all appointments
router.get('/', getAllAppointments);

// Get appointments for a specific farmer
router.get('/farmer/:farmerId', getFarmerAppointments);

// Get appointments for a specific veterinarian
router.get('/vet/:vetId', getVetAppointments);

// Create a new appointment
router.post('/', createAppointment);

// Update appointment status
router.put('/:id', updateAppointmentStatus);

// Delete appointment
router.delete('/:id', deleteAppointment);

// Get available veterinarians
router.get('/vets', getAvailableVets);

module.exports = router;
