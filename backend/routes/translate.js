const express = require("express");
const axios = require("axios");
const router = express.Router();
const { translateCache } = require("../services/translationService");

// Environment variables should be properly loaded in the main application
const GOOGLE_TRANSLATE_API_KEY = process.env.GOOGLE_TRANSLATE_API_KEY;
const AZURE_TRANSLATION_KEY = process.env.AZURE_TRANSLATION_KEY;
const AZURE_TRANSLATION_REGION = process.env.AZURE_TRANSLATION_REGION;

// List of technical terms that need special handling
const technicalTerms = {
  "apple scab": true,
  "early blight": true,
  "late blight": true,
  "bacterial spot": true,
  "black rot": true,
  "powdery mildew": true,
  "downy mildew": true,
  "moderate presence": true,
  "early stage": true,
  "infestation": true,
  "requires prompt treatment": true,
  "treatable with targeted intervention": true
};

// Endpoint to translate text
router.post("/", async (req, res) => {
  try {
    const { text, targetLanguage, isTermTranslation } = req.body;

    // Validation
    if (!text) {
      return res.status(400).json({
        success: false,
        message: "Text is required for translation",
      });
    }

    if (!targetLanguage) {
      return res.status(400).json({
        success: false,
        message: "Target language is required",
      });
    }

    // For technical terms, create a specific cache key
    const cacheKey = isTermTranslation ? 
      `term:${text}:${targetLanguage}` : 
      `${text}:${targetLanguage}`;
      
    // Check cache first
    if (translateCache && translateCache[cacheKey]) {
      console.log(`✅ Translation found in cache for: ${cacheKey}`);
      return res.json({
        success: true,
        translation: translateCache[cacheKey],
      });
    }

    let translatedText = text;

    // Preprocess the text to identify and handle technical terms specially
    const extractedTerms = [];
    if (!isTermTranslation) {
      // Look for known technical terms in the text
      for (const term in technicalTerms) {
        if (text.toLowerCase().includes(term.toLowerCase())) {
          extractedTerms.push(term);
        }
      }
      
      // Look for patterns matching technical terms
      const patternMatches = text.match(/([\w\s]+scab|[\w\s]+blight|[\w\s]+disease|[\w\s]+pest|[\w\s]+deficiency|[\w\s]+rot|[\w\s]+mildew|[\w\s]+virus|moderate presence|early stage|infestation)/gi) || [];
      
      for (const match of patternMatches) {
        if (!extractedTerms.includes(match)) {
          extractedTerms.push(match);
        }
      }
    }

    // Try Google Translate first if available
    if (GOOGLE_TRANSLATE_API_KEY) {
      try {
        // For text with technical terms, break it into parts
        if (!isTermTranslation && extractedTerms.length > 0) {
          // Create a map of terms to their translations
          const termTranslations = {};
          
          // First translate each technical term separately
          for (const term of extractedTerms) {
            try {
              const termResponse = await axios.post(
                `https://translation.googleapis.com/language/translate/v2?key=${GOOGLE_TRANSLATE_API_KEY}`,
                {
                  q: term,
                  target: targetLanguage,
                  format: "text",
                  model: "nmt" // Use Neural Machine Translation
                }
              );
              
              if (termResponse.data?.data?.translations?.length > 0) {
                termTranslations[term] = termResponse.data.data.translations[0].translatedText;
              }
            } catch (termError) {
              console.error(`Error translating term "${term}":`, termError.message);
            }
          }
          
          // Now translate the full text
          const response = await axios.post(
            `https://translation.googleapis.com/language/translate/v2?key=${GOOGLE_TRANSLATE_API_KEY}`,
            {
              q: text,
              target: targetLanguage,
              format: "text",
              model: "nmt" // Use Neural Machine Translation
            }
          );
          
          if (response.data?.data?.translations?.length > 0) {
            translatedText = response.data.data.translations[0].translatedText;
            
            // Use term translations where appropriate to improve quality
            // This helps ensure technical terms are translated consistently
            // Try both strings with spaces before/after to avoid replacing partial words
            for (const term in termTranslations) {
              // Create regex patterns to match the term with word boundaries
              const termPattern = new RegExp(`\\b${term}\\b`, 'gi');
              
              // If the translated text still contains the English term, replace it
              // This fixes cases where the translation API leaves technical terms unchanged
              if (translatedText.match(termPattern)) {
                translatedText = translatedText.replace(termPattern, termTranslations[term]);
              }
            }
            
            // Cache the result
            if (translateCache) {
              translateCache[cacheKey] = translatedText;
            }
            
            return res.json({
              success: true,
              translation: translatedText,
              provider: "google",
              termTranslations: termTranslations
            });
          }
        } else {
          // Standard translation for text without technical terms or for single terms
          const response = await axios.post(
            `https://translation.googleapis.com/language/translate/v2?key=${GOOGLE_TRANSLATE_API_KEY}`,
            {
              q: text,
              target: targetLanguage,
              format: "text",
              model: "nmt" // Use Neural Machine Translation
            }
          );
          
          if (response.data?.data?.translations?.length > 0) {
            translatedText = response.data.data.translations[0].translatedText;
            
            // Cache the result
            if (translateCache) {
              translateCache[cacheKey] = translatedText;
            }
            
            return res.json({
              success: true,
              translation: translatedText,
              provider: "google"
            });
          }
        }
      } catch (googleError) {
        console.error("Google Translation API error:", googleError.message);
        // Fall through to try Azure
      }
    }

    // Try Azure Translator if available and Google failed
    if (AZURE_TRANSLATION_KEY && AZURE_TRANSLATION_REGION) {
      try {
        const endpoint = "https://api.cognitive.microsofttranslator.com";
        
        // For text with technical terms, break it into parts
        if (!isTermTranslation && extractedTerms.length > 0) {
          // Create a map of terms to their translations
          const termTranslations = {};
          
          // First translate each technical term separately
          for (const term of extractedTerms) {
            try {
              const termResponse = await axios.post(
                `${endpoint}/translate?api-version=3.0&to=${targetLanguage}&category=generalnn`,
                [{ Text: term }],
                {
                  headers: {
                    "Ocp-Apim-Subscription-Key": AZURE_TRANSLATION_KEY,
                    "Ocp-Apim-Subscription-Region": AZURE_TRANSLATION_REGION,
                    "Content-type": "application/json",
                  }
                }
              );
              
              if (termResponse.data && termResponse.data[0]?.translations?.length > 0) {
                termTranslations[term] = termResponse.data[0].translations[0].text;
              }
            } catch (termError) {
              console.error(`Error translating term "${term}" with Azure:`, termError.message);
            }
          }
          
          // Now translate the full text
          const response = await axios.post(
            `${endpoint}/translate?api-version=3.0&to=${targetLanguage}`,
            [{ Text: text }],
            {
              headers: {
                "Ocp-Apim-Subscription-Key": AZURE_TRANSLATION_KEY,
                "Ocp-Apim-Subscription-Region": AZURE_TRANSLATION_REGION,
                "Content-type": "application/json",
              },
              params: { category: "generalnn" }
            }
          );
          
          if (response.data && response.data[0]?.translations?.length > 0) {
            translatedText = response.data[0].translations[0].text;
            
            // Use term translations where appropriate to improve quality
            for (const term in termTranslations) {
              // Create regex patterns to match the term with word boundaries
              const termPattern = new RegExp(`\\b${term}\\b`, 'gi');
              
              // If the translated text still contains the English term, replace it
              if (translatedText.match(termPattern)) {
                translatedText = translatedText.replace(termPattern, termTranslations[term]);
              }
            }
            
            // Cache the result
            if (translateCache) {
              translateCache[cacheKey] = translatedText;
            }
            
            return res.json({
              success: true,
              translation: translatedText,
              provider: "azure",
              termTranslations: termTranslations
            });
          }
        } else {
          // Standard translation for text without technical terms or for single terms
          const response = await axios.post(
            `${endpoint}/translate?api-version=3.0&to=${targetLanguage}`,
            [{ Text: text }],
            {
              headers: {
                "Ocp-Apim-Subscription-Key": AZURE_TRANSLATION_KEY,
                "Ocp-Apim-Subscription-Region": AZURE_TRANSLATION_REGION,
                "Content-type": "application/json",
              },
              params: { category: "generalnn" }
            }
          );
          
          if (response.data && response.data[0]?.translations?.length > 0) {
            translatedText = response.data[0].translations[0].text;
            
            // Cache the result
            if (translateCache) {
              translateCache[cacheKey] = translatedText;
            }
            
            return res.json({
              success: true,
              translation: translatedText,
              provider: "azure"
            });
          }
        }
      } catch (azureError) {
        console.error("Azure Translation error:", azureError.message);
        // Return original text if both translation services fail
      }
    }

    // If we reach here, both translation services failed or were unavailable
    return res.json({
      success: true,
      translation: text,
      provider: "none",
    });
  } catch (error) {
    console.error("Translation error:", error);
    return res.status(500).json({
      success: false,
      message: "Error processing translation request",
      error: error.message,
    });
  }
});

module.exports = router; 