const express = require('express');
const router = express.Router();
const axios = require('axios');

// External data routes
router.get('/search', async (req, res) => {
  try {
    const { query, category, state } = req.query;
    
    if (!query) {
      return res.status(400).json({ success: false, message: 'Search query is required' });
    }
    
    console.log(`Searching external data for: ${query}, category: ${category}, state: ${state}`);
    
    // Mock implementation - in production, integrate with actual external APIs
    const results = [
      {
        title: 'Wheat Cultivation Techniques',
        source: 'Indian Council of Agricultural Research',
        url: 'https://icar.org.in/wheat-cultivation',
        snippet: 'Modern techniques for wheat cultivation in India, including soil preparation, seed selection, and irrigation methods.',
        date: '2023-01-15',
        category: 'cultivation'
      },
      {
        title: 'Rice Disease Management',
        source: 'National Rice Research Institute',
        url: 'https://nrri.icar.gov.in/disease-management',
        snippet: 'Comprehensive guide to identifying and managing common rice diseases in tropical climates.',
        date: '2023-03-22',
        category: 'disease-management'
      },
      {
        title: 'Organic Farming Certification Process',
        source: 'Ministry of Agriculture & Farmers Welfare',
        url: 'https://agricoop.gov.in/organic-certification',
        snippet: 'Step-by-step guide to obtaining organic farming certification for Indian farmers.',
        date: '2023-02-10',
        category: 'organic-farming'
      },
      {
        title: 'Market Prices for Agricultural Commodities',
        source: 'Agricultural Marketing Information Network',
        url: 'https://agmarknet.gov.in',
        snippet: 'Daily updates on market prices for various agricultural commodities across different states in India.',
        date: '2023-04-01',
        category: 'market-prices'
      },
      {
        title: 'Soil Health Management Practices',
        source: 'National Bureau of Soil Survey and Land Use Planning',
        url: 'https://nbsslup.icar.gov.in/soil-health',
        snippet: 'Best practices for maintaining and improving soil health for sustainable agriculture.',
        date: '2023-01-30',
        category: 'soil-management'
      }
    ];
    
    // Filter results based on category and state if provided
    let filteredResults = [...results];
    
    if (category) {
      filteredResults = filteredResults.filter(result => result.category === category);
    }
    
    // Add state-specific information if state is provided
    if (state) {
      filteredResults = filteredResults.map(result => ({
        ...result,
        stateRelevance: `This information is particularly relevant for farmers in ${state}.`
      }));
    }
    
    res.status(200).json({
      success: true,
      query,
      category,
      state,
      results: filteredResults
    });
  } catch (error) {
    console.error('Error searching external data:', error);
    res.status(500).json({ success: false, message: 'Failed to search external data', error: error.message });
  }
});

// Get external data categories
router.get('/categories', (req, res) => {
  try {
    const categories = [
      { id: 'cultivation', name: 'Cultivation Techniques' },
      { id: 'disease-management', name: 'Disease Management' },
      { id: 'pest-control', name: 'Pest Control' },
      { id: 'organic-farming', name: 'Organic Farming' },
      { id: 'market-prices', name: 'Market Prices' },
      { id: 'soil-management', name: 'Soil Management' },
      { id: 'irrigation', name: 'Irrigation Methods' },
      { id: 'crop-rotation', name: 'Crop Rotation' },
      { id: 'fertilizers', name: 'Fertilizers' },
      { id: 'government-schemes', name: 'Government Schemes' }
    ];
    
    res.status(200).json({
      success: true,
      categories
    });
  } catch (error) {
    console.error('Error getting external data categories:', error);
    res.status(500).json({ success: false, message: 'Failed to get categories', error: error.message });
  }
});

// Get external data sources
router.get('/sources', (req, res) => {
  try {
    const sources = [
      { id: 'icar', name: 'Indian Council of Agricultural Research', url: 'https://icar.org.in' },
      { id: 'nrri', name: 'National Rice Research Institute', url: 'https://nrri.icar.gov.in' },
      { id: 'agricoop', name: 'Ministry of Agriculture & Farmers Welfare', url: 'https://agricoop.gov.in' },
      { id: 'agmarknet', name: 'Agricultural Marketing Information Network', url: 'https://agmarknet.gov.in' },
      { id: 'nbsslup', name: 'National Bureau of Soil Survey and Land Use Planning', url: 'https://nbsslup.icar.gov.in' },
      { id: 'iari', name: 'Indian Agricultural Research Institute', url: 'https://iari.res.in' },
      { id: 'kvk', name: 'Krishi Vigyan Kendra', url: 'https://kvk.icar.gov.in' },
      { id: 'nabard', name: 'National Bank for Agriculture and Rural Development', url: 'https://www.nabard.org' },
      { id: 'fci', name: 'Food Corporation of India', url: 'https://fci.gov.in' },
      { id: 'apeda', name: 'Agricultural and Processed Food Products Export Development Authority', url: 'https://apeda.gov.in' }
    ];
    
    res.status(200).json({
      success: true,
      sources
    });
  } catch (error) {
    console.error('Error getting external data sources:', error);
    res.status(500).json({ success: false, message: 'Failed to get sources', error: error.message });
  }
});

module.exports = router;
