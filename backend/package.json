{"name": "agricare-backend", "version": "1.0.0", "description": "Backend server for AgriCare application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed:financial": "node scripts/seedFinancialData.js", "seed:testfarmer": "node scripts/seedTestFarmer.js", "check:mongodb": "node scripts/checkMongoDB.js", "setup:mongodb": "bash scripts/setupMongoDB.sh", "backup:mongodb": "bash scripts/create-mongodb-backup.sh", "restore:mongodb": "bash scripts/restore-mongodb-backup.sh", "health:check": "node scripts/health-check.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@azure/cognitiveservices-computervision": "^8.2.0", "@azure/ms-rest-js": "^2.7.0", "@azure/openai": "^1.0.0-beta.11", "@azure/storage-blob": "^12.27.0", "axios": "^1.8.3", "base64-js": "^1.5.1", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.18.2", "express-validator": "^7.2.1", "firebase-admin": "^11.11.1", "fs": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "messagebird": "^4.0.1", "microsoft-cognitiveservices-speech-sdk": "^1.43.0", "mongoose": "^7.8.6", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "openai": "^3.3.0", "ws": "^8.18.1"}, "devDependencies": {"jest": "^29.0.0", "nodemon": "^3.0.2", "supertest": "^6.0.0"}, "engines": {"node": ">=16.0.0"}}