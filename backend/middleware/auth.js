const admin = require('../config/firebase.config');
require('dotenv').config();

const auth = async (req, res, next) => {
  // Always allow in development mode
  if (process.env.NODE_ENV === 'development') {
    req.user = {
      uid: 'emulator-uid',
      id: 'emulator-uid', // Add id field for compatibility
      role: 'Farmer'
    };
    return next();
  }

  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }

    const token = authHeader.split(' ')[1];
    try {
      const decodedToken = await admin.auth().verifyIdToken(token);
      req.user = {
        ...decodedToken,
        id: decodedToken.uid // Add id field for compatibility
      };
      next();
    } catch (error) {
      console.error('Token verification failed:', error);
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error during authentication'
    });
  }
};

module.exports = auth; 