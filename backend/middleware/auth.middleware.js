const admin = require('../config/firebase.config');
const User = require('../models/user.model');

const protect = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.split('Bearer ')[1];
        console.log('Token:', token);
        
        if (!token) {
            return res.status(401).json({ message: 'Not authorized, no token' });
        }

        try {
            // Decode the Base64 token first
            const decodedString = Buffer.from(token, 'base64').toString();
            const [phoneNumber, role, timestamp] = decodedString.split(':');
            const normalizedPhone = phoneNumber.replace(/\D/g, "").slice(-10);
            
            console.log('Decoded token info:', { phoneNumber, role, timestamp, normalizedPhone });

            // Find user by phone number
            let user = await User.findOne({ phoneNumber: normalizedPhone});
            
            if (!user) {
                console.log('No user found with phone number:', normalizedPhone);
                console.log('Available users in DB:', await User.find({}, 'phoneNumber role'));
                return res.status(401).json({ message: 'User not found' });
            }

            console.log('Found user:', user);
            console.log('User ID:', user._id);
            req.user = user;
            next();
        } catch (error) {
            console.error('Token decoding error:', error);
            return res.status(401).json({ message: 'Invalid token format' });
        }
    } catch (error) {
        console.error('Auth Error:', error);
        res.status(401).json({ message: 'Not authorized' });
    }
};

const isAdmin = (req, res, next) => {
    if (req.user && req.user.role === 'admin') {
        next();
    } else {
        res.status(401).json({ message: 'Not authorized as an admin' });
    }
};

module.exports = { protect, isAdmin };