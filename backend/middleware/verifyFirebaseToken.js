const admin = require('../config/firebase.config');

/**
 * Middleware to verify Firebase auth tokens
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const verifyFirebaseToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split('Bearer ')[1];
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }
    
    // Special case for development/testing
    if (token === 'test-token' || process.env.NODE_ENV === 'development') {
      console.log('Development mode: Bypassing Firebase token verification');
      req.user = {
        uid: 'test-user-uid',
        phone_number: req.body.phoneNumber
      };
      return next();
    }
    
    // Verify the Firebase token
    const decodedToken = await admin.auth().verifyIdToken(token);
    console.log('Firebase token verified:', decodedToken);
    
    req.user = decodedToken;
    next();
  } catch (error) {
    console.error('Firebase token verification failed:', error);
    return res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }
};

module.exports = verifyFirebaseToken; 