require('dotenv').config();
const mongoose = require('mongoose');

async function testConnection() {
  try {
    console.log('🔍 Testing MongoDB connection...');
    console.log('📡 Connection string:', process.env.MONGODB_URI ? 'Set' : 'Not set');
    
    if (!process.env.MONGODB_URI) {
      console.error('❌ MONGODB_URI environment variable is not set');
      return;
    }

    // Test basic connectivity first
    const url = new URL(process.env.MONGODB_URI.replace('mongodb://', 'http://'));
    console.log('🌐 Host:', url.hostname);
    console.log('🔌 Port:', url.port);

    // Try to connect with a shorter timeout
    const options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      retryWrites: false,
      serverSelectionTimeoutMS: 5000, // 5 seconds
      socketTimeoutMS: 10000, // 10 seconds
      ssl: true,
      replicaSet: 'globaldb',
      appName: '@qdb@',
    };

    console.log('🔗 Attempting to connect...');
    const conn = await mongoose.connect(process.env.MONGODB_URI, options);
    
    console.log('✅ MongoDB Connected Successfully!');
    console.log(`📊 Database: ${conn.connection.name}`);
    console.log(`🌐 Host: ${conn.connection.host}`);
    
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    
    if (error.message.includes('ENOTFOUND')) {
      console.error('💡 This appears to be a DNS resolution issue.');
      console.error('💡 Possible solutions:');
      console.error('   1. Check your internet connection');
      console.error('   2. Verify the Azure Cosmos DB service is running');
      console.error('   3. Check if the hostname is correct');
      console.error('   4. Try using a local MongoDB instance for development');
    } else if (error.message.includes('ECONNREFUSED')) {
      console.error('💡 This appears to be a connection refused issue.');
      console.error('💡 The service might be down or the port is blocked.');
    } else if (error.message.includes('authentication')) {
      console.error('💡 This appears to be an authentication issue.');
      console.error('💡 Check your username and password in the connection string.');
    }
  }
}

testConnection(); 