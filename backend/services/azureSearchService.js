// Azure Search Service
const axios = require('axios');
const config = require('../config/config');

// Azure Search configuration
const searchEndpoint = config.azureSearch?.endpoint || 'https://agricare.openai.azure.com';
const searchApiKey = config.azureSearch?.apiKey || 'G9tkoQ5gY5EuHqLQ4GZCCzIWp6f0x734sNFzc7kJyrc2galHuktiJQQJ99BDAC77bzfXJ3w3AAABACOGwRYy';
const searchIndexName = config.azureSearch?.indexName || 'agricare-index';

// Initialize Azure Search client
console.log(`Azure Search Service initialized with endpoint: ${searchEndpoint}`);

/**
 * Search the Azure Search index
 * @param {string} query - Search query
 * @param {Object} options - Search options
 * @returns {Promise<Object>} - Search results
 */
const searchDocuments = async (query, options = {}) => {
  try {
    const searchUrl = `${searchEndpoint}/indexes/${searchIndexName}/docs/search?api-version=2023-07-01-Preview`;
    
    const searchPayload = {
      search: query,
      queryType: options.queryType || 'simple',
      searchFields: options.searchFields || ['content', 'title', 'category'],
      select: options.select || ['id', 'title', 'content', 'category', 'url', 'lastUpdated'],
      top: options.top || 10,
      skip: options.skip || 0,
      orderby: options.orderby || null,
      filter: options.filter || null,
      facets: options.facets || null,
      highlight: options.highlight || null,
      scoringProfile: options.scoringProfile || null,
      scoringParameters: options.scoringParameters || null
    };
    
    const response = await axios.post(searchUrl, searchPayload, {
      headers: {
        'Content-Type': 'application/json',
        'api-key': searchApiKey
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error searching Azure Search:', error.message);
    throw new Error('Failed to search documents');
  }
};

/**
 * Get document by ID from Azure Search
 * @param {string} documentId - Document ID
 * @returns {Promise<Object>} - Document
 */
const getDocumentById = async (documentId) => {
  try {
    const lookupUrl = `${searchEndpoint}/indexes/${searchIndexName}/docs/${documentId}?api-version=2023-07-01-Preview`;
    
    const response = await axios.get(lookupUrl, {
      headers: {
        'Content-Type': 'application/json',
        'api-key': searchApiKey
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error getting document from Azure Search:', error.message);
    throw new Error('Failed to get document');
  }
};

/**
 * Get suggestions from Azure Search
 * @param {string} query - Partial query for suggestions
 * @param {Object} options - Suggestion options
 * @returns {Promise<Object>} - Suggestions
 */
const getSuggestions = async (query, options = {}) => {
  try {
    const suggestUrl = `${searchEndpoint}/indexes/${searchIndexName}/docs/suggest?api-version=2023-07-01-Preview`;
    
    const suggestPayload = {
      search: query,
      suggesterName: options.suggesterName || 'sg',
      searchFields: options.searchFields || ['title', 'content'],
      select: options.select || ['id', 'title'],
      top: options.top || 5,
      filter: options.filter || null,
      fuzzy: options.fuzzy || true
    };
    
    const response = await axios.post(suggestUrl, suggestPayload, {
      headers: {
        'Content-Type': 'application/json',
        'api-key': searchApiKey
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error getting suggestions from Azure Search:', error.message);
    throw new Error('Failed to get suggestions');
  }
};

/**
 * Search agricultural knowledge base
 * @param {string} query - Search query
 * @returns {Promise<Array>} - Search results
 */
const searchAgricultureKnowledge = async (query) => {
  try {
    // Mock implementation for now
    console.log(`Searching agriculture knowledge for: ${query}`);
    
    // Return mock data
    return [
      {
        id: 'crop-disease-1',
        title: 'Common Wheat Diseases',
        content: 'Wheat is susceptible to various diseases including rust, powdery mildew, and fusarium head blight.',
        category: 'crop-diseases',
        confidence: 0.92
      },
      {
        id: 'pest-management-1',
        title: 'Integrated Pest Management for Rice',
        content: 'IPM combines biological, cultural, physical, and chemical tools to minimize economic, health, and environmental risks.',
        category: 'pest-management',
        confidence: 0.85
      },
      {
        id: 'soil-health-1',
        title: 'Improving Soil Fertility',
        content: 'Soil fertility can be improved through crop rotation, cover crops, and appropriate fertilization.',
        category: 'soil-management',
        confidence: 0.78
      }
    ];
  } catch (error) {
    console.error('Error searching agriculture knowledge:', error.message);
    throw new Error('Failed to search agriculture knowledge');
  }
};

module.exports = {
  searchDocuments,
  getDocumentById,
  getSuggestions,
  searchAgricultureKnowledge
};
