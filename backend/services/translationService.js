/**
 * Translation Service
 * Provides translation functionality and caching for translations
 */

// Simple in-memory cache for translations
const translateCache = {};

/**
 * Clear the translation cache
 */
const clearTranslateCache = () => {
  const cacheSize = Object.keys(translateCache).length;
  console.log(`Clearing translation cache (${cacheSize} entries)`);
  
  for (const key in translateCache) {
    delete translateCache[key];
  }
};

/**
 * Get the size of the translation cache
 * @returns {number} Number of entries in the cache
 */
const getTranslateCacheSize = () => {
  return Object.keys(translateCache).length;
};

/**
 * Add a translation to the cache
 * @param {string} text - Original text
 * @param {string} targetLanguage - Target language code
 * @param {string} translation - Translated text
 */
const cacheTranslation = (text, targetLanguage, translation) => {
  const cacheKey = `${text}:${targetLanguage}`;
  translateCache[cacheKey] = translation;
};

/**
 * Get a translation from the cache
 * @param {string} text - Original text
 * @param {string} targetLanguage - Target language code
 * @returns {string|null} Translated text or null if not in cache
 */
const getCachedTranslation = (text, targetLanguage) => {
  const cacheKey = `${text}:${targetLanguage}`;
  return translateCache[cacheKey] || null;
};

// Limit the cache size to prevent memory leaks
const MAX_CACHE_SIZE = 10000; // Maximum number of cached translations

/**
 * Check if the cache is full and clear oldest entries if needed
 */
const checkCacheSize = () => {
  const keys = Object.keys(translateCache);
  if (keys.length > MAX_CACHE_SIZE) {
    console.log(`Translation cache exceeds max size (${keys.length}/${MAX_CACHE_SIZE}), clearing oldest entries`);
    
    // Remove 25% of the oldest entries
    const itemsToRemove = Math.floor(MAX_CACHE_SIZE * 0.25);
    const oldestKeys = keys.slice(0, itemsToRemove);
    
    oldestKeys.forEach(key => {
      delete translateCache[key];
    });
    
    console.log(`Cleared ${itemsToRemove} oldest translation cache entries`);
  }
};

// Clean up the cache periodically (every 6 hours)
setInterval(checkCacheSize, 6 * 60 * 60 * 1000);

module.exports = {
  translateCache,
  clearTranslateCache,
  getTranslateCacheSize,
  cacheTranslation,
  getCachedTranslation
}; 