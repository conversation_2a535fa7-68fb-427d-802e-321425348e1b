# Backend Environment Variables
# Copy this file to .env and update the values

# Environment
NODE_ENV=development

# Server Configuration
PORT=8000

# MongoDB Configuration
MONGODB_URI=**************************************************************************************************************************************************************************************************************************

# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=./config/firebase-credentials.json

# Weather API Configuration
OPENWEATHER_API_KEY=your_openweather_api_key
WEATHER_API_KEY=your_weather_api_key

# JWT Configuration
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# Google Translate Configuration
GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key

# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_DEPLOYMENT_NAME=your_deployment_name
AZURE_API_VERSION=your_api_version

# Azure Speech Configuration
AZURE_SPEECH_KEY=your_azure_speech_key
AZURE_SPEECH_REGION=your_azure_speech_region
REACT_APP_AZURE_SPEECH_ENDPOINT=your_azure_speech_endpoint

# Database Configuration
MONGODB_USER=your_mongodb_user
MONGODB_PASSWORD=your_mongodb_password

# API Keys
OPENAI_API_KEY=your_openai_api_key

# Security
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=debug
LOG_FILE_PATH=logs/app.log

# Cache Configuration
CACHE_TTL=3600 # in seconds
REDIS_URL=redis://localhost:6379

# Email Configuration (Optional)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password

# Feature Flags
ENABLE_AI_ANALYSIS=true
ENABLE_WEATHER_FORECAST=true
ENABLE_SOIL_ANALYSIS=true 

VITE_AZURE_TRANSLATION_API_KEY=9DGIQrRiywiKFxqm1utRrYcDY7lSMtLDv35VKWGjkoZeztk0yHWIJQQJ99BEACGhslBXJ3w3AAAbACOGB8ph
VITE_AZURE_TRANSLATION_REGION=centralindia