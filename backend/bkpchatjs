const express = require("express");
const axios = require("axios");

const router = express.Router();

// Load required environment variables
const { AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_API_KEY } = process.env;
if (!AZURE_OPENAI_ENDPOINT || !AZURE_OPENAI_API_KEY) {
  console.error("❌ Missing required API keys in .env!");
  process.exit(1);
}

const GOOGLE_TRANSLATE_URL =
  "https://translation.googleapis.com/language/translate/v2";

// Function to detect and translate text using Google Translate API
const translateText = async (text, targetLanguage) => {
  try {
    const response = await axios.post(
      `${GOOGLE_TRANSLATE_URL}?key=${GOOGLE_TRANSLATE_API_KEY}`,
      {
        q: text,
        target: targetLanguage,
        format: "text",
      }
    );
    return response.data.data.translations?.[0]?.translatedText || text;
  } catch (error) {
    console.error(
      "❌ Translation Error:",
      error?.response?.data || error.message
    );
    return text; // Return original text if translation fails
  }
};

// Chat API Route (language parameter removed)
router.post("/", async (req, res) => {
  try {
    // Destructure only question and context from the request body
    let { question, context } = req.body;

    // Validate required parameter: question
    if (!question) {
      return res.status(400).json({
        error: "❌ Missing required parameter: 'question'",
      });
    }

    console.log("📩 Received Chat Request:", { question, context });

    // Translate user's question to English
    const translatedQuestion = await translateText(question, "en");

    // Build the conversation messages for Azure OpenAI
    const messages = [
      {
        role: "system",
        content: `
          You are QuaminAI, an AI assistant developed by Quamin Tech Solutions LLP to help farmers with plant health, disease detection, and agricultural support.
          IMPORTANT RULES:
          - If asked "Who created you?" or similar, reply: "I am created by Quamin Tech Solutions LLP and designed specifically to assist farmers."
          - Do not mention any external AI platforms.
        `.trim(),
      },
    ];

    // Include extra context if provided
    if (context) {
      messages.push({
        role: "system",
        content: `Plant disease analysis report: ${context}. Use this data to answer related questions accurately.`,
      });
    }

    // Handle questions about the assistant's origin
    const lowerQuestion = translatedQuestion.toLowerCase();
    if (
      lowerQuestion.includes("who created you") ||
      lowerQuestion.includes("who made you") ||
      lowerQuestion.includes("who developed you") ||
      lowerQuestion.includes("who designed you") ||
      lowerQuestion.includes("who built you")
    ) {
      return res.json({
        answer:
          "QuaminAI: I am created by Quamin Tech Solutions LLP and designed specifically to assist farmers.",
      });
    }

    // Append the user's question to the messages
    messages.push({ role: "user", content: translatedQuestion });

    // Call Azure OpenAI API
    const aiResponse = await axios.post(
      AZURE_OPENAI_ENDPOINT,
      {
        messages: messages,
        max_tokens: 1000,
        temperature: 0.7,
      },
      {
        headers: {
          "Content-Type": "application/json",
          "api-key": AZURE_OPENAI_API_KEY,
        },
      }
    );

    console.log("✅ AI Response Data:", aiResponse.data);

    // Extract and adjust the AI response
    let botMessage =
      aiResponse.data.choices?.[0]?.message?.content ||
      "⚠️ No response from AI.";
    botMessage = botMessage.replace(/^AI:/, "QuaminAI:");

    // No final translation using a language parameter—simply return the bot's response
    const finalResponse = botMessage;
    console.log("🌍 Final Response:", finalResponse);

    res.json({ answer: finalResponse });
  } catch (error) {
    console.error(
      "❌ Error fetching chatbot response:",
      error?.response?.data || error.message
    );
    res.status(500).json({
      error:
        "⚠️ Failed to fetch response from QuaminAI. Please try again later.",
    });
  }
});

module.exports = router;
