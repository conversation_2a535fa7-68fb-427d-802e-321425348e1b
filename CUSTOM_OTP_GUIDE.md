# Custom OTP Authentication Guide

This guide explains how to use the custom OTP authentication system that has been implemented as an alternative to Firebase Authentication.

## Setup Instructions

1. **Install Dependencies**:
   ```bash
   cd backend
   npm install
   ```

2. **Add Test Farmer**:
   ```bash
   cd backend
   npm run seed:testfarmer
   ```
   This will add a test farmer with phone number `9611966747` to the database.

3. **Start the Backend Server**:
   ```bash
   cd backend
   npm run dev
   ```

4. **Start the Frontend**:
   ```bash
   cd frontend
   npm run dev
   ```

## Login Instructions

1. Open the application in your browser
2. Go to the login page
3. Enter your phone number: `9611966747`
4. Select role: `Farmer`
5. Click "Get OTP"
6. Enter OTP: `123456`
7. Click "Verify OTP"

## How It Works

The system has been configured to:

1. Use a fixed OTP (`123456`) for development mode and specifically for the phone number `9611966747`
2. Log the OTP to the console instead of sending an actual SMS
3. Accept the fixed OTP for verification

## Troubleshooting

If you encounter any issues:

1. **Check the console logs** in both frontend and backend to see if there are any errors
2. **Verify that MongoDB is running** and accessible
3. **Check that the test farmer was added** to the database
4. **Ensure the environment variables** are set correctly in the `.env` file

## Technical Details

The implementation includes:

1. **OTP Service**: Handles OTP generation, storage, and verification
2. **SMS Service**: Simulates sending SMS messages (logs to console in development)
3. **Auth Controller**: Handles authentication requests and responses
4. **Auth Context**: Manages authentication state in the frontend

For more detailed information, see the `backend/docs/OTP_SERVICE.md` file.
