import React from 'react';
import { Box, Typography, Paper, Button } from '@mui/material';
import { SmartToy as SmartToyIcon } from '@mui/icons-material';

const PersistentAIAgent = () => {
  return (
    <Paper 
      elevation={3} 
      sx={{ 
        p: 2, 
        borderRadius: 2, 
        background: 'linear-gradient(135deg, #1a5d1a 0%, #1e8449 100%)',
        color: 'white',
        height: '100%',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <SmartToyIcon sx={{ mr: 1 }} />
        <Typography variant="h6" fontWeight="bold">
          Quamin AI Assistant
        </Typography>
      </Box>
      
      <Typography variant="body2" sx={{ mb: 2 }}>
        Your personal agricultural advisor is ready to help with crop management, weather insights, and market analysis.
      </Typography>
      
      <Box sx={{ mt: 'auto' }}>
        <Button 
          variant="contained" 
          fullWidth
          sx={{ 
            bgcolor: 'rgba(255,255,255,0.2)', 
            '&:hover': { bgcolor: 'rgba(255,255,255,0.3)' } 
          }}
          href="/chatbot"
        >
          Ask AI Assistant
        </Button>
      </Box>
    </Paper>
  );
};

export default PersistentAIAgent;
