<!DOCTYPE html>
<html>
<head>
  <title>Firebase Phone Authentication Test (Simplified)</title>
  <script src="https://www.gstatic.com/firebasejs/9.6.10/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.10/firebase-auth-compat.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .container {
      border: 1px solid #ccc;
      padding: 20px;
      border-radius: 5px;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
    }
    input {
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:disabled {
      background-color: #cccccc;
    }
    .result {
      margin-top: 20px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #f9f9f9;
    }
    .logs {
      margin-top: 20px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #f9f9f9;
      max-height: 200px;
      overflow-y: auto;
      font-family: monospace;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Firebase Phone Authentication Test</h1>
    
    <div class="form-group">
      <label for="phone">Phone Number (with country code):</label>
      <input type="tel" id="phone" placeholder="+919611966747" value="+919611966747">
    </div>
    
    <div id="recaptcha-container"></div>
    
    <div class="form-group">
      <button id="send-otp">Send OTP</button>
    </div>
    
    <div class="form-group" id="otp-section" style="display: none;">
      <label for="otp">Enter OTP:</label>
      <input type="text" id="otp" placeholder="123456">
      <button id="verify-otp">Verify OTP</button>
    </div>
    
    <div class="result" id="result">Ready to test. Click "Send OTP" to begin.</div>
    
    <div class="logs" id="logs"></div>
  </div>

  <script>
    // Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyA3ex2Lw-RTMetcAB7-Sf0dkEHNX9GTMKA",
      authDomain: "quamin-agricare.firebaseapp.com",
      projectId: "quamin-agricare",
      storageBucket: "quamin-agricare.firebasestorage.app",
      messagingSenderId: "744194487251",
      appId: "1:744194487251:web:e3d0ccee5ad58b6ddef526",
      measurementId: "G-HTCNBBQYJD"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
    
    // Get elements
    const phoneInput = document.getElementById('phone');
    const sendOtpButton = document.getElementById('send-otp');
    const otpSection = document.getElementById('otp-section');
    const otpInput = document.getElementById('otp');
    const verifyOtpButton = document.getElementById('verify-otp');
    const resultDiv = document.getElementById('result');
    const logsDiv = document.getElementById('logs');
    
    // Logging function
    function log(message) {
      const logEntry = document.createElement('div');
      logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      logsDiv.appendChild(logEntry);
      logsDiv.scrollTop = logsDiv.scrollHeight;
      console.log(message);
    }
    
    // Setup reCAPTCHA verifier
    window.recaptchaVerifier = new firebase.auth.RecaptchaVerifier('recaptcha-container', {
      'size': 'normal',
      'callback': (response) => {
        // reCAPTCHA solved, allow signInWithPhoneNumber.
        sendOtpButton.disabled = false;
        log("reCAPTCHA verified successfully");
      }
    });
    
    // Render the reCAPTCHA
    window.recaptchaVerifier.render().then(function(widgetId) {
      window.recaptchaWidgetId = widgetId;
      log("reCAPTCHA rendered");
    });
    
    // Send OTP
    sendOtpButton.addEventListener('click', () => {
      const phoneNumber = phoneInput.value;
      resultDiv.textContent = 'Sending OTP...';
      log(`Attempting to send OTP to ${phoneNumber}`);
      
      firebase.auth().signInWithPhoneNumber(phoneNumber, window.recaptchaVerifier)
        .then((confirmationResult) => {
          // SMS sent. Prompt user to type the code.
          window.confirmationResult = confirmationResult;
          resultDiv.textContent = 'OTP sent successfully! Please check your phone.';
          otpSection.style.display = 'block';
          sendOtpButton.disabled = true;
          log("OTP sent successfully");
        })
        .catch((error) => {
          // Error; SMS not sent
          resultDiv.textContent = 'Error sending OTP: ' + error.message;
          log(`Error sending OTP: ${error.code} - ${error.message}`);
          // Reset reCAPTCHA
          grecaptcha.reset(window.recaptchaWidgetId);
          sendOtpButton.disabled = false;
        });
    });
    
    // Verify OTP
    verifyOtpButton.addEventListener('click', () => {
      const code = otpInput.value;
      resultDiv.textContent = 'Verifying OTP...';
      log(`Attempting to verify OTP: ${code}`);
      
      window.confirmationResult.confirm(code)
        .then((result) => {
          // User signed in successfully.
          const user = result.user;
          resultDiv.textContent = 'OTP verified successfully! User signed in: ' + user.phoneNumber;
          log(`OTP verified successfully. User signed in: ${user.phoneNumber}`);
        })
        .catch((error) => {
          // User couldn't sign in (bad verification code?)
          resultDiv.textContent = 'Error verifying OTP: ' + error.message;
          log(`Error verifying OTP: ${error.code} - ${error.message}`);
        });
    });
    
    // Log initial state
    log("Page loaded. Firebase initialized.");
  </script>
</body>
</html>
