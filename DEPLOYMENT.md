# Quamin AgriCare 1.0 Deployment Guide

This document provides instructions for deploying the Quamin AgriCare 1.0 application to Azure App Service with MongoDB on Azure CosmosDB.

## Prerequisites

1. Azure subscription
2. Azure DevOps account
3. Firebase project with Authentication enabled
4. Azure OpenAI service
5. Azure Speech service
6. OpenWeather API key

## Setting Up Azure DevOps Pipeline

1. Create a new pipeline in Azure DevOps
2. Select the repository containing the code
3. Choose "Existing Azure Pipelines YAML file"
4. Select the `azure-pipelines.yml` file

## Required Pipeline Variables

Set up the following variables in your Azure DevOps pipeline:

| Variable Name | Description |
|---------------|-------------|
| MONGODB_URI | MongoDB connection string (Azure CosmosDB with MongoDB API) |
| JWT_SECRET | Secret key for JWT token generation |
| FIREBASE_PROJECT_ID | Your Firebase project ID |
| AZURE_OPENAI_KEY | Azure OpenAI API key |
| AZURE_OPENAI_ENDPOINT | Azure OpenAI endpoint URL |
| AZURE_SPEECH_KEY | Azure Speech Services key |
| AZURE_SPEECH_REGION | Azure Speech Services region |
| OPENWEATHER_API_KEY | OpenWeather API key |

## Firebase Setup

1. Go to your Firebase project console
2. Navigate to Project Settings > Service Accounts
3. Click "Generate new private key"
4. Download the JSON file
5. Rename it to `firebase-credentials.json`
6. Upload it to Azure DevOps as a secure file
7. Update the pipeline to use this secure file

## Azure Resources Setup

### Azure CosmosDB (MongoDB API)

1. Create a new Azure CosmosDB account with MongoDB API
2. Create a database named `agricare`
3. Note the connection string for the pipeline variables

### Azure App Service

1. Create a new App Service with Linux and Node.js 18 LTS
2. Set the name to match the `appName` variable in the pipeline (default: 'quamin-agricare')
3. Configure the App Service to use the appropriate region (default: 'centralindia')

## Enabling Firebase Phone Authentication

1. Go to your Firebase project console
2. Navigate to Authentication > Sign-in method
3. Enable Phone authentication
4. Add your test phone numbers in the "Phone numbers for testing" section
5. For production, verify your domain and set up reCAPTCHA

## Verifying Deployment

After deployment:

1. Navigate to your Azure App Service URL
2. Verify that the application loads correctly
3. Test the login functionality with OTP
4. Verify that data is being stored in and retrieved from MongoDB

## Troubleshooting

### MongoDB Connection Issues

- Verify the MongoDB connection string is correct
- Check that the IP address of the Azure App Service is whitelisted in CosmosDB
- Verify that the database and collections exist

### Firebase Authentication Issues

- Check that the Firebase credentials file is correctly deployed
- Verify that Phone authentication is enabled in Firebase
- Check the server logs for any Firebase-related errors

### Application Errors

- Check the App Service logs in the Azure portal
- Use Application Insights for more detailed monitoring
- Verify that all environment variables are correctly set

## Contact Support

For assistance with deployment issues, contact:
- <EMAIL>
- <EMAIL>
