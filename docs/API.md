# AgriCare API Documentation

## Base URL
```
http://localhost:8000/api
```

## Authentication
All API requests require authentication using Firebase ID token in the Authorization header:
```
Authorization: Bearer <firebase_id_token>
```

## Endpoints

### Authentication

#### Login
```http
POST /auth/login
Content-Type: application/json

{
  "phoneNumber": "+1234567890",
  "otp": "123456"
}
```

Response:
```json
{
  "success": true,
  "token": "jwt_token",
  "user": {
    "id": "user_id",
    "phoneNumber": "+1234567890",
    "role": "farmer|tm|admin"
  }
}
```

#### Verify OTP
```http
POST /auth/verify-otp
Content-Type: application/json

{
  "phoneNumber": "+1234567890",
  "otp": "123456"
}
```

### Farm Management

#### Get All Farms (TM Only)
```http
GET /farms
```

Response:
```json
{
  "farms": [
    {
      "id": "farm_id",
      "name": "Farm Name",
      "location": {
        "latitude": 12.34,
        "longitude": 56.78
      },
      "farmerId": "farmer_id",
      "status": "active"
    }
  ]
}
```

#### Get Farm Details
```http
GET /farms/:farmId
```

Response:
```json
{
  "id": "farm_id",
  "name": "Farm Name",
  "location": {
    "latitude": 12.34,
    "longitude": 56.78
  },
  "farmerId": "farmer_id",
  "status": "active",
  "crops": [
    {
      "id": "crop_id",
      "name": "Wheat",
      "area": 100,
      "status": "growing"
    }
  ],
  "livestock": [
    {
      "id": "livestock_id",
      "type": "cattle",
      "count": 50,
      "health": "good"
    }
  ]
}
```

### IoT Management

#### Register Device
```http
POST /iot/devices
Content-Type: application/json

{
  "deviceId": "device_serial_number",
  "farmId": "farm_id",
  "type": "sensor|camera|controller",
  "config": {
    "sensors": ["temperature", "humidity", "soil_moisture"],
    "updateInterval": 300
  }
}
```

#### Get Device Data
```http
GET /iot/devices/:deviceId/data
Query Parameters:
  - startDate: ISO date string
  - endDate: ISO date string
  - type: data type (temperature|humidity|soil_moisture)
```

Response:
```json
{
  "deviceId": "device_id",
  "data": [
    {
      "timestamp": "2024-03-30T10:00:00Z",
      "type": "temperature",
      "value": 25.5,
      "unit": "celsius"
    }
  ]
}
```

### Analytics

#### Get Weather Data
```http
GET /analytics/weather
Query Parameters:
  - farmId: string
  - date: ISO date string
```

Response:
```json
{
  "farmId": "farm_id",
  "date": "2024-03-30",
  "data": {
    "temperature": {
      "current": 25.5,
      "min": 20.0,
      "max": 30.0,
      "forecast": [
        {
          "time": "2024-03-30T12:00:00Z",
          "value": 26.0
        }
      ]
    },
    "humidity": {
      "current": 65,
      "min": 50,
      "max": 80
    }
  }
}
```

#### Get Soil Analysis
```http
GET /analytics/soil
Query Parameters:
  - farmId: string
  - date: ISO date string
```

Response:
```json
{
  "farmId": "farm_id",
  "date": "2024-03-30",
  "data": {
    "ph": 6.5,
    "moisture": 45,
    "nutrients": {
      "nitrogen": 20,
      "phosphorus": 15,
      "potassium": 25
    }
  }
}
```

### Error Responses
All endpoints may return the following error responses:

```json
// 400 Bad Request
{
  "error": "Bad Request",
  "message": "Invalid input parameters"
}

// 401 Unauthorized
{
  "error": "Unauthorized",
  "message": "Invalid or expired token"
}

// 403 Forbidden
{
  "error": "Forbidden",
  "message": "Insufficient permissions"
}

// 404 Not Found
{
  "error": "Not Found",
  "message": "Resource not found"
}

// 500 Internal Server Error
{
  "error": "Internal Server Error",
  "message": "An unexpected error occurred"
}
```

## Rate Limiting
- 100 requests per 15 minutes per IP
- 1000 requests per hour per user

## WebSocket Events
See [WebSocket Events Documentation](./WEBSOCKET.md) for real-time event details.

Last Updated: March 30, 2024
Version: 1.0.0 