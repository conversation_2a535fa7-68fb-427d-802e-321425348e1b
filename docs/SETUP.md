# AgriCare Setup Guide

## Prerequisites
- Node.js (v16 or higher)
- MongoDB (v5.0 or higher)
- Firebase CLI
- Git

## Environment Setup

### 1. Clone the Repository
```bash
git clone https://github.com/your-org/agriCare.git
cd agriCare
```

### 2. Backend Setup

#### Install Dependencies
```bash
cd backend
npm install
```

#### Environment Configuration
1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Update the following variables in `.env`:
```env
# Server
PORT=8000
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/agriCare
MONGODB_USER=your_mongodb_user
MONGODB_PASSWORD=your_mongodb_password

# Firebase
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_PRIVATE_KEY=your_private_key
FIREBASE_CLIENT_EMAIL=your_client_email

# API Keys
OPENAI_API_KEY=your_openai_api_key
WEATHER_API_KEY=your_weather_api_key

# Security
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h
```

#### Start MongoDB
```bash
# On macOS/Linux
sudo service mongod start

# On Windows
net start MongoDB
```

#### Start Backend Server
```bash
# Development
npm run dev

# Production
npm start
```

### 3. Frontend Setup

#### Install Dependencies
```bash
cd ../frontend
npm install
```

#### Environment Configuration
1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Update the following variables in `.env`:
```env
# API
VITE_API_URL=http://localhost:8000/api

# Firebase
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_auth_domain
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_storage_bucket
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id

# Feature Flags
VITE_ENABLE_AI_ANALYSIS=true
VITE_ENABLE_WEATHER_FORECAST=true
VITE_ENABLE_SOIL_ANALYSIS=true
```

#### Start Frontend Development Server
```bash
# Development
npm run dev

# Production Build
npm run build
```

### 4. Firebase Setup

#### Install Firebase CLI
```bash
npm install -g firebase-tools
```

#### Login to Firebase
```bash
firebase login
```

#### Initialize Firebase
```bash
firebase init
```

Select the following services:
- Authentication
- Firestore
- Storage
- Hosting

#### Deploy Firebase Configuration
```bash
firebase deploy
```

## Development Workflow

### 1. Start Development Servers
```bash
# Terminal 1 - Backend
cd backend
npm run dev

# Terminal 2 - Frontend
cd frontend
npm run dev
```

### 2. Access the Application
- Frontend: http://localhost:5173
- Backend API: http://localhost:8000/api
- API Documentation: http://localhost:8000/api-docs

### 3. Database Management
- MongoDB Compass: http://localhost:27017
- Default credentials:
  - Username: admin
  - Password: password

## Testing Setup

### 1. Backend Tests
```bash
cd backend
npm test
```

### 2. Frontend Tests
```bash
cd frontend
npm test
```

### 3. E2E Tests
```bash
npm run test:e2e
```

## Common Issues and Solutions

### 1. MongoDB Connection Issues
- Ensure MongoDB service is running
- Check connection string in `.env`
- Verify network connectivity

### 2. Firebase Authentication Issues
- Verify Firebase configuration
- Check API keys in `.env`
- Ensure Firebase project is properly set up

### 3. CORS Issues
- Check backend CORS configuration
- Verify frontend API URL
- Ensure proper headers are set

### 4. WebSocket Connection Issues
- Check WebSocket server status
- Verify authentication token
- Check network connectivity

## Production Deployment

### 1. Backend Deployment
```bash
cd backend
npm run build
npm start
```

### 2. Frontend Deployment
```bash
cd frontend
npm run build
firebase deploy --only hosting
```

### 3. Database Backup
```bash
# Backup MongoDB
mongodump --uri="mongodb://localhost:27017/agriCare" --out=./backup

# Restore MongoDB
mongorestore --uri="mongodb://localhost:27017/agriCare" ./backup
```

## Monitoring and Logging

### 1. Backend Logs
```bash
# View logs
tail -f backend/logs/app.log

# Error logs
tail -f backend/logs/error.log
```

### 2. Frontend Logs
- Check browser console
- View Firebase Analytics
- Monitor error reporting

## Security Considerations

### 1. Environment Variables
- Never commit `.env` files
- Use secure values for secrets
- Rotate API keys regularly

### 2. Database Security
- Enable authentication
- Use strong passwords
- Regular backups
- Network security

### 3. API Security
- Rate limiting
- Input validation
- CORS configuration
- JWT token management

Last Updated: March 30, 2024
Version: 1.0.0 