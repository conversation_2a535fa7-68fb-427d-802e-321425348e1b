# AgriCare Project Documentation

## Project Structure

```
Q_AgriCare1.0/
├── frontend/                 # React frontend application
│   ├── public/              # Static files
│   │   └── index.html      # HTML template
│   │   └── manifest.json   # PWA configuration
│   │   └── robots.txt      # Robots.txt for search engines
│   │   └── service-worker.js
│   │   └── favicon.ico     # Favicon
│   │   └── assets/         # Static assets
│   │   └── styles/         # CSS styles
│   │   └── components/     # Reusable components
│   │   └── Pages/          # Page components
│   │   │   └── Dashboard/  # Dashboard related components
│   │   │       └── DashComponents/
│   │   │           ├── DashData/      # Main dashboard data component
│   │   │           ├── WeeklySchedule/# Weekly farming schedule component
│   │   │           ├── Weather/       # Weather information component
│   │   │           ├── MarketAnalysis/# Market analysis component
│   │   │           └── RecentAlerts/  # Recent alerts component
│   │   ├── services/       # API services
│   │   ├── utils/          # Utility functions
│   │   └── App.jsx         # Main application component
│   └── package.json        # Frontend dependencies
│
├── backend/                 # Node.js backend application
│   ├── config/             # Configuration files
│   │   └── db.js          # Database configuration
│   ├── controllers/        # Route controllers
│   │   ├── aiController.js # AI analysis controller
│   │   ├── weatherController.js
│   │   └── soilController.js
│   ├── models/            # Database models
│   │   ├── Weather.js
│   │   ├── Soil.js
│   │   └── Prediction.js
│   ├── routes/            # API routes
│   │   ├── ai.js
│   │   ├── weather.js
│   │   └── soil.js
│   ├── services/          # Business logic
│   │   └── aiService.js   # AI analysis service
│   └── package.json       # Backend dependencies
│
└── docs/                  # Project documentation
    └── project-structure.md
```

## Database Schema

### Weather Collection
```javascript
{
  temperature: Number,
  humidity: Number,
  rainfall: Number,
  timestamp: Date,
  location: String,
  forecast: [{
    date: Date,
    temperature: Number,
    humidity: Number,
    rainfall: Number
  }]
}
```

### Soil Collection
```javascript
{
  moisture: Number,
  nitrogen: Number,
  phosphorus: Number,
  potassium: Number,
  ec: Number,
  timestamp: Date,
  location: String
}
```

### Predictions Collection
```javascript
{
  weatherId: ObjectId,
  soilId: ObjectId,
  predictions: [{
    title: String,
    description: String,
    confidence: Number,
    category: String
  }],
  timestamp: Date
}
```

## API Endpoints

### Weather API
- `GET /api/weather/current` - Get current weather data
- `GET /api/weather/forecast` - Get weather forecast
- `POST /api/weather/update` - Update weather data

### Soil API
- `GET /api/soil/current` - Get current soil data
- `POST /api/soil/update` - Update soil data
- `GET /api/soil/history` - Get soil data history

### AI Analysis API
- `POST /api/ai/analyze` - Analyze weather and soil data
- `GET /api/ai/predictions` - Get recent predictions
- `POST /api/ai/feedback` - Provide feedback on predictions

## Environment Variables

### Frontend (.env)
```
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_WEATHER_API_KEY=your_weather_api_key
```

### Backend (.env)
```
PORT=8000
MONGODB_URI=mongodb://localhost:27017/agriCare
OPENAI_API_KEY=your_openai_api_key
WEATHER_API_KEY=your_weather_api_key
```

## Dependencies

### Frontend
```json
{
  "dependencies": {
    "@emotion/react": "^11.11.0",
    "@emotion/styled": "^11.11.0",
    "@mui/material": "^5.13.0",
    "@mui/icons-material": "^5.11.16",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-chartjs-2": "^5.2.0",
    "chart.js": "^4.3.0",
    "axios": "^1.4.0"
  }
}
```

### Backend
```json
{
  "dependencies": {
    "express": "^4.18.2",
    "mongoose": "^7.0.3",
    "dotenv": "^16.0.3",
    "openai": "^4.0.0",
    "cors": "^2.8.5",
    "helmet": "^7.0.0"
  }
}
```

## Setup Instructions

1. Clone the repository
2. Install dependencies:
   ```bash
   # Frontend
   cd frontend
   npm install

   # Backend
   cd backend
   npm install
   ```

3. Set up environment variables:
   - Copy `.env.example` to `.env` in both frontend and backend
   - Update the variables with your values

4. Start the development servers:
   ```bash
   # Frontend
   cd frontend
   npm start

   # Backend
   cd backend
   npm run dev
   ```

## Deployment

The application is deployed on Azure DevOps:
- Repository: https://<EMAIL>/Quamin/QuaminAgriCare2.0/_git/AgriCareVersion2.0-3
- CI/CD pipeline is configured for automatic deployment

## Contributing

1. Create a new branch for your feature
2. Make your changes
3. Submit a pull request
4. Wait for review and approval

## License

This project is licensed under the MIT License - see the LICENSE file for details. 