# AgriCare System Architecture Documentation

## Table of Contents
1. [System Overview](#1-system-overview)
2. [Frontend Architecture](#2-frontend-architecture)
3. [Backend Architecture](#3-backend-architecture)
4. [Authentication & Authorization](#4-authentication--authorization)
5. [IoT Integration](#5-iot-integration)
6. [Environment Configuration](#6-environment-configuration)
7. [Data Flow](#7-data-flow)
8. [Security Measures](#8-security-measures)
9. [Deployment](#9-deployment)

## 1. System Overview

### 1.1 Architecture Components
- Frontend: React-based SPA (Single Page Application)
- Backend: Node.js/Express server
- Database: MongoDB
- Authentication: Firebase Authentication
- Cloud Storage: Firebase Storage
- Real-time Updates: WebSocket
- IoT Integration: Custom WebSocket-based protocol

### 1.2 User Roles
1. Territory Manager (TM)
   - Manages multiple farms
   - Onboards farmers
   - Configures IoT devices
   - Monitors farm status

2. Farmers
   - Individual farm dashboard
   - Farm status monitoring
   - Livestock management
   - IoT device data access

3. HR Admin
   - User management
   - Role assignment
   - System configuration

## 2. Frontend Architecture

### 2.1 Directory Structure
```
frontend/
├── src/
│   ├── components/         # Reusable UI components
│   ├── contexts/          # React contexts (Auth, Theme)
│   ├── pages/             # Page components
│   │   ├── Dashboard/     # Farmer dashboard
│   │   ├── TMDashboard/   # Territory Manager dashboard
│   │   ├── Login/         # Authentication pages
│   │   └── Profile/       # User profile
│   ├── services/          # API services
│   └── utils/             # Utility functions
```

### 2.2 Key Components
1. Dashboard Components
   - Weather
   - Soil Health
   - Weekly Schedule
   - Market Analysis
   - Recent Alerts
   - AI Agent

2. TM Dashboard Components
   - Farm Management
   - Farmer Onboarding
   - IoT Configuration
   - Analytics

### 2.3 State Management
- React Context for global state
- Local state for component-specific data
- WebSocket for real-time updates

## 3. Backend Architecture

### 3.1 Directory Structure
```
backend/
├── config/           # Configuration files
├── controllers/      # Route controllers
├── middleware/       # Custom middleware
├── models/          # Database models
├── routes/          # API routes
├── services/        # Business logic
├── utils/           # Utility functions
└── websocket/       # WebSocket handlers
```

### 3.2 API Endpoints
1. Authentication
   - `/api/auth/login`
   - `/api/auth/signup`
   - `/api/auth/verify-otp`

2. Farm Management
   - `/api/farms`
   - `/api/farms/:id`
   - `/api/farms/:id/status`

3. IoT Management
   - `/api/iot/devices`
   - `/api/iot/data`
   - `/api/iot/config`

4. Analytics
   - `/api/analytics/weather`
   - `/api/analytics/soil`
   - `/api/analytics/market`

## 4. Authentication & Authorization

### 4.1 Firebase Authentication
- Phone number authentication
- OTP verification
- JWT token management
- Role-based access control

### 4.2 Protected Routes
1. Farmer Routes
   - Dashboard
   - Farm Details
   - IoT Data

2. TM Routes
   - TM Dashboard
   - Farmer Management
   - IoT Configuration

3. Admin Routes
   - User Management
   - System Settings

## 5. IoT Integration

### 5.1 Device Configuration
1. Device Registration
   - Unique device ID
   - Farm association
   - Sensor configuration

2. Data Collection
   - Real-time sensor data
   - Historical data storage
   - Data validation

3. Alert System
   - Threshold monitoring
   - Alert generation
   - Notification delivery

### 5.2 WebSocket Communication
```javascript
// WebSocket Event Types
{
  DEVICE_DATA: 'device_data',
  ALERT: 'alert',
  CONFIG_UPDATE: 'config_update',
  STATUS_CHANGE: 'status_change'
}
```

## 6. Environment Configuration

### 6.1 Backend (.env)
```env
# Server
PORT=8000
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/agriCare

# API Keys
OPENAI_API_KEY=your_openai_api_key
WEATHER_API_KEY=your_weather_api_key

# Security
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h

# Feature Flags
ENABLE_AI_ANALYSIS=true
ENABLE_WEATHER_FORECAST=true
ENABLE_SOIL_ANALYSIS=true
```

### 6.2 Frontend (.env)
```env
# API
REACT_APP_API_URL=http://localhost:8000/api

# Feature Flags
REACT_APP_ENABLE_AI_ANALYSIS=true
REACT_APP_ENABLE_WEATHER_FORECAST=true
REACT_APP_ENABLE_SOIL_ANALYSIS=true
```

## 7. Data Flow

### 7.1 Farmer Dashboard Flow
```mermaid
graph TD
    A[Farmer Login] --> B[Fetch Farm Data]
    B --> C[Load Dashboard]
    C --> D[Real-time Updates]
    D --> E[Display Data]
    E --> F[User Interaction]
    F --> G[Update UI]
    G --> D
```

### 7.2 TM Dashboard Flow
```mermaid
graph TD
    A[TM Login] --> B[Fetch Territory Data]
    B --> C[Load TM Dashboard]
    C --> D[Monitor Farms]
    D --> E[Manage Farmers]
    E --> F[Configure IoT]
    F --> G[View Analytics]
    G --> D
```

### 7.3 IoT Data Flow
```mermaid
graph TD
    A[IoT Device] --> B[Collect Data]
    B --> C[WebSocket Server]
    C --> D[Process Data]
    D --> E[Store in DB]
    E --> F[Update Dashboard]
    F --> G[Generate Alerts]
    G --> H[Notify Users]
```

## 8. Security Measures

### 8.1 Authentication
- Firebase Phone Auth
- JWT token validation
- Session management
- Role-based access

### 8.2 Data Protection
- API rate limiting
- Input validation
- XSS protection
- CORS configuration

### 8.3 IoT Security
- Device authentication
- Data encryption
- Secure WebSocket
- Access control

## 9. Deployment

### 9.1 Frontend Deployment
- Vite build process
- Environment configuration
- Static file serving
- CDN integration

### 9.2 Backend Deployment
- Node.js server
- MongoDB connection
- WebSocket server
- Load balancing

### 9.3 IoT Deployment
- Device provisioning
- Network configuration
- Data pipeline setup
- Monitoring system

---

## Additional Resources

### API Documentation
- [API Endpoints Documentation](./API.md)
- [WebSocket Events Documentation](./WEBSOCKET.md)
- [Database Schema Documentation](./DATABASE.md)

### Development Guides
- [Setup Guide](./SETUP.md)
- [Contributing Guidelines](./CONTRIBUTING.md)
- [Testing Guide](./TESTING.md)

### Deployment Guides
- [Frontend Deployment](./DEPLOY_FRONTEND.md)
- [Backend Deployment](./DEPLOY_BACKEND.md)
- [IoT Deployment](./DEPLOY_IOT.md)

---

Last Updated: March 30, 2024
Version: 1.0.0 