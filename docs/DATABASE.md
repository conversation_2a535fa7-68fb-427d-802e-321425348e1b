# AgriCare Database Schema Documentation

## MongoDB Collections

### Users Collection
```javascript
{
  _id: ObjectId,
  phoneNumber: String,
  role: String, // "farmer" | "tm" | "admin"
  name: String,
  email: String,
  createdAt: Date,
  updatedAt: Date,
  status: String, // "active" | "inactive" | "suspended"
  territoryId: ObjectId, // Reference to Territory
  lastLogin: Date,
  preferences: {
    language: String,
    notifications: Boolean,
    theme: String
  }
}
```

### Farms Collection
```javascript
{
  _id: ObjectId,
  name: String,
  location: {
    type: "Point",
    coordinates: [Number], // [longitude, latitude]
    address: String
  },
  farmerId: ObjectId, // Reference to Users
  territoryManagerId: ObjectId, // Reference to Users
  size: Number, // in acres
  status: String, // "active" | "inactive" | "maintenance"
  crops: [{
    id: ObjectId,
    name: String,
    variety: String,
    plantingDate: Date,
    expectedHarvestDate: Date,
    area: Number, // in acres
    status: String, // "growing" | "harvested" | "failed"
    yield: Number, // in tons
    quality: Number // 0-100
  }],
  livestock: [{
    id: ObjectId,
    type: String, // "cattle" | "poultry" | "sheep"
    breed: String,
    count: Number,
    health: String, // "good" | "fair" | "poor"
    lastCheckup: Date,
    vaccinations: [{
      date: Date,
      type: String,
      nextDue: Date
    }]
  }],
  createdAt: Date,
  updatedAt: Date
}
```

### IoT Devices Collection
```javascript
{
  _id: ObjectId,
  deviceId: String,
  farmId: ObjectId, // Reference to Farms
  type: String, // "sensor" | "camera" | "controller"
  model: String,
  status: String, // "online" | "offline" | "error"
  lastSeen: Date,
  config: {
    sensors: [String],
    updateInterval: Number,
    thresholds: {
      temperature: {
        min: Number,
        max: Number
      },
      humidity: {
        min: Number,
        max: Number
      },
      soilMoisture: {
        min: Number,
        max: Number
      }
    }
  },
  data: [{
    timestamp: Date,
    type: String,
    value: Number,
    unit: String
  }],
  createdAt: Date,
  updatedAt: Date
}
```

### Alerts Collection
```javascript
{
  _id: ObjectId,
  farmId: ObjectId, // Reference to Farms
  type: String, // "weather" | "soil" | "device" | "security"
  severity: String, // "low" | "medium" | "high" | "critical"
  message: String,
  details: {
    threshold: Number,
    current: Number,
    unit: String,
    deviceId: ObjectId // Reference to IoT Devices
  },
  status: String, // "active" | "acknowledged" | "resolved"
  acknowledgedBy: ObjectId, // Reference to Users
  acknowledgedAt: Date,
  createdAt: Date,
  updatedAt: Date
}
```

### Weather Data Collection
```javascript
{
  _id: ObjectId,
  farmId: ObjectId, // Reference to Farms
  timestamp: Date,
  current: {
    temperature: Number,
    humidity: Number,
    windSpeed: Number,
    precipitation: Number,
    pressure: Number
  },
  forecast: [{
    time: Date,
    temperature: Number,
    humidity: Number,
    windSpeed: Number,
    precipitation: Number
  }],
  createdAt: Date
}
```

### Soil Data Collection
```javascript
{
  _id: ObjectId,
  farmId: ObjectId, // Reference to Farms
  timestamp: Date,
  ph: Number,
  moisture: Number,
  nutrients: {
    nitrogen: Number,
    phosphorus: Number,
    potassium: Number
  },
  temperature: Number,
  location: {
    type: "Point",
    coordinates: [Number] // [longitude, latitude]
  },
  createdAt: Date
}
```

### Market Data Collection
```javascript
{
  _id: ObjectId,
  crop: String,
  timestamp: Date,
  price: Number,
  unit: String,
  market: String,
  region: String,
  trend: String, // "up" | "down" | "stable"
  volume: Number,
  createdAt: Date
}
```

### Territories Collection
```javascript
{
  _id: ObjectId,
  name: String,
  region: String,
  managerId: ObjectId, // Reference to Users
  farms: [ObjectId], // References to Farms
  boundaries: {
    type: "Polygon",
    coordinates: [[[Number]]] // Array of [longitude, latitude] pairs
  },
  createdAt: Date,
  updatedAt: Date
}
```

## Indexes

### Users Collection
```javascript
{
  phoneNumber: 1, // Unique
  role: 1,
  territoryId: 1
}
```

### Farms Collection
```javascript
{
  farmerId: 1,
  territoryManagerId: 1,
  location: "2dsphere"
}
```

### IoT Devices Collection
```javascript
{
  deviceId: 1, // Unique
  farmId: 1,
  status: 1
}
```

### Alerts Collection
```javascript
{
  farmId: 1,
  type: 1,
  severity: 1,
  status: 1,
  createdAt: -1
}
```

### Weather Data Collection
```javascript
{
  farmId: 1,
  timestamp: -1
}
```

### Soil Data Collection
```javascript
{
  farmId: 1,
  timestamp: -1,
  location: "2dsphere"
}
```

### Market Data Collection
```javascript
{
  crop: 1,
  timestamp: -1,
  market: 1
}
```

## Data Relationships

### One-to-Many Relationships
- Territory Manager → Farms
- Farm → Crops
- Farm → Livestock
- Farm → IoT Devices
- Farm → Alerts
- Farm → Weather Data
- Farm → Soil Data

### Many-to-One Relationships
- Farm → Territory Manager
- Farm → Farmer
- IoT Device → Farm
- Alert → Farm
- Weather Data → Farm
- Soil Data → Farm

## Data Validation Rules

### Users
- phoneNumber: Required, unique, format validation
- role: Required, enum ["farmer", "tm", "admin"]
- email: Optional, format validation

### Farms
- name: Required, min length 3
- location: Required, valid GeoJSON Point
- farmerId: Required, valid ObjectId
- territoryManagerId: Required, valid ObjectId

### IoT Devices
- deviceId: Required, unique
- farmId: Required, valid ObjectId
- type: Required, enum ["sensor", "camera", "controller"]

### Alerts
- farmId: Required, valid ObjectId
- type: Required, enum ["weather", "soil", "device", "security"]
- severity: Required, enum ["low", "medium", "high", "critical"]

Last Updated: March 30, 2024
Version: 1.0.0 