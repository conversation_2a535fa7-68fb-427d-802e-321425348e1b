# AgriCare WebSocket Documentation

## Connection
WebSocket connection URL:
```
ws://localhost:8000/ws
```

## Authentication
Connect with Firebase ID token as a query parameter:
```
ws://localhost:8000/ws?token=<firebase_id_token>
```

## Event Types

### 1. Device Data Events

#### Device Data Update
```json
{
  "type": "device_data",
  "data": {
    "deviceId": "device_id",
    "farmId": "farm_id",
    "timestamp": "2024-03-30T10:00:00Z",
    "sensors": {
      "temperature": 25.5,
      "humidity": 65,
      "soil_moisture": 45
    }
  }
}
```

#### Device Status Change
```json
{
  "type": "device_status",
  "data": {
    "deviceId": "device_id",
    "status": "online|offline|error",
    "timestamp": "2024-03-30T10:00:00Z",
    "error": "Error message if status is error"
  }
}
```

### 2. Alert Events

#### Alert Generated
```json
{
  "type": "alert",
  "data": {
    "id": "alert_id",
    "farmId": "farm_id",
    "type": "weather|soil|device|security",
    "severity": "low|medium|high|critical",
    "message": "Alert message",
    "timestamp": "2024-03-30T10:00:00Z",
    "details": {
      "threshold": 30,
      "current": 35,
      "unit": "celsius"
    }
  }
}
```

#### Alert Acknowledged
```json
{
  "type": "alert_acknowledged",
  "data": {
    "alertId": "alert_id",
    "acknowledgedBy": "user_id",
    "timestamp": "2024-03-30T10:00:00Z"
  }
}
```

### 3. Farm Status Events

#### Farm Status Update
```json
{
  "type": "farm_status",
  "data": {
    "farmId": "farm_id",
    "status": "active|inactive|maintenance",
    "timestamp": "2024-03-30T10:00:00Z",
    "metrics": {
      "cropHealth": 85,
      "soilQuality": 90,
      "waterLevel": 75
    }
  }
}
```

### 4. Weather Events

#### Weather Update
```json
{
  "type": "weather_update",
  "data": {
    "farmId": "farm_id",
    "timestamp": "2024-03-30T10:00:00Z",
    "current": {
      "temperature": 25.5,
      "humidity": 65,
      "windSpeed": 12,
      "precipitation": 0
    },
    "forecast": [
      {
        "time": "2024-03-30T11:00:00Z",
        "temperature": 26.0,
        "humidity": 63
      }
    ]
  }
}
```

### 5. Market Data Events

#### Market Price Update
```json
{
  "type": "market_update",
  "data": {
    "crop": "wheat",
    "timestamp": "2024-03-30T10:00:00Z",
    "price": 2500,
    "unit": "per_ton",
    "change": 2.5,
    "changeType": "increase|decrease"
  }
}
```

## Client Implementation

### Connection Setup
```javascript
const ws = new WebSocket(`ws://localhost:8000/ws?token=${firebaseIdToken}`);

ws.onopen = () => {
  console.log('WebSocket connected');
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  handleWebSocketEvent(data);
};

ws.onerror = (error) => {
  console.error('WebSocket error:', error);
};

ws.onclose = () => {
  console.log('WebSocket disconnected');
};
```

### Event Handler
```javascript
function handleWebSocketEvent(data) {
  switch (data.type) {
    case 'device_data':
      handleDeviceData(data.data);
      break;
    case 'alert':
      handleAlert(data.data);
      break;
    case 'farm_status':
      handleFarmStatus(data.data);
      break;
    case 'weather_update':
      handleWeatherUpdate(data.data);
      break;
    case 'market_update':
      handleMarketUpdate(data.data);
      break;
  }
}
```

## Error Handling

### Connection Errors
```json
{
  "type": "error",
  "data": {
    "code": "connection_error",
    "message": "Failed to establish WebSocket connection",
    "timestamp": "2024-03-30T10:00:00Z"
  }
}
```

### Authentication Errors
```json
{
  "type": "error",
  "data": {
    "code": "auth_error",
    "message": "Invalid or expired token",
    "timestamp": "2024-03-30T10:00:00Z"
  }
}
```

## Reconnection Strategy
1. Implement exponential backoff
2. Maximum retry attempts: 5
3. Initial retry delay: 1 second
4. Maximum retry delay: 30 seconds

Example implementation:
```javascript
function connectWebSocket(retryCount = 0) {
  const maxRetries = 5;
  const maxDelay = 30000;
  const delay = Math.min(1000 * Math.pow(2, retryCount), maxDelay);

  const ws = new WebSocket(`ws://localhost:8000/ws?token=${firebaseIdToken}`);

  ws.onclose = () => {
    if (retryCount < maxRetries) {
      setTimeout(() => connectWebSocket(retryCount + 1), delay);
    }
  };

  return ws;
}
```

Last Updated: March 30, 2024
Version: 1.0.0 