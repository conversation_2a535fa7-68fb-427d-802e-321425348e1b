const config = {
  apiUrl: import.meta.env.VITE_API_URL || 'http://localhost:8000/api',
  wsUrl: import.meta.env.VITE_WS_URL || 'ws://localhost:8000',
  weatherApiKey: import.meta.env.VITE_WEATHER_API_KEY,
  enableAIFeatures: import.meta.env.VITE_ENABLE_AI_FEATURES === 'true',
  enableWeatherForecast: import.meta.env.VITE_ENABLE_WEATHER_FORECAST === 'true',
  enableSoilAnalysis: import.meta.env.VITE_ENABLE_SOIL_ANALYSIS === 'true',
  theme: import.meta.env.VITE_THEME || 'dark',
  cacheDuration: parseInt(import.meta.env.VITE_CACHE_DURATION || '3600', 10),
  
  // Azure Configuration
  azureSpeechKey: import.meta.env.VITE_AZURE_SPEECH_KEY,
  azureSpeechRegion: import.meta.env.VITE_AZURE_SPEECH_REGION,
  azureOpenAIEndpoint: import.meta.env.VITE_AZURE_OPENAI_ENDPOINT,
  azureOpenAIKey: import.meta.env.VITE_AZURE_OPENAI_API_KEY,
  azureDeploymentName: import.meta.env.VITE_AZURE_DEPLOYMENT_NAME,
  azureApiVersion: import.meta.env.VITE_AZURE_API_VERSION,
  
  // Firebase Configuration
  firebase: {
    apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
    authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
    projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
    storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
    appId: import.meta.env.VITE_FIREBASE_APP_ID,
    measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
  }
};

// Validate required environment variables
const requiredEnvVars = ['VITE_API_URL', 'VITE_WS_URL'];
requiredEnvVars.forEach(varName => {
  if (!import.meta.env[varName]) {
    console.warn(`Warning: ${varName} is not defined in environment variables`);
  }
});

export default config; 