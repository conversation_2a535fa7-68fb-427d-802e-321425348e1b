// API Configuration
export const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

// API Endpoints
export const ENDPOINTS = {
    AUTH: {
        SIGNUP: '/auth/signup',
        VERIFY_OTP: '/auth/verify-otp',
        LOGIN: '/auth/login'
    },
    TM: {
        VERIFY: '/tm/verify',
        LIST: '/tm/list',
        ADD: '/tm/add',
    },
    HR: {
        LOGIN: '/hr/login',
        SIGNUP: '/hr/signup',
    },
    FARMERS: {
        VERIFY: '/farmers/verify',
        DASHBOARD: '/farmers/dashboard',
        SEARCH: '/farmers/search',
    },
    LIVESTOCK: {
        ANALYZE: '/livestock/analyze',
        HISTORY: '/livestock/history'
    }
};

// Helper function to make API calls
export const makeApiCall = async (endpoint, options = {}) => {
    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`, {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers,
            },
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'API request failed');
        }

        return await response.json();
    } catch (error) {
        console.error('API Error:', error);
        throw error;
    }
}; 