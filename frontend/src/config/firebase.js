import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator, RecaptchaVerifier } from 'firebase/auth';

// Firebase configuration from environment variables
const firebaseConfig = {
    apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "dummy-key",
    authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "agricare-dev.firebaseapp.com",
    projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "agricare-dev",
    storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "agricare-dev.appspot.com",
    messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "123456789",
    appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:123456789:web:abcdef",
    measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || "G-XXXXXXXXXX"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);

// Connect to emulator in development mode
if (import.meta.env.DEV) {
    connectAuthEmulator(auth, 'http://127.0.0.1:9099', {
        disableWarnings: true,
        disableEmulatorWarnings: true
    });
}

// Set up reCAPTCHA verifier for production
export const setupRecaptcha = (containerId, callback) => {
    if (import.meta.env.PROD) {
        return new RecaptchaVerifier(auth, containerId, {
            'size': 'invisible',
            'callback': callback || (() => console.log('reCAPTCHA verified'))
        });
    }
    return null;
};
