/**
 * Environment Configuration
 * 
 * This file extracts environment variables from the import.meta.env object in Vite
 * and exports them for use throughout the application.
 * 
 * Note: In Vite, environment variables must be prefixed with VITE_ to be exposed to client-side code
 */

// API URLs
export const VITE_API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
export const VITE_API_BASE_URL_CHATBOT = import.meta.env.VITE_API_BASE_URL_CHATBOT || 'http://localhost:8000';

// Firebase Configuration
export const VITE_FIREBASE_API_KEY = import.meta.env.VITE_FIREBASE_API_KEY;
export const VITE_FIREBASE_AUTH_DOMAIN = import.meta.env.VITE_FIREBASE_AUTH_DOMAIN;
export const VITE_FIREBASE_PROJECT_ID = import.meta.env.VITE_FIREBASE_PROJECT_ID;
export const VITE_FIREBASE_STORAGE_BUCKET = import.meta.env.VITE_FIREBASE_STORAGE_BUCKET;
export const VITE_FIREBASE_MESSAGING_SENDER_ID = import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID;
export const VITE_FIREBASE_APP_ID = import.meta.env.VITE_FIREBASE_APP_ID;
export const VITE_FIREBASE_MEASUREMENT_ID = import.meta.env.VITE_FIREBASE_MEASUREMENT_ID;

// Azure Speech to Text
export const VITE_AZURE_SPEECH_KEY = import.meta.env.VITE_AZURE_SPEECH_KEY;
export const VITE_AZURE_SPEECH_REGION = import.meta.env.VITE_AZURE_SPEECH_REGION;
export const VITE_AZURE_SPEECH_ENDPOINT = import.meta.env.VITE_AZURE_SPEECH_ENDPOINT || `https://${VITE_AZURE_SPEECH_REGION}.api.cognitive.microsoft.com/`;

// Azure OpenAI
// Note: The endpoint should only include the base URL, not the full path
export const VITE_AZURE_OPENAI_ENDPOINT = import.meta.env.VITE_AZURE_OPENAI_ENDPOINT?.split('/openai/')[0] || 'https://image-gpt4o.openai.azure.com';
export const VITE_AZURE_OPENAI_API_KEY = import.meta.env.VITE_AZURE_OPENAI_API_KEY || '5kxS162ETTa3VMG7ou9OueTBgTgklpRtE758oSmJp1DyQMV1o7ZaJQQJ99BBACYeBjFXJ3w3AAABACOG5O2J';
export const VITE_AZURE_DEPLOYMENT_NAME = import.meta.env.VITE_AZURE_DEPLOYMENT_NAME || 'gpt-4o';
export const VITE_AZURE_API_VERSION = import.meta.env.VITE_AZURE_API_VERSION || '2024-08-01-preview';

// Feature flags
export const IS_DEVELOPMENT = import.meta.env.DEV;
export const IS_PRODUCTION = import.meta.env.PROD; 