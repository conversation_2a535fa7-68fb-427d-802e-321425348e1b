/**
 * AI Configuration Settings
 *
 * This file contains configuration settings for AI features in the application.
 * In a production environment, sensitive values like API keys should be stored
 * in environment variables or a secure vault.
 */

// Feature flags
export const AI_FEATURES = {
  // AI-powered crop analysis comparison is always enabled
  enableAIComparison: true,

  // Enable AI-powered recommendations
  enableAIRecommendations: true,

  // Never use mock data
  useMockDataFallback: false
};

// OpenAI configuration
export const OPENAI_CONFIG = {
  // Default model to use
  model: 'gpt-4o',

  // API endpoint
  apiEndpoint: 'https://api.openai.com/v1/chat/completions',

  // Default parameters
  defaultParams: {
    temperature: 0.3,
    max_tokens: 1000
  },

  // OpenAI API key - IMPORTANT: Replace with your actual API key for production
  // For security, in a real production app, this should be stored in environment variables
  // or fetched from a secure vault, not hardcoded here.
  apiKey: 'YOUR_OPENAI_API_KEY_HERE'
};

// Mock data for development/testing when API is unavailable
export const MOCK_AI_ANALYSIS = {
  growthAnalysis: "The crop has shown moderate growth over the past period. The leaf structure appears more developed, and there are signs of new growth at the apical regions. The overall size has increased by approximately 15-20%.",

  healthAssessment: "The health status has improved from 'fair' to 'good'. The previous yellowing of leaves has reduced, and the plant shows better color and vigor. This improvement likely results from addressing the nutrient deficiency identified in the earlier analysis.",

  resolvedIssues: [
    "Nitrogen deficiency has been successfully addressed",
    "Early signs of leaf curl have been corrected"
  ],

  newIssues: [
    "Minor signs of water stress appearing on newer leaves",
    "Some evidence of uneven growth pattern"
  ],

  persistentIssues: [
    "Slight discoloration on lower leaves remains"
  ],

  predictions: "If current care continues, the crop should reach optimal health within 2-3 weeks. The new water stress should be addressed promptly to prevent it from becoming a significant issue. Overall growth trajectory is positive.",

  recommendations: [
    "Adjust watering schedule to address early signs of water stress",
    "Continue current fertilization regimen as it's showing positive results",
    "Consider light pruning of lower branches to improve air circulation",
    "Monitor the uneven growth pattern and adjust light exposure if possible"
  ],

  environmentalFactors: "The transition from spring to summer conditions may be contributing to the water stress symptoms. The increasing temperatures and light intensity are generally favorable for this crop type but require adjustments to watering frequency."
};
