// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8002/api';

// API Endpoints
const ENDPOINTS = {
    TM: {
        VERIFY: '/tm/verify',
        LIST: '/tm/list',
        ADD: '/tm/add',
    },
    HR: {
        LOGIN: '/hr/login',
        SIGNUP: '/hr/signup',
    },
    FARMER: {
        DASHBOARD: '/farmers/dashboard',
        SEARCH: '/farmers/search',
    }
};

// Helper function to make API calls with retry logic
const makeApiCall = async (endpoint, options = {}, retries = 3) => {
    for (let i = 0; i < retries; i++) {
        try {
            console.log(`Making API call to ${API_BASE_URL}${endpoint}`);
            const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                ...options,
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers,
                },
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || 'API request failed');
            }

            const data = await response.json();
            console.log(`API call successful:`, data);
            return data;
        } catch (error) {
            console.error(`API Error (Attempt ${i + 1}/${retries}):`, error);
            
            // If this is the last attempt, throw the error
            if (i === retries - 1) {
                throw error;
            }
            
            // Wait before retrying (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        }
    }
};

export { API_BASE_URL, ENDPOINTS, makeApiCall }; 