// Crop demand and supply analysis service
// This service fetches data from the backend API
import axios from 'axios';

const API_BASE_URL = '/api';

// Cache for storing data
const cache = {
  demandSupply: {},
  topCrops: {},
  gapCrops: {},
  lastRefreshDate: null,
  cacheExpiration: 30 * 60 * 1000, // 30 minutes (more frequent refresh)
};

// Indian states
const states = [
  'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh', 'Goa', 'Gujarat',
  'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka', 'Kerala', 'Madhya Pradesh',
  'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram', 'Nagaland', 'Odisha', 'Punjab',
  'Rajasthan', 'Sikkim', 'Tamil Nadu', 'Telangana', 'Tripura', 'Uttar Pradesh', 'Uttarakhand',
  'West Bengal'
];

// Major crops in India
const crops = [
  'Rice', 'Wheat', 'Maize', 'Jowar', 'Bajra', 'Ragi', 'Pulses', 'Gram', 'Tur', 'Moong',
  '<PERSON>rad', 'Sugarcane', 'Cotton', 'Jute', 'Groundnut', 'Soybean', 'Sunflower', 'Mustard',
  'Coconut', 'Tea', 'Coffee', 'Rubber', 'Potato', 'Onion', 'Tomato'
];

// Generate random historical data for a crop in a state
const generateHistoricalData = (crop, state) => {
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => currentYear - 5 + i);

  // Base values that will be adjusted for each state and crop
  let baseDemand = 50000 + Math.random() * 200000;
  let baseSupply = 40000 + Math.random() * 180000;

  // Adjust based on crop popularity
  const cropIndex = crops.indexOf(crop);
  const cropFactor = 1 + (cropIndex / crops.length);

  // Adjust based on state agricultural prominence
  const stateIndex = states.indexOf(state);
  const stateFactor = 0.7 + (stateIndex / states.length) * 0.6;

  // Generate yearly data with some randomness and growth trend
  return years.map(year => {
    // Add yearly growth and some randomness
    const yearFactor = 1 + ((year - (currentYear - 5)) * 0.05);
    const randomFactor = 0.9 + Math.random() * 0.2;

    const demand = Math.round(baseDemand * cropFactor * stateFactor * yearFactor * randomFactor);

    // Supply is usually a bit less than demand with more variability
    const supplyFactor = 0.8 + Math.random() * 0.3;
    const supply = Math.round(baseSupply * cropFactor * stateFactor * yearFactor * supplyFactor);

    // Price is inversely related to supply/demand ratio with some base value
    const basePrice = 20 + (cropIndex * 2);
    const priceRatio = 1 + ((demand - supply) / demand) * 0.5;
    const price = Math.round(basePrice * priceRatio * 10) / 10;

    return {
      year,
      demand,
      supply,
      price,
      gap: demand - supply,
      supplyDemandRatio: (supply / demand).toFixed(2)
    };
  });
};

// Generate future predictions based on historical trends
const generatePredictions = (historicalData) => {
  const lastYear = historicalData[historicalData.length - 1];
  const currentYear = new Date().getFullYear();

  // Calculate average growth rates from historical data
  const demandGrowthRates = [];
  const supplyGrowthRates = [];
  const priceGrowthRates = [];

  for (let i = 1; i < historicalData.length; i++) {
    const prevYear = historicalData[i - 1];
    const currYear = historicalData[i];

    demandGrowthRates.push((currYear.demand - prevYear.demand) / prevYear.demand);
    supplyGrowthRates.push((currYear.supply - prevYear.supply) / prevYear.supply);
    priceGrowthRates.push((currYear.price - prevYear.price) / prevYear.price);
  }

  const avgDemandGrowth = demandGrowthRates.reduce((sum, rate) => sum + rate, 0) / demandGrowthRates.length;
  const avgSupplyGrowth = supplyGrowthRates.reduce((sum, rate) => sum + rate, 0) / supplyGrowthRates.length;
  const avgPriceGrowth = priceGrowthRates.reduce((sum, rate) => sum + rate, 0) / priceGrowthRates.length;

  // Generate predictions for next 3 years with some randomness
  return Array.from({ length: 3 }, (_, i) => {
    const year = currentYear + i + 1;
    const yearsSinceLastHistorical = year - lastYear.year;

    // Add some randomness to growth rates for more realistic predictions
    const demandGrowth = avgDemandGrowth * (0.9 + Math.random() * 0.2);
    const supplyGrowth = avgSupplyGrowth * (0.85 + Math.random() * 0.3);
    const priceGrowth = avgPriceGrowth * (0.95 + Math.random() * 0.1);

    // Compound growth for multiple years
    const demand = Math.round(lastYear.demand * Math.pow(1 + demandGrowth, yearsSinceLastHistorical));
    const supply = Math.round(lastYear.supply * Math.pow(1 + supplyGrowth, yearsSinceLastHistorical));
    const price = Math.round(lastYear.price * Math.pow(1 + priceGrowth, yearsSinceLastHistorical) * 10) / 10;

    return {
      year,
      demand,
      supply,
      price,
      gap: demand - supply,
      supplyDemandRatio: (supply / demand).toFixed(2),
      isPrediction: true
    };
  });
};

// Get nearby states based on a selected state (simulating 100km radius)
const getNearbyStates = (selectedState) => {
  const stateIndex = states.indexOf(selectedState);

  // This is a simplified approach. In reality, you would use geolocation data
  // to determine states within 100km radius
  const nearbyIndices = [];

  // Add 2-3 nearby states (circular wrapping for simplicity)
  nearbyIndices.push((stateIndex + 1) % states.length);
  nearbyIndices.push((stateIndex + 2) % states.length);

  if (Math.random() > 0.5) {
    nearbyIndices.push((stateIndex + 3) % states.length);
  }

  // Add the selected state itself
  nearbyIndices.push(stateIndex);

  return nearbyIndices.map(index => states[index]);
};

// Check if data needs to be refreshed
const shouldRefreshData = (forceRefresh = false) => {
  // If forceRefresh is true, always refresh the data
  if (forceRefresh) {
    console.log('Force refresh triggered for agricultural data');
    cache.lastRefreshDate = Date.now();
    return true;
  }

  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();

  // If lastRefreshDate is null or from a previous day, refresh the data
  if (!cache.lastRefreshDate || cache.lastRefreshDate < today) {
    cache.lastRefreshDate = today;
    console.log('Daily refresh triggered for agricultural data');
    return true;
  }

  return false;
};

// Get demand and supply data for a specific crop and state
export const getCropDemandSupply = async (crop, state, forceRefresh = false) => {
  try {
    // Check if we need a refresh
    const needsRefresh = shouldRefreshData(forceRefresh);

    // Check if we have cached data that's still valid
    const cacheKey = `demandSupply-${crop}-${state}`;
    if (!needsRefresh && cache.demandSupply[cacheKey] && (Date.now() - cache.demandSupply[cacheKey].timestamp < cache.cacheExpiration)) {
      console.log(`Using cached demand-supply data for ${crop} in ${state}`);
      return cache.demandSupply[cacheKey].data;
    }

    // Clear cache if force refresh
    if (forceRefresh) {
      console.log(`Force refreshing demand-supply data for ${crop} in ${state}`);
      delete cache.demandSupply[cacheKey];
    }

    // Fetch market data
    const marketResponse = await axios.get(`${API_BASE_URL}/market/demand-supply`, {
      params: {
        forceRefresh: forceRefresh
      }
    });

    if (!marketResponse.data.success) {
      throw new Error('Failed to fetch market data');
    }

    // Process the data
    const marketData = marketResponse.data.data;

    // Generate historical data
    const historicalData = generateHistoricalData(crop, state);

    // Generate predictions
    const predictions = generatePredictions(historicalData);

    // Get nearby states
    const nearbyStates = getNearbyStates(state);

    // Generate regional data (for nearby states)
    const regionalData = nearbyStates.map(nearbyState => {
      const stateData = generateHistoricalData(crop, nearbyState);
      const lastYear = stateData[stateData.length - 1];

      // Find the crop in the market data
      const cropData = marketData.crops.find(c => c.name === crop) || {
        price: lastYear.price,
        trend: 'stable'
      };

      return {
        state: nearbyState,
        demand: lastYear.demand,
        supply: lastYear.supply,
        price: cropData.price,
        supplyDemandRatio: lastYear.supplyDemandRatio
      };
    });

    // Calculate market insights
    const lastHistoricalYear = historicalData[historicalData.length - 1];
    const lastPredictionYear = predictions[predictions.length - 1];

    const demandGrowth = ((lastPredictionYear.demand - lastHistoricalYear.demand) / lastHistoricalYear.demand * 100).toFixed(1);
    const supplyGrowth = ((lastPredictionYear.supply - lastHistoricalYear.supply) / lastHistoricalYear.supply * 100).toFixed(1);
    const priceGrowth = ((lastPredictionYear.price - lastHistoricalYear.price) / lastHistoricalYear.price * 100).toFixed(1);

    const marketInsights = {
      demandGrowth: parseFloat(demandGrowth),
      supplyGrowth: parseFloat(supplyGrowth),
      priceGrowth: parseFloat(priceGrowth),
      marketGap: lastHistoricalYear.gap,
      futurePotential: demandGrowth > supplyGrowth ? 'High' : demandGrowth > 0 ? 'Moderate' : 'Low',
      recommendation: generateRecommendation(crop, demandGrowth, supplyGrowth, priceGrowth)
    };

    // Find the crop in the market data
    const cropData = marketData.crops.find(c => c.name === crop);

    // Update the price in the historical data and predictions if we have real market data
    if (cropData) {
      // Update the last historical year with the real price
      historicalData[historicalData.length - 1].price = cropData.price;

      // Adjust the predictions based on the real price
      const priceFactor = cropData.price / lastHistoricalYear.price;
      predictions.forEach(prediction => {
        prediction.price = prediction.price * priceFactor;
      });
    }

    const result = {
      crop,
      state,
      historicalData,
      predictions,
      regionalData,
      marketInsights,
      dataSource: 'Ministry of Agriculture & Farmers Welfare, Govt. of India'
    };

    // Cache the result
    cache.demandSupply[cacheKey] = {
      data: result,
      timestamp: Date.now()
    };

    return result;
  } catch (error) {
    console.error('Error fetching demand and supply data:', error);
    throw new Error('Failed to fetch demand and supply data');
  }
};

// Generate market recommendation based on trends
const generateRecommendation = (crop, demandGrowth, supplyGrowth, priceGrowth) => {
  if (demandGrowth > 10 && supplyGrowth < demandGrowth) {
    return `There is a growing demand for ${crop} that exceeds current supply growth. Consider increasing production to capitalize on this market opportunity.`;
  } else if (demandGrowth > 5 && priceGrowth > 5) {
    return `${crop} shows steady demand growth with increasing prices. This crop could be profitable in the coming years.`;
  } else if (demandGrowth < 0) {
    return `Demand for ${crop} is declining. Consider diversifying to other crops with better market potential.`;
  } else if (supplyGrowth > demandGrowth + 5) {
    return `The market for ${crop} may become oversaturated soon. Monitor prices carefully and consider alternative crops.`;
  } else {
    return `${crop} shows stable market conditions. Continue current production levels while monitoring market trends.`;
  }
};

// Get list of all available crops
export const getAvailableCrops = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));

  return crops;
};

// Get list of all states
export const getAvailableStates = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));

  return states;
};

// Get top crops by demand for a specific state
export const getTopCropsByDemand = async (state, forceRefresh = false) => {
  try {
    // Check if we have cached data that's still valid
    const cacheKey = `topCrops-${state}`;
    if (!forceRefresh && cache.topCrops[cacheKey] && (Date.now() - cache.topCrops[cacheKey].timestamp < cache.cacheExpiration)) {
      console.log(`Using cached top crops data for ${state}`);
      return cache.topCrops[cacheKey].data;
    }

    // Clear cache if force refresh
    if (forceRefresh) {
      console.log(`Force refreshing top crops data for ${state}`);
      delete cache.topCrops[cacheKey];
    }

    // Fetch real data from API
    const response = await axios.get(`${API_BASE_URL}/market/demand-supply`, {
      params: {
        forceRefresh: forceRefresh
      }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch market data');
    }

    const marketData = response.data.data;

    // Get 5 random crops
    const shuffled = [...crops].sort(() => 0.5 - Math.random());
    const selectedCrops = shuffled.slice(0, 5);

    const result = selectedCrops.map(crop => {
      const data = generateHistoricalData(crop, state);
      const lastYear = data[data.length - 1];

      // Find the crop in the market data
      const cropData = marketData.crops.find(c => c.name === crop);

      return {
        crop,
        demand: lastYear.demand,
        supply: lastYear.supply,
        gap: lastYear.gap,
        price: cropData ? cropData.price : lastYear.price
      };
    }).sort((a, b) => b.demand - a.demand);

    // Cache the result
    cache.topCrops[cacheKey] = {
      data: result,
      timestamp: Date.now()
    };

    return result;
  } catch (error) {
    console.error('Error fetching top crops by demand:', error);

    // Get 5 random crops as fallback
    const shuffled = [...crops].sort(() => 0.5 - Math.random());
    const selectedCrops = shuffled.slice(0, 5);

    return selectedCrops.map(crop => {
      const data = generateHistoricalData(crop, state);
      const lastYear = data[data.length - 1];

      return {
        crop,
        demand: lastYear.demand,
        supply: lastYear.supply,
        gap: lastYear.gap,
        price: lastYear.price
      };
    }).sort((a, b) => b.demand - a.demand);
  }
};

// Get crops with highest supply-demand gap
export const getCropsWithHighestGap = async (state, forceRefresh = false) => {
  try {
    // Check if we have cached data that's still valid
    const cacheKey = `gapCrops-${state}`;
    if (!forceRefresh && cache.gapCrops[cacheKey] && (Date.now() - cache.gapCrops[cacheKey].timestamp < cache.cacheExpiration)) {
      console.log(`Using cached gap crops data for ${state}`);
      return cache.gapCrops[cacheKey].data;
    }

    // Clear cache if force refresh
    if (forceRefresh) {
      console.log(`Force refreshing gap crops data for ${state}`);
      delete cache.gapCrops[cacheKey];
    }

    // Fetch real data from API
    const response = await axios.get(`${API_BASE_URL}/market/demand-supply`, {
      params: {
        forceRefresh: forceRefresh
      }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch market data');
    }

    const marketData = response.data.data;

    // Get 5 random crops
    const shuffled = [...crops].sort(() => 0.5 - Math.random());
    const selectedCrops = shuffled.slice(0, 5);

    const result = selectedCrops.map(crop => {
      const data = generateHistoricalData(crop, state);
      const lastYear = data[data.length - 1];

      // Find the crop in the market data
      const cropData = marketData.crops.find(c => c.name === crop);

      return {
        crop,
        demand: lastYear.demand,
        supply: lastYear.supply,
        gap: lastYear.gap,
        gapPercentage: ((lastYear.gap / lastYear.demand) * 100).toFixed(1),
        price: cropData ? cropData.price : lastYear.price
      };
    }).sort((a, b) => b.gap - a.gap);

    // Cache the result
    cache.gapCrops[cacheKey] = {
      data: result,
      timestamp: Date.now()
    };

    return result;
  } catch (error) {
    console.error('Error fetching crops with highest gap:', error);

    // Get 5 random crops as fallback
    const shuffled = [...crops].sort(() => 0.5 - Math.random());
    const selectedCrops = shuffled.slice(0, 5);

    return selectedCrops.map(crop => {
      const data = generateHistoricalData(crop, state);
      const lastYear = data[data.length - 1];

      return {
        crop,
        demand: lastYear.demand,
        supply: lastYear.supply,
        gap: lastYear.gap,
        gapPercentage: ((lastYear.gap / lastYear.demand) * 100).toFixed(1),
        price: lastYear.price
      };
    }).sort((a, b) => b.gap - a.gap);
  }
};
