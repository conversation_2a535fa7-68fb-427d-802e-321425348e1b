/**
 * AI Analysis Service
 * Provides integration with Azure OpenAI GPT-4o for enhanced crop analysis comparisons
 */

// Import environment variables
import { VITE_API_BASE_URL_CHATBOT, VITE_AZURE_OPENAI_ENDPOINT, VITE_AZURE_OPENAI_API_KEY, VITE_AZURE_DEPLOYMENT_NAME, VITE_AZURE_API_VERSION } from '../config/environment';

// Configuration for Azure OpenAI API
const AZURE_OPENAI_ENDPOINT = VITE_AZURE_OPENAI_ENDPOINT || 'https://image-gpt4o.openai.azure.com/';
const AZURE_OPENAI_API_KEY = VITE_AZURE_OPENAI_API_KEY || '5kxS162ETTa3VMG7ou9OueTBgTgklpRtE758oSmJp1DyQMV1o7ZaJQQJ99BBACYeBjFXJ3w3AAABACOG5O2J';
const AZURE_DEPLOYMENT_NAME = VITE_AZURE_DEPLOYMENT_NAME || 'gpt-4o';
const AZURE_API_VERSION = VITE_AZURE_API_VERSION || '2024-08-01-preview';

// ALWAYS use real data - never use mock data
const USE_MOCK_DATA = false;

/**
 * Generate an AI-powered comparison analysis between two crop analyses
 * @param {Object} currentAnalysis - The current analysis data
 * @param {Object} historicalAnalysis - The historical analysis data to compare with
 * @param {string} apiKey - OpenAI API key (should be stored securely)
 * @returns {Promise<Object>} Enhanced comparison data with AI insights
 */
export async function generateAIComparisonAnalysis(currentAnalysis, historicalAnalysis) {
  try {
    if (!currentAnalysis || !historicalAnalysis) {
      throw new Error('Both current and historical analyses are required');
    }

    // Never use mock data - removed all mock data code paths
    
    if (!AZURE_OPENAI_API_KEY || !AZURE_OPENAI_ENDPOINT) {
      console.warn('Azure OpenAI credentials not found. Cannot proceed with AI analysis.');
      throw new Error('Azure OpenAI credentials not found. Please check your configuration.');
    }

    // Calculate days between analyses
    const currentDate = new Date(currentAnalysis.timestamp);
    const historicalDate = new Date(historicalAnalysis.timestamp);
    const daysDifference = Math.round((currentDate - historicalDate) / (1000 * 60 * 60 * 24));

    // Format the data for GPT-4
    const prompt = createComparisonPrompt(currentAnalysis, historicalAnalysis, daysDifference);

    // Call Azure OpenAI API - ensure the URL is properly formatted without duplicates
    // Correct format: https://endpoint/openai/deployments/deployment-name/chat/completions?api-version=version
    let baseUrl = AZURE_OPENAI_ENDPOINT;
    
    // Remove trailing slash if present
    baseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    
    // Remove any existing path if present (safeguard)
    if (baseUrl.includes('/openai/deployments')) {
      baseUrl = baseUrl.split('/openai/deployments')[0];
    }
    
    const apiUrl = `${baseUrl}/openai/deployments/${AZURE_DEPLOYMENT_NAME}/chat/completions?api-version=${AZURE_API_VERSION}`;
    console.log('Calling Azure OpenAI API at:', apiUrl);

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'api-key': AZURE_OPENAI_API_KEY
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'system',
            content: 'You are an expert agricultural analyst specializing in crop health and growth analysis. Provide detailed, professional insights based on image analysis data.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenAI API error:', errorData);
      throw new Error(`OpenAI API error: ${errorData.error?.message || 'Unknown error'}`);
    }

    const data = await response.json();

    // Process the AI response
    return processAIResponse(data, currentAnalysis, historicalAnalysis, daysDifference);
  } catch (error) {
    console.error('Error generating AI comparison analysis:', error);
    
    // Never fall back to mock data - just handle the error
    // Return a basic comparison with error information
    return {
      ...generateFallbackComparison(currentAnalysis, historicalAnalysis),
      error: error.message,
      aiAnalysis: null
    };
  }
}

/**
 * Create a detailed prompt for the GPT-4o model
 */
function createComparisonPrompt(currentAnalysis, historicalAnalysis, daysDifference) {
  const cropType = currentAnalysis.analysis?.identifiedAs || 'Unknown crop';

  return `
I need a detailed comparison analysis between two images of the same ${cropType} taken ${daysDifference} days apart.

HISTORICAL ANALYSIS (${daysDifference} days ago):
- Crop identified as: ${historicalAnalysis.analysis?.identifiedAs || 'Unknown'}
- Health status: ${historicalAnalysis.analysis?.healthStatus || 'Unknown'}
- Confidence level: ${historicalAnalysis.analysis?.confidence ? Math.round(historicalAnalysis.analysis.confidence * 100) + '%' : 'Unknown'}
- Issues detected: ${formatIssues(historicalAnalysis.analysis?.issues)}
- Recommendations: ${formatRecommendations(historicalAnalysis.analysis?.recommendations)}

CURRENT ANALYSIS (Today):
- Crop identified as: ${currentAnalysis.analysis?.identifiedAs || 'Unknown'}
- Health status: ${currentAnalysis.analysis?.healthStatus || 'Unknown'}
- Confidence level: ${currentAnalysis.analysis?.confidence ? Math.round(currentAnalysis.analysis.confidence * 100) + '%' : 'Unknown'}
- Issues detected: ${formatIssues(currentAnalysis.analysis?.issues)}
- Recommendations: ${formatRecommendations(currentAnalysis.analysis?.recommendations)}

Please provide:
1. A detailed growth analysis comparing these two states
2. An assessment of health changes and their likely causes
3. Identification of resolved issues and new issues that have emerged
4. Predictions for future development if current trends continue
5. Specific recommendations for optimal growth based on this comparison
6. Any environmental or seasonal factors that might be influencing these changes

Format your response as a JSON object with the following structure:
{
  "growthAnalysis": "Detailed analysis of growth changes",
  "healthAssessment": "Assessment of health changes and causes",
  "resolvedIssues": ["List of issues that appear to be resolved"],
  "newIssues": ["List of new issues that have emerged"],
  "persistentIssues": ["List of issues that remain unresolved"],
  "predictions": "Predictions for future development",
  "recommendations": ["List of specific recommendations"],
  "environmentalFactors": "Analysis of environmental or seasonal influences"
}
`;
}

/**
 * Format issues array for the prompt
 */
function formatIssues(issues) {
  if (!issues || !Array.isArray(issues) || issues.length === 0) {
    return 'None detected';
  }
  return issues.map(issue => `"${issue}"`).join(', ');
}

/**
 * Format recommendations array for the prompt
 */
function formatRecommendations(recommendations) {
  if (!recommendations || !Array.isArray(recommendations) || recommendations.length === 0) {
    return 'None provided';
  }
  return recommendations.map(rec => `"${rec}"`).join(', ');
}

/**
 * Process the AI response into a structured format
 */
function processAIResponse(apiResponse, currentAnalysis, historicalAnalysis, daysDifference) {
  try {
    console.log('Processing API response:', apiResponse);

    // Extract the content from the API response
    const content = apiResponse.choices[0]?.message?.content;
    if (!content) {
      console.error('No content in API response:', apiResponse);
      throw new Error('No content in API response');
    }

    console.log('Raw content from API:', content);

    // Parse the JSON response
    // Find JSON object in the response (in case there's additional text)
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      console.error('No valid JSON found in response content:', content);

      // If we can't extract JSON, create a simple object from the text content
      const fallbackAnalysis = {
        growthAnalysis: content.substring(0, 500) + '...',
        healthAssessment: 'Analysis provided as text instead of structured data',
        resolvedIssues: [],
        newIssues: [],
        persistentIssues: [],
        predictions: 'See growth analysis for details',
        recommendations: ['Review the full analysis text for recommendations'],
        environmentalFactors: 'See growth analysis for details'
      };

      return {
        ...generateFallbackComparison(currentAnalysis, historicalAnalysis),
        aiAnalysis: {
          ...fallbackAnalysis,
          isAIGenerated: true,
          rawContent: content // Include the raw content for debugging
        }
      };
    }

    let parsedResponse;
    try {
      parsedResponse = JSON.parse(jsonMatch[0]);
      console.log('Successfully parsed JSON response:', parsedResponse);
    } catch (parseError) {
      console.error('Error parsing JSON from response:', parseError);
      console.log('Attempted to parse:', jsonMatch[0]);

      // Return a fallback with the raw content
      return {
        ...generateFallbackComparison(currentAnalysis, historicalAnalysis),
        aiAnalysis: {
          growthAnalysis: 'Error parsing structured data. Raw analysis provided below.',
          healthAssessment: 'Error parsing response',
          resolvedIssues: [],
          newIssues: [],
          persistentIssues: [],
          predictions: 'Error parsing response',
          recommendations: ['Contact support if this error persists'],
          environmentalFactors: 'Error parsing response',
          isAIGenerated: true,
          rawContent: content
        }
      };
    }

    // Combine with basic comparison data
    return {
      ...generateFallbackComparison(currentAnalysis, historicalAnalysis),
      aiAnalysis: {
        growthAnalysis: parsedResponse.growthAnalysis || 'No growth analysis available',
        healthAssessment: parsedResponse.healthAssessment || 'No health assessment available',
        resolvedIssues: parsedResponse.resolvedIssues || [],
        newIssues: parsedResponse.newIssues || [],
        persistentIssues: parsedResponse.persistentIssues || [],
        predictions: parsedResponse.predictions || 'No predictions available',
        recommendations: parsedResponse.recommendations || [],
        environmentalFactors: parsedResponse.environmentalFactors || 'No environmental analysis available',
        isAIGenerated: true
      }
    };
  } catch (error) {
    console.error('Error processing AI response:', error);
    // Return basic comparison if processing fails
    return generateFallbackComparison(currentAnalysis, historicalAnalysis);
  }
}

/**
 * Generate a basic comparison as fallback when AI is unavailable
 */
function generateFallbackComparison(currentAnalysis, historicalAnalysis) {
  // Calculate days between analyses
  const currentDate = new Date(currentAnalysis.timestamp);
  const historicalDate = new Date(historicalAnalysis.timestamp);
  const daysDifference = Math.round((currentDate - historicalDate) / (1000 * 60 * 60 * 24));

  // Compare health status
  const current = currentAnalysis.analysis || {};
  const historical = historicalAnalysis.analysis || {};

  let healthChange = 'same';
  if (current.healthStatus !== historical.healthStatus) {
    const healthRanking = { 'excellent': 4, 'good': 3, 'fair': 2, 'poor': 1 };
    const currentRank = healthRanking[current.healthStatus?.toLowerCase()] || 0;
    const historicalRank = healthRanking[historical.healthStatus?.toLowerCase()] || 0;

    healthChange = currentRank > historicalRank ? 'improved' : 'declined';
  }

  // Compare issues
  const currentIssues = current.issues || [];
  const historicalIssues = historical.issues || [];

  const resolvedIssues = historicalIssues.filter(issue =>
    !currentIssues.some(currentIssue =>
      currentIssue.toLowerCase().includes(issue.toLowerCase())
    )
  );

  const newIssues = currentIssues.filter(issue =>
    !historicalIssues.some(historicalIssue =>
      historicalIssue.toLowerCase().includes(issue.toLowerCase())
    )
  );

  const persistentIssues = currentIssues.filter(issue =>
    historicalIssues.some(historicalIssue =>
      historicalIssue.toLowerCase().includes(issue.toLowerCase())
    )
  );

  return {
    daysDifference,
    healthChange,
    resolvedIssues,
    newIssues,
    persistentIssues,
    aiAnalysis: null // No AI analysis in fallback mode
  };
}

/**
 * Check if AI analysis is available
 * @returns {boolean} True if AI analysis is available (either via API or mock data)
 */
export function isAIAnalysisAvailable() {
  // Return true if we have API credentials or mock data is enabled
  return !!AZURE_OPENAI_API_KEY || USE_MOCK_DATA;
}
