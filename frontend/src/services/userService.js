import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

// Set auth token for all requests
const setAuthToken = (token) => {
  if (token) {
    axios.defaults.headers.common['x-auth-token'] = token;
  } else {
    delete axios.defaults.headers.common['x-auth-token'];
  }
};

// Get user profile
export const getUserProfile = async () => {
  try {
    const token = localStorage.getItem('token');
    setAuthToken(token);

    const res = await axios.get(`${API_URL}/users/profile`);
    return res.data;
  } catch (err) {
    console.error('Error fetching user profile:', err.response?.data?.msg || err.message);
    throw err;
  }
};

// Update user profile
export const updateUserProfile = async (profileData) => {
  try {
    const token = localStorage.getItem('token');
    setAuthToken(token);

    const res = await axios.put(`${API_URL}/users/profile`, profileData);
    return res.data;
  } catch (err) {
    console.error('Error updating user profile:', err.response?.data?.msg || err.message);
    throw err;
  }
};

// Change user password
export const changePassword = async (passwordData) => {
  try {
    const token = localStorage.getItem('token');
    setAuthToken(token);

    const res = await axios.put(`${API_URL}/users/password`, passwordData);
    return res.data;
  } catch (err) {
    console.error('Error changing password:', err.response?.data?.msg || err.message);
    throw err;
  }
};

// Get user settings
export const getUserSettings = async () => {
  try {
    const token = localStorage.getItem('token');
    setAuthToken(token);

    const res = await axios.get(`${API_URL}/users/settings`);
    return res.data;
  } catch (err) {
    console.error('Error fetching user settings:', err.response?.data?.msg || err.message);
    throw err;
  }
};

// Update user settings
export const updateUserSettings = async (settingsData) => {
  try {
    const token = localStorage.getItem('token');
    setAuthToken(token);

    const res = await axios.put(`${API_URL}/users/settings`, settingsData);
    return res.data;
  } catch (err) {
    console.error('Error updating user settings:', err.response?.data?.msg || err.message);
    throw err;
  }
};
