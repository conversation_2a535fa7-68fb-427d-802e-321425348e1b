/**
 * Crop Demand and Supply Service
 *
 * This service provides functions to fetch crop demand and supply data from the backend API.
 */

import axios from 'axios';
import { API_BASE_URL } from '../config/api';

/**
 * Get top crops by demand for a specific state
 * @param {string} state - The state to get data for
 * @returns {Promise<Array>} - Array of top crops by demand
 */
export const getTopCropsByDemand = async (state = 'Madhya Pradesh') => {
  try {
    const response = await axios.get(`${API_BASE_URL}/crop-demand-supply/top-crops`, {
      params: { state }
    });

    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }

    return [];
  } catch (error) {
    console.error('Error fetching top crops by demand:', error);
    return [];
  }
};

/**
 * Get crops with highest demand-supply gap for a specific state
 * @param {string} state - The state to get data for
 * @returns {Promise<Array>} - Array of crops with highest gap
 */
export const getCropsWithHighestGap = async (state = 'Madhya Pradesh') => {
  try {
    const response = await axios.get(`${API_BASE_URL}/crop-demand-supply/highest-gap`, {
      params: { state }
    });

    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }

    return [];
  } catch (error) {
    console.error('Error fetching crops with highest gap:', error);
    return [];
  }
};

/**
 * Get list of available states
 * @returns {Promise<Array>} - Array of state names
 */
export const getAvailableStates = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/crop-demand-supply/states`);

    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }

    return [];
  } catch (error) {
    console.error('Error fetching available states:', error);
    return [];
  }
};

/**
 * Get last updated timestamp
 * @returns {Promise<Object>} - Object with lastUpdated and nextUpdate timestamps
 */
export const getLastUpdated = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/crop-demand-supply/last-updated`);

    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }

    return {
      lastUpdated: new Date().toISOString(),
      nextUpdate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    };
  } catch (error) {
    console.error('Error fetching last updated timestamp:', error);
    return {
      lastUpdated: new Date().toISOString(),
      nextUpdate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    };
  }
};
