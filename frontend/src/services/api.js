// Task API endpoints
export const fetchTasks = async () => {
    const idToken = await getCurrentUser().firebaseUser.getIdToken();
    const response = await axios.get(
        `${import.meta.env.VITE_API_URL}/api/tasks`,
        {
            headers: { Authorization: `Bearer ${idToken}` }
        }
    );
    return response.data;
};

export const createTask = async (taskData) => {
    const idToken = await getCurrentUser().firebaseUser.getIdToken();
    const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/api/tasks`,
        taskData,
        {
            headers: { Authorization: `Bearer ${idToken}` }
        }
    );
    return response.data;
};

export const updateTask = async (taskId, taskData) => {
    const idToken = await getCurrentUser().firebaseUser.getIdToken();
    const response = await axios.put(
        `${import.meta.env.VITE_API_URL}/api/tasks/${taskId}`,
        taskData,
        {
            headers: { Authorization: `Bearer ${idToken}` }
        }
    );
    return response.data;
};

export const deleteTask = async (taskId) => {
    const idToken = await getCurrentUser().firebaseUser.getIdToken();
    const response = await axios.delete(
        `${import.meta.env.VITE_API_URL}/api/tasks/${taskId}`,
        {
            headers: { Authorization: `Bearer ${idToken}` }
        }
    );
    return response.data;
};

export const toggleTaskStatus = async (taskId) => {
    const idToken = await getCurrentUser().firebaseUser.getIdToken();
    const response = await axios.patch(
        `${import.meta.env.VITE_API_URL}/api/tasks/${taskId}/toggle`,
        {},
        {
            headers: { Authorization: `Bearer ${idToken}` }
        }
    );
    return response.data;
};

// Event API endpoints
export const fetchEvents = async () => {
    const idToken = await getCurrentUser().firebaseUser.getIdToken();
    const response = await axios.get(
        `${import.meta.env.VITE_API_URL}/api/events`,
        {
            headers: { Authorization: `Bearer ${idToken}` }
        }
    );
    return response.data;
};

export const createEvent = async (eventData) => {
    const idToken = await getCurrentUser().firebaseUser.getIdToken();
    const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/api/events`,
        eventData,
        {
            headers: { Authorization: `Bearer ${idToken}` }
        }
    );
    return response.data;
};

export const updateEvent = async (eventId, eventData) => {
    const idToken = await getCurrentUser().firebaseUser.getIdToken();
    const response = await axios.put(
        `${import.meta.env.VITE_API_URL}/api/events/${eventId}`,
        eventData,
        {
            headers: { Authorization: `Bearer ${idToken}` }
        }
    );
    return response.data;
};

export const deleteEvent = async (eventId) => {
    const idToken = await getCurrentUser().firebaseUser.getIdToken();
    const response = await axios.delete(
        `${import.meta.env.VITE_API_URL}/api/events/${eventId}`,
        {
            headers: { Authorization: `Bearer ${idToken}` }
        }
    );
    return response.data;
};

export const updateEventStatus = async (eventId, status) => {
    const idToken = await getCurrentUser().firebaseUser.getIdToken();
    const response = await axios.patch(
        `${import.meta.env.VITE_API_URL}/api/events/${eventId}/status`,
        { status },
        {
            headers: { Authorization: `Bearer ${idToken}` }
        }
    );
    return response.data;
};

export const addEventAttendees = async (eventId, attendees) => {
    const idToken = await getCurrentUser().firebaseUser.getIdToken();
    const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/api/events/${eventId}/attendees`,
        { attendees },
        {
            headers: { Authorization: `Bearer ${idToken}` }
        }
    );
    return response.data;
}; 