// Mock data service for comprehensive farm management
// In a real application, this would fetch data from a backend API

// Mock farms data
const mockFarms = [
  {
    id: '1',
    name: 'Green Valley Farm',
    location: 'Punjab',
    size: 50,
    status: 'Active',
    updatedAt: new Date().toISOString(),
    type: 'Organic',
    description: 'Main farm for wheat and rice cultivation',
    soilType: 'Loamy',
    irrigation: 'Drip Irrigation',
    crops: ['Wheat', 'Rice', 'Maize'],
    owner: '<PERSON><PERSON>',
    coordinates: { lat: 30.9010, lng: 75.8573 },
    image: 'https://placehold.co/600x400/228B22/FFFFFF.png?text=Green+Valley+Farm'
  },
  {
    id: '2',
    name: 'Sunrise Fields',
    location: 'Haryana',
    size: 75,
    status: 'Active',
    updatedAt: new Date().toISOString(),
    type: 'Conventional',
    description: 'Mixed crop farm with modern irrigation',
    soilType: 'Clay',
    irrigation: 'Sprinkler System',
    crops: ['Cotton', 'Sugarcane', 'Pulses'],
    owner: '<PERSON><PERSON>',
    coordinates: { lat: 29.0588, lng: 76.0856 },
    image: 'https://placehold.co/600x400/FFA500/FFFFFF.png?text=Sunrise+Fields'
  },
  {
    id: '3',
    name: 'River View Farm',
    location: 'Uttar Pradesh',
    size: 100,
    status: 'Inactive',
    updatedAt: new Date().toISOString(),
    type: 'Organic',
    description: 'Seasonal farming with focus on vegetables',
    soilType: 'Sandy Loam',
    irrigation: 'Flood Irrigation',
    crops: ['Potato', 'Tomato', 'Onion'],
    owner: 'Priya Sharma',
    coordinates: { lat: 26.8467, lng: 80.9462 },
    image: 'https://placehold.co/600x400/4682B4/FFFFFF.png?text=River+View+Farm'
  },
  {
    id: '4',
    name: 'Mountain Terrace Farm',
    location: 'Himachal Pradesh',
    size: 30,
    status: 'Active',
    updatedAt: new Date().toISOString(),
    type: 'Organic',
    description: 'Terrace farming for apples and other fruits',
    soilType: 'Silt Loam',
    irrigation: 'Rainwater Harvesting',
    crops: ['Apple', 'Pear', 'Plum'],
    owner: 'Vikram Thakur',
    coordinates: { lat: 31.1048, lng: 77.1734 },
    image: 'https://placehold.co/600x400/8B4513/FFFFFF.png?text=Mountain+Terrace+Farm'
  },
  {
    id: '5',
    name: 'Coastal Plantation',
    location: 'Kerala',
    size: 60,
    status: 'Active',
    updatedAt: new Date().toISOString(),
    type: 'Conventional',
    description: 'Plantation farm for spices and coconut',
    soilType: 'Red Soil',
    irrigation: 'Canal Irrigation',
    crops: ['Coconut', 'Black Pepper', 'Cardamom'],
    owner: 'Thomas Joseph',
    coordinates: { lat: 10.8505, lng: 76.2711 },
    image: 'https://placehold.co/600x400/20B2AA/FFFFFF.png?text=Coastal+Plantation'
  }
];

// Mock crop planning data
const mockCropPlanning = [
  {
    farmId: '1',
    plans: [
      {
        id: 'cp1',
        cropName: 'Wheat',
        season: 'Rabi',
        area: 25,
        startDate: '2023-11-15',
        harvestDate: '2024-04-10',
        expectedYield: 1250,
        status: 'Active',
        variety: 'HD-2967',
        seedSource: 'Punjab Agricultural University',
        notes: 'Using organic fertilizers and pest management'
      },
      {
        id: 'cp2',
        cropName: 'Rice',
        season: 'Kharif',
        area: 20,
        startDate: '2023-06-20',
        harvestDate: '2023-10-25',
        expectedYield: 1600,
        status: 'Completed',
        variety: 'Pusa Basmati-1',
        seedSource: 'IARI',
        notes: 'Implemented SRI technique for better yield'
      }
    ]
  },
  {
    farmId: '2',
    plans: [
      {
        id: 'cp3',
        cropName: 'Cotton',
        season: 'Kharif',
        area: 40,
        startDate: '2023-05-10',
        harvestDate: '2023-11-15',
        expectedYield: 1600,
        status: 'Completed',
        variety: 'Bt Cotton',
        seedSource: 'Mahyco Seeds',
        notes: 'Used integrated pest management'
      },
      {
        id: 'cp4',
        cropName: 'Pulses',
        season: 'Rabi',
        area: 30,
        startDate: '2023-10-20',
        harvestDate: '2024-02-15',
        expectedYield: 900,
        status: 'Active',
        variety: 'Moong IPM-02-03',
        seedSource: 'National Seeds Corporation',
        notes: 'Crop rotation after cotton for soil health'
      }
    ]
  }
];

// Mock inventory data
const mockInventory = [
  {
    farmId: '1',
    inventory: [
      {
        id: 'inv1',
        category: 'Seeds',
        name: 'Wheat Seeds (HD-2967)',
        quantity: 500,
        unit: 'kg',
        purchaseDate: '2023-10-05',
        expiryDate: '2024-10-05',
        supplier: 'Punjab Agricultural University',
        cost: 15000
      },
      {
        id: 'inv2',
        category: 'Fertilizer',
        name: 'Organic Compost',
        quantity: 2000,
        unit: 'kg',
        purchaseDate: '2023-09-15',
        expiryDate: '2024-09-15',
        supplier: 'Green Earth Organics',
        cost: 10000
      },
      {
        id: 'inv3',
        category: 'Equipment',
        name: 'Tractor',
        quantity: 1,
        unit: 'unit',
        purchaseDate: '2020-05-10',
        expiryDate: null,
        supplier: 'Mahindra & Mahindra',
        cost: 750000
      }
    ]
  },
  {
    farmId: '2',
    inventory: [
      {
        id: 'inv4',
        category: 'Seeds',
        name: 'Cotton Seeds (Bt)',
        quantity: 200,
        unit: 'kg',
        purchaseDate: '2023-03-20',
        expiryDate: '2024-03-20',
        supplier: 'Mahyco Seeds',
        cost: 30000
      },
      {
        id: 'inv5',
        category: 'Pesticide',
        name: 'Neem Oil',
        quantity: 100,
        unit: 'liter',
        purchaseDate: '2023-04-10',
        expiryDate: '2024-04-10',
        supplier: 'Organic Solutions',
        cost: 15000
      }
    ]
  }
];

// Mock financial data
const mockFinancialData = [
  {
    farmId: '1',
    finances: {
      income: [
        {
          id: 'inc1',
          category: 'Crop Sales',
          description: 'Wheat harvest sale',
          amount: 200000,
          date: '2023-04-20',
          buyer: 'Government Procurement Agency'
        },
        {
          id: 'inc2',
          category: 'Crop Sales',
          description: 'Rice harvest sale',
          amount: 250000,
          date: '2023-11-05',
          buyer: 'Local Market'
        },
        {
          id: 'inc3',
          category: 'Subsidies',
          description: 'Organic farming subsidy',
          amount: 50000,
          date: '2023-07-15',
          buyer: 'Agriculture Department'
        }
      ],
      expenses: [
        {
          id: 'exp1',
          category: 'Seeds',
          description: 'Wheat seeds purchase',
          amount: 15000,
          date: '2023-10-05',
          vendor: 'Punjab Agricultural University'
        },
        {
          id: 'exp2',
          category: 'Fertilizer',
          description: 'Organic compost purchase',
          amount: 10000,
          date: '2023-09-15',
          vendor: 'Green Earth Organics'
        },
        {
          id: 'exp3',
          category: 'Labor',
          description: 'Harvesting labor',
          amount: 30000,
          date: '2023-04-10',
          vendor: 'Local Workers'
        },
        {
          id: 'exp4',
          category: 'Equipment',
          description: 'Tractor maintenance',
          amount: 5000,
          date: '2023-08-20',
          vendor: 'Mahindra Service Center'
        },
        {
          id: 'exp5',
          category: 'Irrigation',
          description: 'Drip irrigation system maintenance',
          amount: 8000,
          date: '2023-05-15',
          vendor: 'Irrigation Solutions'
        }
      ]
    }
  },
  {
    farmId: '2',
    finances: {
      income: [
        {
          id: 'inc4',
          category: 'Crop Sales',
          description: 'Cotton harvest sale',
          amount: 350000,
          date: '2023-11-25',
          buyer: 'Textile Mill'
        },
        {
          id: 'inc5',
          category: 'Crop Sales',
          description: 'Pulses sale',
          amount: 120000,
          date: '2023-03-10',
          buyer: 'Wholesale Market'
        }
      ],
      expenses: [
        {
          id: 'exp6',
          category: 'Seeds',
          description: 'Cotton seeds purchase',
          amount: 30000,
          date: '2023-03-20',
          vendor: 'Mahyco Seeds'
        },
        {
          id: 'exp7',
          category: 'Pesticide',
          description: 'Neem oil purchase',
          amount: 15000,
          date: '2023-04-10',
          vendor: 'Organic Solutions'
        },
        {
          id: 'exp8',
          category: 'Labor',
          description: 'Cotton picking labor',
          amount: 50000,
          date: '2023-11-10',
          vendor: 'Local Workers'
        },
        {
          id: 'exp9',
          category: 'Equipment',
          description: 'Sprinkler system installation',
          amount: 100000,
          date: '2023-02-15',
          vendor: 'Irrigation Solutions'
        }
      ]
    }
  }
];

// Mock task management data
const mockTasks = [
  {
    farmId: '1',
    tasks: [
      {
        id: 'task1',
        title: 'Apply fertilizer to wheat fields',
        description: 'Apply organic compost to the wheat fields at the recommended rate',
        dueDate: '2023-12-15',
        status: 'Pending',
        priority: 'High',
        assignedTo: 'Farm Manager',
        category: 'Crop Management'
      },
      {
        id: 'task2',
        title: 'Repair irrigation system',
        description: 'Fix leaks in the drip irrigation system in the eastern section',
        dueDate: '2023-12-10',
        status: 'In Progress',
        priority: 'Medium',
        assignedTo: 'Maintenance Team',
        category: 'Infrastructure'
      },
      {
        id: 'task3',
        title: 'Order seeds for next season',
        description: 'Place order for rice seeds for the upcoming Kharif season',
        dueDate: '2024-01-15',
        status: 'Completed',
        priority: 'Medium',
        assignedTo: 'Procurement Officer',
        category: 'Inventory'
      }
    ]
  },
  {
    farmId: '2',
    tasks: [
      {
        id: 'task4',
        title: 'Pest monitoring in cotton fields',
        description: 'Conduct weekly monitoring for bollworm infestation',
        dueDate: '2023-12-20',
        status: 'Pending',
        priority: 'High',
        assignedTo: 'Pest Control Team',
        category: 'Crop Management'
      },
      {
        id: 'task5',
        title: 'Prepare fields for pulses',
        description: 'Prepare soil and beds for pulse cultivation',
        dueDate: '2023-10-15',
        status: 'Completed',
        priority: 'High',
        assignedTo: 'Field Workers',
        category: 'Crop Management'
      }
    ]
  }
];

// Mock weather data
const mockWeatherData = [
  {
    farmId: '1',
    forecast: [
      {
        date: '2023-12-05',
        temperature: { min: 10, max: 22 },
        humidity: 65,
        precipitation: 0,
        windSpeed: 8,
        description: 'Clear'
      },
      {
        date: '2023-12-06',
        temperature: { min: 12, max: 24 },
        humidity: 60,
        precipitation: 0,
        windSpeed: 10,
        description: 'Sunny'
      },
      {
        date: '2023-12-07',
        temperature: { min: 14, max: 25 },
        humidity: 55,
        precipitation: 0,
        windSpeed: 12,
        description: 'Sunny'
      },
      {
        date: '2023-12-08',
        temperature: { min: 13, max: 23 },
        humidity: 70,
        precipitation: 20,
        windSpeed: 15,
        description: 'Partly Cloudy'
      },
      {
        date: '2023-12-09',
        temperature: { min: 11, max: 20 },
        humidity: 80,
        precipitation: 60,
        windSpeed: 18,
        description: 'Rain'
      }
    ]
  },
  {
    farmId: '2',
    forecast: [
      {
        date: '2023-12-05',
        temperature: { min: 12, max: 26 },
        humidity: 55,
        precipitation: 0,
        windSpeed: 6,
        description: 'Clear'
      },
      {
        date: '2023-12-06',
        temperature: { min: 14, max: 28 },
        humidity: 50,
        precipitation: 0,
        windSpeed: 8,
        description: 'Sunny'
      },
      {
        date: '2023-12-07',
        temperature: { min: 15, max: 29 },
        humidity: 45,
        precipitation: 0,
        windSpeed: 10,
        description: 'Sunny'
      },
      {
        date: '2023-12-08',
        temperature: { min: 16, max: 27 },
        humidity: 60,
        precipitation: 10,
        windSpeed: 12,
        description: 'Partly Cloudy'
      },
      {
        date: '2023-12-09',
        temperature: { min: 14, max: 25 },
        humidity: 70,
        precipitation: 40,
        windSpeed: 14,
        description: 'Rain'
      }
    ]
  }
];

// Mock yield data
const mockYieldData = [
  {
    farmId: '1',
    yields: [
      {
        year: 2020,
        crops: [
          { name: 'Wheat', yield: 45, unit: 'quintals/acre', area: 25 },
          { name: 'Rice', yield: 50, unit: 'quintals/acre', area: 20 }
        ]
      },
      {
        year: 2021,
        crops: [
          { name: 'Wheat', yield: 48, unit: 'quintals/acre', area: 25 },
          { name: 'Rice', yield: 52, unit: 'quintals/acre', area: 20 }
        ]
      },
      {
        year: 2022,
        crops: [
          { name: 'Wheat', yield: 47, unit: 'quintals/acre', area: 25 },
          { name: 'Rice', yield: 55, unit: 'quintals/acre', area: 20 }
        ]
      },
      {
        year: 2023,
        crops: [
          { name: 'Wheat', yield: 50, unit: 'quintals/acre', area: 25 },
          { name: 'Rice', yield: 58, unit: 'quintals/acre', area: 20 }
        ]
      }
    ]
  },
  {
    farmId: '2',
    yields: [
      {
        year: 2020,
        crops: [
          { name: 'Cotton', yield: 25, unit: 'quintals/acre', area: 40 },
          { name: 'Pulses', yield: 15, unit: 'quintals/acre', area: 30 }
        ]
      },
      {
        year: 2021,
        crops: [
          { name: 'Cotton', yield: 28, unit: 'quintals/acre', area: 40 },
          { name: 'Pulses', yield: 16, unit: 'quintals/acre', area: 30 }
        ]
      },
      {
        year: 2022,
        crops: [
          { name: 'Cotton', yield: 30, unit: 'quintals/acre', area: 40 },
          { name: 'Pulses', yield: 18, unit: 'quintals/acre', area: 30 }
        ]
      },
      {
        year: 2023,
        crops: [
          { name: 'Cotton', yield: 32, unit: 'quintals/acre', area: 40 },
          { name: 'Pulses', yield: 20, unit: 'quintals/acre', area: 30 }
        ]
      }
    ]
  }
];

// Get all farms
export const getAllFarms = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return [...mockFarms];
};

// Get farm by ID
export const getFarmById = async (farmId) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const farm = mockFarms.find(farm => farm.id === farmId);
  
  if (!farm) {
    throw new Error('Farm not found');
  }
  
  return { ...farm };
};

// Add new farm
export const addFarm = async (farmData) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 700));
  
  const newFarm = {
    id: Date.now().toString(),
    ...farmData,
    updatedAt: new Date().toISOString(),
    status: 'Active'
  };
  
  mockFarms.push(newFarm);
  
  return newFarm;
};

// Update farm
export const updateFarm = async (farmId, farmData) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 700));
  
  const farmIndex = mockFarms.findIndex(farm => farm.id === farmId);
  
  if (farmIndex === -1) {
    throw new Error('Farm not found');
  }
  
  const updatedFarm = {
    ...mockFarms[farmIndex],
    ...farmData,
    updatedAt: new Date().toISOString()
  };
  
  mockFarms[farmIndex] = updatedFarm;
  
  return updatedFarm;
};

// Delete farm
export const deleteFarm = async (farmId) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const farmIndex = mockFarms.findIndex(farm => farm.id === farmId);
  
  if (farmIndex === -1) {
    throw new Error('Farm not found');
  }
  
  mockFarms.splice(farmIndex, 1);
  
  return { success: true };
};

// Get crop planning data for a farm
export const getCropPlanningByFarmId = async (farmId) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 400));
  
  const farmCropPlanning = mockCropPlanning.find(item => item.farmId === farmId);
  
  if (!farmCropPlanning) {
    return { farmId, plans: [] };
  }
  
  return { ...farmCropPlanning };
};

// Add crop plan
export const addCropPlan = async (farmId, planData) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 600));
  
  const farmCropPlanningIndex = mockCropPlanning.findIndex(item => item.farmId === farmId);
  
  const newPlan = {
    id: Date.now().toString(),
    ...planData
  };
  
  if (farmCropPlanningIndex === -1) {
    // Create new entry for farm
    mockCropPlanning.push({
      farmId,
      plans: [newPlan]
    });
  } else {
    // Add to existing farm plans
    mockCropPlanning[farmCropPlanningIndex].plans.push(newPlan);
  }
  
  return newPlan;
};

// Get inventory for a farm
export const getInventoryByFarmId = async (farmId) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 400));
  
  const farmInventory = mockInventory.find(item => item.farmId === farmId);
  
  if (!farmInventory) {
    return { farmId, inventory: [] };
  }
  
  return { ...farmInventory };
};

// Add inventory item
export const addInventoryItem = async (farmId, itemData) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 600));
  
  const farmInventoryIndex = mockInventory.findIndex(item => item.farmId === farmId);
  
  const newItem = {
    id: Date.now().toString(),
    ...itemData
  };
  
  if (farmInventoryIndex === -1) {
    // Create new entry for farm
    mockInventory.push({
      farmId,
      inventory: [newItem]
    });
  } else {
    // Add to existing farm inventory
    mockInventory[farmInventoryIndex].inventory.push(newItem);
  }
  
  return newItem;
};

// Get financial data for a farm
export const getFinancialDataByFarmId = async (farmId) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 400));
  
  const farmFinancialData = mockFinancialData.find(item => item.farmId === farmId);
  
  if (!farmFinancialData) {
    return { 
      farmId, 
      finances: {
        income: [],
        expenses: []
      } 
    };
  }
  
  return { ...farmFinancialData };
};

// Add financial transaction
export const addFinancialTransaction = async (farmId, transactionType, transactionData) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 600));
  
  const farmFinancialDataIndex = mockFinancialData.findIndex(item => item.farmId === farmId);
  
  const newTransaction = {
    id: Date.now().toString(),
    ...transactionData,
    date: transactionData.date || new Date().toISOString().split('T')[0]
  };
  
  if (farmFinancialDataIndex === -1) {
    // Create new entry for farm
    const newFinancialData = {
      farmId,
      finances: {
        income: [],
        expenses: []
      }
    };
    
    newFinancialData.finances[transactionType].push(newTransaction);
    mockFinancialData.push(newFinancialData);
  } else {
    // Add to existing farm financial data
    mockFinancialData[farmFinancialDataIndex].finances[transactionType].push(newTransaction);
  }
  
  return newTransaction;
};

// Get tasks for a farm
export const getTasksByFarmId = async (farmId) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 400));
  
  const farmTasks = mockTasks.find(item => item.farmId === farmId);
  
  if (!farmTasks) {
    return { farmId, tasks: [] };
  }
  
  return { ...farmTasks };
};

// Add task
export const addTask = async (farmId, taskData) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 600));
  
  const farmTasksIndex = mockTasks.findIndex(item => item.farmId === farmId);
  
  const newTask = {
    id: Date.now().toString(),
    ...taskData,
    status: taskData.status || 'Pending'
  };
  
  if (farmTasksIndex === -1) {
    // Create new entry for farm
    mockTasks.push({
      farmId,
      tasks: [newTask]
    });
  } else {
    // Add to existing farm tasks
    mockTasks[farmTasksIndex].tasks.push(newTask);
  }
  
  return newTask;
};

// Update task status
export const updateTaskStatus = async (farmId, taskId, status) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 400));
  
  const farmTasksIndex = mockTasks.findIndex(item => item.farmId === farmId);
  
  if (farmTasksIndex === -1) {
    throw new Error('Farm tasks not found');
  }
  
  const taskIndex = mockTasks[farmTasksIndex].tasks.findIndex(task => task.id === taskId);
  
  if (taskIndex === -1) {
    throw new Error('Task not found');
  }
  
  mockTasks[farmTasksIndex].tasks[taskIndex].status = status;
  
  return mockTasks[farmTasksIndex].tasks[taskIndex];
};

// Get weather data for a farm
export const getWeatherByFarmId = async (farmId) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const farmWeather = mockWeatherData.find(item => item.farmId === farmId);
  
  if (!farmWeather) {
    // Generate random weather data if not found
    const randomForecast = Array.from({ length: 5 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() + i);
      
      return {
        date: date.toISOString().split('T')[0],
        temperature: { 
          min: 10 + Math.floor(Math.random() * 10), 
          max: 20 + Math.floor(Math.random() * 10) 
        },
        humidity: 50 + Math.floor(Math.random() * 40),
        precipitation: Math.floor(Math.random() * 80),
        windSpeed: 5 + Math.floor(Math.random() * 15),
        description: ['Clear', 'Sunny', 'Partly Cloudy', 'Cloudy', 'Rain'][Math.floor(Math.random() * 5)]
      };
    });
    
    return { farmId, forecast: randomForecast };
  }
  
  return { ...farmWeather };
};

// Get yield data for a farm
export const getYieldDataByFarmId = async (farmId) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 400));
  
  const farmYieldData = mockYieldData.find(item => item.farmId === farmId);
  
  if (!farmYieldData) {
    return { farmId, yields: [] };
  }
  
  return { ...farmYieldData };
};

// Add yield data
export const addYieldData = async (farmId, yearData) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 600));
  
  const farmYieldDataIndex = mockYieldData.findIndex(item => item.farmId === farmId);
  
  if (farmYieldDataIndex === -1) {
    // Create new entry for farm
    mockYieldData.push({
      farmId,
      yields: [yearData]
    });
  } else {
    // Check if year already exists
    const yearIndex = mockYieldData[farmYieldDataIndex].yields.findIndex(y => y.year === yearData.year);
    
    if (yearIndex === -1) {
      // Add new year data
      mockYieldData[farmYieldDataIndex].yields.push(yearData);
    } else {
      // Update existing year data
      mockYieldData[farmYieldDataIndex].yields[yearIndex] = yearData;
    }
  }
  
  return yearData;
};
