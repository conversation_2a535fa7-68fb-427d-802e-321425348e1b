/**
 * Azure Search Service
 * 
 * This service provides functionality to search for agricultural data using Azure Cognitive Search.
 */

import axios from 'axios';

const API_URL = 'http://localhost:8000/api';

/**
 * Search for agricultural data
 * @param {string} query - The search query
 * @param {string} state - The state to filter results by
 * @param {string} crop - The crop to filter results by
 * @param {number} limit - The maximum number of results to return
 * @returns {Promise<Array>} - The search results
 */
export const searchAgriculturalData = async (query, state = null, crop = null, limit = 10) => {
  try {
    let url = `${API_URL}/external/azure-search?query=${encodeURIComponent(query)}`;
    
    if (state) {
      url += `&state=${encodeURIComponent(state)}`;
    }
    
    if (crop) {
      url += `&crop=${encodeURIComponent(crop)}`;
    }
    
    if (limit) {
      url += `&limit=${limit}`;
    }
    
    const response = await axios.get(url);
    
    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to search agricultural data');
    }
  } catch (error) {
    console.error('Error searching agricultural data:', error);
    throw error;
  }
};

/**
 * Get available states for filtering
 * @returns {Promise<Array>} - The available states
 */
export const getAvailableStates = async () => {
  try {
    const response = await axios.get(`${API_URL}/external/azure-search/states`);
    
    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to get available states');
    }
  } catch (error) {
    console.error('Error getting available states:', error);
    throw error;
  }
};

/**
 * Get available crops for filtering
 * @returns {Promise<Array>} - The available crops
 */
export const getAvailableCrops = async () => {
  try {
    const response = await axios.get(`${API_URL}/external/azure-search/crops`);
    
    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to get available crops');
    }
  } catch (error) {
    console.error('Error getting available crops:', error);
    throw error;
  }
};
