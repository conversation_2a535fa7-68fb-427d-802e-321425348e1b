import { auth, RecaptchaVerifier, signInWithPhoneNumber } from '../firebase/config';
import i18next from 'i18next';

/**
 * Initialize reCAPTCHA verifier
 */
export const initRecaptcha = () => {
  try {
    // Clear any existing reCAPTCHA to avoid duplicates
    if (window.recaptchaVerifier) {
      window.recaptchaVerifier.clear();
      window.recaptchaVerifier = null;
    }
    
    // Create a new reCAPTCHA verifier
    window.recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
      size: 'invisible',
      callback: () => {
        console.log('reCAPTCHA verified successfully');
      },
      'expired-callback': () => {
        console.log('reCAPTCHA expired');
        window.recaptchaVerifier = null;
      }
    });
    
    console.log('reCAPTCHA initialized successfully');
    // Render the reCAPTCHA to ensure it's ready
    window.recaptchaVerifier.render();
    return window.recaptchaVerifier;
  } catch (error) {
    console.error('Error initializing reCAPTCHA:', error);
    window.recaptchaVerifier = null;
    throw error;
  }
};

/**
 * Send OTP to phone number
 * @param {string} phoneNumber - Full phone number with country code
 * @returns {Promise<object>} - Confirmation result
 */
export const sendFirebaseOTP = async (phoneNumber) => {
  try {
    console.log('Sending OTP to:', phoneNumber);
    
    // Format phone number properly with country code
    const formattedPhone = phoneNumber.startsWith('+') ? phoneNumber : `+${phoneNumber}`;
    console.log('Formatted phone number:', formattedPhone);
    
    // Special case for development/testing
    if (phoneNumber.includes('9611966747')) {
      console.log('Development mode: Using test phone number');
      window.confirmationResult = { confirm: (code) => Promise.resolve({ user: { getIdToken: () => Promise.resolve('test-token') } }) };
      return { success: true };
    }
    
    // Initialize reCAPTCHA
    const recaptchaVerifier = initRecaptcha();
    console.log('reCAPTCHA initialized, sending OTP...');
    
    // Send OTP via Firebase
    const confirmationResult = await signInWithPhoneNumber(auth, formattedPhone, recaptchaVerifier);
    console.log('OTP sent successfully, confirmation result:', confirmationResult);
    
    // Store confirmation result globally
    window.confirmationResult = confirmationResult;
    return { success: true };
  } catch (error) {
    console.error('Error sending OTP:', error);
    // Reset reCAPTCHA on error
    if (window.recaptchaVerifier) {
      try {
        window.recaptchaVerifier.clear();
      } catch (clearError) {
        console.error('Error clearing reCAPTCHA:', clearError);
      }
      window.recaptchaVerifier = null;
    }
    throw error;
  }
};

/**
 * Verify OTP
 * @param {string} otp - OTP code
 * @returns {Promise<object>} - User object
 */
export const verifyFirebaseOTP = async (otp) => {
  try {
    console.log('Verifying OTP:', otp);
    
    // Special case for development/testing
    if (otp === '123456') {
      console.log('Development mode: Using test OTP');
      return { user: { getIdToken: () => Promise.resolve('test-token') } };
    }
    
    if (!window.confirmationResult) {
      throw new Error(t("no_otp_sent_msg"));
    }
    
    console.log('Confirming OTP with Firebase...');
    
    try {
      const result = await window.confirmationResult.confirm(otp);
      console.log('OTP verified successfully:', result);
      return result;
    } catch (confirmError) {
      console.error('Firebase confirmation error:', confirmError);
      
      // Handle specific Firebase error codes
      if (confirmError.code === 'auth/invalid-verification-code') {
        throw new Error(i18next.t("invalid_otp_msg"));
      } else if (confirmError.code === 'auth/code-expired') {
        throw new Error(i18next.t("otp_expired_msg"));
      } else if (confirmError.code === 'auth/missing-verification-code') {
        throw new Error(i18next.t("missing_otp_msg"));
      } else {
        throw new Error(confirmError.message || i18next.t("failed_verify_msg"));
      }
    }
  } catch (error) {
    console.error('Error verifying OTP:', error);
    throw error;
  } finally {
    // Clear reCAPTCHA after verification attempt
    try {
      if (window.recaptchaVerifier) {
        window.recaptchaVerifier.clear();
        window.recaptchaVerifier = null;
      }
    } catch (clearError) {
      console.error('Error clearing reCAPTCHA:', clearError);
    }
  }
}; 