import axios from 'axios';
import { API_BASE_URL } from '../config/api';

// Get financial data for a specific farmer
export const getFarmerFinancialData = async (farmerId) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/financial/farmer/${farmerId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching financial data:', error);
    throw error;
  }
};

// Get yield history with filters
export const getYieldHistory = async (farmerId, filters = {}) => {
  try {
    const { startYear, endYear, crop } = filters;
    let url = `${API_BASE_URL}/financial/yield-history?farmerId=${farmerId}`;

    if (startYear) url += `&startYear=${startYear}`;
    if (endYear) url += `&endYear=${endYear}`;
    if (crop) url += `&crop=${crop}`;

    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching yield history:', error);
    throw error;
  }
};

// Get payment history with filters
export const getPaymentHistory = async (farmerId, filters = {}) => {
  try {
    const { startDate, endDate, status } = filters;
    let url = `${API_BASE_URL}/financial/payment-history?farmerId=${farmerId}`;

    if (startDate) url += `&startDate=${startDate}`;
    if (endDate) url += `&endDate=${endDate}`;
    if (status) url += `&status=${status}`;

    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching payment history:', error);
    throw error;
  }
};

// Get loan details with filters
export const getLoanDetails = async (farmerId, filters = {}) => {
  try {
    const { status, bankName } = filters;
    let url = `${API_BASE_URL}/financial/loans?farmerId=${farmerId}`;

    if (status) url += `&status=${status}`;
    if (bankName) url += `&bankName=${bankName}`;

    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching loan details:', error);
    throw error;
  }
};

// Generate financial report
export const generateFinancialReport = async (farmerId, filters = {}) => {
  try {
    const { startDate, endDate, reportType = 'comprehensive' } = filters;
    let url = `${API_BASE_URL}/financial/farmer/${farmerId}/report?reportType=${reportType}`;

    if (startDate) url += `&startDate=${startDate}`;
    if (endDate) url += `&endDate=${endDate}`;

    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Error generating financial report:', error);
    throw error;
  }
};
