/**
 * External Data Service
 *
 * This service provides functions to fetch agricultural data from external sources
 * using Quamin AI Search and Computer Vision.
 */

// Import environment variables
import { 
  VITE_AZURE_OPENAI_ENDPOINT, 
  VITE_AZURE_OPENAI_API_KEY, 
  VITE_AZURE_DEPLOYMENT_NAME,
  VITE_AZURE_API_VERSION 
} from '../config/environment';

// Base URL for external data API
const API_BASE_URL = '/api/external';

/**
 * Search for agricultural data across external sources
 * @param {Object} params - Search parameters
 * @returns {Promise<Object>} - Search results
 */
export const searchExternalData = async (params = {}) => {
  try {
    const { query, category, region, limit = 10, offset = 0 } = params;

    // Build query string
    const queryParams = new URLSearchParams();
    if (query) queryParams.append('query', query);
    if (category) queryParams.append('category', category);
    if (region) queryParams.append('region', region);
    if (limit) queryParams.append('limit', limit);
    if (offset) queryParams.append('offset', offset);

    try {
      const response = await fetch(`${API_BASE_URL}/search?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`Failed to search external data: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // If we have results, return them
      if (data.success && data.data && data.data.results && data.data.results.length > 0) {
        return data;
      }

      // Otherwise, fall back to mock data
      throw new Error('No results found from API');
    } catch (apiError) {
      console.warn('API search failed, using mock data:', apiError);

      // Generate mock data based on the query
      return generateMockExternalData(query, category, region, limit);
    }
  } catch (error) {
    console.error('Error searching external data:', error);
    return {
      success: false,
      message: error.message,
      data: { results: [] }
    };
  }
};

/**
 * Generate mock external data for search results
 * @param {string} query - Search query
 * @param {string} category - Category filter
 * @param {string} region - Region filter
 * @param {number} limit - Result limit
 * @returns {Object} - Mock search results
 */
const generateMockExternalData = (query, category, region, limit) => {
  console.log('Generating mock external data for:', query);

  // Define some mock crop data
  const mockCrops = [
    {
      id: 'grain-001-pb',
      name: 'Rice',
      category: 'Grains',
      description: 'Premium basmati rice known for its aroma and long grains.',
      price: 48.50,
      demandQuantity: 125000,
      supplyQuantity: 118000,
      region: 'Punjab',
      season: 'Kharif',
      source: 'Quamin Agricultural Market Intelligence',
      lastUpdated: '2023-12-15',
      exportQuantity: 45000,
      domesticConsumption: 73000,
      yieldPerHectare: '4.0 tonnes',
      totalCultivationArea: '3.1 million hectares',
      majorVarieties: 'Pusa Basmati 1121, Punjab Basmati 3, Pusa Basmati 1509',
      nutritionalValue: 'Rich in carbohydrates, contains essential amino acids',
      marketTrend: 'Rising',
      priceProjection: 50.90,
      demandSupplyRatio: 1.06
    },
    {
      id: 'grain-002-hr',
      name: 'Wheat',
      category: 'Grains',
      description: 'High-quality wheat with excellent protein content for bread and chapati making.',
      price: 32.75,
      demandQuantity: 142000,
      supplyQuantity: 138000,
      region: 'Haryana',
      season: 'Rabi',
      source: 'Quamin Agricultural Market Intelligence',
      lastUpdated: '2023-12-10',
      exportQuantity: 28000,
      domesticConsumption: 110000,
      yieldPerHectare: '3.8 tonnes',
      totalCultivationArea: '2.8 million hectares',
      majorVarieties: 'HD-2967, HD-3086, DBW-17',
      nutritionalValue: 'Rich in protein, fiber, and complex carbohydrates',
      marketTrend: 'Stable',
      priceProjection: 32.75,
      demandSupplyRatio: 1.03
    },
    {
      id: 'veg-001-mh',
      name: 'Tomato',
      category: 'Vegetables',
      description: 'Fresh, ripe tomatoes with excellent color and flavor.',
      price: 25.40,
      demandQuantity: 85000,
      supplyQuantity: 78000,
      region: 'Maharashtra',
      season: 'Year-round',
      source: 'Quamin Agricultural Market Intelligence',
      lastUpdated: '2023-12-12',
      exportQuantity: 12000,
      domesticConsumption: 66000,
      yieldPerHectare: '25 tonnes',
      totalCultivationArea: '0.8 million hectares',
      majorVarieties: 'Pusa Ruby, Arka Vikas, Pusa Hybrid-1',
      nutritionalValue: 'Rich in vitamin C, potassium, and lycopene',
      marketTrend: 'Rising',
      priceProjection: 26.70,
      demandSupplyRatio: 1.09
    },
    {
      id: 'veg-002-kr',
      name: 'Potato',
      category: 'Vegetables',
      description: 'High-quality potatoes suitable for various culinary applications.',
      price: 18.90,
      demandQuantity: 110000,
      supplyQuantity: 105000,
      region: 'Karnataka',
      season: 'Rabi',
      source: 'Quamin Agricultural Market Intelligence',
      lastUpdated: '2023-12-08',
      exportQuantity: 8000,
      domesticConsumption: 97000,
      yieldPerHectare: '22 tonnes',
      totalCultivationArea: '1.2 million hectares',
      majorVarieties: 'Kufri Jyoti, Kufri Bahar, Kufri Sindhuri',
      nutritionalValue: 'Good source of vitamin C, potassium, and fiber',
      marketTrend: 'Stable',
      priceProjection: 18.90,
      demandSupplyRatio: 1.05
    },
    {
      id: 'pulse-001-mp',
      name: 'Chickpea',
      category: 'Pulses',
      description: 'Nutritious chickpeas with excellent cooking quality.',
      price: 65.30,
      demandQuantity: 75000,
      supplyQuantity: 68000,
      region: 'Madhya Pradesh',
      season: 'Rabi',
      source: 'Quamin Agricultural Market Intelligence',
      lastUpdated: '2023-12-05',
      exportQuantity: 15000,
      domesticConsumption: 53000,
      yieldPerHectare: '1.2 tonnes',
      totalCultivationArea: '2.5 million hectares',
      majorVarieties: 'JG-11, JAKI 9218, Vijay',
      nutritionalValue: 'High in protein, fiber, and essential minerals',
      marketTrend: 'Rising',
      priceProjection: 68.60,
      demandSupplyRatio: 1.10
    },
    {
      id: 'oil-001-gj',
      name: 'Groundnut',
      category: 'Oilseeds',
      description: 'Premium quality groundnuts with high oil content.',
      price: 78.20,
      demandQuantity: 65000,
      supplyQuantity: 60000,
      region: 'Gujarat',
      season: 'Kharif',
      source: 'Quamin Agricultural Market Intelligence',
      lastUpdated: '2023-12-07',
      exportQuantity: 18000,
      domesticConsumption: 42000,
      yieldPerHectare: '1.8 tonnes',
      totalCultivationArea: '1.5 million hectares',
      majorVarieties: 'GG-20, TG-37A, GJG-22',
      nutritionalValue: 'Rich in healthy fats, protein, and vitamin E',
      marketTrend: 'Rising',
      priceProjection: 82.10,
      demandSupplyRatio: 1.08
    },
    {
      id: 'oil-002-mp',
      name: 'Soybean',
      category: 'Oilseeds',
      description: 'High-quality soybeans with excellent protein content.',
      price: 42.80,
      demandQuantity: 95000,
      supplyQuantity: 88000,
      region: 'Madhya Pradesh',
      season: 'Kharif',
      source: 'Quamin Agricultural Market Intelligence',
      lastUpdated: '2023-12-09',
      exportQuantity: 25000,
      domesticConsumption: 63000,
      yieldPerHectare: '1.5 tonnes',
      totalCultivationArea: '5.2 million hectares',
      majorVarieties: 'JS-335, JS-9560, NRC-37',
      nutritionalValue: 'High in protein, fiber, and omega-3 fatty acids',
      marketTrend: 'Stable',
      priceProjection: 42.80,
      demandSupplyRatio: 1.08
    },
    {
      id: 'fruit-001-mh',
      name: 'Mango',
      category: 'Fruits',
      description: 'Sweet and flavorful mangoes from premium varieties.',
      price: 85.60,
      demandQuantity: 55000,
      supplyQuantity: 48000,
      region: 'Maharashtra',
      season: 'Summer',
      source: 'Quamin Agricultural Market Intelligence',
      lastUpdated: '2023-12-11',
      exportQuantity: 15000,
      domesticConsumption: 33000,
      yieldPerHectare: '8 tonnes',
      totalCultivationArea: '0.9 million hectares',
      majorVarieties: 'Alphonso, Kesar, Dasheri',
      nutritionalValue: 'Rich in vitamins A and C, and antioxidants',
      marketTrend: 'Rising',
      priceProjection: 89.90,
      demandSupplyRatio: 1.15
    }
  ];

  // Filter based on query
  let filteredResults = mockCrops;

  if (query) {
    const lowerQuery = query.toLowerCase();
    filteredResults = filteredResults.filter(item =>
      item.name.toLowerCase().includes(lowerQuery) ||
      item.description.toLowerCase().includes(lowerQuery) ||
      item.category.toLowerCase().includes(lowerQuery)
    );
  }

  // Filter by category if provided
  if (category) {
    filteredResults = filteredResults.filter(item =>
      item.category.toLowerCase() === category.toLowerCase()
    );
  }

  // Filter by region if provided
  if (region && region !== 'all') {
    filteredResults = filteredResults.filter(item =>
      item.region.toLowerCase() === region.toLowerCase()
    );
  }

  // Limit results
  const limitedResults = filteredResults.slice(0, limit);

  return {
    success: true,
    message: 'Mock data from Quamin AI Search',
    data: {
      results: limitedResults,
      count: limitedResults.length
    }
  };
};

/**
 * Get market trends and forecasts for a specific crop
 * @param {Object} params - Forecast parameters
 * @returns {Promise<Object>} - Forecast data
 */
export const getMarketForecast = async (params = {}) => {
  try {
    const { crop, region, timeframe = '30d' } = params;

    // Build query string
    const queryParams = new URLSearchParams();
    if (crop) queryParams.append('crop', crop);
    if (region) queryParams.append('region', region);
    if (timeframe) queryParams.append('timeframe', timeframe);

    const response = await fetch(`${API_BASE_URL}/forecast?${queryParams.toString()}`);

    if (!response.ok) {
      throw new Error(`Failed to get market forecast: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting market forecast:', error);
    return {
      success: false,
      message: error.message,
      data: []
    };
  }
};

/**
 * Get current market data for a specific crop
 * @param {Object} params - Market data parameters
 * @returns {Promise<Object>} - Market data
 */
export const getMarketData = async (params = {}) => {
  try {
    const { crop, region, limit = 10 } = params;

    // Build query string
    const queryParams = new URLSearchParams();
    if (crop) queryParams.append('crop', crop);
    if (region) queryParams.append('region', region);
    if (limit) queryParams.append('limit', limit);

    const response = await fetch(`${API_BASE_URL}/data?${queryParams.toString()}`);

    if (!response.ok) {
      throw new Error(`Failed to get market data: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting market data:', error);
    return {
      success: false,
      message: error.message,
      data: []
    };
  }
};

/**
 * Analyze crop image using Quamin Computer Vision
 * @param {File} imageFile - The image file to analyze
 * @returns {Promise<Object>} - Analysis results
 */
export const analyzeCropImage = async (imageFile) => {
  try {
    const formData = new FormData();
    formData.append('image', imageFile);

    // Try to use the API endpoint
    try {
      const response = await fetch(`/api/computer-vision/analyze-crop`, {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        return await response.json();
      }
      
      throw new Error('API endpoint failed. Trying Azure OpenAI directly.');
    } catch (apiError) {
      console.warn('API endpoint failed, trying Azure OpenAI directly:', apiError);
    }

    // Create object URL for the image file
    const imageUrl = URL.createObjectURL(imageFile);

    // If API call fails, try Azure OpenAI directly
    if (VITE_AZURE_OPENAI_API_KEY && VITE_AZURE_OPENAI_ENDPOINT) {
      console.log('Using Azure OpenAI API for crop analysis');
      
      try {
        // Convert image to base64
        const imageBase64 = await convertFileToBase64(imageFile);
        
        // Prepare the API request - ensure proper URL format
        let baseUrl = VITE_AZURE_OPENAI_ENDPOINT;
        
        // Remove trailing slash if present
        baseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
        
        // Remove any existing path if present (safeguard)
        if (baseUrl.includes('/openai/deployments')) {
          baseUrl = baseUrl.split('/openai/deployments')[0];
        }
        
        const apiUrl = `${baseUrl}/openai/deployments/${VITE_AZURE_DEPLOYMENT_NAME}/chat/completions?api-version=${VITE_AZURE_API_VERSION}`;
        
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'api-key': VITE_AZURE_OPENAI_API_KEY
          },
          body: JSON.stringify({
            messages: [
              {
                role: 'system',
                content: 'You are an expert agricultural analyst with deep knowledge of crop identification, health assessment, and growth analysis. Provide detailed, professional insights based on the crop image.'
              },
              {
                role: 'user',
                content: [
                  {
                    type: 'text',
                    text: 'Please analyze this crop image and provide a detailed assessment including: crop type identification, health status, growth stage, estimated yield, and recommendations for optimal growth.'
                  },
                  {
                    type: 'image_url',
                    image_url: { url: `data:image/jpeg;base64,${imageBase64}` }
                  }
                ]
              }
            ],
            temperature: 0.3,
            max_tokens: 1000
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('Azure OpenAI API error:', errorData);
          throw new Error(`Azure OpenAI API error: ${errorData.error?.message || 'Unknown error'}`);
        }

        const data = await response.json();
        
        // Extract the response content
        const content = data.choices[0]?.message?.content;
        if (!content) {
          throw new Error('No content in API response');
        }

        // Parse the AI response to extract structured data
        const analysisResult = parseAIAnalysisResponse(content);
        
        return {
          success: true,
          message: 'Analysis completed using Azure OpenAI',
          data: {
            imageUrl,
            ...analysisResult
          }
        };
      } catch (azureError) {
        console.error('Error using Azure OpenAI API:', azureError);
        // Don't fall back to mock data, instead throw the error
        throw new Error(`Failed to analyze image: ${azureError.message}`);
      }
    } else {
      // Don't use mock data, throw error if API credentials are missing
      throw new Error('Azure OpenAI API credentials not found. Please check your configuration.');
    }
  } catch (error) {
    console.error('Error analyzing crop image:', error);
    return {
      success: false,
      message: error.message,
      data: null
    };
  }
};

/**
 * Convert File object to base64 string
 * @param {File} file - The file to convert
 * @returns {Promise<string>} - Base64 encoded string
 */
const convertFileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      const base64String = reader.result.split(',')[1];
      resolve(base64String);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

/**
 * Parse the AI analysis response into structured data
 * @param {string} content - The AI response text
 * @returns {Object} - Structured analysis data
 */
const parseAIAnalysisResponse = (content) => {
  try {
    // Initialize with default structure
    const result = {
      tags: [],
      description: '',
      confidence: 0.8,
      objects: [],
      result: {
        identifiedAs: '',
        confidence: 0.8,
        healthStatus: '',
        growthStage: '',
        estimatedYield: '',
        recommendations: []
      }
    };

    // Extract crop type
    const cropTypeMatch = content.match(/(?:crop type|identified as)[:\s]+([A-Za-z]+)/i);
    if (cropTypeMatch && cropTypeMatch[1]) {
      result.result.identifiedAs = cropTypeMatch[1];
      result.tags.push({ name: cropTypeMatch[1], confidence: 0.9 });
    }

    // Extract health status
    const healthMatch = content.match(/(?:health status|health)[:\s]+(excellent|good|fair|poor)/i);
    if (healthMatch && healthMatch[1]) {
      result.result.healthStatus = healthMatch[1].charAt(0).toUpperCase() + healthMatch[1].slice(1);
    }

    // Extract growth stage
    const growthMatch = content.match(/(?:growth stage|stage)[:\s]+(seedling|vegetative|flowering|fruiting|mature)/i);
    if (growthMatch && growthMatch[1]) {
      result.result.growthStage = growthMatch[1].charAt(0).toUpperCase() + growthMatch[1].slice(1);
    }

    // Extract yield estimate
    const yieldMatch = content.match(/(?:estimated yield|yield)[:\s]+([0-9.-]+\s*(?:quintals|tons|tonnes)\/(?:acre|hectare))/i);
    if (yieldMatch && yieldMatch[1]) {
      result.result.estimatedYield = yieldMatch[1];
    }

    // Extract recommendations (assuming they're in a list format)
    const recommendationsMatch = content.match(/recommendations?:?(?:\s*\n?)((?:[-*•]\s*[^\n]+\n?)+)/i);
    if (recommendationsMatch && recommendationsMatch[1]) {
      result.result.recommendations = recommendationsMatch[1]
        .split(/[-*•]/)
        .map(item => item.trim())
        .filter(item => item.length > 0);
    }

    // Set description
    result.description = content.slice(0, 100) + '...';

    // Add generic agriculture tags
    result.tags.push({ name: 'Agriculture', confidence: 0.95 });
    result.tags.push({ name: 'Plant', confidence: 0.98 });
    
    return result;
  } catch (error) {
    console.error('Error parsing AI analysis response:', error);
    return {
      tags: [{ name: 'Plant', confidence: 0.9 }],
      description: 'Analysis completed',
      confidence: 0.8,
      objects: [],
      result: {
        identifiedAs: 'Crop',
        confidence: 0.8,
        healthStatus: 'Unknown',
        growthStage: 'Unknown',
        estimatedYield: 'Unknown',
        recommendations: ['Consult with an agricultural expert']
      }
    };
  }
};

/**
 * Get random health status for mock data
 * @returns {string} - Random health status
 */
const getRandomHealthStatus = () => {
  const statuses = ['Excellent', 'Good', 'Fair', 'Poor'];
  const weights = [0.3, 0.4, 0.2, 0.1]; // Weighted probabilities

  let random = Math.random();
  let cumulativeWeight = 0;

  for (let i = 0; i < statuses.length; i++) {
    cumulativeWeight += weights[i];
    if (random <= cumulativeWeight) {
      return statuses[i];
    }
  }

  return statuses[0];
};

/**
 * Get random growth stage for mock data
 * @returns {string} - Random growth stage
 */
const getRandomGrowthStage = () => {
  const stages = ['Seedling', 'Vegetative', 'Flowering', 'Fruiting', 'Mature'];
  return stages[Math.floor(Math.random() * stages.length)];
};

/**
 * Get random yield estimate for mock data
 * @returns {string} - Random yield estimate
 */
const getRandomYield = () => {
  const baseYield = 20 + Math.floor(Math.random() * 10);
  return `${baseYield}-${baseYield + 5} quintals/acre`;
};

/**
 * Get recommendations based on crop type
 * @param {string} cropType - Type of crop
 * @returns {Array<string>} - List of recommendations
 */
const getRecommendations = (cropType) => {
  const commonRecommendations = [
    'Maintain proper irrigation schedule',
    'Monitor for pest infestations regularly',
    'Ensure adequate nutrient supply'
  ];

  const cropSpecificRecommendations = {
    'Rice': [
      'Maintain water level of 5-7 cm during vegetative stage',
      'Apply nitrogen fertilizer in split doses',
      'Monitor for rice blast and bacterial leaf blight'
    ],
    'Wheat': [
      'Ensure timely irrigation at critical growth stages',
      'Apply potassium for better grain filling',
      'Monitor for rust and powdery mildew'
    ],
    'Maize': [
      'Ensure adequate soil moisture during tasseling and silking',
      'Apply balanced NPK fertilizer',
      'Monitor for fall armyworm and stem borer'
    ],
    'Potato': [
      'Maintain consistent soil moisture',
      'Hill up soil around plants as they grow',
      'Monitor for late blight and early blight'
    ],
    'Tomato': [
      'Provide support for plants as they grow',
      'Prune suckers for indeterminate varieties',
      'Monitor for early blight and tomato hornworm'
    ],
    'Cotton': [
      'Apply growth regulators at appropriate stages',
      'Monitor for bollworms and whiteflies',
      'Ensure adequate potassium for fiber development'
    ],
    'Sugarcane': [
      'Maintain proper row spacing',
      'Apply nitrogen in split doses',
      'Monitor for red rot and smut'
    ]
  };

  // Return crop-specific recommendations if available, otherwise common recommendations
  return cropType && cropSpecificRecommendations[cropType]
    ? [...cropSpecificRecommendations[cropType], ...commonRecommendations]
    : commonRecommendations;
};

/**
 * Get news and articles related to agriculture
 * @param {Object} params - Search parameters
 * @returns {Promise<Object>} - News and articles
 */
export const getAgricultureNews = async (params = {}) => {
  try {
    const { query, category, limit = 5 } = params;

    // Build query string
    const queryParams = new URLSearchParams();
    if (query) queryParams.append('query', query);
    if (category) queryParams.append('category', category);
    if (limit) queryParams.append('limit', limit);

    const response = await fetch(`${API_BASE_URL}/news?${queryParams.toString()}`);

    if (!response.ok) {
      throw new Error(`Failed to get agriculture news: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting agriculture news:', error);
    return {
      success: false,
      message: error.message,
      data: []
    };
  }
};

/**
 * Get government schemes and subsidies for agriculture
 * @param {Object} params - Search parameters
 * @returns {Promise<Object>} - Schemes and subsidies
 */
export const getGovernmentSchemes = async (params = {}) => {
  try {
    const { state, cropType, limit = 10 } = params;

    // Build query string
    const queryParams = new URLSearchParams();
    if (state) queryParams.append('state', state);
    if (cropType) queryParams.append('cropType', cropType);
    if (limit) queryParams.append('limit', limit);

    const response = await fetch(`${API_BASE_URL}/schemes?${queryParams.toString()}`);

    if (!response.ok) {
      throw new Error(`Failed to get government schemes: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting government schemes:', error);
    return {
      success: false,
      message: error.message,
      data: []
    };
  }
};
