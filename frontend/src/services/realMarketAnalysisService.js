/**
 * Real Market Analysis Service
 *
 * This service provides functions to fetch real market data from the backend API.
 */

import axios from 'axios';
import { API_BASE_URL } from '../config/api';

/**
 * Get market overview data
 * @returns {Promise<Object>} Market overview data
 */
export const getMarketOverview = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/real-market/overview`);
    
    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error('Invalid response from server');
  } catch (error) {
    console.error('Error fetching market overview:', error);
    throw error;
  }
};

/**
 * Get available states
 * @returns {Promise<Array>} List of states
 */
export const getAvailableStates = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/real-market/states`);
    
    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error('Invalid response from server');
  } catch (error) {
    console.error('Error fetching available states:', error);
    throw error;
  }
};

/**
 * Get mandis for a specific state
 * @param {string} state - The state name
 * @returns {Promise<Array>} List of mandis
 */
export const getMandisByState = async (state) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/real-market/mandis`, {
      params: { state }
    });
    
    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error('Invalid response from server');
  } catch (error) {
    console.error(`Error fetching mandis for ${state}:`, error);
    throw error;
  }
};

/**
 * Get available crops
 * @returns {Promise<Array>} List of crops
 */
export const getAvailableCrops = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/real-market/crops`);
    
    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error('Invalid response from server');
  } catch (error) {
    console.error('Error fetching available crops:', error);
    throw error;
  }
};

/**
 * Get market data for a specific crop, state, and mandi
 * @param {string} crop - The crop name
 * @param {string} state - The state name
 * @param {string} mandi - The mandi name
 * @returns {Promise<Object>} Market data
 */
export const getMarketData = async (crop, state, mandi) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/real-market/prices`, {
      params: { crop, state, mandi }
    });
    
    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error('Invalid response from server');
  } catch (error) {
    console.error(`Error fetching market data for ${crop} in ${mandi}, ${state}:`, error);
    throw error;
  }
};
