const mongoose = require('mongoose');

const soilDataSchema = new mongoose.Schema({
  location: {
    coordinates: {
      lat: { type: Number, required: true },
      lng: { type: Number, required: true }
    },
    state: String,
    district: String,
    block: String,
    village: String,
    plot: String,
    level: {
      type: String,
      enum: ['coordinates', 'state', 'district', 'block', 'village', 'plot'],
      required: true
    }
  },
  dataSource: {
    type: String,
    enum: ['satellite', 'iot', 'lab_testing', 'manual'],
    required: true
  },
  soilData: {
    // Primary Macronutrients (NPK)
    nitrogen: { type: Number, min: 0 }, // ppm or mg/kg
    phosphorus: { type: Number, min: 0 }, // ppm or mg/kg
    potassium: { type: Number, min: 0 }, // ppm or mg/kg

    // Secondary Macronutrients
    calcium: { type: Number, min: 0 }, // ppm or mg/kg
    magnesium: { type: Number, min: 0 }, // ppm or mg/kg
    sulfur: { type: Number, min: 0 }, // ppm or mg/kg

    // Essential Micronutrients
    iron: { type: Number, min: 0 }, // ppm or mg/kg
    manganese: { type: Number, min: 0 }, // ppm or mg/kg
    zinc: { type: Number, min: 0 }, // ppm or mg/kg
    copper: { type: Number, min: 0 }, // ppm or mg/kg
    boron: { type: Number, min: 0 }, // ppm or mg/kg
    molybdenum: { type: Number, min: 0 }, // ppm or mg/kg
    chlorine: { type: Number, min: 0 }, // ppm or mg/kg
    nickel: { type: Number, min: 0 }, // ppm or mg/kg

    // Soil Physical Properties
    ph: { type: Number, min: 0, max: 14 }, // pH units
    organicMatter: { type: Number, min: 0, max: 100 }, // percentage
    organicCarbon: { type: Number, min: 0 }, // percentage
    cationExchangeCapacity: { type: Number, min: 0 }, // meq/100g
    electricalConductivity: { type: Number, min: 0 }, // dS/m

    // Soil Texture
    sand: { type: Number, min: 0, max: 100 }, // percentage
    clay: { type: Number, min: 0, max: 100 }, // percentage
    silt: { type: Number, min: 0, max: 100 }, // percentage

    // Environmental Conditions
    moisture: { type: Number, min: 0, max: 100 }, // percentage
    temperature: { type: Number }, // °C - air temperature
    soilTemperature: { type: Number }, // °C - soil temperature
    precipitation: { type: Number, min: 0 }, // mm/day
    humidity: { type: Number, min: 0, max: 100 }, // percentage

    // Additional Properties
    bulkDensity: { type: Number, min: 0 }, // g/cm³
    porosity: { type: Number, min: 0, max: 100 }, // percentage
    waterHoldingCapacity: { type: Number, min: 0 }, // percentage
    infiltrationRate: { type: Number, min: 0 }, // mm/hr

    // Biological Properties
    microbialBiomass: { type: Number, min: 0 }, // mg/kg
    enzymeActivity: { type: Number, min: 0 }, // various units
    earthwormCount: { type: Number, min: 0 }, // count per m²

    // Additional Lab Parameters
    availableWater: { type: Number, min: 0 }, // percentage
    fieldCapacity: { type: Number, min: 0 }, // percentage
    wiltingPoint: { type: Number, min: 0 }, // percentage

    // Data source indicators
    weatherCondition: String,
    moistureLevel: String,
    isRealTimeWeather: { type: Boolean, default: false },
    lastUpdated: { type: Date, default: Date.now },
    dataSource: String
  },
  labInfo: {
    labId: String,
    analyst: String,
    testDate: Date,
    method: String,
    accuracy: Number
  },
  iotInfo: {
    sensorId: String,
    deviceId: String,
    readingTime: Date,
    batteryLevel: Number,
    signalStrength: Number
  },
  dataQuality: {
    type: String,
    enum: ['high', 'medium', 'low'],
    default: 'high'
  },
  interpretation: {
    // Primary Macronutrients
    nitrogen: { level: String, description: String, recommendation: String },
    phosphorus: { level: String, description: String, recommendation: String },
    potassium: { level: String, description: String, recommendation: String },

    // Secondary Macronutrients
    calcium: { level: String, description: String, recommendation: String },
    magnesium: { level: String, description: String, recommendation: String },
    sulfur: { level: String, description: String, recommendation: String },

    // Essential Micronutrients
    iron: { level: String, description: String, recommendation: String },
    manganese: { level: String, description: String, recommendation: String },
    zinc: { level: String, description: String, recommendation: String },
    copper: { level: String, description: String, recommendation: String },
    boron: { level: String, description: String, recommendation: String },
    molybdenum: { level: String, description: String, recommendation: String },

    // Soil Properties
    ph: { level: String, description: String, recommendation: String },
    organicMatter: { level: String, description: String, recommendation: String },
    moisture: { level: String, description: String, recommendation: String },
    electricalConductivity: { level: String, description: String, recommendation: String },
    cationExchangeCapacity: { level: String, description: String, recommendation: String }
  },
  recommendations: [{
    category: String,
    description: String,
    priority: String,
    implementation: String
  }],
  suitableCrops: [{
    name: String,
    suitability: String,
    reason: String,
    yield: String
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  verificationDate: Date,
  notes: String,
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for efficient querying
soilDataSchema.index({ 'location.coordinates': '2dsphere' });
soilDataSchema.index({ 'location.state': 1, 'location.district': 1 });
soilDataSchema.index({ createdAt: -1 });
soilDataSchema.index({ dataSource: 1 });
soilDataSchema.index({ createdBy: 1 });

// Pre-save middleware to update updatedAt
soilDataSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

module.exports = mongoose.model('SoilData', soilDataSchema);

