import axios from 'axios';

/**
 * TranslationPreloader - Preloads common translations in the background
 * to improve user experience by reducing translation delays
 */
class TranslationPreloader {
  constructor() {
    this.isPreloading = false;
    this.preloadQueue = [];
    this.commonTexts = {
      // Dashboard common texts
      dashboard: {
        weather: [
          'Temperature',
          'Humidity',
          'Wind Speed',
          'Precipitation',
          'Weather Forecast',
          'Today\'s Weather',
          'Weekly Forecast'
        ],
        soil: [
          'Soil Analysis',
          'pH Level',
          'Nitrogen',
          'Phosphorus',
          'Potassium',
          'Organic Matter',
          'Soil Health'
        ],
        crops: [
          'Crop Health',
          'Growth Stage',
          'Yield Prediction',
          'Disease Detection',
          'Pest Control',
          'Fertilization',
          'Irrigation'
        ],
        alerts: [
          'High Priority',
          'Medium Priority',
          'Low Priority',
          'Weather Alert',
          'Disease Alert',
          'Pest Alert',
          'Maintenance Due'
        ],
        schedule: [
          'Today\'s Tasks',
          'Upcoming Tasks',
          'Completed Tasks',
          'Fertilization',
          'Irrigation',
          'Pest Control',
          'Harvest'
        ]
      },
      // Chatbot common texts
      chatbot: {
        analysis: [
          'Analysis Summary',
          'Health Status',
          'Confidence Level',
          'Issues Detected',
          'Recommendations',
          'Treatment Required',
          'Prevention Measures'
        ],
        practices: [
          'Cultural Practices',
          'Mechanical Practices',
          'Biological Practices',
          'Chemical Practices',
          'Crop Rotation',
          'Mulching',
          'Intercropping',
          'Tillage',
          'Hand Weeding',
          'Trellising',
          'Pruning',
          'Beneficial Insects',
          'Microbial Inoculants',
          'Nematode Control',
          'Trap Crops',
          'Fertilization',
          'Fungicide',
          'pH Management',
          'Integrated Approach'
        ],
        soil: [
          'Soil Texture',
          'Soil Structure',
          'Porosity',
          'Water Retention',
          'Organic Matter',
          'Nitrogen (N)',
          'Phosphorus (P)',
          'Potassium (K)',
          'Calcium (Ca)',
          'Magnesium (Mg)',
          'Estimated pH',
          'Cation Exchange Capacity (CEC)',
          'Biological Activity',
          'Compaction Risk',
          'Crop Suitability',
          'Irrigation Needs',
          'Drainage',
          'Fertility Management'
        ]
      },
      // Common agricultural terms
      agricultural: [
        'Moderate presence',
        'Early stage',
        'Infestation',
        'Disease',
        'Pest',
        'Deficiency',
        'Treatment',
        'Prevention',
        'Fertilizer',
        'Pesticide',
        'Herbicide',
        'Fungicide',
        'Irrigation',
        'Harvest',
        'Planting',
        'Germination',
        'Flowering',
        'Fruiting',
        'Maturity',
        'Yield'
      ],
      // Common UI elements
      ui: [
        'Loading...',
        'Error',
        'Success',
        'Warning',
        'Info',
        'Close',
        'Save',
        'Cancel',
        'Delete',
        'Edit',
        'Add',
        'Update',
        'Refresh',
        'Search',
        'Filter',
        'Sort',
        'Export',
        'Import',
        'Download',
        'Upload'
      ],
      // Chatbot interface elements
      chatbot: [
        'New Chat',
        'Back',
        'Chat',
        'History',
        'Analysis',
        'Search Chat History',
        'Search for keywords...',
        'SEARCH',
        'Conversations by Date',
        'VIEW CONVERSATION',
        'Delete',
        'messages',
        'Image Analysis History',
        'FILTER',
        'Upload Image',
        'Start Recording',
        'Type your message...',
        'Language',
        'Send',
        'Suggested Questions',
        'You',
        'Bot',
        'No conversations found',
        'No analysis history found',
        'Reset',
        'Apply Filters'
      ]
    };
  }

  /**
   * Start preloading translations for a specific language
   * @param {string} targetLanguage - The language to preload for
   */
  async startPreloading(targetLanguage) {
    if (this.isPreloading || targetLanguage === 'en-IN' || targetLanguage === 'en') {
      return;
    }

    console.log(`🚀 Starting background translation preloading for ${targetLanguage}`);
    this.isPreloading = true;

    try {
      // Flatten all common texts into a single array
      const allTexts = this.flattenTexts(this.commonTexts);
      
      // Process translations in batches to avoid overwhelming the server
      const batchSize = 10;
      const batches = this.chunkArray(allTexts, batchSize);
      
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`📦 Preloading batch ${i + 1}/${batches.length} (${batch.length} texts)`);
        
        // Process batch in parallel
        await Promise.allSettled(
          batch.map(text => this.preloadTranslation(text, targetLanguage))
        );
        
        // Small delay between batches to be respectful to the server
        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
      
      console.log(`✅ Background translation preloading completed for ${targetLanguage}`);
    } catch (error) {
      console.error('❌ Error during background preloading:', error);
    } finally {
      this.isPreloading = false;
    }
  }

  /**
   * Preload a single translation
   * @param {string} text - Text to translate
   * @param {string} targetLanguage - Target language
   */
  async preloadTranslation(text, targetLanguage) {
    try {
      const response = await axios.post('/api/translate', {
        text,
        targetLanguage: targetLanguage.split('-')[0]
      });
      
      if (response.data && response.data.translation) {
        // Store in a global cache that can be accessed by other components
        this.storeInGlobalCache(text, targetLanguage, response.data.translation);
        return response.data.translation;
      }
    } catch (error) {
      console.warn(`⚠️ Failed to preload translation for "${text}":`, error.message);
    }
  }

  /**
   * Store translation in a global cache
   * @param {string} text - Original text
   * @param {string} targetLanguage - Target language
   * @param {string} translation - Translated text
   */
  storeInGlobalCache(text, targetLanguage, translation) {
    const cacheKey = `${text}:${targetLanguage}`;
    
    // Store in a global object that can be accessed by TranslationService
    if (!window.translationCache) {
      window.translationCache = {};
    }
    
    window.translationCache[cacheKey] = translation;
  }

  /**
   * Flatten nested text objects into a single array
   * @param {Object} texts - Nested text object
   * @returns {Array} Flattened array of texts
   */
  flattenTexts(texts) {
    const result = [];
    
    const flatten = (obj) => {
      for (const key in obj) {
        if (Array.isArray(obj[key])) {
          result.push(...obj[key]);
        } else if (typeof obj[key] === 'object') {
          flatten(obj[key]);
        }
      }
    };
    
    flatten(texts);
    return result;
  }

  /**
   * Split array into chunks
   * @param {Array} array - Array to chunk
   * @param {number} size - Chunk size
   * @returns {Array} Array of chunks
   */
  chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * Add custom texts to preload
   * @param {Array} texts - Array of texts to add
   * @param {string} category - Category for the texts
   */
  addCustomTexts(texts, category = 'custom') {
    if (!this.commonTexts[category]) {
      this.commonTexts[category] = [];
    }
    this.commonTexts[category].push(...texts);
  }

  /**
   * Get preloaded translation from cache
   * @param {string} text - Original text
   * @param {string} targetLanguage - Target language
   * @returns {string|null} Cached translation or null
   */
  getPreloadedTranslation(text, targetLanguage) {
    if (!window.translationCache) {
      return null;
    }
    
    const cacheKey = `${text}:${targetLanguage}`;
    return window.translationCache[cacheKey] || null;
  }

  /**
   * Clear all cached translations
   */
  clearCache() {
    if (window.translationCache) {
      window.translationCache = {};
    }
  }
}

// Create singleton instance
const translationPreloader = new TranslationPreloader();

export default translationPreloader; 