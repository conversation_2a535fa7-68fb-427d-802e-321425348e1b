// Mock data service for comprehensive market analysis
// In a real application, this would fetch data from government APIs and market databases

// Major mandis in India by state
const mandisByState = {
  'Andhra Pradesh': ['Guntur', 'Kurnool', 'Anantapur', 'Kadapa', 'Visakhapatnam'],
  'Assam': ['Guwahati', 'Jorhat', 'Silchar', 'Dibrugarh', 'Tezpur'],
  'Bihar': ['Patna', 'Muzaffarpur', 'Gaya', 'Bhagalpur', 'Darbhanga'],
  'Chhattisgarh': ['Raipur', 'Durg', 'Bilaspur', 'Korba', 'Rajnandgaon'],
  'Gujarat': ['Ahmedabad', 'Surat', 'Vadodara', 'Rajkot', 'Bhavnagar'],
  'Haryana': ['Karnal', 'Ambala', 'Hisar', 'Rohtak', 'Panipat'],
  'Himachal Pradesh': ['Shimla', 'Mandi', 'Solan', 'Kullu', 'Hamirpur'],
  'Jharkhand': ['Ranchi', 'Jamshedpur', 'Dhanbad', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
  'Karnataka': ['Bengaluru', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Bel<PERSON><PERSON>', 'Mangaluru'],
  'Kerala': ['Thiruvananthapuram', 'Kochi', 'Kozhikode', 'Thrissur', 'Kollam'],
  'Madhya Pradesh': ['Indore', 'Bhopal', 'Jabalpur', 'Gwalior', 'Ujjain'],
  'Maharashtra': ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad'],
  'Odisha': ['Bhubaneswar', 'Cuttack', 'Rourkela', 'Berhampur', 'Sambalpur'],
  'Punjab': ['Ludhiana', 'Amritsar', 'Jalandhar', 'Patiala', 'Bathinda'],
  'Rajasthan': ['Jaipur', 'Jodhpur', 'Udaipur', 'Kota', 'Ajmer'],
  'Tamil Nadu': ['Chennai', 'Coimbatore', 'Madurai', 'Tiruchirappalli', 'Salem'],
  'Telangana': ['Hyderabad', 'Warangal', 'Nizamabad', 'Karimnagar', 'Khammam'],
  'Uttar Pradesh': ['Lucknow', 'Kanpur', 'Agra', 'Varanasi', 'Meerut'],
  'Uttarakhand': ['Dehradun', 'Haridwar', 'Roorkee', 'Haldwani', 'Rudrapur'],
  'West Bengal': ['Kolkata', 'Asansol', 'Siliguri', 'Durgapur', 'Bardhaman']
};

// Major crops in India
const crops = [
  'Rice', 'Wheat', 'Maize', 'Jowar', 'Bajra', 'Ragi', 'Pulses', 'Gram', 'Tur', 'Moong', 
  'Urad', 'Sugarcane', 'Cotton', 'Jute', 'Groundnut', 'Soybean', 'Sunflower', 'Mustard', 
  'Coconut', 'Tea', 'Coffee', 'Rubber', 'Potato', 'Onion', 'Tomato'
];

// Quality parameters for different crops
const qualityParameters = {
  'Rice': ['Moisture Content', 'Broken Grains', 'Foreign Matter', 'Damaged Grains', 'Chalky Grains'],
  'Wheat': ['Moisture Content', 'Foreign Matter', 'Damaged Grains', 'Shriveled Grains', 'Protein Content'],
  'Maize': ['Moisture Content', 'Foreign Matter', 'Damaged Grains', 'Broken Kernels', 'Aflatoxin Level'],
  'Pulses': ['Moisture Content', 'Foreign Matter', 'Damaged Grains', 'Weeviled Grains', 'Admixture'],
  'Cotton': ['Staple Length', 'Micronaire Value', 'Strength', 'Uniformity', 'Trash Content'],
  'Sugarcane': ['Sucrose Content', 'Fiber Content', 'Purity', 'Juice Extraction', 'Pol Reading'],
  'Onion': ['Size', 'Color', 'Firmness', 'Sprouting', 'Rot'],
  'Potato': ['Size', 'Skin Quality', 'Greening', 'Sprouting', 'Internal Defects'],
  'Tomato': ['Size', 'Color', 'Firmness', 'Ripeness', 'Blemishes'],
  'default': ['Moisture Content', 'Foreign Matter', 'Damaged Grains', 'Appearance', 'Odor']
};

// Export destinations by crop
const exportDestinations = {
  'Rice': ['Saudi Arabia', 'Iran', 'UAE', 'Nepal', 'Bangladesh', 'Benin', 'USA'],
  'Wheat': ['Bangladesh', 'Nepal', 'UAE', 'Sri Lanka', 'Indonesia', 'Yemen'],
  'Maize': ['Bangladesh', 'Nepal', 'Sri Lanka', 'UAE', 'Vietnam', 'Malaysia'],
  'Pulses': ['UAE', 'USA', 'UK', 'Saudi Arabia', 'Sri Lanka', 'Malaysia'],
  'Cotton': ['Bangladesh', 'China', 'Vietnam', 'Indonesia', 'Pakistan', 'Thailand'],
  'Sugarcane': ['UAE', 'Somalia', 'Saudi Arabia', 'USA', 'Myanmar', 'Sudan'],
  'Onion': ['Bangladesh', 'Malaysia', 'UAE', 'Sri Lanka', 'Nepal', 'Qatar'],
  'Potato': ['Nepal', 'Oman', 'Malaysia', 'UAE', 'Sri Lanka', 'Mauritius'],
  'Tomato': ['UAE', 'Nepal', 'Bangladesh', 'UK', 'Malaysia', 'Singapore'],
  'default': ['Bangladesh', 'UAE', 'Nepal', 'USA', 'Saudi Arabia', 'Malaysia']
};

// Generate random price for a crop in a specific mandi
const generatePrice = (crop, mandi, state) => {
  // Base prices for different crops (in ₹ per quintal)
  const basePrices = {
    'Rice': 2000,
    'Wheat': 1800,
    'Maize': 1500,
    'Jowar': 2200,
    'Bajra': 2100,
    'Ragi': 3000,
    'Pulses': 5500,
    'Gram': 4800,
    'Tur': 6000,
    'Moong': 7000,
    'Urad': 6500,
    'Sugarcane': 280,
    'Cotton': 5500,
    'Jute': 4200,
    'Groundnut': 5000,
    'Soybean': 3800,
    'Sunflower': 5200,
    'Mustard': 4500,
    'Coconut': 2000,
    'Tea': 20000,
    'Coffee': 35000,
    'Rubber': 16000,
    'Potato': 1200,
    'Onion': 1500,
    'Tomato': 1800
  };

  // Get base price for the crop
  const basePrice = basePrices[crop] || 2000;
  
  // Add some randomness based on mandi and state
  const mandiIndex = mandisByState[state].indexOf(mandi);
  const stateIndex = Object.keys(mandisByState).indexOf(state);
  
  // Calculate price variations
  const stateFactor = 0.9 + (stateIndex / Object.keys(mandisByState).length) * 0.2;
  const mandiFactor = 0.95 + (mandiIndex / mandisByState[state].length) * 0.1;
  const randomFactor = 0.9 + Math.random() * 0.2;
  
  // Calculate current price
  const currentPrice = Math.round(basePrice * stateFactor * mandiFactor * randomFactor);
  
  // Calculate previous price (1-10% difference)
  const priceDiffPercent = -5 + Math.random() * 10;
  const previousPrice = Math.round(currentPrice * (100 / (100 + priceDiffPercent)));
  
  // Calculate weekly, monthly, and yearly trends
  const weeklyChange = -2 + Math.random() * 4;
  const monthlyChange = -5 + Math.random() * 10;
  const yearlyChange = -10 + Math.random() * 20;
  
  return {
    currentPrice,
    previousPrice,
    weeklyChange: weeklyChange.toFixed(1),
    monthlyChange: monthlyChange.toFixed(1),
    yearlyChange: yearlyChange.toFixed(1),
    unit: 'quintal'
  };
};

// Generate quality parameters for a crop
const generateQualityParameters = (crop) => {
  const parameters = qualityParameters[crop] || qualityParameters.default;
  
  return parameters.map(param => {
    // Generate a random score between 70 and 100
    const score = 70 + Math.floor(Math.random() * 30);
    
    // Determine status based on score
    let status;
    if (score >= 90) status = 'Excellent';
    else if (score >= 80) status = 'Good';
    else if (score >= 70) status = 'Average';
    else status = 'Poor';
    
    return {
      parameter: param,
      score,
      status
    };
  });
};

// Generate export data for a crop
const generateExportData = (crop) => {
  const destinations = exportDestinations[crop] || exportDestinations.default;
  
  return {
    totalExport: Math.round(100000 + Math.random() * 900000),
    unit: 'MT',
    yearOnYearGrowth: (-10 + Math.random() * 20).toFixed(1),
    destinations: destinations.map(country => ({
      country,
      volume: Math.round(10000 + Math.random() * 90000),
      growth: (-15 + Math.random() * 30).toFixed(1)
    }))
  };
};

// Generate MSP (Minimum Support Price) data
const generateMSPData = (crop) => {
  // Base MSP prices for different crops (in ₹ per quintal)
  const baseMSP = {
    'Rice': 1940,
    'Wheat': 1975,
    'Maize': 1850,
    'Jowar': 2620,
    'Bajra': 2150,
    'Ragi': 3295,
    'Pulses': 6000,
    'Gram': 5100,
    'Tur': 6300,
    'Moong': 7275,
    'Urad': 6300,
    'Sugarcane': 290,
    'Cotton': 5850,
    'Jute': 4500,
    'Groundnut': 5550,
    'Soybean': 3950,
    'Sunflower': 5885,
    'Mustard': 5050
  };
  
  const currentMSP = baseMSP[crop] || 0;
  
  if (currentMSP === 0) {
    return {
      available: false,
      message: "MSP not applicable for this crop"
    };
  }
  
  // Previous year's MSP (3-8% less)
  const previousMSP = Math.round(currentMSP / (1 + (3 + Math.random() * 5) / 100));
  
  return {
    available: true,
    currentMSP,
    previousMSP,
    change: ((currentMSP - previousMSP) / previousMSP * 100).toFixed(1),
    effectiveFrom: `${new Date().getFullYear()}-${new Date().getMonth() < 6 ? '04' : '10'}-01`
  };
};

// Get market data for a specific crop and mandi
export const getMarketData = async (crop, state, mandi) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  try {
    // Generate price data
    const priceData = generatePrice(crop, mandi, state);
    
    // Generate quality parameters
    const qualityData = generateQualityParameters(crop);
    
    // Generate export data
    const exportData = generateExportData(crop);
    
    // Generate MSP data
    const mspData = generateMSPData(crop);
    
    // Get arrival data (in quintals)
    const arrivalData = {
      today: Math.round(1000 + Math.random() * 9000),
      yesterday: Math.round(1000 + Math.random() * 9000),
      lastWeek: Math.round(5000 + Math.random() * 15000),
      lastMonth: Math.round(20000 + Math.random() * 80000)
    };
    
    return {
      crop,
      state,
      mandi,
      priceData,
      qualityData,
      exportData,
      mspData,
      arrivalData,
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error fetching market data:', error);
    throw new Error('Failed to fetch market data');
  }
};

// Get all available states
export const getAvailableStates = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return Object.keys(mandisByState);
};

// Get all mandis for a specific state
export const getMandisByState = async (state) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return mandisByState[state] || [];
};

// Get all available crops
export const getAvailableCrops = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return crops;
};

// Get market overview data (top crops, price trends, etc.)
export const getMarketOverview = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Generate data for top 5 crops
  const topCrops = crops.slice(0, 5);
  
  const cropData = topCrops.map(crop => {
    const priceData = generatePrice(crop, 'Mumbai', 'Maharashtra');
    return {
      name: crop,
      currentPrice: priceData.currentPrice,
      previousPrice: priceData.previousPrice,
      weeklyChange: priceData.weeklyChange,
      monthlyChange: priceData.monthlyChange,
      yearlyChange: priceData.yearlyChange,
      unit: priceData.unit
    };
  });
  
  // Generate overall market trends
  const marketTrends = {
    weeklyChange: ((-2 + Math.random() * 4)).toFixed(1),
    monthlyChange: ((-5 + Math.random() * 10)).toFixed(1),
    yearlyChange: ((-10 + Math.random() * 20)).toFixed(1)
  };
  
  // Generate market news
  const marketNews = [
    {
      title: "Government increases MSP for Rabi crops",
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      source: "Ministry of Agriculture"
    },
    {
      title: "Onion export ban lifted after price stabilization",
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      source: "Economic Times"
    },
    {
      title: "Record wheat production expected this year",
      date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      source: "Krishi Jagran"
    },
    {
      title: "New trade agreement to boost agricultural exports",
      date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
      source: "Business Standard"
    }
  ];
  
  return {
    crops: cropData,
    trends: marketTrends,
    news: marketNews,
    lastUpdated: new Date().toISOString()
  };
};
