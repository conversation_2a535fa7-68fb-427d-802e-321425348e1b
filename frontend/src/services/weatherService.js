import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL;

// Get current weather and forecast
export const getWeatherData = async (farmerId) => {
    try {
        // Get current weather and bulletin
        const bulletinResponse = await axios.get(
            `${API_BASE_URL}/weather/bulletin/${farmerId}`
        );

        // Get forecast
        const forecastResponse = await axios.get(
            `${API_BASE_URL}/weather/forecast/${farmerId}`
        );

        if (!bulletinResponse.data.success || !forecastResponse.data.success) {
            throw new Error('Failed to fetch weather data');
        }

        // Format the response to match the expected structure
        const weatherData = {
            current: bulletinResponse.data.data.current,
            forecast: forecastResponse.data.data.forecast,
            bulletin: bulletinResponse.data.data.bulletin
        };

        return weatherData;
    } catch (error) {
        console.error('Error fetching weather data:', error);
        throw error;
    }
};

export const getWeatherAlert = async (farmerId) => {
    try {
        const weatherData = await getWeatherData(farmerId);
        
        // Generate weather alert message
        let alertMessage = '';

        // Check for extreme temperatures
        if (weatherData.current.temperature > 35) {
            alertMessage += 'High temperature alert! Ensure proper irrigation. ';
        } else if (weatherData.current.temperature < 10) {
            alertMessage += 'Low temperature alert! Protect sensitive crops. ';
        }

        // Check for rain probability
        const todayForecast = weatherData.forecast[0];
        if (todayForecast.precipitation > 0.7) {
            alertMessage += 'High probability of rain today. Plan field activities accordingly. ';
        }

        // Check for strong winds
        if (weatherData.current.windSpeed > 8.33) { // 30 km/h in m/s
            alertMessage += 'Strong winds detected. Secure any vulnerable crops or structures. ';
        }

        // Add any bulletin alerts
        if (weatherData.bulletin?.alerts) {
            alertMessage += weatherData.bulletin.alerts.join(' ');
        }

        return alertMessage || null;
    } catch (error) {
        console.error('Error generating weather alert:', error);
        return null;
    }
};

export const getCropAdvisory = async (farmerId) => {
    try {
        const response = await axios.get(
            `${API_BASE_URL}/crop-advisory/${farmerId}`
        );
        return response.data.advisory;
    } catch (error) {
        console.error('Error fetching crop advisory:', error);
        return null;
    }
}; 