import axios from 'axios';
import { getTranslation as getLocalTranslation } from '../translations';

/**
 * TranslationService provides methods for translating text using both
 * local translations and Azure Translation API for dynamic content
 */
class TranslationService {
  constructor() {
    this.cache = {};
    this.languages = {
      'en-IN': 'English',
      'hi-IN': 'Hindi',
      'ta-IN': 'Tamil',
      'te-IN': 'Telugu',
      'kn-IN': 'Kannada',
      'ml-IN': 'Malayalam',
      'bn-IN': 'Bengali',
      'gu-IN': 'Gujarati',
      'mr-IN': 'Marathi',
      'pa-IN': 'Punjabi',
      'or-IN': 'Odia',
    };
    this.defaultLanguage = 'en-IN';
    
    // Common agricultural technical terms that need special handling
    this.technicalTerms = {
      "apple scab": true,
      "early blight": true,
      "late blight": true,
      "bacterial spot": true,
      "black rot": true,
      "powdery mildew": true,
      "gray mold": true, 
      "downy mildew": true,
      "nitrogen deficiency": true,
      "phosphorus deficiency": true,
      "potassium deficiency": true,
      "septoria leaf spot": true,
      "target spot": true,
      "yellow leaf curl": true,
      "mosaic virus": true,
      "cercospora leaf spot": true, 
      "bacterial wilt": true,
      "anthracnose": true,
      "moderate presence": true,
      "early stage": true
    };
  }

  /**
   * Get a list of supported languages
   * @returns {Object} An object containing language codes and names
   */
  getSupportedLanguages() {
    return this.languages;
  }

  /**
   * Get translation for a key from local translations
   * @param {string} key - Translation key
   * @param {string} language - Target language code
   * @returns {string} Translated text
   */
  getLocalTranslation(key, language = this.defaultLanguage) {
    return getLocalTranslation(key, language);
  }

  /**
   * Generate a cache key for a text and language combination
   * @param {string} text - Text to translate
   * @param {string} targetLanguage - Target language code
   * @returns {string} Cache key
   */
  getCacheKey(text, targetLanguage) {
    return `${text}:${targetLanguage}`;
  }

  /**
   * Check if a translation is in the cache
   * @param {string} text - Text to translate
   * @param {string} targetLanguage - Target language code
   * @returns {boolean} True if translation is in cache
   */
  isCached(text, targetLanguage) {
    const key = this.getCacheKey(text, targetLanguage);
    
    // Check local cache first
    if (this.cache[key]) {
      return true;
    }
    
    // Check global preloaded cache
    if (window.translationCache && window.translationCache[key]) {
      return true;
    }
    
    return false;
  }

  /**
   * Get a cached translation
   * @param {string} text - Text to translate
   * @param {string} targetLanguage - Target language code
   * @returns {string|null} Cached translation or null
   */
  getCachedTranslation(text, targetLanguage) {
    const key = this.getCacheKey(text, targetLanguage);
    
    // Check local cache first
    if (this.cache[key]) {
      return this.cache[key];
    }
    
    // Check global preloaded cache
    if (window.translationCache && window.translationCache[key]) {
      return window.translationCache[key];
    }
    
    return null;
  }

  /**
   * Store a translation in the cache
   * @param {string} text - Original text
   * @param {string} targetLanguage - Target language code
   * @param {string} translation - Translated text
   */
  cacheTranslation(text, targetLanguage, translation) {
    const key = this.getCacheKey(text, targetLanguage);
    this.cache[key] = translation;
  }

  /**
   * Extract technical terms from text for separate translation
   * @param {string} text - Text to analyze
   * @returns {Array} Array of technical terms found in the text
   */
  extractTechnicalTerms(text) {
    if (!text) return [];
    
    const terms = [];
    // Check against our dictionary of known terms
    for (const term in this.technicalTerms) {
      if (text.toLowerCase().includes(term.toLowerCase())) {
        terms.push(term);
      }
    }
    
    // Also check for pattern matches
    const patternMatches = text.match(/([\w\s]+scab|[\w\s]+disease|[\w\s]+pest|[\w\s]+deficiency|[\w\s]+rot|[\w\s]+blight|[\w\s]+virus|[\w\s]+mold|[\w\s]+mildew|moderate presence|early stage)/gi) || [];
    
    patternMatches.forEach(match => {
      if (!terms.includes(match)) {
        terms.push(match);
      }
    });
    
    return terms;
  }

  /**
   * Translate text to the target language with special handling for technical terms
   * @param {string} text - Text to translate
   * @param {string} targetLanguage - Target language code
   * @param {Object} options - Additional options (forceNewRequest to bypass cache)
   * @returns {Promise<string>} Translated text
   */
  async translateText(text, targetLanguage, options = {}) {
    // Don't translate empty strings or null values
    if (!text) return text;
    
    // If target language is English or matches source language, return the original text
    if (targetLanguage === 'en-IN' || targetLanguage === this.defaultLanguage) {
      return text;
    }
    
    // Special handling for problematic phrases that consistently fail to translate
    if (text === "Moderate presence") {
      const moderate = await this.translateTermDirectly("Moderate", targetLanguage);
      const presence = await this.translateTermDirectly("presence", targetLanguage);
      return `${moderate} ${presence}`;
    }
    
    if (text === "infestation") {
      // Try specific translation methods for this term
      try {
        // Add agricultural context for better translation
        const withContext = await this.translateTermDirectly("pest infestation", targetLanguage);
        // Return just the second word if it's a compound translation
        const parts = withContext.split(/\s+/);
        if (parts.length > 1) {
          return parts[parts.length - 1]; // Return the last word
        }
        return withContext;
      } catch (err) {
        console.error("Error translating infestation with context:", err);
      }
    }
    
    if (text === "Moderate presence, requires prompt treatment") {
      const moderate = await this.translateTermDirectly("Moderate", targetLanguage);
      const presence = await this.translateTermDirectly("presence", targetLanguage);
      const requires = await this.translateTermDirectly("requires", targetLanguage);
      const prompt = await this.translateTermDirectly("prompt", targetLanguage);
      const treatment = await this.translateTermDirectly("treatment", targetLanguage);
      
      return `${moderate} ${presence}, ${requires} ${prompt} ${treatment}`;
    }
    
    // Check cache first (unless forceNewRequest is true)
    if (!options.forceNewRequest && this.isCached(text, targetLanguage)) {
      return this.getCachedTranslation(text, targetLanguage);
    }
    
    // Detect if this is agricultural practice content
    const isAgriculturalPractice = text.includes('Crop rotation:') || 
                                  text.includes('Row spacing:') || 
                                  text.includes('Mulching:') || 
                                  text.includes('Intercropping:') ||
                                  text.includes('Tillage:') ||
                                  text.includes('Hand weeding:') ||
                                  text.includes('Trellising:') ||
                                  text.includes('Pruning:') ||
                                  text.includes('Beneficial insects:') ||
                                  text.includes('Microbial inoculants:') ||
                                  text.includes('Nematode control:') ||
                                  text.includes('Trap crops:') ||
                                  text.includes('Fertilization:') ||
                                  text.includes('Fungicide:') ||
                                  text.includes('pH management:') ||
                                  text.includes('Integrated approach:');
    
    try {
      // Extract technical terms for special handling
      const technicalTerms = this.extractTechnicalTerms(text);
      
      // Add agricultural practice keywords to technical terms for better translation
      if (isAgriculturalPractice) {
        const practiceTerms = [
          "Crop rotation", "Row spacing", "Mulching", "Intercropping",
          "Tillage", "Hand weeding", "Trellising", "Pruning",
          "Beneficial insects", "Microbial inoculants", "Nematode control", "Trap crops",
          "Fertilization", "Fungicide", "pH management", "Integrated approach"
        ];
        
        for (const term of practiceTerms) {
          if (text.includes(term) && !technicalTerms.includes(term)) {
            technicalTerms.push(term);
          }
        }
      }
      
      if (technicalTerms.length > 0) {
        // First translate the technical terms individually
        let modifiedText = text;
        const termTranslations = {};
        
        for (const term of technicalTerms) {
          // Replace with a placeholder to prevent translation issues
          const placeholder = `__TERM_${Math.random().toString(36).substring(2, 10)}__`;
          modifiedText = modifiedText.replace(new RegExp(term, 'gi'), placeholder);
          
          // Translate the term separately
          const termTranslation = await this.translateTermDirectly(term, targetLanguage);
          termTranslations[placeholder] = termTranslation;
        }
        
        // Translate the modified text
        const response = await axios.post('/api/translate', {
          text: modifiedText,
          targetLanguage: targetLanguage.split('-')[0] // Remove country code for API
        });
        
        let translatedText = response.data?.translation || modifiedText;
        
        // Replace the placeholders with their translations
        for (const [placeholder, translation] of Object.entries(termTranslations)) {
          translatedText = translatedText.replace(new RegExp(placeholder, 'g'), translation);
        }
        
        // Cache and return the result
        this.cacheTranslation(text, targetLanguage, translatedText);
        return translatedText;
      } else {
        // No technical terms, standard translation
        const response = await axios.post('/api/translate', {
          text,
          targetLanguage: targetLanguage.split('-')[0] // Remove country code for API
        });
        
        if (response.data && response.data.translation) {
          // Cache the result
          this.cacheTranslation(text, targetLanguage, response.data.translation);
          return response.data.translation;
        }
      }
      
      return text; // Return original if translation failed
    } catch (error) {
      console.error('Translation error:', error);
      return text; // Return original text if translation fails
    }
  }
  
  /**
   * Translate a technical term directly
   * @param {string} term - Term to translate
   * @param {string} targetLanguage - Target language code
   * @returns {Promise<string>} Translated term
   */
  async translateTermDirectly(term, targetLanguage) {
    try {
      // Check cache first
      if (this.isCached(term, targetLanguage)) {
        return this.getCachedTranslation(term, targetLanguage);
      }
      
      // Make direct translation API call
      const response = await axios.post('/api/translate', {
        text: term,
        targetLanguage: targetLanguage.split('-')[0], // Remove country code for API
        isTermTranslation: true // Flag to indicate this is a technical term
      });
      
      if (response.data && response.data.translation) {
        // Cache the result
        this.cacheTranslation(term, targetLanguage, response.data.translation);
        return response.data.translation;
      }
      
      return term; // Return original if translation failed
    } catch (error) {
      console.error('Term translation error:', error);
      return term;
    }
  }

  /**
   * Batch translate multiple texts
   * @param {Object} texts - Object with keys as ids and values as texts to translate
   * @param {string} targetLanguage - Target language code
   * @returns {Promise<Object>} Object with translated texts
   */
  async batchTranslate(texts, targetLanguage) {
    // Don't translate if target language is English
    if (targetLanguage === 'en-IN' || targetLanguage === this.defaultLanguage) {
      return { ...texts };
    }
    
    const result = { ...texts };
    const keys = Object.keys(texts);
    
    // Process translations in parallel
    await Promise.all(
      keys.map(async (key) => {
        if (texts[key]) {
          result[key] = await this.translateText(texts[key], targetLanguage);
        }
      })
    );
    
    return result;
  }
}

const translationService = new TranslationService();
export default translationService; 