import axios from 'axios';
import config from '../config/env';

// Cache implementation
const cache = new Map();
const CACHE_DURATION = config.cacheDuration * 1000; // Convert to milliseconds

// Exponential backoff delay calculation
const getBackoffDelay = (attempt) => Math.min(1000 * Math.pow(2, attempt), 10000);

// Cache management
const getCachedData = (key) => {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  return null;
};

const setCachedData = (key, data) => {
  cache.set(key, {
    data,
    timestamp: Date.now()
  });
};

// API Configuration
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api',
  timeout: 30000,
  retries: 3,
  retryDelay: 2000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// Create axios instance with default config
const api = axios.create(API_CONFIG);

// Request interceptor for adding auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Handle network errors
    if (!error.response) {
      console.error('Network Error:', error.message);
      return Promise.reject({
        message: 'Unable to connect to server. Please check your internet connection.',
        code: 'NETWORK_ERROR'
      });
    }

    // Handle 401 Unauthorized
    if (error.response.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
      return Promise.reject({
        message: 'Session expired. Please login again.',
        code: 'UNAUTHORIZED'
      });
    }

    // Handle 500 Internal Server Error
    if (error.response.status === 500) {
      return Promise.reject({
        message: 'Server error. Please try again later.',
        code: 'SERVER_ERROR'
      });
    }

    return Promise.reject(error);
  }
);

// Fetch with retry logic
export const fetchWithRetry = async (endpoint, options = {}, retries = 3) => {
  try {
    console.log(`Making request to ${endpoint}`);
    const response = await api({
      method: options.method || 'get',
      url: endpoint,
      ...options,
      retry: true,
      timeout: options.timeout || API_CONFIG.timeout
    });
    console.log(`Successfully fetched data from ${endpoint}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching ${endpoint}:`, error);
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || !error.response || error.code === 'ECONNABORTED')) {
      console.log(`Retrying request to ${endpoint}, ${retries} attempts remaining`);
      await new Promise(resolve => setTimeout(resolve, getBackoffDelay(API_CONFIG.retries - retries)));
      return fetchWithRetry(endpoint, options, retries - 1);
    }
    throw error;
  }
};

// Retry logic for failed requests
const retryRequest = async (error, retries = API_CONFIG.retries) => {
  const { config } = error;
  
  if (!config || !config.retry) {
    return Promise.reject(error);
  }

  config.retryCount = config.retryCount || 0;

  if (config.retryCount >= retries) {
    return Promise.reject(error);
  }

  config.retryCount += 1;

  // Create new promise to handle retry delay
  const delayRetry = new Promise(resolve => {
    setTimeout(resolve, API_CONFIG.retryDelay * config.retryCount);
  });

  return delayRetry.then(() => api(config));
};

// API utility functions
export const makeApiCall = async (method, endpoint, data = null, params = null) => {
  try {
    console.log(`Making API call to ${endpoint}`);
    const response = await api({
      method,
      url: endpoint,
      data,
      params,
      retry: true
    });
    console.log(`Successfully completed API call to ${endpoint}`);
    return response.data;
  } catch (error) {
    console.error(`API call failed for ${endpoint}:`, error);
    if (error.code === 'NETWORK_ERROR') {
      return retryRequest(error);
    }
    throw error;
  }
};

// Specific API functions
export const handleGetOtp = async (phoneNumber) => {
  try {
    return await makeApiCall('post', '/auth/get-otp', { phoneNumber });
  } catch (error) {
    console.error('OTP Error:', error);
    throw new Error(error.response?.data?.message || 'Failed to send OTP. Please try again.');
  }
};

export const handleVerifyOtp = async (phoneNumber, otp) => {
  try {
    return await makeApiCall('post', '/auth/verify-otp', { phoneNumber, otp });
  } catch (error) {
    console.error('OTP Verification Error:', error);
    throw new Error(error.response?.data?.message || 'Failed to verify OTP. Please try again.');
  }
};

export const handleLogin = async (credentials) => {
  try {
    return await makeApiCall('post', '/auth/login', credentials);
  } catch (error) {
    console.error('Login Error:', error);
    throw new Error(error.response?.data?.message || 'Login failed. Please try again.');
  }
};

export const handleLogout = async () => {
  try {
    await makeApiCall('post', '/auth/logout');
    localStorage.removeItem('token');
    window.location.href = '/login';
  } catch (error) {
    console.error('Logout Error:', error);
    throw new Error('Failed to logout. Please try again.');
  }
};

// Dashboard data functions
export const fetchDashboardData = async () => {
  try {
    const [weather, soil, market, schedule, alerts] = await Promise.allSettled([
      makeApiCall('get', '/dashboard/weather'),
      makeApiCall('get', '/dashboard/soil'),
      makeApiCall('get', '/dashboard/market'),
      makeApiCall('get', '/dashboard/schedule'),
      makeApiCall('get', '/dashboard/alerts')
    ]);

    return {
      weather: weather.status === 'fulfilled' ? weather.value : null,
      soil: soil.status === 'fulfilled' ? soil.value : null,
      market: market.status === 'fulfilled' ? market.value : null,
      schedule: schedule.status === 'fulfilled' ? schedule.value : null,
      alerts: alerts.status === 'fulfilled' ? alerts.value : null
    };
  } catch (error) {
    console.error('Dashboard Data Error:', error);
    throw new Error('Failed to load dashboard data. Please try again.');
  }
};

// Task management functions
export const updateTask = async (taskId, taskData) => {
  try {
    return await makeApiCall('put', `/tasks/${taskId}`, taskData);
  } catch (error) {
    console.error('Task Update Error:', error);
    throw new Error(error.response?.data?.message || 'Failed to update task. Please try again.');
  }
};

export const createTask = async (taskData) => {
  try {
    return await makeApiCall('post', '/tasks', taskData);
  } catch (error) {
    console.error('Task Creation Error:', error);
    throw new Error(error.response?.data?.message || 'Failed to create task. Please try again.');
  }
};

export const deleteTask = async (taskId) => {
  try {
    return await makeApiCall('delete', `/tasks/${taskId}`);
  } catch (error) {
    console.error('Task Deletion Error:', error);
    throw new Error(error.response?.data?.message || 'Failed to delete task. Please try again.');
  }
};

// Error handling utility
export const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error
    return {
      message: error.response.data.message || 'An error occurred',
      status: error.response.status,
      code: error.response.data.code
    };
  } else if (error.request) {
    // Request made but no response
    return {
      message: 'No response from server. Please check your connection.',
      status: 0,
      code: 'NO_RESPONSE'
    };
  } else {
    // Error in request setup
    return {
      message: error.message || 'An error occurred',
      status: 0,
      code: 'REQUEST_ERROR'
    };
  }
};

export default api;

// Clear cache
export const clearCache = () => {
  console.log('Clearing all cached data');
  cache.clear();
};

// Clear specific cache entry
export const clearCacheEntry = (endpoint) => {
  const url = `${config.apiUrl}${endpoint}`;
  console.log(`Clearing cache for: ${url}`);
  for (const [key] of cache) {
    if (key.startsWith(url)) {
      cache.delete(key);
    }
  }
}; 