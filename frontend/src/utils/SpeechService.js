// Voice mapping for natural-sounding voices
const getVoiceName = (lang) => {
  const voiceMapping = {
    en: "en-US-JennyNeural",
    "en-IN": "en-US-JennyNeural",
    hi: "hi-IN-SwaraNeural",
    "hi-IN": "hi-IN-SwaraNeural",
    bn: "bn-IN-SumonaNeural",
    "bn-IN": "bn-IN-SumonaNeural",
    gu: "gu-IN-NehaNeural",
    "gu-IN": "gu-IN-NehaNeural",
    kn: "kn-IN-ChitraNeural",
    "kn-IN": "kn-IN-ChitraNeural",
    ml: "ml-IN-SobhaNeural",
    "ml-IN": "ml-IN-SobhaNeural",
    mr: "mr-IN-AarohiNeural",
    "mr-IN": "mr-IN-AarohiNeural",
    pa: "pa-IN-ManeetNeural",
    "pa-IN": "pa-IN-ManeetNeural",
    ta: "ta-IN-PallaviNeural",
    "ta-IN": "ta-IN-PallaviNeural",
    te: "te-IN-ShrutiNeural",
    "te-IN": "te-IN-ShrutiNeural",
    ur: "ur-IN-ZaraNeural",
    "ur-IN": "ur-IN-ZaraNeural",
    or: "en-US-JennyNeural", // Fallback for Odia
    "or-IN": "en-US-JennyNeural", // Fallback for Odia
    as: "en-US-JennyNeural", // Fallback for Assamese
    "as-IN": "en-US-JennyNeural", // Fallback for Assamese
    bho: "en-US-JennyNeural", // Fallback for Bhojpuri
    "bho-IN": "en-US-JennyNeural", // Fallback for Bhojpuri
    ma: "en-US-JennyNeural", // Fallback for Maithili
    "ma-IN": "en-US-JennyNeural", // Fallback for Maithili
    sa: "en-US-JennyNeural", // Fallback for Sanskrit
    "sa-IN": "en-US-JennyNeural", // Fallback for Sanskrit
    ks: "en-US-JennyNeural", // Fallback for Kashmiri
    "ks-IN": "en-US-JennyNeural", // Fallback for Kashmiri
    mni: "en-US-JennyNeural", // Fallback for Manipuri
    "mni-IN": "en-US-JennyNeural", // Fallback for Manipuri
    ne: "en-US-JennyNeural",
    "ne-IN": "en-US-JennyNeural",
    sd: "en-US-JennyNeural",
    "sd-IN": "en-US-JennyNeural",
  };

  return voiceMapping[lang] || "en-US-JennyNeural"; // Use fallback value if lang is not found
};

class SpeechService {
  constructor() {
    this.synthesis = window.speechSynthesis;
    this.utterance = null;
    this.isPaused = false;
    this.isPlaying = false;
    this.text = "";
    this.language = "en-US";
    this.audioElement = null;
    this.useAzure = true; // Default to using Azure if available
    this.azureAvailable = null; // Will be determined on first use
  }

  // Initialize with language mapping
  init() {
    if (!this.synthesis && !this.useAzure) {
      console.error("Speech synthesis not supported");
      return false;
    }
    return true;
  }

  // Convert markdown to plain text for better speech
  markdownToPlainText(md) {
    if (!md) return "";
    return md
      .replace(/(\*\*|__)(.*?)\1/g, "$2")
      .replace(/(\*|_)(.*?)\1/g, "$2")
      .replace(/~~(.*?)~~/g, "$1")
      .replace(/`(.*?)`/g, "$1")
      .replace(/---/g, " ")
      .replace(/#+\s?/g, "")
      .replace(/[-*]\s/g, "")
      .replace(/>\s?/g, "")
      .replace(/[\n\r]+/g, ". ")
      .replace(/\s\s+/g, " ")
      .trim();
  }

  // Map language codes to speech synthesis language codes
  mapLanguage(langCode) {
    const langMap = {
      en: "en-US",
      hi: "hi-IN",
      te: "te-IN",
      ta: "ta-IN",
      kn: "kn-IN",
      ml: "ml-IN",
      pa: "pa-IN",
      bn: "bn-IN",
      gu: "gu-IN",
      mr: "mr-IN",
      ur: "ur-IN",
      or: "en-US", // Fallback
      as: "en-US", // Fallback
      bho: "en-US", // Fallback
      ma: "en-US", // Fallback
      sa: "en-US", // Fallback
      ks: "en-US", // Fallback
      mni: "en-US", // Fallback
      ne: "en-US", // Fallback
      sd: "en-US", // Fallback
    };
    return langMap[langCode] || "en-US";
  }

  // Check if Azure SDK is available and not blocked
  async checkAzureAvailability() {
    // If we've already checked, use the cached result
    if (this.azureAvailable !== null) {
      return this.azureAvailable;
    }

    try {
      // Check if the Microsoft Speech SDK is available
      if (typeof SpeechSDK === "undefined") {
        console.warn("Microsoft Speech SDK not available");
        this.azureAvailable = false;
        return false;
      }

      // Get Azure credentials from environment variables
      const key = import.meta.env.VITE_AZURE_SPEECH_KEY;
      const region = import.meta.env.VITE_AZURE_SPEECH_REGION;

      if (!key || !region) {
        console.warn("Azure Speech key or region is missing");
        this.azureAvailable = false;
        return false;
      }

      // Try to create a minimal speech config to test if SDK is blocked
      try {
        const testConfig = SpeechSDK.SpeechConfig.fromSubscription(key, region);
        if (testConfig) {
          this.azureAvailable = true;
          return true;
        }
      } catch (e) {
        console.warn("Azure Speech SDK initialization failed:", e);
        this.azureAvailable = false;
        return false;
      }

      this.azureAvailable = false;
      return false;
    } catch (error) {
      console.warn("Error checking Azure availability:", error);
      this.azureAvailable = false;
      return false;
    }
  }

  // Try to use Azure Speech Services
  async speakWithAzure(text, language, onEndCallBack = () => {}) {
    console.log("Speak with Azure called!!");
    try {
      // Check if Azure is available
      const isAvailable = await this.checkAzureAvailability();
      if (!isAvailable) {
        console.warn("Azure Speech Services unavailable, falling back to browser TTS");
        return this.speakWithBrowser(text, language, onEndCallBack);
      }

      const key = import.meta.env.VITE_AZURE_SPEECH_KEY;
      const region = import.meta.env.VITE_AZURE_SPEECH_REGION;
      const voiceName = getVoiceName(language);
      
      console.log(
        `Using Azure TTS with voice: ${voiceName} for language: ${language}`
      );

      // Create speech configuration with error handling
      let speechConfig;
      try {
        speechConfig = SpeechSDK.SpeechConfig.fromSubscription(key, region);
        speechConfig.speechSynthesisVoiceName = voiceName;
      } catch (e) {
        console.error("Failed to create Azure speech config:", e);
        return this.speakWithBrowser(text, language, onEndCallBack);
      }

      // Try to use PullAudioOutputStream with fallback
      let audioConfig;
      try {
        const stream = SpeechSDK.AudioOutputStream.createPullStream();
        audioConfig = SpeechSDK.AudioConfig.fromStreamOutput(stream);
      } catch (e) {
        console.warn("Failed to create pull stream, trying default speaker:", e);
        try {
          audioConfig = SpeechSDK.AudioConfig.fromDefaultSpeakerOutput();
        } catch (e2) {
          console.error("Failed to create audio config:", e2);
          return this.speakWithBrowser(text, language, onEndCallBack);
        }
      }

      // Create synthesizer with error handling
      let synthesizer;
      try {
        synthesizer = new SpeechSDK.SpeechSynthesizer(
          speechConfig,
          audioConfig
        );
      } catch (e) {
        console.error("Failed to create speech synthesizer:", e);
        return this.speakWithBrowser(text, language, onEndCallBack);
      }

      // Try to synthesize speech
      try {
        const result = await new Promise((resolve, reject) => {
          synthesizer.speakTextAsync(
            text,
            (res) => {
              synthesizer.close();
              resolve(res);
            },
            (err) => {
              synthesizer.close();
              reject(err);
            }
          );
        });

        if (result.reason === SpeechSDK.ResultReason.SynthesizingAudioCompleted) {
          console.log("✅ Azure TTS completed successfully");

          // Convert audio data to Blob
          const audioData = result.audioData;
          const blob = new Blob([audioData], { type: "audio/wav" });
          const blobUrl = URL.createObjectURL(blob);

          // Create audio element and play it
          const audio = new Audio(blobUrl);
          this.audioElement = audio;

          this.isPlaying = true;
          this.isPaused = false;

          audio.onended = () => {
            this.isPlaying = false;
            this.audioElement = null;
            URL.revokeObjectURL(blobUrl);
            onEndCallBack(); // Call the provided callback
          };

          audio.onerror = (e) => {
            console.error("Audio playback error:", e);
            this.isPlaying = false;
            this.audioElement = null;
            URL.revokeObjectURL(blobUrl);
            // Try browser fallback on audio error
            this.speakWithBrowser(text, language, onEndCallBack);
          };

          // Try to play with error handling
          try {
            audio.play();
            return true;
          } catch (e) {
            console.error("Failed to play audio:", e);
            URL.revokeObjectURL(blobUrl);
            return this.speakWithBrowser(text, language, onEndCallBack);
          }
        } else {
          console.error("Azure TTS failed:", result.errorDetails);
          return this.speakWithBrowser(text, language, onEndCallBack);
        }
      } catch (e) {
        console.error("Azure speech synthesis failed:", e);
        return this.speakWithBrowser(text, language, onEndCallBack);
      }
    } catch (error) {
      console.error("Error in Azure TTS:", error);
      return this.speakWithBrowser(text, language, onEndCallBack);
    }
  }

  // Use browser's built-in speech synthesis
  speakWithBrowser(text, language, onEndCallBack = () => {}) {
    console.log("Speak with browser called!");
    if (!this.synthesis) {
      console.error("Browser speech synthesis not supported");
      return false;
    }

    // Stop any current speech
    this.stop();

    // Create utterance
    this.utterance = new SpeechSynthesisUtterance(text);
    this.utterance.lang = this.mapLanguage(language);
    this.utterance.rate = 1.0;
    this.utterance.pitch = 1.0;

    // Set event handlers
    this.utterance.onstart = () => {
      this.isPlaying = true;
      this.isPaused = false;
    };

    this.utterance.onend = () => {
      this.isPlaying = false;
      this.isPaused = false;
      this.utterance = null;
      onEndCallBack(); // Call the provided callback
    };

    this.utterance.onerror = (event) => {
      console.error("Speech synthesis error:", event);
      this.isPlaying = false;
      this.isPaused = false;
      this.utterance = null;
    };

    // Speak with Chrome bug workaround
    this.synthesis.speak(this.utterance);
    
    // Chrome bug workaround - if speech doesn't start in 1 second, try to resume
    setTimeout(() => {
      if (this.synthesis && this.synthesis.paused) {
        try {
          this.synthesis.resume();
        } catch (e) {
          console.warn('Failed to resume speech synthesis:', e);
        }
      }
    }, 1000);
    
    return true;
  }

  // Speak text with proper error handling
  async speak(text, language = "en", onEndCallBack = () => {}) {
    console.log("Speak called!!");
    if (!this.init()) return false;

    // Stop any current speech
    this.stop();

    // Convert markdown to plain text
    const plainText = this.markdownToPlainText(text);
    this.text = plainText;
    this.language = language;

    // Try Azure first if it hasn't been determined to be unavailable
    if (this.useAzure && this.azureAvailable !== false) {
      try {
        const azureResult = await this.speakWithAzure(plainText, language, onEndCallBack);
        if (azureResult) return true;
      } catch (e) {
        console.warn("Azure speech failed, falling back to browser:", e);
      }
    }
    
    // Fall back to browser
    return this.speakWithBrowser(plainText, language, onEndCallBack);
  }

  // Stop speech and reset state
  stop() {
    console.log("Stop speech called!!");
    // Stop browser speech synthesis if active
    if (this.synthesis) {
      this.synthesis.cancel();
    }

    // Stop Azure audio if active
    if (this.audioElement) {
      try {
        console.log("stop condition reached!");
        this.audioElement.pause();
        this.audioElement.currentTime = 0;
        if (
          this.audioElement.src &&
          this.audioElement.src.startsWith("blob:")
        ) {
          URL.revokeObjectURL(this.audioElement.src);
        }
        this.audioElement = null;
      } catch (error) {
        console.error("Error stopping Azure audio:", error);
      }
    }

    this.isPlaying = false;
    this.isPaused = false;
    this.utterance = null;
    return true;
  }

  // Check if speech synthesis is speaking
  isSpeaking() {
    return this.isPlaying;
  }

  // Check if speech synthesis is paused
  isPausedState() {
    return this.isPaused;
  }
}

export default new SpeechService();
