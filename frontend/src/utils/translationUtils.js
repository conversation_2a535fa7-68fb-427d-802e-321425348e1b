import { useTranslation } from '../hooks/useTranslation';

/**
 * Translates a number to the target language
 * @param {number|string} number - The number to translate
 * @param {Function} t - Translation function from useTranslation hook
 * @returns {string} - The translated number
 */
export const translateNumber = (number, t) => {
  if (number === undefined || number === null) return '';
  
  const numStr = number.toString();
  let result = '';
  
  // Translate each digit
  for (let i = 0; i < numStr.length; i++) {
    const char = numStr[i];
    if (/\d/.test(char)) {
      // If it's a digit, translate it
      result += t(`numbers.${char}`, char);
    } else {
      // Keep other characters (like decimal points, commas, etc.)
      result += char;
    }
  }
  
  return result;
};

/**
 * Translates a unit to the target language
 * @param {string} unit - The unit to translate
 * @param {Function} t - Translation function from useTranslation hook
 * @returns {string} - The translated unit
 */
export const translateUnit = (unit, t) => {
  if (!unit) return '';
  return t(`units.${unit.toLowerCase()}`, unit);
};

/**
 * Formats a number with unit for display with translation
 * @param {number|string} value - The value to format
 * @param {string} unit - The unit to append
 * @param {Function} t - Translation function from useTranslation hook
 * @returns {string} - The formatted value with translated number and unit
 */
export const formatValueWithUnit = (value, unit, t) => {
  if (value === undefined || value === null) return '';
  const translatedNumber = translateNumber(value, t);
  const translatedUnit = translateUnit(unit, t);
  return `${translatedNumber} ${translatedUnit}`;
};

/**
 * Formats a date with translation
 * @param {Date|string} date - The date to format
 * @param {Function} t - Translation function from useTranslation hook
 * @returns {string} - The formatted date with translated month and day names
 */
export const formatDate = (date, t) => {
  if (!date) return '';
  
  const dateObj = date instanceof Date ? date : new Date(date);
  if (isNaN(dateObj.getTime())) return '';
  
  const day = dateObj.getDate();
  const month = dateObj.getMonth();
  const year = dateObj.getFullYear();
  
  // Get translated day name
  const dayName = dateObj.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
  const translatedDayName = t(`days.${dayName}`, dateObj.toLocaleDateString('en-US', { weekday: 'long' }));
  
  // Format the date based on locale
  const dateFormatter = new Intl.DateTimeFormat('en-US', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  });
  
  const formattedDate = dateFormatter.format(dateObj);
  
  // Replace the day number with translated version
  const translatedDay = translateNumber(day, t);
  
  return formattedDate.replace(day.toString(), translatedDay);
};

export default {
  translateNumber,
  translateUnit,
  formatValueWithUnit,
  formatDate
}; 