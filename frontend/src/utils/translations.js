const translations = {
  en: {
    marketAnalysis: {
      noData: 'No market data available',
      crop: 'Crop',
      price: 'Price',
      trend: 'Trend',
      volume: 'Volume',
      market: 'Market',
      up: 'Up',
      down: 'Down',
      stable: 'Stable'
    },
    alerts: {
      noAlerts: 'No alerts to display'
    },
    dashboard: {
      welcome: 'Welcome',
    },
    chatbot: {
      title: 'Quamin SmartBot',
      tooltip: 'Chat with Quamin AI',
      inputPlaceholder: 'Type your message...',
      defaultResponse: 'I\'m your Quamin assistant. How can I help you today?',
      followup1: 'How do I analyze my soil health?',
      followup2: 'What crops are best for my region?',
      followup3: 'How can I improve crop yield?',
      languageTooltip: 'Change language',
      historyTooltip: 'View chat history',
      suggestedQuestions: 'Suggested questions',
      analyzingImage: 'Please analyze this image',
      analysisResults: 'Analysis Results',
      identified: 'Identified as',
      confidence: 'confidence',
      healthStatus: 'Health Status',
      issues: 'Issues',
      recommendations: 'Recommendations',
      nutrientDeficiency: 'minor nutrient deficiency',
      recommendation1: 'Consider adding nitrogen-rich fertilizer',
      recommendation2: 'Maintain current watering schedule',
      recommendation3: 'Monitor for pest activity in the next week',
      imageFollowup1: 'What fertilizers do you recommend?',
      imageFollowup2: 'How can I prevent nutrient deficiency?',
      imageFollowup3: 'When should I apply fertilizer?',
      imageAnalysisError: 'Sorry, I couldn\'t analyze this image. Please try again with a clearer image.',
      chatTab: 'Chat',
      historyTab: 'History',
      analysisTab: 'Analysis',
      searchHistory: 'Search Chat History',
      searchPlaceholder: 'Search for keywords...',
      search: 'Search',
      recentConversations: 'Recent Conversations',
      noHistory: 'No chat history found',
      messages: 'messages',
      preview: 'Preview',
      you: 'You',
      bot: 'Bot',
      viewConversation: 'View Conversation',
      imageAnalysisHistory: 'Image Analysis History',
      noAnalysisHistory: 'No image analysis history found',
      viewDetails: 'View Details',
      uploadImage: 'Upload image',
      startRecording: 'Voice input',
      stopRecording: 'Stop recording',
      send: 'Send',
      back: 'Back',
      poweredBy: 'Powered by Azure OpenAI',
      readAnalysis: 'Read',
      chatDeleted: 'Chat history deleted successfully',
      errorDeletingChat: 'Error deleting chat history',
      deleteChat: 'Delete chat',
      deleteAnalysis: 'Delete analysis',
      microphoneError: 'Could not access microphone. Please check permissions.',
      networkError: 'Network error. Please check your connection and try again.',
      errorProcessingMessage: 'Sorry, I encountered an error processing your message. Please try again.'
    }
  },
  hi: {
    marketAnalysis: {
      noData: 'कोई बाजार डेटा उपलब्ध नहीं है',
      crop: 'फसल',
      price: 'कीमत',
      trend: 'प्रवृत्ति',
      volume: 'मात्रा',
      market: 'बाजार',
      up: 'बढ़ोतरी',
      down: 'गिरावट',
      stable: 'स्थिर'
    },
    alerts: {
      noAlerts: 'दिखाने के लिए कोई अलर्ट नहीं'
    },
    dashboard: {
      welcome: 'स्वागत है',
    },
    chatbot: {
      title: 'क्वामिन स्मार्टबॉट',
      tooltip: 'क्वामिन AI से चैट करें',
      inputPlaceholder: 'अपना संदेश लिखें...',
      defaultResponse: 'मैं आपका क्वामिन सहायक हूं। आज मैं आपकी कैसे मदद कर सकता हूं?',
      followup1: 'मैं अपनी मिट्टी के स्वास्थ्य का विश्लेषण कैसे करूं?',
      followup2: 'मेरे क्षेत्र के लिए कौन सी फसलें सबसे अच्छी हैं?',
      followup3: 'मैं फसल उपज कैसे बढ़ा सकता हूं?',
      languageTooltip: 'भाषा बदलें',
      historyTooltip: 'चैट इतिहास देखें',
      suggestedQuestions: 'सुझाए गए प्रश्न',
      analyzingImage: 'कृपया इस छवि का विश्लेषण करें',
      analysisResults: 'विश्लेषण परिणाम',
      identified: 'पहचान',
      confidence: 'विश्वास',
      healthStatus: 'स्वास्थ्य स्थिति',
      issues: 'समस्याएं',
      recommendations: 'सिफारिशें',
      nutrientDeficiency: 'मामूली पोषक तत्व की कमी',
      recommendation1: 'नाइट्रोजन युक्त उर्वरक जोड़ने पर विचार करें',
      recommendation2: 'वर्तमान सिंचाई कार्यक्रम बनाए रखें',
      recommendation3: 'अगले सप्ताह कीट गतिविधि की निगरानी करें',
      imageFollowup1: 'आप किन उर्वरकों की सिफारिश करते हैं?',
      imageFollowup2: 'मैं पोषक तत्वों की कमी को कैसे रोक सकता हूं?',
      imageFollowup3: 'मुझे उर्वरक कब लगाना चाहिए?',
      imageAnalysisError: 'क्षमा करें, मैं इस छवि का विश्लेषण नहीं कर सका। कृपया एक स्पष्ट छवि के साथ फिर से प्रयास करें।',
      chatTab: 'चैट',
      historyTab: 'इतिहास',
      analysisTab: 'विश्लेषण',
      searchHistory: 'चैट इतिहास खोजें',
      searchPlaceholder: 'कीवर्ड खोजें...',
      search: 'खोजें',
      recentConversations: 'हाल की बातचीत',
      noHistory: 'कोई चैट इतिहास नहीं मिला',
      messages: 'संदेश',
      preview: 'पूर्वावलोकन',
      you: 'आप',
      bot: 'बॉट',
      viewConversation: 'बातचीत देखें',
      imageAnalysisHistory: 'छवि विश्लेषण इतिहास',
      noAnalysisHistory: 'कोई छवि विश्लेषण इतिहास नहीं मिला',
      viewDetails: 'विवरण देखें',
      uploadImage: 'छवि अपलोड करें',
      startRecording: 'आवाज़ इनपुट',
      stopRecording: 'रिकॉर्डिंग बंद करें',
      send: 'भेजें',
      back: 'वापस',
      poweredBy: 'आज़ुर ओपनएआई द्वारा संचालित',
      readAnalysis: 'पढ़ें',
      chatDeleted: 'चैट इतिहास सफलतापूर्वक हटा दिया गया',
      errorDeletingChat: 'चैट इतिहास हटाने में त्रुटि',
      deleteChat: 'चैट हटाएं',
      deleteAnalysis: 'विश्लेषण हटाएं',
      microphoneError: 'माइक्रोफोन तक पहुंच नहीं सका। कृपया अनुमतियां जांचें।',
      networkError: 'नेटवर्क त्रुटि। कृपया अपने कनेक्शन की जांच करें और पुनः प्रयास करें।',
      errorProcessingMessage: 'माफ़ करें, आपके संदेश को संसाधित करने में त्रुटि हुई। कृपया पुनः प्रयास करें।'
    }
  }
};

export const getTranslation = (key, language = 'en') => {
  const keys = key.split('.');
  let value = translations[language] || translations['en'];

  for (const k of keys) {
    value = value?.[k];
    if (value === undefined) {
      return key;
    }
  }

  return value;
};