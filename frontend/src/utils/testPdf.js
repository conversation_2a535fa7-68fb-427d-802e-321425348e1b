import { jsPD<PERSON> } from 'jspdf';
import autoTable from 'jspdf-autotable';

// Test function to verify jsPDF and autoTable are working
const testPdf = () => {
  try {
    console.log('Creating test PDF...');
    
    // Create a new PDF document
    const doc = new jsPDF();
    
    // Add some text
    doc.text('Testing jsPDF and autoTable', 10, 10);
    
    // Try to use autoTable
    try {
      console.log('Adding autoTable to document...');
      console.log('autoTable function:', autoTable);
      
      // Add autoTable manually if needed
      if (typeof autoTable === 'function' && !doc.autoTable) {
        console.log('Manually adding autoTable to doc');
        autoTable(doc);
      }
      
      // Check if autoTable is available
      if (doc.autoTable) {
        console.log('autoTable is available on doc object');
        
        // Create a simple table
        doc.autoTable({
          head: [['Name', 'Email', 'Country']],
          body: [
            ['David', '<EMAIL>', 'Sweden'],
            ['Castille', '<EMAIL>', 'Spain'],
          ],
          startY: 20,
        });
        
        console.log('Table added successfully');
      } else {
        console.error('autoTable is not available on doc object');
      }
    } catch (tableError) {
      console.error('Error using autoTable:', tableError);
    }
    
    // Save the PDF
    const pdfBlob = doc.output('blob');
    console.log('PDF created successfully:', pdfBlob);
    
    return pdfBlob;
  } catch (error) {
    console.error('Error in testPdf:', error);
    throw error;
  }
};

export default testPdf; 