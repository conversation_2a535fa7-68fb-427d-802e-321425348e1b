// Import jsPDF and autoTable
import { jsPDF } from 'jspdf';
// Import autoTable and apply it to jsPDF
import { applyPlugin } from 'jspdf-autotable';
import html2canvas from 'html2canvas';

// Apply autoTable plugin to jsPDF
applyPlugin(jsPDF);

// Create custom jsPDF constructor with autoTable
const createPdf = (options = {}) => {
  const defaultOptions = {
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4',
  };
  
  const mergedOptions = { ...defaultOptions, ...options };
  
  try {
    // Create jsPDF instance
    const doc = new jsPDF(mergedOptions);
    
    // Verify autoTable is available
    if (!doc.autoTable) {
      console.warn('autoTable is not available on the document object');
    } else {
      console.log('autoTable is available on the document object');
    }
    
    return doc;
  } catch (error) {
    console.error('Error creating PDF document:', error);
    // Fallback to basic jsPDF without options
    try {
      const basicDoc = new jsPDF();
      return basicDoc;
    } catch (fallbackError) {
      console.error('Fallback PDF creation failed:', fallbackError);
      throw new Error('Failed to create PDF document');
    }
  }
};

// Get the app logo as a base64 string
const getAppLogo = async () => {
  try {
    // Try to load the logo from the assets folder
    const logoUrl = '/images/logo.png'; // Adjust path as needed
    
    // Try direct loading first
    try {
      const directResult = await loadImage(logoUrl);
      if (directResult) {
        return directResult;
      }
    } catch (directError) {
      console.warn('Direct logo loading failed:', directError);
    }
    
    // Try with absolute URL if relative path fails
    try {
      // Construct absolute URL based on current origin
      const absoluteLogoUrl = `${window.location.origin}${logoUrl}`;
      const absoluteResult = await loadImage(absoluteLogoUrl);
      if (absoluteResult) {
        return absoluteResult;
      }
    } catch (absoluteError) {
      console.warn('Absolute path logo loading failed:', absoluteError);
    }
    
    // Try to load from a different path as fallback
    try {
      const fallbackLogoUrl = '/assets/images/logo.png'; // Alternative path
      const fallbackResult = await loadImage(fallbackLogoUrl);
      if (fallbackResult) {
        return fallbackResult;
      }
    } catch (fallbackError) {
      console.warn('Fallback logo loading failed:', fallbackError);
    }
    
    // If all attempts fail, generate a simple logo
    console.warn('All logo loading attempts failed, generating placeholder');
    return generatePlaceholderLogo();
  } catch (error) {
    console.error('Error in getAppLogo:', error);
    return generatePlaceholderLogo();
  }
};

// Generate a simple placeholder logo
const generatePlaceholderLogo = () => {
  try {
    const canvas = document.createElement('canvas');
    canvas.width = 200;
    canvas.height = 60;
    const ctx = canvas.getContext('2d');
    
    // Fill background
    ctx.fillStyle = '#27ae60'; // Agricare green
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Add text
    ctx.fillStyle = '#ffffff'; // White text
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('AgriCare', canvas.width / 2, canvas.height / 2);
    
    return canvas.toDataURL('image/png');
  } catch (error) {
    console.error('Error generating placeholder logo:', error);
    return null;
  }
};

// Add a placeholder image as a fallback
const getPlaceholderImage = (text) => {
  try {
    const canvas = document.createElement('canvas');
    canvas.width = 300;
    canvas.height = 200;
    const ctx = canvas.getContext('2d');
    
    // Fill background
    ctx.fillStyle = '#f5f5f5';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw border
    ctx.strokeStyle = '#cccccc';
    ctx.lineWidth = 2;
    ctx.strokeRect(5, 5, canvas.width - 10, canvas.height - 10);
    
    // Draw text
    ctx.fillStyle = '#666666';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(text || 'Image not available', canvas.width / 2, canvas.height / 2);
    ctx.font = '12px Arial';
    ctx.fillText('Agricare Analysis Report', canvas.width / 2, canvas.height / 2 + 30);
    
    return canvas.toDataURL('image/png');
  } catch (error) {
    console.warn('Error creating placeholder image:', error);
    return null;
  }
};

// Capture the image as a data URL
const captureImage = async (imageUrl) => {
  try {
    // Handle data URLs directly
    if (imageUrl.startsWith('data:')) {
      return imageUrl;
    }
    
    // Try to load the image directly first
    try {
      const directResult = await loadImage(imageUrl);
      if (directResult) {
        return directResult;
      }
    } catch (directError) {
      console.warn('Direct image loading failed, trying with CORS proxy:', directError);
    }
    
    // If direct loading fails, try with a CORS proxy
    try {
      // Use a CORS proxy service
      const corsProxyUrl = `https://cors-anywhere.herokuapp.com/${imageUrl}`;
      const proxyResult = await loadImage(corsProxyUrl);
      if (proxyResult) {
        return proxyResult;
      }
    } catch (proxyError) {
      console.warn('CORS proxy image loading failed:', proxyError);
    }
    
    // Try an alternative proxy if the first one fails
    try {
      // Use an alternative CORS proxy service
      const altProxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(imageUrl)}`;
      const altProxyResult = await loadImage(altProxyUrl);
      if (altProxyResult) {
        return altProxyResult;
      }
    } catch (altProxyError) {
      console.warn('Alternative CORS proxy image loading failed:', altProxyError);
    }
    
    // Try local canvas-based approach as a last resort
    try {
      console.log('Attempting local canvas-based image capture as last resort');
      // Create an img element to display the image
      const imgElement = document.createElement('img');
      imgElement.crossOrigin = 'Anonymous';
      imgElement.style.position = 'absolute';
      imgElement.style.left = '-9999px'; // Hide off-screen
      imgElement.style.top = '-9999px';
      
      // Add to DOM temporarily
      document.body.appendChild(imgElement);
      
      // Try to load with various approaches
      imgElement.src = imageUrl;
      
      // Wait for image to load or fail
      await new Promise((resolve) => {
        imgElement.onload = resolve;
        imgElement.onerror = resolve; // Continue even if error
        setTimeout(resolve, 3000); // Timeout after 3 seconds
      });
      
      // Try to capture via canvas if the image loaded
      if (imgElement.complete && imgElement.naturalWidth > 0) {
        const canvas = document.createElement('canvas');
        canvas.width = imgElement.naturalWidth;
        canvas.height = imgElement.naturalHeight;
        const ctx = canvas.getContext('2d');
        
        try {
          ctx.drawImage(imgElement, 0, 0);
          const dataUrl = canvas.toDataURL('image/png');
          
          // Clean up
          document.body.removeChild(imgElement);
          
          return dataUrl;
        } catch (canvasError) {
          console.warn('Canvas drawing failed:', canvasError);
          document.body.removeChild(imgElement);
        }
      } else {
        // Clean up
        document.body.removeChild(imgElement);
      }
    } catch (localError) {
      console.warn('Local canvas-based approach failed:', localError);
    }
    
    // If all methods fail, return null
    console.warn('All image loading methods failed for:', imageUrl);
    return null;
  } catch (error) {
    console.warn('Error capturing image:', error);
    return null;
  }
};

// Helper function to load an image and convert to data URL
const loadImage = (url) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'Anonymous';
    
    // Add a timeout to handle cases where the image loading hangs
    const timeoutId = setTimeout(() => {
      console.warn('Image loading timed out');
      resolve(null);
    }, 5000);
    
    // Try with different CORS settings if first attempt fails
    let corsRetryAttempted = false;
    
    const attemptLoad = (crossOriginValue) => {
      img.crossOrigin = crossOriginValue;
      
      img.onload = () => {
        clearTimeout(timeoutId);
        try {
          const canvas = document.createElement('canvas');
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext('2d');
          
          // Try to draw the image to canvas
          try {
            ctx.drawImage(img, 0, 0);
            const dataUrl = canvas.toDataURL('image/png');
            resolve(dataUrl);
          } catch (drawError) {
            console.warn('Error drawing image to canvas (possible CORS issue):', drawError);
            
            // If this is the first attempt, try again with a different crossOrigin value
            if (!corsRetryAttempted) {
              corsRetryAttempted = true;
              console.log('Retrying with different CORS setting');
              // Try with 'anonymous' if we used something else, or with null if we used 'anonymous'
              attemptLoad(crossOriginValue === 'anonymous' ? null : 'anonymous');
            } else {
              resolve(null);
            }
          }
        } catch (canvasError) {
          console.warn('Error creating canvas for image:', canvasError);
          resolve(null);
        }
      };
      
      img.onerror = (err) => {
        clearTimeout(timeoutId);
        console.warn('Error loading image:', err);
        
        // If this is the first attempt, try again with a different crossOrigin value
        if (!corsRetryAttempted) {
          corsRetryAttempted = true;
          console.log('Retrying with different CORS setting after error');
          // Try with 'anonymous' if we used something else, or with null if we used 'anonymous'
          attemptLoad(crossOriginValue === 'anonymous' ? null : 'anonymous');
        } else {
          resolve(null);
        }
      };
      
      img.src = url;
    };
    
    // Start with 'Anonymous'
    attemptLoad('anonymous');
  });
};

/**
 * Generate a PDF report for crop or soil analysis
 * @param {Object} analysis - The analysis data
 * @param {string} imageUrl - URL of the analyzed image
 * @returns {Promise<Blob>} - PDF file as a Blob
 */
const generateAnalysisPDF = async (analysis, imageUrl) => {
  try {
    // Create a new PDF document with autoTable support
    const doc = createPdf();
    
    // Set document properties
    doc.setProperties({
      title: `${analysis.identifiedAs || 'Crop'} Analysis Report`,
      subject: 'Agricultural Analysis',
      author: 'Agricare by Quamin Tech',
      creator: 'Agricare App',
    });
    
    // Define colors based on Agricare theme
    const primaryColor = [39, 174, 96]; // Green in RGB
    const secondaryColor = [52, 73, 94]; // Dark blue in RGB
    const accentColor = [41, 128, 185]; // Light blue in RGB
    
    // Add header with logo
    const logoData = await getAppLogo();
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    
    // Header background
    doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    doc.rect(0, 0, pageWidth, 30, 'F');
    
    // Add logo if available
    if (logoData) {
      doc.addImage(logoData, 'PNG', 10, 5, 20, 20);
    }
    
    // Add title
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(255, 255, 255); // White text
    doc.setFontSize(22);
    doc.text('Agricare', 35, 15);
    doc.setFontSize(14);
    doc.text('Powered by Quamin Tech', 35, 22);
    
    // Add report title
    doc.setFontSize(18);
    doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
    doc.text(`${analysis.identifiedAs || 'Crop'} Analysis Report`, pageWidth / 2, 40, { align: 'center' });
    
    // Add date
    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    doc.setFontSize(12);
    doc.text(`Generated on: ${currentDate}`, pageWidth - 15, 50, { align: 'right' });
    
    // Add image if available
    if (imageUrl) {
      try {
        const capturedImage = await captureImage(imageUrl);
        
        // Calculate dimensions to maintain aspect ratio
        const imgWidth = 100;
        const imgHeight = 80;
        
        if (capturedImage) {
          try {
            // Add the image
            doc.addImage(capturedImage, 'PNG', (pageWidth - imgWidth) / 2, 55, imgWidth, imgHeight);
            
            // Add image caption
            doc.setFontSize(10);
            doc.setTextColor(100, 100, 100); // Gray text
            doc.text(`Image of ${analysis.identifiedAs || 'analyzed crop/soil'}`, pageWidth / 2, 140, { align: 'center' });
          } catch (imgError) {
            console.warn('Error adding image to PDF document:', imgError);
            
            // Try using a placeholder image
            const placeholderImage = getPlaceholderImage(`${analysis.identifiedAs || 'Crop'} Analysis`);
            if (placeholderImage) {
              doc.addImage(placeholderImage, 'PNG', (pageWidth - imgWidth) / 2, 55, imgWidth, imgHeight);
            } else {
              // Add a placeholder text instead
              doc.setFontSize(12);
              doc.setTextColor(100, 100, 100);
              doc.text('Image could not be displayed', pageWidth / 2, 95, { align: 'center' });
              doc.setFontSize(10);
              doc.text(`Analysis of ${analysis.identifiedAs || 'crop/soil'}`, pageWidth / 2, 105, { align: 'center' });
            }
          }
        } else {
          // Try using a placeholder image
          const placeholderImage = getPlaceholderImage(`${analysis.identifiedAs || 'Crop'} Analysis`);
          if (placeholderImage) {
            doc.addImage(placeholderImage, 'PNG', (pageWidth - imgWidth) / 2, 55, imgWidth, imgHeight);
          } else {
            // Add a placeholder text if image capture failed
            doc.setFontSize(12);
            doc.setTextColor(100, 100, 100);
            doc.text('Image could not be displayed', pageWidth / 2, 95, { align: 'center' });
            doc.setFontSize(10);
            doc.text(`Analysis of ${analysis.identifiedAs || 'crop/soil'}`, pageWidth / 2, 105, { align: 'center' });
          }
        }
      } catch (error) {
        console.warn('Error processing image for PDF:', error);
        
        // Try using a placeholder image
        const placeholderImage = getPlaceholderImage(`${analysis.identifiedAs || 'Crop'} Analysis`);
        if (placeholderImage) {
          const imgWidth = 100;
          const imgHeight = 80;
          doc.addImage(placeholderImage, 'PNG', (pageWidth - imgWidth) / 2, 55, imgWidth, imgHeight);
        } else {
          // Add a placeholder text
          doc.setFontSize(12);
          doc.setTextColor(100, 100, 100);
          doc.text('Image could not be displayed', pageWidth / 2, 95, { align: 'center' });
          doc.setFontSize(10);
          doc.text(`Analysis of ${analysis.identifiedAs || 'crop/soil'}`, pageWidth / 2, 105, { align: 'center' });
        }
      }
    }
    
    // Add basic information section
    let yPos = imageUrl ? 150 : 60;
    
    doc.setFillColor(accentColor[0], accentColor[1], accentColor[2]);
    doc.rect(10, yPos, pageWidth - 20, 10, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(14);
    doc.text('Basic Information', 15, yPos + 7);
    
    yPos += 15;
    doc.setTextColor(0, 0, 0);
    doc.setFontSize(12);
    
    // Add basic info table
    const basicInfoData = [
      ['Type', analysis.identifiedAs || 'N/A'],
      ['Health Status', analysis.healthStatus || 'N/A'],
      ['Confidence Level', analysis.confidence ? `${Math.round(analysis.confidence * 100)}%` : 'N/A'],
    ];
    
    doc.autoTable({
      startY: yPos,
      head: [['Property', 'Value']],
      body: basicInfoData,
      theme: 'grid',
      headStyles: {
        fillColor: primaryColor,
        textColor: [255, 255, 255],
        fontStyle: 'bold',
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240],
      },
      margin: { left: 10, right: 10 },
    });
    
    yPos = doc.lastAutoTable.finalY + 10;
    
    // Add issues section if available
    if (analysis.issues && analysis.issues.length > 0) {
      doc.setFillColor(accentColor[0], accentColor[1], accentColor[2]);
      doc.rect(10, yPos, pageWidth - 20, 10, 'F');
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(14);
      doc.text('Issues Detected', 15, yPos + 7);
      
      yPos += 15;
      doc.setTextColor(0, 0, 0);
      doc.setFontSize(12);
      
      const issuesData = analysis.issues.map(issue => [issue]);
      
      doc.autoTable({
        startY: yPos,
        head: [['Issue Description']],
        body: issuesData,
        theme: 'grid',
        headStyles: {
          fillColor: primaryColor,
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240],
        },
        margin: { left: 10, right: 10 },
      });
      
      yPos = doc.lastAutoTable.finalY + 10;
    } else {
      doc.setFillColor(accentColor[0], accentColor[1], accentColor[2]);
      doc.rect(10, yPos, pageWidth - 20, 10, 'F');
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(14);
      doc.text('Issues Detected', 15, yPos + 7);
      
      yPos += 15;
      doc.setTextColor(0, 0, 0);
      doc.setFontSize(12);
      doc.text('No issues detected', 15, yPos);
      
      yPos += 10;
    }
    
    // Add recommendations section if available
    if (analysis.recommendations && analysis.recommendations.length > 0) {
      // Check if we need a new page
      if (yPos > pageHeight - 60) {
        doc.addPage();
        yPos = 20;
      }
      
      doc.setFillColor(accentColor[0], accentColor[1], accentColor[2]);
      doc.rect(10, yPos, pageWidth - 20, 10, 'F');
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(14);
      doc.text('Recommendations', 15, yPos + 7);
      
      yPos += 15;
      doc.setTextColor(0, 0, 0);
      doc.setFontSize(12);
      
      const recommendationsData = analysis.recommendations.map(rec => [rec]);
      
      doc.autoTable({
        startY: yPos,
        head: [['Recommendation']],
        body: recommendationsData,
        theme: 'grid',
        headStyles: {
          fillColor: primaryColor,
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240],
        },
        margin: { left: 10, right: 10 },
      });
      
      yPos = doc.lastAutoTable.finalY + 10;
    }
    
    // Add detailed analysis section based on crop or soil type
    if (yPos > pageHeight - 60) {
      doc.addPage();
      yPos = 20;
    }
    
    doc.setFillColor(accentColor[0], accentColor[1], accentColor[2]);
    doc.rect(10, yPos, pageWidth - 20, 10, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(14);
    doc.text('Detailed Analysis', 15, yPos + 7);
    
    yPos += 15;
    doc.setTextColor(0, 0, 0);
    doc.setFontSize(12);
    
    // Check if this is soil analysis
    const isSoil = analysis.identifiedAs?.toLowerCase().includes('soil');
    
    if (isSoil) {
      // Soil composition analysis
      const soilCompositionData = [
        ['Texture', `${analysis.identifiedAs || 'Loamy'} - balanced mixture of sand, silt, and clay particles`],
        ['Structure', 'Moderately aggregated with medium crumb structure'],
        ['Porosity', 'Moderate (approximately 45-55%)'],
        ['Water retention', 'Medium capacity (holds approximately 1.5-2 inches of water per foot)'],
      ];
      
      doc.autoTable({
        startY: yPos,
        head: [['Soil Composition Analysis', '']],
        body: soilCompositionData,
        theme: 'grid',
        headStyles: {
          fillColor: primaryColor,
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240],
        },
        margin: { left: 10, right: 10 },
      });
      
      yPos = doc.lastAutoTable.finalY + 10;
      
      // Check if we need a new page
      if (yPos > pageHeight - 60) {
        doc.addPage();
        yPos = 20;
      }
      
      // Nutrient content
      const healthStatus = analysis.healthStatus?.toLowerCase() || 'moderate';
      let organicMatter, nitrogen, phosphorus, potassium;
      
      if (healthStatus === 'good' || healthStatus === 'excellent') {
        organicMatter = 'Adequate (3-5%)';
        nitrogen = 'Good availability';
        phosphorus = 'Good availability';
        potassium = 'Good availability';
      } else if (healthStatus === 'fair' || healthStatus === 'moderate') {
        organicMatter = 'Low to moderate (1-3%)';
        nitrogen = 'Low to moderate availability';
        phosphorus = 'Moderate availability';
        potassium = 'Moderate to high availability';
      } else {
        organicMatter = 'Very low (<1%)';
        nitrogen = 'Deficient';
        phosphorus = 'Low availability';
        potassium = 'Low availability';
      }
      
      const nutrientData = [
        ['Organic Matter', organicMatter],
        ['Nitrogen (N)', nitrogen],
        ['Phosphorus (P)', phosphorus],
        ['Potassium (K)', potassium],
        ['Calcium (Ca)', 'Adequate levels'],
        ['Magnesium (Mg)', 'Adequate levels'],
      ];
      
      doc.autoTable({
        startY: yPos,
        head: [['Estimated Nutrient Content', '']],
        body: nutrientData,
        theme: 'grid',
        headStyles: {
          fillColor: primaryColor,
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240],
        },
        margin: { left: 10, right: 10 },
      });
      
      yPos = doc.lastAutoTable.finalY + 10;
      
      // Check if we need a new page
      if (yPos > pageHeight - 60) {
        doc.addPage();
        yPos = 20;
      }
      
      // Agricultural potential
      let cropSuitability, irrigationNeeds, fertilityManagement;
      
      if (healthStatus === 'good' || healthStatus === 'excellent') {
        cropSuitability = 'Well-suited for a wide range of crops including corn, wheat, soybeans, vegetables, and many fruit trees';
        irrigationNeeds = 'Low to moderate - efficient water use with good retention';
        fertilityManagement = 'Maintenance fertilization sufficient; focus on sustaining organic matter';
      } else if (healthStatus === 'fair' || healthStatus === 'moderate') {
        cropSuitability = 'Well-suited for a wide range of crops including corn, wheat, soybeans, vegetables, and many fruit trees';
        irrigationNeeds = 'Moderate - requires regular irrigation during dry periods';
        fertilityManagement = 'Will respond well to organic matter additions and balanced fertilization';
      } else {
        cropSuitability = 'Limited crop selection recommended until soil health improves';
        irrigationNeeds = 'High - careful irrigation management required';
        fertilityManagement = 'Requires comprehensive soil building program with organic amendments and balanced nutrients';
      }
      
      const agriculturalData = [
        ['Crop suitability', cropSuitability],
        ['Irrigation needs', irrigationNeeds],
        ['Drainage', 'Good natural drainage with moderate water infiltration rate'],
        ['Fertility management', fertilityManagement],
      ];
      
      doc.autoTable({
        startY: yPos,
        head: [['Agricultural Potential', '']],
        body: agriculturalData,
        theme: 'grid',
        headStyles: {
          fillColor: primaryColor,
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240],
        },
        margin: { left: 10, right: 10 },
      });
    } else {
      // Crop health analysis
      const healthStatus = analysis.healthStatus?.toLowerCase() || 'moderate';
      let leafColor, canopyDensity, stressIndicators;
      
      if (healthStatus === 'good' || healthStatus === 'excellent') {
        leafColor = 'Deep green, indicating adequate nitrogen';
        canopyDensity = 'Optimal for current growth stage';
        stressIndicators = 'Minimal visible stress';
      } else {
        leafColor = 'Slightly pale, suggesting possible nutrient deficiency';
        canopyDensity = 'Below optimal for current growth stage';
        stressIndicators = analysis.issues?.length > 0 ? 'Present - see Issues Detected section' : 'Some stress indicators visible';
      }
      
      const cropHealthData = [
        ['Growth stage', analysis.growthStage || 'Mid-season vegetative growth'],
        ['Leaf color', leafColor],
        ['Canopy density', canopyDensity],
        ['Stress indicators', stressIndicators],
      ];
      
      doc.autoTable({
        startY: yPos,
        head: [['Crop Health Analysis', '']],
        body: cropHealthData,
        theme: 'grid',
        headStyles: {
          fillColor: primaryColor,
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240],
        },
        margin: { left: 10, right: 10 },
      });
      
      yPos = doc.lastAutoTable.finalY + 10;
      
      // Check if we need a new page
      if (yPos > pageHeight - 60) {
        doc.addPage();
        yPos = 20;
      }
      
      // Yield potential
      let trajectory, limitingFactors;
      
      if (healthStatus === 'good' || healthStatus === 'excellent') {
        trajectory = 'On track for optimal yield';
        limitingFactors = 'None significant detected';
      } else if (healthStatus === 'fair' || healthStatus === 'moderate') {
        trajectory = 'Moderate yield potential with proper management';
        limitingFactors = analysis.issues?.length > 0 ? analysis.issues[0] : 'Some limiting factors present';
      } else {
        trajectory = 'Below average yield potential without intervention';
        limitingFactors = analysis.issues?.length > 0 ? analysis.issues[0] : 'Significant limiting factors present';
      }
      
      const yieldData = [
        ['Current trajectory', trajectory],
        ['Limiting factors', limitingFactors],
        ['Improvement potential', 'High - implementing recommended actions could significantly improve outcomes'],
      ];
      
      doc.autoTable({
        startY: yPos,
        head: [['Estimated Yield Potential', '']],
        body: yieldData,
        theme: 'grid',
        headStyles: {
          fillColor: primaryColor,
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240],
        },
        margin: { left: 10, right: 10 },
      });
      
      // Check if we need a new page
      if (yPos > pageHeight - 100) {
        doc.addPage();
        yPos = 20;
      } else {
        yPos = doc.lastAutoTable.finalY + 10;
      }
      
      // Detailed pest & disease assessment
      if (analysis.issues && analysis.issues.length > 0) {
        const diseaseData = analysis.issues.map(issue => {
          const severity = issue.toLowerCase().includes('severe') ? 'High' : 
                          issue.toLowerCase().includes('moderate') ? 'Moderate' : 'Low';
          const treatment = issue.toLowerCase().includes('blight') ? 'Apply copper-based fungicide' :
                           issue.toLowerCase().includes('rust') ? 'Apply sulfur-based fungicide' :
                           issue.toLowerCase().includes('wilt') ? 'Improve drainage and reduce irrigation' :
                           'Follow integrated pest management practices';
          
          return [issue, severity, treatment];
        });
        
        doc.autoTable({
          startY: yPos,
          head: [['Pest/Disease', 'Severity', 'Treatment Approach']],
          body: diseaseData,
          theme: 'grid',
          headStyles: {
            fillColor: primaryColor,
            textColor: [255, 255, 255],
            fontStyle: 'bold',
          },
          alternateRowStyles: {
            fillColor: [240, 240, 240],
          },
          margin: { left: 10, right: 10 },
        });
        
        yPos = doc.lastAutoTable.finalY + 10;
      }
      
      // Add Agricultural Practices section if available
      if (!isSoil) {
        // Always add a new page for agricultural practices
        doc.addPage();
        yPos = 20;
        
        doc.setFillColor(accentColor[0], accentColor[1], accentColor[2]);
        doc.rect(10, yPos, pageWidth - 20, 10, 'F');
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(14);
        doc.text('Agricultural Practices', 15, yPos + 7);
        
        yPos += 15;
        
        // Define default practices based on crop type and health status
        const healthStatus = analysis.healthStatus?.toLowerCase() || 'moderate';
        const cropName = analysis.identifiedAs || 'crop';
        
        // Check if agriculturalPractices are available in the analysis
        const hasApiPractices = analysis.agriculturalPractices && 
          (analysis.agriculturalPractices.cultural?.length > 0 || 
           analysis.agriculturalPractices.mechanical?.length > 0 || 
           analysis.agriculturalPractices.biological?.length > 0 || 
           analysis.agriculturalPractices.chemical?.length > 0);
        
        // 1. Cultural Practices
        const culturalPractices = hasApiPractices && analysis.agriculturalPractices.cultural?.length > 0 ? 
          analysis.agriculturalPractices.cultural : [
            `Crop rotation: Rotate ${cropName} with legumes or non-host crops to break pest cycles.`,
            `Row spacing: Optimal spacing for ${cropName} is typically 75-90 cm between rows.`,
            `Mulching: Apply organic mulch to conserve moisture and suppress weeds.`,
            `Intercropping: Consider companion planting with pest-repelling plants.`
        ];
        
        doc.autoTable({
          startY: yPos,
          head: [['Cultural Practices']],
          body: culturalPractices.map(practice => [practice]),
          theme: 'grid',
          headStyles: {
            fillColor: primaryColor,
            textColor: [255, 255, 255],
            fontStyle: 'bold',
          },
          alternateRowStyles: {
            fillColor: [240, 240, 240],
          },
          margin: { left: 10, right: 10 },
        });
        
        yPos = doc.lastAutoTable.finalY + 10;
        
        // Check if we need a new page
        if (yPos > pageHeight - 60) {
          doc.addPage();
          yPos = 20;
        }
        
        // 2. Mechanical Practices
        const mechanicalPractices = hasApiPractices && analysis.agriculturalPractices.mechanical?.length > 0 ? 
          analysis.agriculturalPractices.mechanical : [
            `Tillage: ${healthStatus === 'poor' ? 'Deep' : 'Minimum'} tillage recommended based on current soil conditions.`,
            `Hand weeding: Regular weeding helps reduce competition for nutrients.`,
            `Trellising: ${cropName === 'tomato' || cropName === 'cucumber' ? 'Essential for proper growth and fruit development.' : 'Not typically required for this crop.'}`,
            `Pruning: ${cropName === 'tomato' || cropName === 'grape' ? 'Regular pruning helps improve air circulation and sunlight exposure.' : 'Limited pruning needed, focus on removing damaged parts.'}`
        ];
        
        doc.autoTable({
          startY: yPos,
          head: [['Mechanical Practices']],
          body: mechanicalPractices.map(practice => [practice]),
          theme: 'grid',
          headStyles: {
            fillColor: primaryColor,
            textColor: [255, 255, 255],
            fontStyle: 'bold',
          },
          alternateRowStyles: {
            fillColor: [240, 240, 240],
          },
          margin: { left: 10, right: 10 },
        });
        
        yPos = doc.lastAutoTable.finalY + 10;
        
        // Check if we need a new page
        if (yPos > pageHeight - 60) {
          doc.addPage();
          yPos = 20;
        }
        
        // 3. Biological Practices
        const biologicalPractices = hasApiPractices && analysis.agriculturalPractices.biological?.length > 0 ? 
          analysis.agriculturalPractices.biological : [
            `Beneficial insects: Introduce ladybugs and lacewings to control aphids${analysis.issues?.some(i => i.toLowerCase().includes('aphid')) ? ' (highly recommended based on current issues)' : '.'}`,
            `Microbial inoculants: Apply rhizobacteria to improve nutrient uptake and root health.`,
            `Nematode control: Use nematode-suppressive cover crops in rotation.`,
            `Trap crops: Plant ${cropName === 'cotton' ? 'okra as trap crop for bollworms' : 'mustard as trap crop for pest diversion'}.`
        ];
        
        doc.autoTable({
          startY: yPos,
          head: [['Biological Practices']],
          body: biologicalPractices.map(practice => [practice]),
          theme: 'grid',
          headStyles: {
            fillColor: primaryColor,
            textColor: [255, 255, 255],
            fontStyle: 'bold',
          },
          alternateRowStyles: {
            fillColor: [240, 240, 240],
          },
          margin: { left: 10, right: 10 },
        });
        
        yPos = doc.lastAutoTable.finalY + 10;
        
        // Check if we need a new page
        if (yPos > pageHeight - 60) {
          doc.addPage();
          yPos = 20;
        }
        
        // 4. Chemical Practices
        const hasDeficiency = analysis.issues?.some(i => i.toLowerCase().includes('deficiency'));
        const hasFungalDisease = analysis.issues?.some(i => i.toLowerCase().includes('fungal') || i.toLowerCase().includes('disease'));
        
        const chemicalPractices = hasApiPractices && analysis.agriculturalPractices.chemical?.length > 0 ? 
          analysis.agriculturalPractices.chemical : [
            `Fertilization: ${hasDeficiency ? 'Apply balanced NPK with focus on deficient nutrients' : 'Balanced NPK application based on soil test results'}.`,
            `Fungicide: ${hasFungalDisease ? 'Apply copper-based fungicide at recommended rates' : 'Preventive application before rainy season'}.`,
            `pH management: Maintain soil pH between 6.0-7.0 for optimal nutrient availability.`,
            `Integrated approach: Combine chemical controls with other management practices for sustainable pest management.`
        ];
        
        doc.autoTable({
          startY: yPos,
          head: [['Chemical Practices']],
          body: chemicalPractices.map(practice => [practice]),
          theme: 'grid',
          headStyles: {
            fillColor: primaryColor,
            textColor: [255, 255, 255],
            fontStyle: 'bold',
          },
          alternateRowStyles: {
            fillColor: [240, 240, 240],
          },
          margin: { left: 10, right: 10 },
        });
        
        yPos = doc.lastAutoTable.finalY + 10;
      }
    }
    
    // Add footer
    const footerText = 'This report was generated by Agricare - Powered by Quamin Tech';
    
    // Add footer to all pages
    const totalPages = doc.internal.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);
      
      // Footer line
      doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2]);
      doc.line(10, pageHeight - 20, pageWidth - 10, pageHeight - 20);
      
      // Footer text
      doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
      doc.setFontSize(10);
      doc.text(footerText, pageWidth / 2, pageHeight - 10, { align: 'center' });
      
      // Page number
      doc.text(`Page ${i} of ${totalPages}`, pageWidth - 15, pageHeight - 10, { align: 'right' });
    }
    
    // Return the PDF as a blob
    return doc.output('blob');
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};

export default {
  generateAnalysisPDF,
}; 