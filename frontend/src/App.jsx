import React from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { AuthProvider } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import Routes from './Routes';
import TranslationPreloader from './Components/TranslationPreloader/TranslationPreloader';
import "./App.css";

const theme = createTheme({
  palette: {
    primary: {
      main: '#2e7d32',
    },
    secondary: {
      main: '#1b5e20',
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <LanguageProvider>
          <Routes />
          <TranslationPreloader />
        </LanguageProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;

