import React, { createContext, useContext, useState, useEffect } from "react";
import axios from "axios";
import { API_BASE_URL } from "../config/api";

const AuthContext = createContext();

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [otpSent, setOtpSent] = useState(false);
  const [selectedRole, setSelectedRole] = useState(null);

  useEffect(() => {
    // Check if user is logged in
    const checkAuth = async () => {
      try {
        const userData = localStorage.getItem("user");
        const token = localStorage.getItem("token");

        if (userData && token) {
          const user = JSON.parse(userData);
          // Validate token format
          if (typeof token !== "string" || !token.trim()) {
            throw new Error("Invalid token format");
          }
          // Set user data with token
          setCurrentUser({ ...user, token });
        } else {
          // Clear any partial data
          localStorage.removeItem("user");
          localStorage.removeItem("token");
          setCurrentUser(null);
        }
      } catch (error) {
        console.error("Auth check failed:", error);
        localStorage.removeItem("user");
        localStorage.removeItem("token");
        setCurrentUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const sendOTP = async (phoneNumber, role) => {
    try {
      // Normalize role to handle both 'TM' and 'TMManager'
      const normalizedRole = role === "TMManager" ? "TM" : role;

      if (!normalizedRole || !["Farmer", "TM"].includes(normalizedRole)) {
        throw new Error(
          "Invalid role. Must be either Farmer or Territory Manager"
        );
      }

      // Configure axios with timeout for mobile networks
      const axiosConfig = {
        timeout: 15000, // 15 seconds timeout
        // Don't add custom headers that might trigger CORS issues
      };

      // Store the role for later use
      setSelectedRole(normalizedRole);

      try {
        // Verify the user exists with the selected role
        const verifyEndpoint =
          normalizedRole === "Farmer" ? "/farmers/verify" : "/tm/verify";
        console.log(
          "Verifying user with endpoint:",
          `${API_BASE_URL}${verifyEndpoint}`
        );
        phoneNumber = phoneNumber.replace(/^(\+91)/, "");
        console.log("Phone number:", phoneNumber);
        console.log("Role:", normalizedRole);

        try {
          const verifyResponse = await axios.post(
            `${API_BASE_URL}${verifyEndpoint}`,
            {
              phoneNumber,
            },
            axiosConfig
          );
          console.log("Verify response:", verifyResponse.data);
        } catch (verifyError) {
          console.error(
            "Verify error:",
            verifyError.response
              ? verifyError.response.data
              : verifyError.message
          );
          throw verifyError;
        }

        // Send OTP using our custom backend service
        const otpResponse = await axios.post(
          `${API_BASE_URL}/auth/send-otp`,
          {
            phoneNumber,
            role: normalizedRole,
          },
          axiosConfig
        );

        if (!otpResponse.data.success) {
          throw new Error(otpResponse.data.message || "Failed to send OTP");
        }

        console.log("OTP sent successfully");
        setOtpSent(true);
        return true;
      } catch (sendError) {
        console.error("OTP sending error:", sendError);
        if (
          sendError.message.includes("Network Error") ||
          sendError.code === "ECONNABORTED"
        ) {
          throw new Error(
            "Network connection issue. Please check your internet connection and try again."
          );
        }
        throw new Error(
          sendError.response?.data?.message ||
            "Failed to send OTP. Please try again."
        );
      }
    } catch (error) {
      console.error("Failed to send OTP:", error);
      throw error;
    }
  };

  // New method for Firebase verification
  const verifyFirebaseAuth = async (phoneNumber, role, firebaseUid) => {
    try {
      console.log("Starting Firebase verification for:", {
        phoneNumber,
        role,
        firebaseUid
      });

      // Configure axios with timeout for mobile networks
      const axiosConfig = {
        timeout: 15000, // 15 seconds timeout
      };

      // Send verification to backend
      const response = await axios.post(
        `${API_BASE_URL}/auth/verify-firebase`,
        {
          phoneNumber,
          role,
          firebaseUid
        },
        axiosConfig
      );

      if (!response.data.success) {
        throw new Error(response.data.message || "Firebase verification failed");
      }

      if (!response.data.data || !response.data.data.user) {
        throw new Error("Invalid response format from server");
      }

      // Generate a client-side token
      const timestamp = Date.now();
      const token = btoa(`${phoneNumber}:${role}:${timestamp}`);

      const userData = {
        id: response.data.data.user.id,
        name: response.data.data.user.name,
        mobile: response.data.data.user.phoneNumber,
        role: response.data.data.user.role,
        token,
        tokenTimestamp: timestamp,
      };

      console.log("Setting user data with generated token");
      localStorage.setItem("token", token);
      localStorage.setItem("user", JSON.stringify(userData));
      setCurrentUser(userData);
      setOtpSent(false);
      setSelectedRole(null);
      return userData;
    } catch (error) {
      console.error("Firebase verification failed:", error);
      // Clear any partial data
      localStorage.removeItem("user");
      localStorage.removeItem("token");
      setCurrentUser(null);
      throw error;
    }
  };

  const verifyOTP = async (phoneNumber, otp, role, isFirebaseVerified = false, firebaseUid = null) => {
    try {
      if (!role) {
        throw new Error("Role not selected. Please select a role first.");
      }

      console.log("Starting verification process for:", {
        phoneNumber,
        role: role,
        isFirebaseVerified
      });

      // Special case for test phone number 9611966747
      if (phoneNumber.includes("9611966747") && otp === "123456") {
        console.log(
          "Using test phone number with fixed OTP, bypassing verification"
        );

        // Create a user object for Manish Kumar with proper ID
        const mockUser = {
          id: "67dbeb95072698d242a3f415", // Use a valid MongoDB ObjectId
          name: "Manish Kumar",
          mobile: "9611966747",
          phoneNumber: "9611966747",
          role: role,
          preferredLanguage: "en",
        };

        // Generate a client-side token
        const timestamp = Date.now();
        const token = btoa(`9611966747:${role}:${timestamp}`);

        // Store user data in localStorage
        const userData = {
          ...mockUser,
          token,
          tokenTimestamp: timestamp,
        };

        console.log("Setting mock user data:", userData);
        localStorage.setItem("token", token);
        localStorage.setItem("user", JSON.stringify(userData));
        setCurrentUser(userData);
        setOtpSent(false);
        setSelectedRole(null);

        return mockUser;
      }

      // If Firebase verified, use the Firebase verification endpoint
      if (isFirebaseVerified) {
        return verifyFirebaseAuth(phoneNumber, role, firebaseUid);
      }

      // Configure axios with timeout for mobile networks
      const axiosConfig = {
        timeout: 15000, // 15 seconds timeout
        // Don't add custom headers that might trigger CORS issues
      };

      // Extract country code and phone number
      let countryCode = "+91"; // Default country code
      let normalizedPhone = phoneNumber;
      
      // If phone number starts with +, extract the country code
      if (phoneNumber.startsWith('+')) {
        const matches = phoneNumber.match(/^(\+\d+)(\d{10})$/);
        if (matches && matches.length === 3) {
          countryCode = matches[1];
          normalizedPhone = matches[2];
        } else {
          // If no match, assume the last 10 digits are the phone number
          normalizedPhone = phoneNumber.replace(/\D/g, "").slice(-10);
        }
      } else if (phoneNumber.length > 10) {
        // Handle case where country code might be without +
        normalizedPhone = phoneNumber.replace(/\D/g, "").slice(-10);
        countryCode = "+" + phoneNumber.replace(/\D/g, "").slice(0, -10);
        if (countryCode === "+") countryCode = "+91"; // Default if empty
      } else {
        // Just a 10-digit number, use default country code
        normalizedPhone = phoneNumber;
      }

      // First verify the user exists
      const verifyEndpoint =
        role === "Farmer" ? "/farmers/verify" : "/tm/verify";
      console.log("Verifying user at endpoint:", verifyEndpoint);
      console.log("Phone Number in verify: ", normalizedPhone);

      // Try to verify user with improved error handling
      let verifyResponse;
      try {
        verifyResponse = await axios.post(
          `${API_BASE_URL}${verifyEndpoint}`,
          {
            phoneNumber: normalizedPhone,
          },
          axiosConfig
        );
      } catch (verifyError) {
        console.error("User verification network error:", verifyError);

        // Special case for test phone number 9611966747 even if verification fails
        if (phoneNumber.includes("9611966747") && otp === "123456") {
          return verifyOTP(phoneNumber, otp, role);
        }

        if (
          verifyError.message.includes("Network Error") ||
          verifyError.code === "ECONNABORTED"
        ) {
          throw new Error(
            "Network connection issue. Please check your internet connection and try again."
          );
        }
        throw new Error(
          verifyError.response?.data?.message ||
            "User verification failed. Please try again."
        );
      }

      console.log("User verification response:", verifyResponse.data);

      if (!verifyResponse.data.success) {
        throw new Error(
          verifyResponse.data.message || "User verification failed"
        );
      }

      // Then verify the OTP
      console.log("Verifying OTP with role:", role);
      let otpResponse;
      try {
        otpResponse = await axios.post(
          `${API_BASE_URL}/auth/verify-otp`,
          {
            phoneNumber: normalizedPhone,
            countryCode: countryCode,
            otp,
            role: role,
          },
          axiosConfig
        );

        console.log("Full OTP response:", {
          success: otpResponse.data.success,
          hasData: !!otpResponse.data.data,
          hasUser: !!otpResponse.data.data?.user,
          responseStructure: JSON.stringify(otpResponse.data, null, 2),
        });
      } catch (otpError) {
        console.error("OTP verification network error:", otpError);
        if (
          otpError.message.includes("Network Error") ||
          otpError.code === "ECONNABORTED"
        ) {
          throw new Error(
            "Network connection issue during OTP verification. Please check your internet connection and try again."
          );
        }

        // Handle specific error cases
        if (
          otpError.response?.status === 400 &&
          otpError.response?.data?.message?.includes("already registered")
        ) {
          throw new Error(
            "This phone number is already registered. Please try logging in with a different role or contact support."
          );
        }

        throw new Error(
          otpError.response?.data?.message ||
            "OTP verification failed. Please try again."
        );
      }

      if (!otpResponse.data.success) {
        throw new Error(otpResponse.data.message || "OTP verification failed");
      }

      if (!otpResponse.data.data || !otpResponse.data.data.user) {
        throw new Error("Invalid response format from server");
      }

      // Generate a client-side token since server doesn't provide one
      const timestamp = Date.now();
      const token = btoa(`${normalizedPhone}:${role}:${timestamp}`);

      const userData = {
        id: otpResponse.data.data.user.id,
        name: otpResponse.data.data.user.name,
        mobile: otpResponse.data.data.user.phoneNumber,
        role: otpResponse.data.data.user.role,
        token,
        tokenTimestamp: timestamp,
      };

      console.log("Setting user data with generated token");
      localStorage.setItem("token", token);
      localStorage.setItem("user", JSON.stringify(userData));
      setCurrentUser(userData);
      setOtpSent(false);
      setSelectedRole(null);
      return userData;
    } catch (error) {
      console.error("Login failed:", error);
      // Clear any partial data
      localStorage.removeItem("user");
      localStorage.removeItem("token");
      setCurrentUser(null);
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem("user");
    localStorage.removeItem("token");
    setCurrentUser(null);
    setOtpSent(false);
    setSelectedRole(null);
  };

  const signup = async (userData) => {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/auth/signup`,
        userData
      );

      if (response.data.success) {
        const newUser = response.data.data.user;
        localStorage.setItem("user", JSON.stringify(newUser));
        setCurrentUser(newUser);
        return newUser;
      } else {
        throw new Error(response.data.message || "Failed to create account");
      }
    } catch (error) {
      console.error("Signup failed:", error);
      throw error;
    }
  };

  const login = async (userData) => {
    try {
      // Generate a client-side token
      const timestamp = Date.now();
      const token = btoa(`${userData.email}:${userData.role}:${timestamp}`);

      // Add token to user data
      const userWithToken = {
        ...userData,
        token,
        tokenTimestamp: timestamp,
      };

      // Save to localStorage
      localStorage.setItem("token", token);
      localStorage.setItem("user", JSON.stringify(userWithToken));

      // Update state
      setCurrentUser(userWithToken);
      return userWithToken;
    } catch (error) {
      console.error("Login failed:", error);
      // Clear any partial data
      localStorage.removeItem("user");
      localStorage.removeItem("token");
      setCurrentUser(null);
      throw error;
    }
  };

  const getToken = () => {
    try {
      // First try to get token from currentUser
      if (currentUser?.token) {
        // Check if token is expired (24 hours)
        const tokenTimestamp = currentUser.tokenTimestamp;
        if (
          tokenTimestamp &&
          Date.now() - tokenTimestamp < 24 * 60 * 60 * 1000
        ) {
          return currentUser.token;
        }
      }

      // Then try localStorage
      const token = localStorage.getItem("token");
      const userData = localStorage.getItem("user");
      if (token && userData) {
        try {
          const user = JSON.parse(userData);
          if (
            user.tokenTimestamp &&
            Date.now() - user.tokenTimestamp < 24 * 60 * 60 * 1000
          ) {
            return token;
          }
        } catch (e) {
          console.error("Error parsing user data:", e);
        }
      }

      // If token is expired or invalid, clear storage and return null
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      setCurrentUser(null);
      return null;
    } catch (error) {
      console.error("Error getting token:", error);
      return null;
    }
  };

  const value = {
    currentUser,
    loading,
    otpSent,
    selectedRole,
    sendOTP,
    verifyOTP,
    verifyFirebaseAuth,
    logout,
    signup,
    login,
    getToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}
