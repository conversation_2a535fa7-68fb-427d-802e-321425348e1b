import React, { createContext, useContext, useState, useEffect } from "react";
import i18n from "../translations/i18n/i18n";
import translationPreloader from "../services/TranslationPreloader";

const LanguageContext = createContext();

export const LanguageProvider = ({ children }) => {
  const [selectedLanguage, setSelectedLanguage] = useState(() => {
    const storedLanguage = localStorage.getItem("selectedLanguage");
    // Migration: convert old 'en' to 'en-IN'
    if (storedLanguage === "en") {
      localStorage.setItem("selectedLanguage", "en-IN");
      return "en-IN";
    }
    return storedLanguage || "en-IN";
  });

  useEffect(() => {
    localStorage.setItem("selectedLanguage", selectedLanguage);
    i18n.changeLanguage(selectedLanguage);
    
    // Clear global cache when language changes
    translationPreloader.clearCache();
    
    // Start background preloading for non-English languages
    if (selectedLanguage !== 'en-IN' && selectedLanguage !== 'en') {
      // Start preloading in the background without blocking the UI
      translationPreloader.startPreloading(selectedLanguage).catch(error => {
        console.warn('Background preloading failed:', error);
      });
    }
  }, [selectedLanguage]);

  return (
    <LanguageContext.Provider value={{ selectedLanguage, setSelectedLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
};
