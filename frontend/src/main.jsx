import React from "react";
import { createRoot } from "react-dom/client";
import { <PERSON>rowserRouter } from "react-router-dom";
import App from "./App";
import "./index.css";
import "./translations/i18n/i18n";

// Global error handler
window.onerror = function (message, source, lineno, colno, error) {
  console.error("Global error:", { message, source, lineno, colno, error });
  return false;
};

console.log("Starting application initialization...");

const container = document.getElementById("root");
if (!container) {
  throw new Error("Failed to find the root element");
}
const root = createRoot(container);
root.render(
  <BrowserRouter>
    <App />
  </BrowserRouter>
);
