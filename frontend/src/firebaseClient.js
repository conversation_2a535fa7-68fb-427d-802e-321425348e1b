import { initializeApp } from "firebase/app";
import { 
  getA<PERSON>, 
  Recap<PERSON>aVerifier, 
  signInWithPhoneNumber, 
  connectAuthEmulator 
} from "firebase/auth";
import { getAnalytics } from "firebase/analytics";

// Firebase Configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "AIzaSyA3ex2Lw-RTMetcAB7-Sf0dkEHNX9GTMKA",
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "quamin-agricare.firebaseapp.com",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "quamin-agricare",
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "quamin-agricare.firebasestorage.app",
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "744194487251",
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:744194487251:web:e3d0ccee5ad58b6ddef526",
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || "G-HTCNBBQYJD"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const analytics = getAnalytics(app);

// ✅ Detect if Running on Firebase Emulator
const isEmulator = window.location.hostname === "localhost";

if (isEmulator) {
  console.warn("⚠️ Using Firebase Emulator for Authentication.");
  connectAuthEmulator(auth, "http://127.0.0.1:9099");
}

// ✅ Setup reCAPTCHA (Only for Production)
const setupRecaptcha = () => {
  if (isEmulator) return; // ❌ Skip reCAPTCHA in Emulator Mode

  if (!window.recaptchaVerifier) {
    window.recaptchaVerifier = new RecaptchaVerifier(auth, "recaptcha-container", {
      size: "invisible",
      callback: () => console.log("✅ reCAPTCHA solved"),
      "expired-callback": () => console.log("⚠️ reCAPTCHA expired. Please try again."),
    });
  }
};

// ✅ Send OTP (Handles Emulator and Production)
const sendOtp = async (phoneNumber) => {
  if (!isEmulator) {
    setupRecaptcha(); // Enable reCAPTCHA in Production
  }

  try {
    const appVerifier = isEmulator ? undefined : window.recaptchaVerifier; // ❌ No Recaptcha for Emulator
    const confirmationResult = await signInWithPhoneNumber(auth, phoneNumber, appVerifier);
    window.confirmationResult = confirmationResult;
    console.log("✅ OTP Sent!");
    return confirmationResult;
  } catch (error) {
    console.error("❌ Error Sending OTP:", error);
    throw error;
  }
};

// ✅ Verify OTP (Handles Emulator Test OTP)
const verifyOtp = async (otp) => {
  try {
    if (isEmulator) {
      if (otp === "123456") {
        console.log("✅ Test OTP Verified!");
        return { user: { phoneNumber: "Test User" } };
      } else {
        throw new Error("❌ Invalid Test OTP! Use `123456` in Emulator.");
      }
    }

    if (!window.confirmationResult) {
      throw new Error("❌ No OTP Request Found. Please retry.");
    }

    const result = await window.confirmationResult.confirm(otp);
    console.log("✅ OTP Verified Successfully:", result.user);
    return result.user;
  } catch (error) {
    console.error("❌ Error Verifying OTP:", error);
    throw error;
  }
};

// Export Functions
export { auth, setupRecaptcha, sendOtp, verifyOtp };
export default app;

