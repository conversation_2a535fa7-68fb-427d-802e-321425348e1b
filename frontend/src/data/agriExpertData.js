// Import state-specific data
import { punjabKVKs, punjabAgriUniversities, punjabResearchInstitutes, punjabAgriExperts } from './kvkData';
import { haryanaKVKs, haryanaAgriUniversities, haryanaResearchInstitutes, haryanaAgriExperts } from './kvkDataHaryana';
import { uttarPradeshKVKs, uttarPradeshAgriUniversities, uttarPradeshResearchInstitutes, uttarPradeshAgriExperts } from './kvkDataUP';
import { madhyaPradeshKVKs, madhyaPradeshAgriUniversities, madhyaPradeshResearchInstitutes, madhyaPradeshAgriExperts } from './kvkDataMP';
import { maharashtraKVKs, maharashtraAgriUniversities, maharashtraResearchInstitutes, maharashtraAgriExperts } from './kvkDataMH';
import { tamilNaduKVKs, tamilNaduAgriUniversities, tamilNaduResearchInstitutes, tamilNaduAgriExperts } from './kvkDataTN';
import { andhraPradeshKVKs, andhraPradeshAgriUniversities, andhraPradeshResearchInstitutes, andhraPradeshAgriExperts } from './kvkDataAP';
import { keralaKVKs, keralaAgriUniversities, keralaResearchInstitutes, keralaAgriExperts } from './kvkDataKL';
import { karnatakaKVKs, karnatakaAgriUniversities, karnatakaResearchInstitutes, karnatakaAgriExperts } from './kvkDataKA';

// Combine all KVKs
export const allKVKs = [
  ...punjabKVKs,
  ...haryanaKVKs,
  ...uttarPradeshKVKs,
  ...madhyaPradeshKVKs,
  ...maharashtraKVKs,
  ...tamilNaduKVKs,
  ...andhraPradeshKVKs,
  ...keralaKVKs,
  ...karnatakaKVKs,
  // More states will be added here
];

// Combine all Agricultural Universities
export const allAgriUniversities = [
  ...punjabAgriUniversities,
  ...haryanaAgriUniversities,
  ...uttarPradeshAgriUniversities,
  ...madhyaPradeshAgriUniversities,
  ...maharashtraAgriUniversities,
  ...tamilNaduAgriUniversities,
  ...andhraPradeshAgriUniversities,
  ...keralaAgriUniversities,
  ...karnatakaAgriUniversities,
  // More states will be added here
];

// Combine all Research Institutes
export const allResearchInstitutes = [
  ...punjabResearchInstitutes,
  ...haryanaResearchInstitutes,
  ...uttarPradeshResearchInstitutes,
  ...madhyaPradeshResearchInstitutes,
  ...maharashtraResearchInstitutes,
  ...tamilNaduResearchInstitutes,
  ...andhraPradeshResearchInstitutes,
  ...keralaResearchInstitutes,
  ...karnatakaResearchInstitutes,
  // More states will be added here
];

// Combine all Agricultural Experts
export const allAgriExperts = [
  ...punjabAgriExperts,
  ...haryanaAgriExperts,
  ...uttarPradeshAgriExperts,
  ...madhyaPradeshAgriExperts,
  ...maharashtraAgriExperts,
  ...tamilNaduAgriExperts,
  ...andhraPradeshAgriExperts,
  ...keralaAgriExperts,
  ...karnatakaAgriExperts,
  // More states will be added here
];

// List of all Indian states and union territories
export const indianStates = [
  { name: 'Andhra Pradesh', code: 'AP' },
  { name: 'Arunachal Pradesh', code: 'AR' },
  { name: 'Assam', code: 'AS' },
  { name: 'Bihar', code: 'BR' },
  { name: 'Chhattisgarh', code: 'CG' },
  { name: 'Goa', code: 'GA' },
  { name: 'Gujarat', code: 'GJ' },
  { name: 'Haryana', code: 'HR' },
  { name: 'Himachal Pradesh', code: 'HP' },
  { name: 'Jharkhand', code: 'JH' },
  { name: 'Karnataka', code: 'KA' },
  { name: 'Kerala', code: 'KL' },
  { name: 'Madhya Pradesh', code: 'MP' },
  { name: 'Maharashtra', code: 'MH' },
  { name: 'Manipur', code: 'MN' },
  { name: 'Meghalaya', code: 'ML' },
  { name: 'Mizoram', code: 'MZ' },
  { name: 'Nagaland', code: 'NL' },
  { name: 'Odisha', code: 'OD' },
  { name: 'Punjab', code: 'PB' },
  { name: 'Rajasthan', code: 'RJ' },
  { name: 'Sikkim', code: 'SK' },
  { name: 'Tamil Nadu', code: 'TN' },
  { name: 'Telangana', code: 'TG' },
  { name: 'Tripura', code: 'TR' },
  { name: 'Uttar Pradesh', code: 'UP' },
  { name: 'Uttarakhand', code: 'UK' },
  { name: 'West Bengal', code: 'WB' },
  { name: 'Andaman and Nicobar Islands', code: 'AN' },
  { name: 'Chandigarh', code: 'CH' },
  { name: 'Dadra and Nagar Haveli and Daman and Diu', code: 'DN' },
  { name: 'Delhi', code: 'DL' },
  { name: 'Jammu and Kashmir', code: 'JK' },
  { name: 'Ladakh', code: 'LA' },
  { name: 'Lakshadweep', code: 'LD' },
  { name: 'Puducherry', code: 'PY' }
];

// KVK count by state (for states with data)
export const kvkCountByState = {
  'Punjab': punjabKVKs.length,
  'Haryana': haryanaKVKs.length,
  'Uttar Pradesh': uttarPradeshKVKs.length,
  'Madhya Pradesh': madhyaPradeshKVKs.length,
  'Maharashtra': maharashtraKVKs.length,
  'Tamil Nadu': tamilNaduKVKs.length,
  'Andhra Pradesh': andhraPradeshKVKs.length,
  'Kerala': keralaKVKs.length,
  'Karnataka': karnatakaKVKs.length,
  // More states will be added here
};

// Function to get KVKs by state
export const getKVKsByState = (stateName) => {
  switch (stateName) {
    case 'Punjab':
      return punjabKVKs;
    case 'Haryana':
      return haryanaKVKs;
    case 'Uttar Pradesh':
      return uttarPradeshKVKs;
    case 'Madhya Pradesh':
      return madhyaPradeshKVKs;
    case 'Maharashtra':
      return maharashtraKVKs;
    case 'Tamil Nadu':
      return tamilNaduKVKs;
    case 'Andhra Pradesh':
      return andhraPradeshKVKs;
    case 'Kerala':
      return keralaKVKs;
    case 'Karnataka':
      return karnatakaKVKs;
    default:
      return [];
  }
};

// Function to get Agricultural Universities by state
export const getAgriUniversitiesByState = (stateName) => {
  switch (stateName) {
    case 'Punjab':
      return punjabAgriUniversities;
    case 'Haryana':
      return haryanaAgriUniversities;
    case 'Uttar Pradesh':
      return uttarPradeshAgriUniversities;
    case 'Madhya Pradesh':
      return madhyaPradeshAgriUniversities;
    case 'Maharashtra':
      return maharashtraAgriUniversities;
    case 'Tamil Nadu':
      return tamilNaduAgriUniversities;
    case 'Andhra Pradesh':
      return andhraPradeshAgriUniversities;
    case 'Kerala':
      return keralaAgriUniversities;
    case 'Karnataka':
      return karnatakaAgriUniversities;
    default:
      return [];
  }
};

// Function to get Research Institutes by state
export const getResearchInstitutesByState = (stateName) => {
  switch (stateName) {
    case 'Punjab':
      return punjabResearchInstitutes;
    case 'Haryana':
      return haryanaResearchInstitutes;
    case 'Uttar Pradesh':
      return uttarPradeshResearchInstitutes;
    case 'Madhya Pradesh':
      return madhyaPradeshResearchInstitutes;
    case 'Maharashtra':
      return maharashtraResearchInstitutes;
    case 'Tamil Nadu':
      return tamilNaduResearchInstitutes;
    case 'Andhra Pradesh':
      return andhraPradeshResearchInstitutes;
    case 'Kerala':
      return keralaResearchInstitutes;
    case 'Karnataka':
      return karnatakaResearchInstitutes;
    default:
      return [];
  }
};

// Function to get Agricultural Experts by state
export const getAgriExpertsByState = (stateName) => {
  switch (stateName) {
    case 'Punjab':
      return punjabAgriExperts;
    case 'Haryana':
      return haryanaAgriExperts;
    case 'Uttar Pradesh':
      return uttarPradeshAgriExperts;
    case 'Madhya Pradesh':
      return madhyaPradeshAgriExperts;
    case 'Maharashtra':
      return maharashtraAgriExperts;
    case 'Tamil Nadu':
      return tamilNaduAgriExperts;
    case 'Andhra Pradesh':
      return andhraPradeshAgriExperts;
    case 'Kerala':
      return keralaAgriExperts;
    case 'Karnataka':
      return karnatakaAgriExperts;
    default:
      return [];
  }
};

// Agricultural topics for advisory
export const agriculturalTopics = [
  {
    id: 'crop-management',
    name: 'Crop Management',
    subtopics: [
      'Crop Selection',
      'Crop Rotation',
      'Intercropping',
      'Planting Techniques',
      'Harvesting Methods'
    ]
  },
  {
    id: 'soil-health',
    name: 'Soil Health',
    subtopics: [
      'Soil Testing',
      'Soil Fertility Management',
      'Soil Conservation',
      'Organic Matter Management',
      'Soil Amendments'
    ]
  },
  {
    id: 'water-management',
    name: 'Water Management',
    subtopics: [
      'Irrigation Scheduling',
      'Water Conservation',
      'Drip Irrigation',
      'Sprinkler Irrigation',
      'Rainwater Harvesting'
    ]
  },
  {
    id: 'pest-management',
    name: 'Pest Management',
    subtopics: [
      'Integrated Pest Management',
      'Biological Control',
      'Chemical Control',
      'Cultural Control',
      'Pest Monitoring'
    ]
  },
  {
    id: 'disease-management',
    name: 'Disease Management',
    subtopics: [
      'Disease Identification',
      'Preventive Measures',
      'Chemical Control',
      'Biological Control',
      'Resistant Varieties'
    ]
  },
  {
    id: 'seed-technology',
    name: 'Seed Technology',
    subtopics: [
      'Seed Selection',
      'Seed Treatment',
      'Seed Storage',
      'Seed Quality',
      'Seed Production'
    ]
  },
  {
    id: 'organic-farming',
    name: 'Organic Farming',
    subtopics: [
      'Organic Certification',
      'Organic Inputs',
      'Composting',
      'Vermicomposting',
      'Organic Pest Management'
    ]
  },
  {
    id: 'farm-mechanization',
    name: 'Farm Mechanization',
    subtopics: [
      'Tractor Operations',
      'Implements Selection',
      'Machinery Maintenance',
      'Custom Hiring',
      'Precision Farming'
    ]
  },
  {
    id: 'post-harvest',
    name: 'Post-Harvest Management',
    subtopics: [
      'Storage Techniques',
      'Processing Methods',
      'Value Addition',
      'Packaging',
      'Marketing'
    ]
  },
  {
    id: 'livestock',
    name: 'Livestock Management',
    subtopics: [
      'Animal Nutrition',
      'Animal Health',
      'Breeding Management',
      'Housing Management',
      'Waste Management'
    ]
  }
];
