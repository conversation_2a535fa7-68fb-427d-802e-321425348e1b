import React, { Suspense } from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import ProtectedRoute from './components/auth/ProtectedRoute';
import RoleBasedRoute from './components/auth/RoleBasedRoute';
import { useAuth } from './contexts/AuthContext';
import NavBar from "./Components/NavBar/NavBar";
import Footer from "./Components/Footer/Footer";

// Pages
import Login from "./Pages/Login/Login";
import Dashboard from "./Pages/Dashboard/Dashboard";
import Signup from "./Pages/Signup/Signup";
import NotFound404 from "./Pages/NotFound404/NotFound404";
import PlanSelect from "./Pages/PlanSelect/PlanSelect";
import Profile from "./Pages/Profile/Profile";
import Settings from "./Pages/Settings/Settings";
import ChatBotApp from "./Pages/ChatBot/ChatBotApp";
import ChatbotPage from "./Pages/ChatBot/ChatbotPage";
import DroneMonitoring from "./Pages/DroneMonitoring/DroneMonitoring";
import MarketAnalysis from "./Pages/MarketTrends/MarketAnalysis";
import CreditScore from "./Pages/CreditScore/CreditScore";
import AgriExpert from "./Pages/AgriExpert/AgriExpert";
import FarmerDash from "./Pages/FarmerDashboard/FarmerDash";
import HRLogin from "./Pages/HR/HRLogin";
import HRDash from "./Pages/HR/HRDash";
import Farms from "./Pages/Farms/Farms";
import DemandSupplyAnalysis from "./Pages/DemandSupplyAnalysis/DemandSupplyAnalysis";
import TMHome from './Pages/TMDashboard/Home/TMHome';
import FarmerOnboarding from './Pages/TMDashboard/Onboarding/FarmerOnboarding';
import MonitoringDashboard from './Pages/TMDashboard/Monitoring/MonitoringDashboard';
import LivestockMonitoring from './Pages/TMDashboard/Monitoring/LivestockMonitoring';
import LivestockDashboard from './Pages/FarmerDashboard/Livestock';
import InsightsDashboard from './Pages/TMDashboard/Insights/InsightsDashboard';
import AnalyticsDashboard from './Pages/TMDashboard/Analytics/AnalyticsDashboard';
import IOTConfig from './Pages/TMDashboard/IOTConfig/IOTConfig';
import HRSignup from "./Pages/HR/HRSignup";
import TMDashboard from './Pages/TMDashboard/TMDashboard';
import MainContent from "./Components/MainContent/MainContent";
import Services from "./Components/Services/Services";
import About from "./Components/WhyUs/WhyUs";
import ModernFaqContainer from "./Components/Faq/ModernFaqContainer";
import EditFarmerDetails from "./Pages/TMDashboard/EditFarmerDetails/EditFarmerDetails";
import SoilAnalysis from "./Pages/FarmerDashboard/SoilAnalysis/SoilAnalysis";

// Error Boundary Component
class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }

    componentDidCatch(error, errorInfo) {
        console.error('Route Error:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div style={{ padding: '20px', textAlign: 'center' }}>
                    <h1>Something went wrong.</h1>
                    <pre>{this.state.error?.toString()}</pre>
                </div>
            );
        }
        return this.props.children;
    }
}

// Loading Component
const Loading = () => (
    <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: '#ffffff'
    }}>
        <div style={{ textAlign: 'center' }}>
            <div style={{
                width: '50px',
                height: '50px',
                border: '5px solid #f3f3f3',
                borderTop: '5px solid #3498db',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                margin: '0 auto 20px'
            }} />
            <div>Loading application...</div>
        </div>
    </div>
);

const AppRoutes = () => {
    const { currentUser } = useAuth();
    console.log('Rendering AppRoutes');

    return (
        <ErrorBoundary>
            <Suspense fallback={<Loading />}>
                <Routes>
                    {/* Public Routes */}
                    <Route path="/" element={
                        <>
                            <NavBar />
                            <MainContent />
                            <Services />
                            <About />
                            <ModernFaqContainer />
                            <Footer />
                        </>
                    } />
                    <Route path="/login" element={<Login />} />
                    <Route path="/signup" element={<Signup />} />
                    <Route path="/hrlogin" element={<HRLogin />} />
                    <Route path="/hrsignup" element={<HRSignup />} />

                    {/* Farmer Routes */}
                    <Route path="/farmer-dashboard" element={
                        <RoleBasedRoute allowedRoles={['Farmer']}>
                            <FarmerDash />
                        </RoleBasedRoute>
                    } />

                    {/* HR Routes */}
                    <Route path="/hrdash" element={
                        <RoleBasedRoute allowedRoles={['HR']}>
                            <HRDash />
                        </RoleBasedRoute>
                    } />
                    {/* Redirect /hr to /hrdash for compatibility */}
                    <Route path="/hr" element={<Navigate to="/hrdash" replace />} />

                    {/* Protected Routes */}
                    <Route path="/dashboard" element={
                        <ProtectedRoute>
                            <Dashboard />
                        </ProtectedRoute>
                    } />
                    <Route path="/dashboard/profile" element={
                        <ProtectedRoute>
                            <Profile />
                        </ProtectedRoute>
                    } />
                    <Route path="/profile" element={
                        <ProtectedRoute>
                            <Profile />
                        </ProtectedRoute>
                    } />
                    <Route path="/settings" element={
                        <ProtectedRoute>
                            <Settings />
                        </ProtectedRoute>
                    } />
                    <Route path="/dashboard/chatbot" element={
                        <ProtectedRoute>
                            <ChatBotApp />
                        </ProtectedRoute>
                    } />
                    <Route path="/chatbot" element={
                        <ProtectedRoute>
                            <ChatbotPage />
                        </ProtectedRoute>
                    } />
                    <Route path="/farm" element={
                        <ProtectedRoute>
                            <Farms />
                        </ProtectedRoute>
                    } />
                    <Route path="/livestock" element={
                        <ProtectedRoute>
                            <LivestockDashboard />
                        </ProtectedRoute>
                    } />
                    <Route path="/market-analysis" element={
                        <ProtectedRoute>
                            <MarketAnalysis />
                        </ProtectedRoute>
                    } />
                    <Route path="/drone-monitoring" element={
                        <ProtectedRoute>
                            <DroneMonitoring />
                        </ProtectedRoute>
                    } />
                    <Route path="/credit-score" element={
                        <ProtectedRoute>
                            <CreditScore />
                        </ProtectedRoute>
                    } />
                    <Route path="/soil-analysis" element={
                        <ProtectedRoute>
                            <SoilAnalysis />
                        </ProtectedRoute>
                    } />
                    <Route path="/agri-expert" element={
                        <ProtectedRoute>
                            <AgriExpert />
                        </ProtectedRoute>
                    } />
                    <Route path="/analysis" element={
                        <ProtectedRoute>
                            <DemandSupplyAnalysis />
                        </ProtectedRoute>
                    } />
                    <Route path="/demand-supply-analysis" element={
                        <ProtectedRoute>
                            <DemandSupplyAnalysis />
                        </ProtectedRoute>
                    } />
                    <Route path="/planselect" element={
                        <ProtectedRoute>
                            <PlanSelect />
                        </ProtectedRoute>
                    } />

                    {/* TM Dashboard with Nested Routes */}
                    <Route
                        path="/tm"
                        element={
                            <RoleBasedRoute allowedRoles={['TM']}>
                                <TMDashboard />
                            </RoleBasedRoute>
                        }
                    >
                        <Route index element={<TMHome />} />
                        <Route path="onboarding" element={<FarmerOnboarding />} />
                        <Route path="monitoring" element={<MonitoringDashboard />} />
                        <Route path="livestock" element={<LivestockMonitoring />} />
                        <Route path="insights" element={<InsightsDashboard />} />
                        <Route path="analytics" element={<AnalyticsDashboard />} />
                        <Route path="iot-config" element={<IOTConfig />} />
                        <Route path="edit" element={<EditFarmerDetails/>} />
                    </Route>

                    {/* Default Route for unmatched paths */}
                    <Route path="*" element={<NotFound404 />} />
                </Routes>
            </Suspense>
        </ErrorBoundary>
    );
};

export default AppRoutes;


