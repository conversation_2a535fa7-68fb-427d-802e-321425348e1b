<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="500px" viewBox="0 0 800 500" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#004d7a" stop-opacity="0.85" offset="0%"></stop>
            <stop stop-color="#008793" stop-opacity="0.85" offset="50%"></stop>
            <stop stop-color="#00bf72" stop-opacity="0.85" offset="100%"></stop>
        </linearGradient>
        <filter x="-5.0%" y="-5.0%" width="110.0%" height="110.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="10" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="FAQ-Preview" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect id="Background" fill="url(#linearGradient-1)" x="0" y="0" width="800" height="500" rx="20"></rect>
        
        <!-- Grid Pattern -->
        <g id="Grid" opacity="0.2" stroke="#00ffaa" stroke-width="1">
            <g id="Vertical-Lines">
                <line x1="40" y1="0" x2="40" y2="500"></line>
                <line x1="80" y1="0" x2="80" y2="500"></line>
                <line x1="120" y1="0" x2="120" y2="500"></line>
                <line x1="160" y1="0" x2="160" y2="500"></line>
                <line x1="200" y1="0" x2="200" y2="500"></line>
                <line x1="240" y1="0" x2="240" y2="500"></line>
                <line x1="280" y1="0" x2="280" y2="500"></line>
                <line x1="320" y1="0" x2="320" y2="500"></line>
                <line x1="360" y1="0" x2="360" y2="500"></line>
                <line x1="400" y1="0" x2="400" y2="500"></line>
                <line x1="440" y1="0" x2="440" y2="500"></line>
                <line x1="480" y1="0" x2="480" y2="500"></line>
                <line x1="520" y1="0" x2="520" y2="500"></line>
                <line x1="560" y1="0" x2="560" y2="500"></line>
                <line x1="600" y1="0" x2="600" y2="500"></line>
                <line x1="640" y1="0" x2="640" y2="500"></line>
                <line x1="680" y1="0" x2="680" y2="500"></line>
                <line x1="720" y1="0" x2="720" y2="500"></line>
                <line x1="760" y1="0" x2="760" y2="500"></line>
            </g>
            <g id="Horizontal-Lines">
                <line x1="0" y1="40" x2="800" y2="40"></line>
                <line x1="0" y1="80" x2="800" y2="80"></line>
                <line x1="0" y1="120" x2="800" y2="120"></line>
                <line x1="0" y1="160" x2="800" y2="160"></line>
                <line x1="0" y1="200" x2="800" y2="200"></line>
                <line x1="0" y1="240" x2="800" y2="240"></line>
                <line x1="0" y1="280" x2="800" y2="280"></line>
                <line x1="0" y1="320" x2="800" y2="320"></line>
                <line x1="0" y1="360" x2="800" y2="360"></line>
                <line x1="0" y1="400" x2="800" y2="400"></line>
                <line x1="0" y1="440" x2="800" y2="440"></line>
                <line x1="0" y1="480" x2="800" y2="480"></line>
            </g>
        </g>
        
        <!-- Data Streams -->
        <g id="Data-Streams" opacity="0.6">
            <rect id="Stream-1" fill="#00ffaa" x="680" y="100" width="1" height="300" rx="0.5" filter="url(#filter-2)"></rect>
            <rect id="Stream-2" fill="#00ffaa" x="640" y="50" width="1" height="400" rx="0.5" filter="url(#filter-2)"></rect>
            <rect id="Stream-3" fill="#00ffaa" x="720" y="150" width="1" height="200" rx="0.5" filter="url(#filter-2)"></rect>
        </g>
        
        <!-- Tech Circle -->
        <g id="Tech-Circle" transform="translate(700, 80)">
            <circle id="Outer-Circle" stroke="#00ffaa" stroke-width="2" cx="40" cy="40" r="40"></circle>
            <circle id="Inner-Point" fill="#00ffaa" cx="40" cy="40" r="5" filter="url(#filter-2)"></circle>
        </g>
        
        <!-- Header -->
        <g id="Header" transform="translate(60, 60)">
            <text id="Title" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#FFFFFF">
                <tspan x="0" y="36">Frequently Asked Questions</tspan>
            </text>
            <text id="Subtitle" font-family="Arial, sans-serif" font-size="18" font-weight="normal" fill="#FFFFFF" opacity="0.8">
                <tspan x="0" y="70">Everything you need to know about Smart Farming</tspan>
            </text>
        </g>
        
        <!-- FAQ Items -->
        <g id="FAQ-Items" transform="translate(60, 150)">
            <!-- Item 1 (Open) -->
            <g id="Item-1">
                <rect id="Item-1-BG" fill="#FFFFFF" opacity="0.2" x="0" y="0" width="680" height="120" rx="12"></rect>
                <text id="Question-1" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="#FFFFFF">
                    <tspan x="20" y="30">What is Smart Farming technology?</tspan>
                </text>
                <text id="Answer-1" font-family="Arial, sans-serif" font-size="14" font-weight="normal" fill="#FFFFFF" opacity="0.9">
                    <tspan x="20" y="60">Smart Farming is the application of modern Information and Communication</tspan>
                    <tspan x="20" y="80">Technologies (ICT) into agriculture. It incorporates IoT sensors, drones, AI,</tspan>
                    <tspan x="20" y="100">and data analytics to optimize farming operations and increase yields.</tspan>
                </text>
                <g id="Plus-Icon" transform="translate(650, 20)">
                    <line x1="0" y1="10" x2="20" y2="10" stroke="#00ffaa" stroke-width="2" transform="rotate(45, 10, 10)"></line>
                    <line x1="0" y1="10" x2="20" y2="10" stroke="#00ffaa" stroke-width="2" transform="rotate(-45, 10, 10)"></line>
                </g>
            </g>
            
            <!-- Item 2 (Closed) -->
            <g id="Item-2" transform="translate(0, 140)">
                <rect id="Item-2-BG" fill="#FFFFFF" opacity="0.1" x="0" y="0" width="680" height="60" rx="12"></rect>
                <text id="Question-2" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="#FFFFFF">
                    <tspan x="20" y="30">How does soil monitoring technology work?</tspan>
                </text>
                <g id="Plus-Icon-2" transform="translate(650, 20)">
                    <line x1="0" y1="10" x2="20" y2="10" stroke="#00ffaa" stroke-width="2"></line>
                    <line x1="10" y1="0" x2="10" y2="20" stroke="#00ffaa" stroke-width="2"></line>
                </g>
            </g>
            
            <!-- Item 3 (Closed) -->
            <g id="Item-3" transform="translate(0, 220)">
                <rect id="Item-3-BG" fill="#FFFFFF" opacity="0.1" x="0" y="0" width="680" height="60" rx="12"></rect>
                <text id="Question-3" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="#FFFFFF">
                    <tspan x="20" y="30">What benefits do agricultural drones provide?</tspan>
                </text>
                <g id="Plus-Icon-3" transform="translate(650, 20)">
                    <line x1="0" y1="10" x2="20" y2="10" stroke="#00ffaa" stroke-width="2"></line>
                    <line x1="10" y1="0" x2="10" y2="20" stroke="#00ffaa" stroke-width="2"></line>
                </g>
            </g>
        </g>
    </g>
</svg>
