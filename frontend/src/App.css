/* Removed color transitions - using only green */

.layout {
  background-color: #ffffff; /* Changed from light grey to white */
  min-height: 100vh;
  width: 100%;
  overflow: auto;
}

.main-content-container {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  padding-left: 128px;
  padding-right: 128px;
  margin-top: 4rem;
}

/* Responsive styles for mobile devices */
@media screen and (max-width: 768px) {
  .main-content-container {
    grid-template-columns: 1fr;
    padding-left: 20px;
    padding-right: 20px;
    margin-top: 4rem;
  }

  .heading-container h2 {
    font-size: 1.8rem;
    line-height: 2.5rem;
  }

  .last-heading-animate {
    font-size: 1.8rem;
    line-height: 2.5rem;
    color: #006400 !important; /* Ensure green color on mobile */
  }

  .image-container {
    display: none; /* Hide image on mobile to save space */
  }
}

.heading-container h2 {
  font-size: 2.3rem;
  line-height: 4rem;
  color: black;
  font-weight: 700;
}

.animate-heading {
  position: relative;
  display: flex;
  width: 100%;
  padding-bottom: 2rem;
}

.last-heading-animate {
  position: absolute;
  cursor: pointer;
  font-size: 2.3rem;
  line-height: 4rem;
  letter-spacing: -0.025em;
  font-weight: 700;
  color: #006400 !important; /* Dark green with !important to override any other styles */
}

.desc-container {
  margin-top: 3rem;
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.desc-container p {
  width: 331px;
  height: 74px;
  color: black;
  font-display: swap;
}

.login-button-container {
  font-size: 1.25rem;
  background-color: #006400; /* Dark green */
  padding: 0.75rem;
  font-weight: bold;
  border-radius: 0.5rem;
  width: 8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  text-decoration: none;
  transition: transform 0.3s ease;
  width: fit-content;
}

.login-button-container:hover {
  transform: scale(1.1);
}

.login-button-container:hover .fa-solid.fa-arrow-right-long {
  transform: translateX(0.25rem);
  transition: transform 200ms ease;
}

.fa-solid.fa-arrow-right-long {
  font-size: 1.25rem;
  color: white;
  transition: transform 200ms ease;
}

.login-button-container h1 {
  font-weight: 600;
  font-size: 1.25rem;
  color: white;
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 30px;
  background: linear-gradient(135deg, rgba(27, 94, 32, 0.9), rgba(46, 125, 50, 0.9), rgba(56, 142, 60, 0.9));
  border-radius: 16px;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  perspective: 1500px;
  transform-style: preserve-3d;
}

/* Glowing tech circle */
@keyframes pulse3d {
  0% { transform: translate3d(-50%, -50%, 0) scale3d(1, 1, 1) rotateY(0deg); opacity: 0.7; }
  50% { transform: translate3d(-50%, -50%, 20px) scale3d(1.05, 1.05, 1.2) rotateY(180deg); opacity: 1; }
  100% { transform: translate3d(-50%, -50%, 0) scale3d(1, 1, 1) rotateY(360deg); opacity: 0.7; }
}

.image-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 40%, transparent 70%);
  border-radius: 50%;
  transform: translate3d(-50%, -50%, 0);
  z-index: 1;
  animation: pulse3d 6s infinite ease-in-out;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

/* Digital circuit lines */
@keyframes circuit-glow {
  0% { opacity: 0.3; box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }
  50% { opacity: 0.7; box-shadow: 0 0 15px rgba(255, 255, 255, 0.7); }
  100% { opacity: 0.3; box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); }
}

.image-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(90deg, transparent 98%, rgba(255, 255, 255, 0.3) 98%, rgba(255, 255, 255, 0.3) 100%),
    linear-gradient(0deg, transparent 98%, rgba(255, 255, 255, 0.3) 98%, rgba(255, 255, 255, 0.3) 100%);
  background-size: 30px 30px;
  z-index: 1;
  animation: circuit-glow 4s infinite ease-in-out;
  transform: translateZ(5px);
}

/* Tech dots pattern */
@keyframes float-dots {
  0% { transform: translate3d(0, 0, 0); opacity: 0.3; }
  25% { transform: translate3d(5px, 0, 10px); opacity: 0.5; }
  50% { transform: translate3d(0, 5px, 20px); opacity: 0.7; }
  75% { transform: translate3d(-5px, 0, 10px); opacity: 0.5; }
  100% { transform: translate3d(0, 0, 0); opacity: 0.3; }
}

.image-container > div:first-child {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.5) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 1;
  opacity: 0.3;
  animation: float-dots 8s infinite ease-in-out;
  transform-style: preserve-3d;
}

/* Horizontal scanning line */
@keyframes scan3d {
  0% { top: 0; transform: translateZ(30px); width: 90%; left: 5%; }
  50% { transform: translateZ(50px); width: 100%; left: 0; }
  100% { top: 100%; transform: translateZ(30px); width: 90%; left: 5%; }
}

.image-container > div:nth-child(2) {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  z-index: 3;
  animation: scan3d 3s infinite ease-in-out alternate;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.8), 0 0 20px rgba(255, 255, 255, 0.4);
  transform-style: preserve-3d;
}

/* Digital data stream */
@keyframes dataflow3d {
  0% { transform: translate3d(0, -100%, 10px); opacity: 0; }
  50% { transform: translate3d(0, 0%, 30px); opacity: 1; }
  100% { transform: translate3d(0, 100%, 10px); opacity: 0; }
}

.image-container > div:nth-child(3) {
  position: absolute;
  top: 0;
  left: 20px;
  width: 1px;
  height: 100%;
  background: linear-gradient(0deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  z-index: 1;
  animation: dataflow3d 2s infinite ease-out;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.8), 0 0 16px rgba(255, 255, 255, 0.4);
  transform-style: preserve-3d;
}

/* Right side data stream */
.image-container > div:nth-child(3)::before {
  content: '';
  position: absolute;
  top: 0;
  right: -60px;
  width: 1px;
  height: 100%;
  background: linear-gradient(0deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: dataflow3d 3s infinite ease-out 1s;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.8), 0 0 16px rgba(255, 255, 255, 0.4);
  transform-style: preserve-3d;
}

/* Left side data stream */
.image-container > div:nth-child(3)::after {
  content: '';
  position: absolute;
  top: 0;
  right: -120px;
  width: 1px;
  height: 100%;
  background: linear-gradient(0deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: dataflow3d 2.5s infinite ease-out 0.5s;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.8), 0 0 16px rgba(255, 255, 255, 0.4);
  transform-style: preserve-3d;
}

.image-container img {
  width: 80%;
  max-height: 400px;
  object-fit: contain;
  border-radius: 16px;
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 0 25px rgba(255, 255, 255, 0.4));
  transition: all 0.5s ease;
  transform-style: preserve-3d;
  transform: translateZ(60px) rotateX(2deg);
  backface-visibility: hidden;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

@keyframes float-image {
  0% { transform: translateZ(60px) rotateX(2deg) rotateY(0deg); filter: drop-shadow(0 0 25px rgba(255, 255, 255, 0.4)); }
  50% { transform: translateZ(100px) rotateX(0deg) rotateY(2deg); filter: drop-shadow(0 0 40px rgba(255, 255, 255, 0.6)); }
  100% { transform: translateZ(60px) rotateX(2deg) rotateY(0deg); filter: drop-shadow(0 0 25px rgba(255, 255, 255, 0.4)); }
}

.image-container:hover img {
  animation: float-image 5s infinite ease-in-out;
  border: 2px solid rgba(255, 255, 255, 0.6);
}

/* Tech circles */
@keyframes rotate-circle {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes pulse-circle {
  0% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.9); }
  50% { opacity: 0.7; transform: translate(-50%, -50%) scale(1.1); }
  100% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.9); }
}

.tech-circle-1 {
  position: absolute;
  top: 30%;
  left: 15%;
  width: 80px;
  height: 80px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  z-index: 2;
  transform: translate(-50%, -50%);
  animation: rotate-circle 8s linear infinite;
}

.tech-circle-1::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10px;
  height: 10px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
}

.tech-circle-2 {
  position: absolute;
  top: 70%;
  left: 85%;
  width: 60px;
  height: 60px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  z-index: 2;
  transform: translate(-50%, -50%);
  animation: pulse-circle 4s ease-in-out infinite;
}

.tech-circle-2::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30px;
  height: 30px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

/* Agricultural data panels */
@keyframes float-panel {
  0% { transform: translate3d(0, 0, 40px) rotateY(0deg); }
  25% { transform: translate3d(5px, -8px, 80px) rotateY(5deg); }
  50% { transform: translate3d(0, -15px, 120px) rotateY(0deg); }
  75% { transform: translate3d(-5px, -8px, 80px) rotateY(-5deg); }
  100% { transform: translate3d(0, 0, 40px) rotateY(0deg); }
}

@keyframes data-update {
  0% { opacity: 0.9; text-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
  10% { opacity: 0.3; text-shadow: 0 0 10px rgba(255, 255, 255, 0.8); }
  15% { opacity: 0.9; text-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
  100% { opacity: 0.9; text-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
}

.agri-data-panel {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 110px;
  height: 110px;
  background: rgba(27, 94, 32, 0.5);
  backdrop-filter: blur(8px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow:
    0 0 30px rgba(255, 255, 255, 0.3),
    inset 0 0 20px rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.95);
  font-family: 'Outfit', sans-serif;
  z-index: 3;
  transform-style: preserve-3d;
  animation: float-panel 8s infinite ease-in-out;
  transform-origin: center center;
}

.agri-data-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), transparent);
  border-radius: 12px;
  z-index: -1;
  transform: translateZ(-1px);
}

.agri-data-panel::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 14px;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.5), transparent, rgba(255, 255, 255, 0.5));
  z-index: -2;
  filter: blur(2px);
  opacity: 0.5;
  animation: rotate-circle 8s linear infinite;
}

.panel-icon {
  font-size: 16px;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 1);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
  transform: translateZ(10px);
}

.panel-value {
  font-size: 26px;
  font-weight: bold;
  margin-bottom: 8px;
  animation: data-update 8s infinite;
  transform: translateZ(15px);
  letter-spacing: 1px;
}

.panel-label {
  font-size: 13px;
  opacity: 0.9;
  transform: translateZ(5px);
  letter-spacing: 0.5px;
}

/* Position each panel */
.temp-panel {
  top: 15%;
  left: 12%;
  animation-delay: 0s;
}

.humidity-panel {
  top: 15%;
  right: 12%;
  animation-delay: 1s;
}

.soil-panel {
  bottom: 15%;
  left: 12%;
  animation-delay: 1.5s;
}

.light-panel {
  bottom: 15%;
  right: 12%;
  animation-delay: 0.5s;
}

.yield-panel {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateZ(150px) !important;
  animation-delay: 0.8s;
  z-index: 4;
  width: 120px;
  height: 120px;
  background: rgba(0, 40, 60, 0.2); /* Much more transparent background */
  backdrop-filter: blur(2px); /* Reduced blur effect */
}

.yield-panel .panel-value {
  font-size: 32px;
  text-shadow: 0 0 10px rgba(0, 255, 170, 1), 0 0 20px rgba(0, 255, 170, 0.8); /* Enhanced glow */
}

.yield-panel .panel-label {
  text-shadow: 0 0 8px rgba(0, 255, 170, 0.8); /* Added glow to label */
}

.yield-panel .panel-icon {
  text-shadow: 0 0 10px rgba(0, 255, 170, 1); /* Enhanced glow */
}

.yield-panel::after {
  animation: rotate-circle 6s linear infinite reverse;
  opacity: 0.3; /* More transparent border */
}

/* Panel indicators */
@keyframes blink-indicator {
  0%, 100% { opacity: 0.3; transform: scale(0.8); box-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
  50% { opacity: 1; transform: scale(1.2); box-shadow: 0 0 15px rgba(255, 255, 255, 0.8), 0 0 30px rgba(255, 255, 255, 0.4); }
}

.panel-indicator {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background-color: rgba(255, 255, 255, 1);
  border-radius: 50%;
  animation: blink-indicator 2s infinite;
  transform: translateZ(20px);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
}

.temp-panel .panel-indicator { animation-delay: 0.2s; }
.humidity-panel .panel-indicator { animation-delay: 0.7s; }
.soil-panel .panel-indicator { animation-delay: 0.4s; }
.light-panel .panel-indicator { animation-delay: 1.1s; }

/* Connection lines */
@keyframes data-pulse {
  0% { opacity: 0.1; height: 1px; box-shadow: 0 0 2px rgba(255, 255, 255, 0.2); }
  50% { opacity: 0.7; height: 2px; box-shadow: 0 0 10px rgba(255, 255, 255, 0.5), 0 0 20px rgba(255, 255, 255, 0.3); }
  100% { opacity: 0.1; height: 1px; box-shadow: 0 0 2px rgba(255, 255, 255, 0.2); }
}

@keyframes data-flow {
  0% { background-position: 0% 50%; }
  100% { background-position: 100% 50%; }
}

.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
  transform-style: preserve-3d;
}

.connection-lines::before,
.connection-lines::after {
  content: '';
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  background-size: 200% 100%;
  animation:
    data-pulse 3s infinite ease-in-out,
    data-flow 5s infinite linear;
  transform-style: preserve-3d;
}

.connection-lines::before {
  top: 35%;
  left: 15%;
  width: 70%;
  animation-delay: 0.5s, 0s;
  transform: translateZ(30px) rotateX(75deg);
}

.connection-lines::after {
  bottom: 35%;
  right: 15%;
  width: 70%;
  animation-delay: 1.5s, 1s;
  transform: translateZ(30px) rotateX(75deg);
}

/* Drone path */
@keyframes drone-flight {
  0% { stroke-dashoffset: 1000; opacity: 0.3; }
  50% { opacity: 0.8; }
  100% { stroke-dashoffset: 0; opacity: 0.3; }
}

.drone-path {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  pointer-events: none;
  transform-style: preserve-3d;
  transform: translateZ(80px);
}

.drone-path::before {
  content: '';
  position: absolute;
  top: 30%;
  left: 20%;
  width: 60%;
  height: 40%;
  background: transparent;
  border: 2px dashed rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
  animation: drone-flight 15s infinite linear;
  stroke-dasharray: 1000;
  opacity: 0.6;
}

/* Field grid */
@keyframes grid-pulse {
  0% { opacity: 0.1; }
  50% { opacity: 0.3; }
  100% { opacity: 0.1; }
}

.field-grid {
  position: absolute;
  bottom: 10%;
  left: 50%;
  width: 80%;
  height: 40%;
  transform: translateX(-50%) translateZ(20px) rotateX(60deg);
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.2) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.2) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 1;
  opacity: 0.2;
  animation: grid-pulse 5s infinite ease-in-out;
  transform-style: preserve-3d;
  perspective: 1000px;
}

.services-container {
  margin-top: 6rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1.25rem;
}

.services-container h1 {
  color: black;
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 700;
}

.features-container {
  display: flex;
  column-gap: 1.5rem;
  height: 653px;
}

.feature-card-container {
  height: 432px;
  width: 270px;
  background-color: #a6e36a80;
  border-radius: 1rem;
  margin-top: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.feature-image-icon-container {
  margin-left: auto;
  margin-right: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 9999px;
}

.feature-image-icon-container img {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.scientist-gif {
  margin: 3rem;
}

.feature-text-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  padding-bottom: 2rem;
}

.feature-text-container h1 {
  color: black;
  font-weight: 800;
  font-size: 1.5rem;
  line-height: 2rem;
}

.feature-text-container p {
  color: #4b5563;
  text-align: center;
  padding-left: 1rem;
  padding-right: 1rem;
  font-weight: 700;
}

.feature-card-container2 {
  height: 432px;
  width: 270px;
  background-color: #a6e36a80;
  border-radius: 1rem;
  margin-top: 150px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.feature-card-container3 {
  height: 432px;
  width: 270px;
  background-color: #a6e36a80;
  border-radius: 1rem;
  margin-top: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.feature-card-container4 {
  height: 432px;
  width: 270px;
  background-color: #a6e36a80;
  border-radius: 1rem;
  margin-top: 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.why-us-heading {
  margin-top: 5rem;
  font-weight: 700;
  color: black;
  font-size: 1.875rem;
  line-height: 2.25rem;
  text-align: center;
}

.why-us-container {
  margin-top: 5rem;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.why-us-card {
  background-color: #8a6a4e99;
  width: 100%;
  height: 24rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4rem;
}

.why-us-card2 {
  background-color: #f1eeeb;
  width: 100%;
  height: 24rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4rem;
}

.why-us-text-card {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 700px;
  justify-content: center;
  align-items: start;
  transform: translate(0, 20px);
}

.why-us-text-card h1 {
  color: black;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
}

.why-us-text-card p {
  color: black;
  width: 601px;
  height: 106px;
}

.faqs-heading {
  margin-top: 10rem;
  font-weight: 700;
  color: black;
  font-size: 1.875rem;
  line-height: 2.25rem;
  text-align: center;
}

.faqs-container {
  margin-top: 5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  row-gap: 2rem;
}

.footer {
  margin-top: 5rem;
}

@media (max-width: 1360px) {
  .image-container {
    display: none;
  }

  .main-content-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  .services-container {
    margin-top: 12rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1.25rem;
  }

  .services-container h1 {
    color: black;
    font-size: 1.875rem;
    line-height: 2.25rem;
    font-weight: 700;
  }

  .heading-container h2 {
    font-size: 2.6rem;
    line-height: 4rem;
    color: black;
    font-weight: 700;
  }

  .animate-heading {
    position: relative;
    display: flex;
    width: 100%;
    padding-bottom: 4rem;
    text-align: center;
  }

  .last-heading-animate {
    position: absolute;
    cursor: pointer;
    font-size: 2.6rem;
    line-height: 4rem;
    letter-spacing: -0.025em;
    font-weight: 700;
    color: #c40f3a;
    width: max-content;
  }

  .desc-container {
    margin-top: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 3rem;
  }

  .desc-container p {
    width: 331px;
    height: 74px;
    color: black;
    text-align: center;
  }

  .features-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }

  .feature-card-container {
    height: 432px;
    width: 270px;
    background-color: #a6e36a80;
    border-radius: 1rem;
    margin-top: 4rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .feature-image-icon-container {
    margin-left: auto;
    margin-right: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 9999px;
    height: 4rem;
    width: 4rem;
  }

  .feature-image-icon-container img {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .scientist-gif {
    margin: 3rem;
  }

  .feature-text-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    transform: translate(0, -20px);
    padding-bottom: 2rem;
  }

  .feature-text-container p {
    color: #4b5563;
    text-align: center;
    padding-left: 1rem;
    padding-right: 1rem;
    font-weight: 700;
  }

  .feature-card-container2 {
    height: 432px;
    width: 270px;
    background-color: #a6e36a80;
    border-radius: 1rem;
    margin-top: 4rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .feature-card-container3 {
    height: 432px;
    width: 270px;
    background-color: #a6e36a80;
    border-radius: 1rem;
    margin-top: 4rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .feature-card-container4 {
    height: 432px;
    width: 270px;
    background-color: #a6e36a80;
    border-radius: 1rem;
    margin-top: 4rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}

@media (max-width: 560px) {
  .heading-container h2 {
    font-size: 1.7rem;
    line-height: 3rem;
    color: black;
    font-weight: 700;
  }

  .animate-heading {
    position: relative;
    display: flex;
    width: 100%;
    padding-bottom: 4rem;
    text-align: center;
  }

  .last-heading-animate {
    position: absolute;
    cursor: pointer;
    font-size: 1.7rem;
    line-height: 4rem;
    letter-spacing: -0.025em;
    font-weight: 700;
    color: #c40f3a;
    width: max-content;
  }
}

@media (max-width: 1150px) {
  .features-container {
    margin-bottom: 25rem;
  }
}

@media (max-width: 564px) {
  .features-container {
    margin-bottom: 85rem;
  }
}

@media (max-width: 1300px) {
  .why-us-img {
    display: none;
  }

  .why-us-text-card {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    width: 700px;
    justify-content: center;
    align-items: center;
    transform: translate(0, 20px);
    text-align: center;
  }
}

@media (max-width: 750px) {
  .why-us-text-card {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    width: 700px;
    justify-content: center;
    align-items: center;
    transform: translate(0, 20px);
    text-align: center;
  }

  .why-us-text-card h1 {
    color: black;
    font-size: 1.2rem;
    line-height: 2rem;
    font-weight: 700;
  }

  .why-us-text-card p {
    color: black;
    width: 360px;
    height: 106px;
  }
}
