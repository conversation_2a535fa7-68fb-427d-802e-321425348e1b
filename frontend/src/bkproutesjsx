import React, { Suspense, lazy } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import ProtectedRoute from './components/auth/ProtectedRoute';
import RoleBasedRoute from './components/auth/RoleBasedRoute';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { Box } from '@mui/material';
import MainContent from "./Components/MainContent/MainContent.jsx";
import About from "./Components/WhyUs/WhyUs.jsx";
import Services from "./Components/Services/Services.jsx";
import FaqContainer from "./Components/Faq/FaqContainer.jsx";
import Footer from "./Components/Footer/Footer.jsx";
import NavBar from "./Components/NavBar/NavBar.jsx";
import Dashboard from "./Pages/Dashboard/Dashboard.jsx";
import Login from "./Pages/Auth/Login.jsx";
import Register from "./Pages/Auth/Register.jsx";

// Pages
import App from "./App";
import Signup from "./Pages/Signup/Signup";
import NotFound404 from "./Pages/NotFound404/NotFound404";
import PlanSelect from "./Pages/PlanSelect/PlanSelect";
import Profile from "./Pages/Profile/Profile";
import ChatBotApp from "./Pages/ChatBot/ChatBotApp";
import MarketAnalysis from "./Pages/MarketTrends/MarketAnalysis";
import FarmerDash from "./Pages/FarmerDashboard/FarmerDash";
import HRLogin from "./Pages/HR/HRLogin";
import HRDash from "./Pages/HR/HRDash";
import Farms from "./Pages/Farms/Farms";
import Analysis from "./Pages/Analysis/Analysis";
import LegacyTMDashboard from './Pages/TMDashboard/LegacyTMDashboard';
import TMHome from './Pages/TMDashboard/Home/TMHome';
import FarmerOnboarding from './Pages/TMDashboard/Onboarding/FarmerOnboarding';
import MonitoringDashboard from './Pages/TMDashboard/Monitoring/MonitoringDashboard';
import LivestockMonitoring from './Pages/TMDashboard/Monitoring/LivestockMonitoring';
import LivestockDashboard from './Pages/FarmerDashboard/Livestock';
import InsightsDashboard from './Pages/TMDashboard/Insights/InsightsDashboard';
import AnalyticsDashboard from './Pages/TMDashboard/Analytics/AnalyticsDashboard';
import HRSignup from "./Pages/HR/HRSignup";
import TMDashboard from './Pages/TMDashboard/TMDashboard';
import IOTConfig from './Pages/TMDashboard/IOTConfig/IOTConfig';

// Error Boundary Component
class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }

    componentDidCatch(error, errorInfo) {
        console.error('Route Error:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div style={{ padding: '20px', textAlign: 'center' }}>
                    <h1>Something went wrong.</h1>
                    <pre>{this.state.error?.toString()}</pre>
                </div>
            );
        }
        return this.props.children;
    }
}

// Loading Component
const Loading = () => (
    <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        backgroundColor: '#ffffff'
    }}>
        <div style={{ textAlign: 'center' }}>
            <div style={{ 
                width: '50px', 
                height: '50px', 
                border: '5px solid #f3f3f3',
                borderTop: '5px solid #3498db',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                margin: '0 auto 20px'
            }} />
            <div>Loading application...</div>
        </div>
    </div>
);

// Lazy load components
const FarmerDash = lazy(() => import("./Pages/Dashboard/FarmerDash.jsx"));
const HRDash = lazy(() => import("./Pages/Dashboard/HRDash.jsx"));
const Profile = lazy(() => import("./Pages/Dashboard/DashComponents/Profile/Profile.jsx"));
const ChatBotApp = lazy(() => import("./Pages/Dashboard/DashComponents/ChatBot/ChatBotApp.jsx"));
const Farms = lazy(() => import("./Pages/Dashboard/DashComponents/Farms/Farms.jsx"));
const LivestockDashboard = lazy(() => import("./Pages/Dashboard/DashComponents/Livestock/LivestockDashboard.jsx"));
const MarketAnalysis = lazy(() => import("./Pages/Dashboard/DashComponents/MarketAnalysis/MarketAnalysis.jsx"));
const Analysis = lazy(() => import("./Pages/Dashboard/DashComponents/Analysis/Analysis.jsx"));
const PlanSelect = lazy(() => import("./Pages/Dashboard/DashComponents/PlanSelect/PlanSelect.jsx"));
const TMDashboard = lazy(() => import("./Pages/TMDashboard/TMDashboard.jsx"));
const TMHome = lazy(() => import("./Pages/TMDashboard/TMHome.jsx"));
const FarmerOnboarding = lazy(() => import("./Pages/TMDashboard/FarmerOnboarding.jsx"));
const MonitoringDashboard = lazy(() => import("./Pages/TMDashboard/MonitoringDashboard.jsx"));
const LivestockMonitoring = lazy(() => import("./Pages/TMDashboard/LivestockMonitoring.jsx"));
const InsightsDashboard = lazy(() => import("./Pages/TMDashboard/InsightsDashboard.jsx"));
const AnalyticsDashboard = lazy(() => import("./Pages/TMDashboard/AnalyticsDashboard.jsx"));
const IOTConfig = lazy(() => import("./Pages/TMDashboard/IOTConfig.jsx"));
const NotFound404 = lazy(() => import("./Pages/NotFound404.jsx"));

const AppRoutes = () => {
    const { currentUser } = useAuth();

    return (
        <ErrorBoundary>
            <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
                <NavBar />
                
                <Box component="main" sx={{ flexGrow: 1 }}>
                    <Suspense fallback={<Loading />}>
                        <Routes>
                            {/* Public routes */}
                            <Route path="/" element={
                                <>
                                    <MainContent />
                                    <Services />
                                    <About />
                                    <FaqContainer />
                                </>
                            } />
                            <Route path="/login" element={!currentUser ? <Login /> : <Navigate to="/dashboard" />} />
                            <Route path="/register" element={!currentUser ? <Register /> : <Navigate to="/dashboard" />} />

                            {/* Protected routes */}
                            <Route path="/dashboard" element={currentUser ? <Dashboard /> : <Navigate to="/login" />} />

                            {/* Farmer Routes */}
                            <Route path="/farmer-dashboard" element={
                                <RoleBasedRoute allowedRoles={['Farmer']}>
                                    <FarmerDash />
                                </RoleBasedRoute>
                            } />

                            {/* HR Routes */}
                            <Route path="/hrdash" element={
                                <RoleBasedRoute allowedRoles={['HR']}>
                                    <HRDash />
                                </RoleBasedRoute>
                            } />

                            <Route path="/dashboard/profile" element={
                                <ProtectedRoute>
                                    <Profile />
                                </ProtectedRoute>
                            } />
                            <Route path="/dashboard/chatbot" element={
                                <ProtectedRoute>
                                    <ChatBotApp />
                                </ProtectedRoute>
                            } />
                            <Route path="/farm" element={
                                <ProtectedRoute>
                                    <Farms />
                                </ProtectedRoute>
                            } />
                            <Route path="/livestock" element={
                                <ProtectedRoute>
                                    <LivestockDashboard />
                                </ProtectedRoute>
                            } />
                            <Route path="/market-analysis" element={
                                <ProtectedRoute>
                                    <MarketAnalysis />
                                </ProtectedRoute>
                            } />
                            <Route path="/analysis" element={
                                <ProtectedRoute>
                                    <Analysis />
                                </ProtectedRoute>
                            } />
                            <Route path="/planselect" element={
                                <ProtectedRoute>
                                    <PlanSelect />
                                </ProtectedRoute>
                            } />

                            {/* TM Dashboard with Nested Routes */}
                            <Route
                                path="/tm"
                                element={
                                    <RoleBasedRoute allowedRoles={['TM']}>
                                        <TMDashboard />
                                    </RoleBasedRoute>
                                }
                            >
                                <Route index element={<TMHome />} />
                                <Route path="onboarding" element={<FarmerOnboarding />} />
                                <Route path="monitoring" element={<MonitoringDashboard />} />
                                <Route path="livestock" element={<LivestockMonitoring />} />
                                <Route path="insights" element={<InsightsDashboard />} />
                                <Route path="analytics" element={<AnalyticsDashboard />} />
                                <Route path="iot-config" element={<IOTConfig />} />
                            </Route>

                            {/* Default Route for unmatched paths */}
                            <Route path="*" element={<NotFound404 />} />
                        </Routes>
                    </Suspense>
                </Box>

                <Footer />
            </Box>
        </ErrorBoundary>
    );
};

const App = () => {
    return (
        <Router>
            <AuthProvider>
                <LanguageProvider>
                    <AppRoutes />
                </LanguageProvider>
            </AuthProvider>
        </Router>
    );
};

export default App;

