.faq-section {
    width: 80%;
    border: 1px solid #6b7280;
    border-radius: 0.75rem;
    outline: none;
    box-shadow: none;
    text-decoration: none;
    background-color: transparent;
}

.faq-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    outline: none;
    box-shadow: none;
    text-decoration: none;
    background-color: transparent;
    height: 66px;
}

.faq-card h1 {
    font-size: 1.25rem;
    line-height: 1.75rem;
    color: black;
    font-weight: 700;
    padding: 1rem;
}

.faq-icon {
    padding: 1rem;
    outline: none;
    text-decoration: none;
    box-shadow: none;
    background-color: transparent;
    cursor: pointer;
}

.fa-solid.fa-circle-chevron-down {
    font-size: 3rem;
    line-height: 1;
    outline: none;
    box-shadow: none;
    text-decoration: none;
    background-color: transparent;
    color: black;
}

.down-animation {
    overflow: hidden;
}

.faq-hr {
    border-top-width: 1px;
    border-color: #d1d5db;
}

.answer-container {
    padding: 2rem;
}

.answer-container p {
    color: #374151;
}

@media (max-width: 800px) {
    .faq-card h1 {
        font-size: 1rem;
        line-height: 1.75rem;
        color: black;
        font-weight: 700;
        padding: 1rem;
    }
}

@media (max-width: 660px) {
    .faq-card h1 {
        font-size: 0.85rem;
        line-height: 1rem;
        color: black;
        font-weight: 700;
        padding: 1rem;
    }
}
