/* Modern FAQ with 3D agricultural tech theme */
.modern-faq-container {
  position: relative;
  max-width: 1000px;
  margin: 80px auto;
  padding: 40px;
  background: linear-gradient(135deg, rgba(27, 94, 32, 0.9), rgba(46, 125, 50, 0.9), rgba(56, 142, 60, 0.9));
  border-radius: 20px;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  perspective: 1500px;
  transform-style: preserve-3d;
  font-family: 'Outfit', sans-serif;
  margin-top: 10rem; /* Match the existing faqs-heading margin-top */
}

/* Header section */
.faq-header {
  position: relative;
  margin-bottom: 40px;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faq-title-container {
  transform: translateZ(30px);
}

.faq-header h2 {
  font-size: 2.5rem;
  color: white;
  margin-bottom: 10px;
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
  transform-style: preserve-3d;
}

.faq-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20px;
}

.faq-tech-circle {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: rotate-circle 8s linear infinite;
  transform-style: preserve-3d;
  transform: translateZ(20px);
}

.faq-tech-circle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10px;
  height: 10px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
}

/* FAQ Items */
.faq-items-container {
  position: relative;
  z-index: 2;
  transform-style: preserve-3d;
}

.faq-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  border-radius: 12px;
  margin-bottom: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform-style: preserve-3d;
  transform: translateZ(20px);
  transition: all 0.3s ease;
}

.faq-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateZ(30px) scale(1.01);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.faq-item.active {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2), 0 0 20px rgba(255, 255, 255, 0.2);
}

.faq-question {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transform-style: preserve-3d;
}

.faq-question-text {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  transform: translateZ(10px);
}

.faq-icon-container {
  width: 24px;
  height: 24px;
  position: relative;
  transform: translateZ(15px);
}

.faq-plus-icon {
  position: absolute;
  width: 24px;
  height: 24px;
}

.faq-plus-icon::before,
.faq-plus-icon::after {
  content: '';
  position: absolute;
  background-color: rgba(255, 255, 255, 1);
  border-radius: 2px;
}

.faq-plus-icon::before {
  width: 2px;
  height: 24px;
  top: 0;
  left: 11px;
}

.faq-plus-icon::after {
  width: 24px;
  height: 2px;
  top: 11px;
  left: 0;
}

.faq-answer {
  overflow: hidden;
  transform-style: preserve-3d;
}

.faq-answer-content {
  padding: 0 20px 20px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  transform: translateZ(5px);
}

/* Decorative elements */
.faq-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

/* Data stream effect */
@keyframes data-flow {
  0% { transform: translateY(-100%); opacity: 0; }
  50% { transform: translateY(0); opacity: 1; }
  100% { transform: translateY(100%); opacity: 0; }
}

.faq-data-stream {
  position: absolute;
  top: 0;
  right: 80px;
  width: 1px;
  height: 100%;
  background: linear-gradient(0deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  z-index: 1;
  animation: data-flow 4s infinite ease-out;
}

.faq-data-stream::before {
  content: '';
  position: absolute;
  top: 0;
  left: -40px;
  width: 1px;
  height: 100%;
  background: linear-gradient(0deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: data-flow 6s infinite ease-out 1s;
}

.faq-data-stream::after {
  content: '';
  position: absolute;
  top: 0;
  left: 40px;
  width: 1px;
  height: 100%;
  background: linear-gradient(0deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: data-flow 5s infinite ease-out 2s;
}

/* Grid effect */
.faq-grid {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 40px 40px;
  opacity: 0.2;
}

/* Animation keyframes */
@keyframes rotate-circle {
  0% { transform: translateZ(20px) rotate(0deg); }
  100% { transform: translateZ(20px) rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modern-faq-container {
    padding: 30px 20px;
    margin: 40px 20px;
    margin-top: 8rem;
  }

  .faq-header h2 {
    font-size: 2rem;
  }

  .faq-subtitle {
    font-size: 1rem;
  }

  .faq-question-text {
    font-size: 1.1rem;
  }

  .faq-tech-circle {
    width: 80px;
    height: 80px;
  }
}

@media (max-width: 480px) {
  .modern-faq-container {
    padding: 20px 15px;
    margin-top: 6rem;
  }

  .faq-header h2 {
    font-size: 1.8rem;
  }

  .faq-subtitle {
    font-size: 0.9rem;
  }

  .faq-question-text {
    font-size: 1rem;
  }

  .faq-tech-circle {
    width: 60px;
    height: 60px;
    right: -10px;
    top: -10px;
  }

  .faq-data-stream, .faq-grid {
    opacity: 0.15; /* Reduce background elements opacity on mobile */
  }
}
