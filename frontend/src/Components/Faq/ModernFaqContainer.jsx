import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import "./ModernFaq.css";
import { useTranslation } from "react-i18next";

const ModernFaqContainer = () => {
  const { t } = useTranslation();
  // FAQ data with agricultural questions
  const faqData = [
    {
      id: 1,
      question: t("faq1"),
      answer: t("faq1_ans"),
    },
    {
      id: 2,
      question: t("faq2"),
      answer: t("faq2_ans"),
    },
    {
      id: 3,
      question: t("faq3"),
      answer: t("faq3_ans"),
    },
    {
      id: 4,
      question: t("faq4"),
      answer: t("faq4_ans"),
    },
    {
      id: 5,
      question: t("faq5"),
      answer: t("faq5_ans"),
    },
  ];
  const [activeIndex, setActiveIndex] = useState(null);

  const toggleFaq = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  return (
    <div className="modern-faq-container" id="faq">
      <div className="faq-header">
        <div className="faq-title-container">
          <h2>{t("faq_heading")}</h2>
          <div className="faq-subtitle">{t("faq_subheading")}</div>
        </div>
        <div className="faq-tech-circle"></div>
      </div>

      <div className="faq-items-container">
        {faqData.map((faq, index) => (
          <div
            className={`faq-item ${activeIndex === index ? "active" : ""}`}
            key={faq.id}
            onClick={() => toggleFaq(index)}
          >
            <div className="faq-question">
              <span className="faq-question-text">{faq.question}</span>
              <div className="faq-icon-container">
                <motion.div
                  animate={{ rotate: activeIndex === index ? 45 : 0 }}
                  transition={{ duration: 0.3 }}
                  className="faq-icon"
                >
                  <span className="faq-plus-icon"></span>
                </motion.div>
              </div>
            </div>

            <AnimatePresence>
              {activeIndex === index && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="faq-answer"
                >
                  <div className="faq-answer-content">
                    <p>{faq.answer}</p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ))}
      </div>

      <div className="faq-decoration">
        <div className="faq-data-stream"></div>
        <div className="faq-grid"></div>
      </div>
    </div>
  );
};

export default ModernFaqContainer;
