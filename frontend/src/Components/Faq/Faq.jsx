import React from "react";
import { useState, useRef } from "react";
import { motion } from "framer-motion";
import "./Faq.css";

const Faq = ({ question, answer }) => {
    const [isOpen, setIsOpen] = useState(false);
    const contentRef = useRef(null);

    const toggleDropdown = () => {
        setIsOpen(!isOpen);
    };
    return (
        <div className="faq-section">
            {/* Header Section with Question and Icon */}
            <div className="faq-card">
                <h1>{question}</h1>
                {/* Toggling between down/up icons */}
                <motion.div
                    className="faq-icon"
                    onClick={toggleDropdown}
                    animate={{ rotate: isOpen ? 180 : 0 }}
                    transition={{ duration: 0.5 }}
                >
                    <i className="fa-solid fa-circle-chevron-down"></i>
                </motion.div>
            </div>

            {/* Expanding Content (hr + answer) inside the same border but below the header */}
            <motion.div
                initial={false}
                animate={isOpen ? "open" : "closed"}
                variants={{
                    open: {
                        height: contentRef.current?.scrollHeight,
                        opacity: 1,
                    },
                    closed: { height: 0, opacity: 0 },
                }}
                transition={{ duration: 0.5 }}
                className="down-animation"
            >
                <div ref={contentRef}>
                    <hr className="faq-hr" />
                    <div className="answer-container">
                        <p>{answer}</p>
                    </div>
                </div>
            </motion.div>
        </div>
    );
};

export default Faq;
