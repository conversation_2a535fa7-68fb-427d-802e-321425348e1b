import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const faqs = [
  {
    question: 'What is AgriCare?',
    answer: 'AgriCare is a comprehensive smart farming platform that helps farmers optimize their operations through data-driven insights, expert advice, and advanced agricultural technologies.',
  },
  {
    question: 'How does AgriCare help farmers?',
    answer: 'AgriCare provides farmers with real-time monitoring, predictive analytics, expert consultations, and smart farming solutions to improve crop yields and operational efficiency.',
  },
  {
    question: 'Is AgriCare suitable for all types of farms?',
    answer: 'Yes, AgriCare is designed to be adaptable to various farm sizes and types. Our platform can be customized to meet the specific needs of different agricultural operations.',
  },
  {
    question: 'What kind of support do you offer?',
    answer: 'We provide 24/7 technical support, regular training sessions, and access to agricultural experts who can help you with any farming-related queries or challenges.',
  },
  {
    question: 'How secure is my farm data?',
    answer: 'We implement enterprise-grade security measures to protect your data. All information is encrypted, and we follow strict privacy protocols to ensure your farm data remains confidential.',
  },
];

const FaqContainer = () => {
  const [expanded, setExpanded] = useState(false);

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  return (
    <Box id="faq" sx={{ py: 8, bgcolor: 'background.default' }}>
      <Container maxWidth="md">
        <Typography
          variant="h3"
          component="h2"
          align="center"
          gutterBottom
          sx={{ mb: 6 }}
        >
          Frequently Asked Questions
        </Typography>
        {faqs.map((faq, index) => (
          <Accordion
            key={index}
            expanded={expanded === `panel${index}`}
            onChange={handleChange(`panel${index}`)}
            sx={{ mb: 2 }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls={`panel${index}bh-content`}
              id={`panel${index}bh-header`}
            >
              <Typography variant="h6">{faq.question}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography color="text.secondary">{faq.answer}</Typography>
            </AccordionDetails>
          </Accordion>
        ))}
      </Container>
    </Box>
  );
};

export default FaqContainer;
