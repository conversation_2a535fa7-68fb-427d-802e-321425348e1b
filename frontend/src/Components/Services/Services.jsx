import React from "react";
import {
  Box,
  Container,
  Grid,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  But<PERSON>,
} from "@mui/material";
import { Link } from "react-router-dom";
import {
  Agriculture as AgricultureIcon,
  Analytics as AnalyticsIcon,
  SupportAgent as SupportIcon,
  Security as SecurityIcon,
  Cloud as CloudIcon,
  EnergySavingsLeaf as EcoIcon,
  LocalShipping as ShippingIcon,
  Biotech as BiotechIcon,
} from "@mui/icons-material";
import { useTranslation } from "react-i18next";

const Services = () => {
  const { t } = useTranslation();
  const services = [
    {
      title: t("our_services_sub1"),
      description: t("our_services_desc1"),
      icon: <BiotechIcon sx={{ fontSize: 40 }} />,
      path: "/dashboard",
    },
    {
      title: t("our_services_sub2"),
      description: t("our_services_desc2"),
      icon: <CloudIcon sx={{ fontSize: 40 }} />,
      path: "/dashboard",
    },
    {
      title: t("our_services_sub3"),
      description: t("our_services_desc3"),
      icon: <EcoIcon sx={{ fontSize: 40 }} />,
      path: "/farm",
    },
    {
      title: t("our_services_sub4"),
      description: t("our_services_desc4"),
      icon: <ShippingIcon sx={{ fontSize: 40 }} />,
      path: "/market-analysis",
    },
  ];
  return (
    <Box id="services" sx={{ py: 8, bgcolor: "background.default" }}>
      <Container maxWidth="lg">
        <Typography
          variant="h3"
          component="h2"
          align="center"
          gutterBottom
          sx={{ mb: 6 }}
        >
          {t("our_services_heading")}
        </Typography>
        <Grid container spacing={4}>
          {services.map((service, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card
                sx={{
                  height: "100%",
                  display: "flex",
                  flexDirection: "column",
                  transition: "transform 0.2s",
                  "&:hover": {
                    transform: "translateY(-8px)",
                  },
                }}
              >
                <CardContent
                  sx={{
                    flexGrow: 1,
                    textAlign: "center",
                    display: "flex",
                    flexDirection: "column",
                    height: "100%",
                  }}
                >
                  <Box sx={{ color: "primary.main", mb: 2 }}>
                    {service.icon}
                  </Box>
                  <Typography gutterBottom variant="h5" component="h3">
                    {service.title}
                  </Typography>
                  <Typography color="text.secondary" sx={{ mb: 2 }}>
                    {service.description}
                  </Typography>
                  <Box sx={{ mt: "auto", pt: 2 }}>
                    <Button
                      component={Link}
                      to={service.path}
                      variant="outlined"
                      color="primary"
                      size="small"
                    >
                      {t("learn")}
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
};

export default Services;
