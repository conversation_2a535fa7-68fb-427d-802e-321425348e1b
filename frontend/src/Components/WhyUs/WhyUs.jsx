import React from "react";
import { Box, Container, Grid, Typography, Paper } from "@mui/material";
import SpeedOutlined from "@mui/icons-material/SpeedOutlined";
import NatureOutlined from "@mui/icons-material/NatureOutlined";
import PetsOutlined from "@mui/icons-material/PetsOutlined";
import SmartToyOutlined from "@mui/icons-material/SmartToyOutlined";
import TrendingUpOutlined from "@mui/icons-material/TrendingUpOutlined";
import AccountBalanceOutlined from "@mui/icons-material/AccountBalanceOutlined";
import SchoolOutlined from "@mui/icons-material/SchoolOutlined";
import AnalyticsOutlined from "@mui/icons-material/AnalyticsOutlined";
import { useTranslation } from "react-i18next";

const WhyUs = () => {
  const { t } = useTranslation();
  const features = [
    {
      title: t("why_us_sub1"),
      icon: <NatureOutlined sx={{ fontSize: 40 }} />,
      description: t("why_us_desc1"),
    },
    {
      title: t("why_us_sub2"),
      icon: <PetsOutlined sx={{ fontSize: 40 }} />,
      description: t("why_us_desc2"),
    },
    {
      title: t("why_us_sub3"),
      icon: <SmartToyOutlined sx={{ fontSize: 40 }} />,
      description: t("why_us_desc3"),
    },
    {
      title: t("why_us_sub4"),
      icon: <TrendingUpOutlined sx={{ fontSize: 40 }} />,
      description: t("why_us_desc4"),
    },
    {
      title: t("why_us_sub5"),
      icon: <AccountBalanceOutlined sx={{ fontSize: 40 }} />,
      description: t("why_us_desc5"),
    },
    {
      title: t("why_us_sub6"),
      icon: <SchoolOutlined sx={{ fontSize: 40 }} />,
      description: t("why_us_desc6"),
    },
    {
      title: t("why_us_sub7"),
      icon: <AnalyticsOutlined sx={{ fontSize: 40 }} />,
      description: t("why_us_desc7"),
    },
    {
      title: t("why_us_sub8"),
      icon: <SpeedOutlined sx={{ fontSize: 40 }} />,
      description: t("why_us_desc8"),
    },
  ];

  return (
    <Box id="about" sx={{ py: 8, bgcolor: "background.paper" }}>
      <Container maxWidth="lg">
        <Typography
          variant="h3"
          component="h2"
          align="center"
          gutterBottom
          sx={{ mb: 6 }}
        >
          {t("why_us_heading")}
        </Typography>
        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid item xs={12} sm={6} md={3} lg={3} key={index}>
              <Paper
                elevation={3}
                sx={{
                  p: 3,
                  height: "100%",
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  textAlign: "center",
                  transition: "transform 0.2s",
                  "&:hover": {
                    transform: "translateY(-8px)",
                  },
                }}
              >
                <Box sx={{ color: "primary.main", mb: 2 }}>{feature.icon}</Box>
                <Typography gutterBottom variant="h5" component="h3">
                  {feature.title}
                </Typography>
                <Typography color="text.secondary">
                  {feature.description}
                </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
};

export default WhyUs;
