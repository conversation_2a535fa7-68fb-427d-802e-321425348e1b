import React, { useState, useEffect } from 'react';
import { Box, Button, Typography, Paper } from '@mui/material';
import { useLanguage } from '../../contexts/LanguageContext';
import translationPreloader from '../../services/TranslationPreloader';

/**
 * Test component to verify translation preloading works
 * This can be used for debugging and testing
 */
const TranslationPreloaderTest = () => {
  const { selectedLanguage } = useLanguage();
  const [testResults, setTestResults] = useState({});
  const [isTesting, setIsTesting] = useState(false);

  const testTexts = [
    'Temperature',
    'Soil Analysis',
    'Crop Health',
    'Moderate presence',
    'Treatment',
    'Fertilization'
  ];

  const runTest = async () => {
    setIsTesting(true);
    const results = {};

    for (const text of testTexts) {
      const startTime = performance.now();
      const translation = await translationPreloader.getPreloadedTranslation(text, selectedLanguage);
      const endTime = performance.now();
      
      results[text] = {
        translation,
        time: endTime - startTime,
        cached: !!translation
      };
    }

    setTestResults(results);
    setIsTesting(false);
  };

  const clearCache = () => {
    translationPreloader.clearCache();
    setTestResults({});
  };

  const startPreloading = async () => {
    setIsTesting(true);
    await translationPreloader.startPreloading(selectedLanguage);
    setIsTesting(false);
  };

  return (
    <Paper sx={{ p: 2, m: 2, maxWidth: 600 }}>
      <Typography variant="h6" gutterBottom>
        Translation Preloader Test
      </Typography>
      
      <Typography variant="body2" color="text.secondary" gutterBottom>
        Current Language: {selectedLanguage}
      </Typography>

      <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
        <Button 
          variant="contained" 
          onClick={runTest}
          disabled={isTesting}
        >
          Test Cache
        </Button>
        
        <Button 
          variant="outlined" 
          onClick={startPreloading}
          disabled={isTesting}
        >
          Start Preloading
        </Button>
        
        <Button 
          variant="outlined" 
          color="error"
          onClick={clearCache}
        >
          Clear Cache
        </Button>
      </Box>

      {Object.keys(testResults).length > 0 && (
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Test Results:
          </Typography>
          {Object.entries(testResults).map(([text, result]) => (
            <Box key={text} sx={{ mb: 1, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="body2">
                <strong>{text}:</strong> {result.translation || 'Not cached'}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Time: {result.time.toFixed(2)}ms | Cached: {result.cached ? 'Yes' : 'No'}
              </Typography>
            </Box>
          ))}
        </Box>
      )}

      {isTesting && (
        <Typography variant="body2" color="primary">
          Testing...
        </Typography>
      )}
    </Paper>
  );
};

export default TranslationPreloaderTest; 