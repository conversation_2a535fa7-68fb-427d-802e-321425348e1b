import React, { useState, useEffect } from 'react';
import { Box, LinearProgress, Typography, Fade } from '@mui/material';
import { useLanguage } from '../../contexts/LanguageContext';
import translationPreloader from '../../services/TranslationPreloader';

/**
 * Component that shows translation preloading progress
 * Appears briefly when language changes to non-English
 */
const TranslationPreloader = () => {
  const { selectedLanguage } = useLanguage();
  const [isPreloading, setIsPreloading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showProgress, setShowProgress] = useState(false);

  useEffect(() => {
    // Only show for non-English languages
    if (selectedLanguage === 'en-IN' || selectedLanguage === 'en') {
      setShowProgress(false);
      setIsPreloading(false);
      return;
    }

    // Show progress indicator
    setShowProgress(true);
    setIsPreloading(true);
    setProgress(0);

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 200);

    // Complete progress after a delay
    const completeTimeout = setTimeout(() => {
      setProgress(100);
      setTimeout(() => {
        setShowProgress(false);
        setIsPreloading(false);
      }, 500);
    }, 3000);

    return () => {
      clearInterval(progressInterval);
      clearTimeout(completeTimeout);
    };
  }, [selectedLanguage]);

  if (!showProgress) {
    return null;
  }

  return (
    <Fade in={showProgress}>
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 9999,
          bgcolor: 'primary.main',
          color: 'white',
          py: 1,
          px: 2,
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}
      >
        <Typography variant="body2" sx={{ whiteSpace: 'nowrap' }}>
          Loading translations...
        </Typography>
        <LinearProgress
          variant="determinate"
          value={progress}
          sx={{
            flexGrow: 1,
            height: 4,
            borderRadius: 2,
            bgcolor: 'rgba(255,255,255,0.3)',
            '& .MuiLinearProgress-bar': {
              bgcolor: 'white'
            }
          }}
        />
        <Typography variant="body2" sx={{ whiteSpace: 'nowrap' }}>
          {progress}%
        </Typography>
      </Box>
    </Fade>
  );
};

export default TranslationPreloader; 