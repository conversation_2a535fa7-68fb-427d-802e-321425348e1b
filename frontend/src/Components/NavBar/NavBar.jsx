import React, { useState } from "react";
import {
  AppBar,
  Box,
  Tool<PERSON>,
  IconButton,
  Typography,
  Menu,
  Container,
  Avatar,
  Button,
  Tooltip,
  MenuItem,
} from "@mui/material";
import quaminLogo from "../../assets/quaminLogo.webp";
import MenuIcon from "@mui/icons-material/Menu";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { useLanguage } from "../../contexts/LanguageContext";
import { getTranslation } from "../../translations";
import { useTranslation } from "react-i18next";

const NavBar = () => {
  const { t } = useTranslation();

  const pages = [
    { title: t("home"), path: "/" },
    { title: t("services"), path: "#services" },
    { title: t("about"), path: "#about" },
    { title: t("faq"), path: "#faq" },
  ];
  const [anchorElNav, setAnchorElNav] = useState(null);
  const [anchorElUser, setAnchorElUser] = useState(null);
  const navigate = useNavigate();
  const { currentUser, logout } = useAuth();
  const { selectedLanguage } = useLanguage();

  const handleOpenNavMenu = (event) => {
    setAnchorElNav(event.currentTarget);
  };
  const handleOpenUserMenu = (event) => {
    setAnchorElUser(event.currentTarget);
  };

  const handleCloseNavMenu = () => {
    setAnchorElNav(null);
  };

  const handleCloseUserMenu = () => {
    setAnchorElUser(null);
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate("/");
    } catch (error) {
      console.error("Failed to log out:", error);
    }
  };

  return (
    <AppBar
      position="static"
      sx={{
        background: "linear-gradient(90deg, #2e7d32 0%, #388e3c 100%)",
        boxShadow: "0 4px 20px rgba(0, 0, 0, 0.15)",
      }}
    >
      <Container maxWidth="xl" sx={{ px: { xs: 1, sm: 2, md: 3 } }}>
        <Toolbar disableGutters>
          {/* Logo - Desktop */}
          <Box
            sx={{
              display: { xs: "none", md: "flex" },
              alignItems: "center",
              mr: 2,
              cursor: "pointer",
            }}
            onClick={() => navigate("/")}
          >
            <Box
              component="img"
              src={quaminLogo}
              alt="Quamin Agricare"
              sx={{
                height: 40,
                objectFit: "contain",
                filter: "brightness(0) invert(1)", // Makes the logo white
              }}
            />
          </Box>

          {/* Mobile Menu */}
          <Box sx={{ flexGrow: 1, display: { xs: "flex", md: "none" } }}>
            <IconButton
              size="large"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleOpenNavMenu}
              color="inherit"
            >
              <MenuIcon />
            </IconButton>
            <Menu
              id="menu-appbar"
              anchorEl={anchorElNav}
              anchorOrigin={{
                vertical: "bottom",
                horizontal: "left",
              }}
              keepMounted
              transformOrigin={{
                vertical: "top",
                horizontal: "left",
              }}
              open={Boolean(anchorElNav)}
              onClose={handleCloseNavMenu}
              sx={{
                display: { xs: "block", md: "none" },
                "& .MuiPaper-root": {
                  borderRadius: "8px",
                  boxShadow: "0 8px 16px rgba(0, 0, 0, 0.1)",
                  border: "1px solid rgba(46, 125, 50, 0.1)",
                  minWidth: "200px",
                },
              }}
            >
              {pages.map((page) => (
                <MenuItem
                  key={page.title}
                  onClick={() => {
                    handleCloseNavMenu();
                    if (page.path.startsWith("#")) {
                      // For anchor links, scroll to the section
                      const element = document.getElementById(
                        page.path.substring(1)
                      );
                      if (element) {
                        element.scrollIntoView({ behavior: "smooth" });
                      } else if (window.location.pathname !== "/") {
                        // If we're not on the home page, navigate to home and then to the anchor
                        navigate("/" + page.path);
                      }
                    } else {
                      // For regular links, use navigate
                      navigate(page.path);
                    }
                  }}
                  sx={{
                    borderRadius: "4px",
                    my: 0.5,
                    px: 1,
                    py: 1,
                    transition: "all 0.2s ease",
                    "&:hover": {
                      backgroundColor: "rgba(46, 125, 50, 0.1)",
                    },
                  }}
                >
                  <Typography sx={{ fontWeight: 500 }}>
                    {getTranslation(page.title, selectedLanguage)}
                  </Typography>
                </MenuItem>
              ))}
            </Menu>
          </Box>

          {/* Mobile Logo */}
          <Box
            sx={{
              display: { xs: "flex", md: "none" },
              alignItems: "center",
              flexGrow: 1,
              cursor: "pointer",
            }}
            onClick={() => navigate("/")}
          >
            <Box
              component="img"
              src={quaminLogo}
              alt="Quamin Agricare"
              sx={{
                height: 32,
                objectFit: "contain",
                filter: "brightness(0) invert(1)", // Makes the logo white
              }}
            />
          </Box>

          {/* Desktop Menu */}
          <Box
            sx={{
              flexGrow: 1,
              display: { xs: "none", md: "flex" },
              justifyContent: "center",
            }}
          >
            {pages.map((page) => (
              <Button
                key={page.title}
                onClick={() => {
                  handleCloseNavMenu();
                  if (page.path.startsWith("#")) {
                    // For anchor links, scroll to the section
                    const element = document.getElementById(
                      page.path.substring(1)
                    );
                    if (element) {
                      element.scrollIntoView({ behavior: "smooth" });
                    } else if (window.location.pathname !== "/") {
                      // If we're not on the home page, navigate to home and then to the anchor
                      navigate("/" + page.path);
                    }
                  } else {
                    // For regular links, use navigate
                    navigate(page.path);
                  }
                }}
                sx={{
                  my: 2,
                  mx: 1.5,
                  px: 2,
                  py: 0.8,
                  color: "white",
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  borderRadius: "4px",
                  position: "relative",
                  overflow: "hidden",
                  fontWeight: 500,
                  fontSize: "0.85rem", // Smaller font size
                  transition: "all 0.3s ease",
                  "&:hover": {
                    background: "rgba(255, 255, 255, 0.15)",
                    transform: "translateY(-2px)",
                    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
                  },
                  "&::after": {
                    content: '""',
                    position: "absolute",
                    bottom: 0,
                    left: "50%",
                    width: 0,
                    height: "2px",
                    background: "#ffffff",
                    transition: "all 0.3s ease",
                    transform: "translateX(-50%)",
                  },
                  "&:hover::after": {
                    width: "80%",
                  },
                }}
              >
                {/* {getTranslation(page.title, selectedLanguage)} */}
                {page.title}
              </Button>
            ))}
          </Box>

          {/* User Menu */}
          <Box
            sx={{ flexGrow: 0, display: "flex", alignItems: "center", gap: 2 }}
          >
            {currentUser ? (
              <Tooltip title="Open settings">
                <IconButton
                  onClick={handleOpenUserMenu}
                  sx={{
                    p: 0.5,
                    border: "2px solid rgba(255, 255, 255, 0.7)",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      border: "2px solid rgba(255, 255, 255, 1)",
                      transform: "scale(1.05)",
                    },
                  }}
                >
                  <Avatar
                    alt={currentUser?.name || "User"}
                    src={currentUser?.photoURL}
                  >
                    {currentUser?.name?.[0] || "U"}
                  </Avatar>
                </IconButton>
              </Tooltip>
            ) : (
              <Button
                variant="outlined"
                color="inherit"
                onClick={() => navigate("/login")}
                sx={{
                  borderColor: "rgba(255, 255, 255, 0.7)",
                  "&:hover": {
                    borderColor: "rgba(255, 255, 255, 1)",
                    backgroundColor: "rgba(255, 255, 255, 0.1)",
                  },
                }}
              >
                {t("login")}
              </Button>
            )}
            <Menu
              sx={{
                mt: "45px",
                "& .MuiPaper-root": {
                  borderRadius: "8px",
                  boxShadow: "0 8px 16px rgba(0, 0, 0, 0.1)",
                  border: "1px solid rgba(46, 125, 50, 0.1)",
                  minWidth: "200px",
                },
              }}
              id="menu-appbar"
              anchorEl={anchorElUser}
              anchorOrigin={{
                vertical: "top",
                horizontal: "right",
              }}
              keepMounted
              transformOrigin={{
                vertical: "top",
                horizontal: "right",
              }}
              open={Boolean(anchorElUser)}
              onClose={handleCloseUserMenu}
            >
              <MenuItem
                onClick={() => {
                  handleCloseUserMenu();
                  navigate("/dashboard");
                }}
              >
                <Typography textAlign="center">Dashboard</Typography>
              </MenuItem>
              <MenuItem
                onClick={() => {
                  handleCloseUserMenu();
                  navigate("/profile");
                }}
              >
                <Typography textAlign="center">Profile</Typography>
              </MenuItem>
              <MenuItem
                onClick={() => {
                  handleCloseUserMenu();
                  handleLogout();
                }}
              >
                <Typography textAlign="center">Logout</Typography>
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
};

export default NavBar;
