.navbar-container {
    padding: 1rem;
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: sticky;
    z-index: 10;
    top: 0;
    background-color: #f1eeeb;
    box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
}

.nav-items-container {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.quamin-logo {
    --tw-text-opacity: 1;
    color: rgb(0 0 0 / var(--tw-text-opacity));
    font-size: 1.25rem;
    line-height: 1.75rem;
}

.nav-menu {
    --tw-text-opacity: 1;
    color: rgb(0 0 0 / var(--tw-text-opacity));
    display: flex;
    gap: 4rem;
}

.nav-menu-link {
    font-size: 1.15rem;
    line-height: 1.75rem;
    transition-duration: 150ms;
    animation-duration: 150ms;
    text-decoration: none;
    color: inherit;
}

.nav-menu-link:hover {
    --tw-text-opacity: 1;
    color: rgb(21 128 61 / var(--tw-text-opacity));
    text-decoration: none;
    transform: translateY(-0.25rem);
    cursor: pointer;
}

.login-button-mobile {
    display: none;
}

.ham-menu {
    display: none;
}

button {
    cursor: pointer;
}

@media (max-width: 1000px) {
    .navbar-container {
        padding: 1rem;
        display: flex;
        justify-content: space-between;
        position: sticky;
        z-index: 10;
        top: 0;
        background-color: #f1eeeb;
        box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
    }

    .ham-button {
        background-color: transparent;
        border: none;
        outline: none;
        text-decoration: none;
        padding: 0.5rem;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: background-color 0.2s ease;
        color: black;
    }

    .ham-button:active {
        background-color: #d3d3d1;
        outline: none;
        text-decoration: none;
        color: black;
    }

    .login-button {
        display: none !important;
    }

    .fa-solid.fa-xmark {
        font-size: 1.5rem;
        color: black;
    }

    .fa-solid.fa-bars {
        font-size: 1.5rem;
        color: black;
    }

    .nav-menu {
        display: none;
    }

    .mobile-menu {
        position: fixed;
        top: 6rem;
        right: -300px;
        width: 18rem;
        height: fit-content;
        padding: 3rem;
        padding-left: 2rem;
        background-color: #f1eeeb;
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        border-radius: 1rem;
        z-index: 20;
        gap: 1.3rem;
        transition: right 0.5s ease-in-out;
    }

    .mobile-menu.open {
        right: 1rem;
    }

    .login-button-mobile {
        display: inline-block;
        text-decoration: none;
        outline: none;
        color: black;
        font-size: 1.25rem;
        line-height: 1.75rem;
        transition-duration: 150ms;
        animation-duration: 150ms;
    }

    .login-button-mobile:hover {
        color: #c40f3a;
        text-decoration: none;
        transform: translateY(-0.25rem);
        font-weight: 700;
    }

    .ham-menu {
        display: inline-block;
    }
}

.login-button {
    margin-left: 2.5rem;
    font-size: 1.1rem;
    background-color: #c40f3a;
    padding: 0.5rem;
    font-weight: bold;
    border-radius: 0.5rem;
    width: 8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.login-button:hover {
    transform: scale(1.1);
}

.login-button:hover .fa-solid.fa-arrow-right-long {
    transform: translateX(0.25rem);
    transition: transform 200ms ease;
}

.fa-solid.fa-arrow-right-long {
    font-size: 1.5rem;
    color: white;
    transition: transform 200ms ease;
}

.login-button h1 {
    font-weight: 600;
    font-size: 1.25rem;
    color: white;
}

.welcome-msg {
    font-size: 0.75rem;
    font-weight: 600;
}
