.banner {
  padding: 0 5%;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 120px;
  background-color: #0a0a0a;
  color: #fafafa;
  z-index: 999;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
}

.banner-flex-left {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.banner-flex-left-name {
  text-transform: uppercase;
  font-weight: 600;
  font-size: 24px;
}

.banner-flex-left-tier {
  background-color: #97f5ff;
  color: #0a0a0a;
  font-size: 10px;
  font-weight: 800;
  text-transform: uppercase;
  padding: 2px;
  border-radius: 2px;
}

.banner-flex-mid {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.banner-flex-mid img {
  width: 80px;
  height: 80px;
}

.banner-flex-right {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 32px;
}

.profile-button {
  text-decoration: none;
  color: #fafafa;
  font-size: 26px;
  font-weight: 600;
}

a {
  text-decoration: none;
  color: #fafafa;
}

.logout-btn {
  background-color: rgb(82, 82, 82);
  color: #fafafa;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 18px;
  border: none;
}
