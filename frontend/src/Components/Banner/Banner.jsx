import React from "react";
import { Link } from "react-router-dom";
import "./Banner.css";
import profileImg from "../../assets/profile_logo.png";
import quaminLogo from "../../assets/QuaminGoldMain.webp";

const Banner = () => {
  return (
    <div className="banner">
      <div className="banner-flex-left">
        <img src={profileImg} alt="" />
        <Link to="/dashboard/profile">
          <p className="banner-flex-left-name"><PERSON><PERSON></p>
        </Link>
        <p className="banner-flex-left-tier">Premium</p>
      </div>
      <div className="banner-flex-mid">
        <img src={quaminLogo} alt="quaminLogo" />
      </div>
      <div className="banner-flex-right">
        {/* Profile page links here */}
        <button className="logout-btn">Log out</button>
      </div>
    </div>
  );
};

export default Banner;
