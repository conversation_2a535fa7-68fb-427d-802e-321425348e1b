import { useEffect } from "react";
import Box from "@mui/material/Box";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import CircularProgress from "@mui/material/CircularProgress";
import { useLanguage } from "../../contexts/LanguageContext";
import { useTranslation } from "react-i18next";

const languages = [
  { code: "en-IN", name: "English" },
  { code: "hi", name: "हिंदी" },
  { code: "ta", name: "தமிழ்" },
  { code: "mr", name: "मराठी" },
  // { code: "bn", name: "বাংলা" },
  // { code: "te", name: "తెలుగు" },
  // { code: "kn", name: "ಕನ್ನಡ" },
  // { code: "ml", name: "മലയാളം" },
  // { code: "gu", name: "ગુજરાતી" },
  // { code: "pa", name: "ਪੰਜਾਬੀ" },
];

export default function LanguageSelector({ isLoading, size = "medium" }) {
  const { t } = useTranslation();
  const { selectedLanguage, setSelectedLanguage } = useLanguage();

  const handleChange = (event) => {
    setSelectedLanguage(event.target.value);
  };

  useEffect(() => {
    console.log("Current selectedLanguage is:", selectedLanguage);
  }, [selectedLanguage]);

  return (
    <Box sx={{ width: size === "small" ? "120px" : "250px", position: "relative" }}>
      <FormControl fullWidth size={size}>
        <InputLabel id="language-select-label">{t("lang")}</InputLabel>
        <Select
          labelId="language-select-label"
          id="language-select"
          value={selectedLanguage}
          label={t("lang")}
          onChange={handleChange}
          disabled={isLoading}
        >
          {languages.map((lang) => (
            <MenuItem key={lang.code} value={lang.code}>
              {lang.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      {isLoading && (
        <CircularProgress 
          size={24} 
          sx={{ 
            position: 'absolute', 
            top: '50%', 
            right: size === "small" ? 8 : 24, 
            marginTop: '-12px',
            color: 'primary.main'
          }} 
        />
      )}
    </Box>
  );
}
