import React, { useState } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import DynamicTranslation from '../../components/DynamicTranslation/DynamicTranslation';
import {
  AppBar,
  Box,
  Toolbar,
  IconButton,
  Typography,
  Menu,
  Container,
  Avatar,
  Button,
  Tooltip,
  MenuItem,
  ListItemIcon
} from '@mui/material';
import quaminLogo from '../../assets/quaminLogo.webp';
import {
  Menu as MenuIcon,
  Logout,
  Person,
  Dashboard,
  Agriculture,
  Analytics,
  Pets,
  Settings,
  TrendingUp,
  SmartToy as DroneIcon,
  AccountBalance as BankIcon,
  School as EducationIcon,
  Science as ScienceIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const DashboardNav = () => {
  const navigate = useNavigate();
  const { currentUser, logout } = useAuth();
  const { t } = useTranslation();
  const [anchorElNav, setAnchorElNav] = useState(null);
  const [anchorElUser, setAnchorElUser] = useState(null);

  const menuItems = [
    { text: t('menuItems.dashboard', 'Dashboard'), icon: <Dashboard />, path: '/farmer' },
    { text: t('menuItems.myFarm', 'My Farm'), icon: <Agriculture />, path: '/farmer/farm' },
    { text: t('menuItems.livestock', 'Livestock'), icon: <Pets />, path: '/farmer/livestock' },
    { text: t('menuItems.droneMonitoring', 'Drone Monitoring'), icon: <DroneIcon />, path: '/farmer/drone-monitoring' },
    { text: t('menuItems.marketAnalysis', 'Market Analysis'), icon: <TrendingUp />, path: '/farmer/market-analysis' },
    { text: t('menuItems.creditScore', 'Credit Score'), icon: <BankIcon />, path: '/farmer/credit-score' },
    { text: t('menuItems.soilAnalysis', 'Soil Analysis'), icon: <ScienceIcon />, path: '/farmer/soil-analysis' },
    { text: t('menuItems.agriXpertConnect', 'AgriXpert Connect'), icon: <EducationIcon />, path: '/farmer/agri-expert' },
    { text: t('menuItems.analysis', 'Analysis'), icon: <Analytics />, path: '/farmer/analysis' }
  ];

  const settings = [
    { title: t('userMenu.profile', 'Profile'), icon: <Person />, action: () => navigate('/profile') },
    { title: t('userMenu.settings', 'Settings'), icon: <Settings />, action: () => navigate('/settings') },
    { title: t('userMenu.logout', 'Logout'), icon: <Logout />, action: handleLogout }
  ];

  const handleOpenNavMenu = (event) => {
    setAnchorElNav(event.currentTarget);
  };

  const handleOpenUserMenu = (event) => {
    setAnchorElUser(event.currentTarget);
  };

  const handleCloseNavMenu = () => {
    setAnchorElNav(null);
  };

  const handleCloseUserMenu = () => {
    setAnchorElUser(null);
  };

  const handleNavigation = (path) => {
    navigate(path);
    handleCloseNavMenu();
  };

  async function handleLogout() {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Failed to log out:', error);
    }
  }

  return (
    <AppBar position="fixed" sx={{
      zIndex: (theme) => theme.zIndex.drawer + 1,
      background: 'linear-gradient(90deg, #2e7d32 0%, #388e3c 100%)',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)'
    }}>
      <Container maxWidth="xl" sx={{ px: { xs: 1, sm: 2, md: 3 } }}>
        <Toolbar disableGutters>
          {/* Logo - Desktop */}
          <Box
            sx={{
              display: { xs: 'none', md: 'flex' },
              alignItems: 'center',
              mr: 2,
              cursor: 'pointer'
            }}
            onClick={() => navigate('/dashboard')}
          >
            <Box
              component="img"
              src={quaminLogo}
              alt="Quamin Agricare"
              sx={{
                height: 40,
                objectFit: 'contain',
                filter: 'brightness(0) invert(1)',  // Makes the logo white
                transition: 'all 0.3s ease'
              }}
            />
          </Box>

          {/* Mobile Menu */}
          <Box sx={{ flexGrow: 1, display: { xs: 'flex', md: 'none' } }}>
            <IconButton
              size="large"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleOpenNavMenu}
              color="inherit"
            >
              <MenuIcon />
            </IconButton>
            <Menu
              id="menu-appbar"
              anchorEl={anchorElNav}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
              }}
              open={Boolean(anchorElNav)}
              onClose={handleCloseNavMenu}
              sx={{
                display: { xs: 'block', md: 'none' },
                '& .MuiPaper-root': {
                  borderRadius: '8px',
                  boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)',
                  border: '1px solid rgba(46, 125, 50, 0.1)',
                  minWidth: '200px'
                }
              }}
            >
              {menuItems.map((item) => (
                <MenuItem
                  key={item.text}
                  onClick={() => handleNavigation(item.path)}
                  sx={{
                    borderRadius: '4px',
                    my: 0.5,
                    px: 1,
                    py: 1,
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      backgroundColor: 'rgba(46, 125, 50, 0.1)'
                    }
                  }}
                >
                  <ListItemIcon sx={{ color: '#2e7d32', minWidth: 40 }}>{item.icon}</ListItemIcon>
                  <Typography sx={{ fontWeight: 500 }}><DynamicTranslation text={item.text} /></Typography>
                </MenuItem>
              ))}
            </Menu>
          </Box>

          {/* Logo - Mobile */}
          <Box
            sx={{
              display: { xs: 'flex', md: 'none' },
              alignItems: 'center',
              flexGrow: 1,
              cursor: 'pointer'
            }}
            onClick={() => navigate('/dashboard')}
          >
            <Box
              component="img"
              src={quaminLogo}
              alt="Quamin Agricare"
              sx={{
                height: 32,
                objectFit: 'contain',
                filter: 'brightness(0) invert(1)',  // Makes the logo white
                transition: 'all 0.3s ease'
              }}
            />
          </Box>

          {/* Desktop Menu */}
          <Box sx={{ flexGrow: 1, display: { xs: 'none', md: 'flex' }, justifyContent: 'flex-start', ml: 2 }}>
            {menuItems.map((item) => (
              <Button
                key={item.text}
                onClick={() => handleNavigation(item.path)}
                sx={{
                  my: 2,
                  mx: 0.75,  // Reduced spacing between items
                  px: 1.5,   // Reduced padding
                  py: 0.8,
                  color: 'white',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  borderRadius: '4px',
                  position: 'relative',
                  overflow: 'hidden',
                  fontWeight: 500,
                  fontSize: '0.85rem', // Smaller font size
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    background: 'rgba(255, 255, 255, 0.15)',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)'
                  },
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    bottom: 0,
                    left: '50%',
                    width: 0,
                    height: '2px',
                    background: '#ffffff',
                    transition: 'all 0.3s ease',
                    transform: 'translateX(-50%)'
                  },
                  '&:hover::after': {
                    width: '80%'
                  }
                }}
                startIcon={item.icon}
              >
                <DynamicTranslation text={item.text} />
              </Button>
            ))}
          </Box>

          {/* User Menu */}
          <Box sx={{ flexGrow: 0, display: 'flex', alignItems: 'center', gap: 1, ml: 'auto', mr: 1 }}>
            {currentUser ? (
              <Tooltip title="Open settings">
                <IconButton
                  onClick={handleOpenUserMenu}
                  sx={{
                    p: 0.5,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'scale(1.05)'
                    }
                  }}>
                  <Avatar alt={currentUser?.name || 'User'} src={currentUser?.photoURL}>
                    {currentUser?.name?.[0] || 'U'}
                  </Avatar>
                </IconButton>
              </Tooltip>
            ) : (
              <Button
                variant="outlined"
                color="inherit"
                onClick={() => navigate('/login')}
                sx={{
                  borderColor: 'rgba(255, 255, 255, 0.7)',
                  '&:hover': {
                    borderColor: 'rgba(255, 255, 255, 1)',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }
                }}
              >
                <DynamicTranslation text={t('login', 'Login')} />
              </Button>
            )}
            <Menu
              sx={{
                mt: '45px',
                '& .MuiPaper-root': {
                  borderRadius: '8px',
                  boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)',
                  border: '1px solid rgba(46, 125, 50, 0.1)',
                  minWidth: '200px'
                }
              }}
              id="menu-appbar"
              anchorEl={anchorElUser}
              anchorOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              open={Boolean(anchorElUser)}
              onClose={handleCloseUserMenu}
            >
              {settings.map((setting) => (
                <MenuItem
                  key={setting.title}
                  onClick={() => {
                    setting.action();
                    handleCloseUserMenu();
                  }}
                  sx={{
                    borderRadius: '4px',
                    my: 0.5,
                    px: 2,
                    py: 1,
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      backgroundColor: 'rgba(46, 125, 50, 0.1)'
                    }
                  }}
                >
                  <ListItemIcon sx={{ color: '#2e7d32', minWidth: 40 }}>{setting.icon}</ListItemIcon>
                  <Typography sx={{ fontWeight: 500 }}><DynamicTranslation text={setting.title} /></Typography>
                </MenuItem>
              ))}
            </Menu>
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
};

export default DashboardNav;