.navbar-container {
    padding: 0.75rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    z-index: 1000;
    top: 0;
    background-color: #f1eeeb;
    box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
}

.nav-items-container {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.quamin-logo {
    width: 140px;
    height: auto;
}

.nav-menu {
    display: flex;
    gap: 2.5rem;
    margin: 0 2rem;
}

.nav-menu-link {
    font-size: 0.95rem;
    line-height: 1.5rem;
    transition-duration: 150ms;
    text-decoration: none;
    color: #4a5568;
    font-weight: 500;
}

.nav-menu-link:hover {
    color: rgb(21 128 61);
    text-decoration: none;
    transform: translateY(-0.15rem);
}

.login-button-mobile {
    display: none;
    background: none;
    border: none;
    font-size: 0.95rem;
    color: inherit;
    padding: 0;
}

.ham-menu {
    display: none;
}

.login-button {
    margin-left: 1rem;
    font-size: 0.95rem;
    background-color: #c40f3a;
    padding: 0.5rem 1rem;
    font-weight: 600;
    border-radius: 0.375rem;
    width: auto;
    min-width: 6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
    transition: transform 0.2s ease;
    border: none;
    color: white;
}

.login-button:hover {
    transform: scale(1.05);
}

.login-button:hover .fa-solid.fa-arrow-right-long {
    transform: translateX(0.25rem);
}

.fa-solid.fa-arrow-right-long {
    font-size: 1.1rem;
    color: white;
    transition: transform 0.2s ease;
}

.login-button h1 {
    font-weight: 600;
    font-size: 0.95rem;
    color: white;
    margin: 0;
}

@media (max-width: 1000px) {
    .navbar-container {
        padding: 0.75rem 1rem;
    }

    .nav-menu {
        display: none;
    }

    .mobile-menu {
        position: fixed;
        top: 4.5rem;
        right: -300px;
        width: 16rem;
        height: auto;
        padding: 1.5rem;
        background-color: #f1eeeb;
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        border-radius: 0.5rem;
        z-index: 1000;
        gap: 1rem;
        transition: right 0.3s ease-in-out;
    }

    .mobile-menu.open {
        right: 1rem;
    }

    .login-button {
        display: none !important;
    }

    .login-button-mobile {
        display: inline-block;
        font-size: 0.95rem;
        font-weight: 500;
        padding: 0;
        text-align: left;
    }

    .login-button-mobile:hover {
        color: #c40f3a;
    }

    .ham-menu {
        display: inline-block;
    }

    .ham-button {
        background: none;
        border: none;
        padding: 0.5rem;
        color: #4a5568;
    }

    .fa-solid.fa-xmark,
    .fa-solid.fa-bars {
        font-size: 1.25rem;
    }
}

.welcome-msg {
    font-size: 0.75rem;
    font-weight: 600;
} 