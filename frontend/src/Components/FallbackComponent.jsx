import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  IconButton,
  Tooltip
} from '@mui/material';
import { Refresh as RefreshIcon } from '@mui/icons-material';

const FallbackComponent = ({ title, error, onRetry, children }) => {
  return (
    <Paper 
      sx={{ 
        p: 3, 
        bgcolor: 'rgba(255, 255, 255, 0.1)',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <Typography variant="h6" sx={{ color: 'white', mb: 2 }}>
        {title}
      </Typography>
      
      {error ? (
        <>
          <Typography color="error" sx={{ mb: 2, textAlign: 'center' }}>
            {error}
          </Typography>
          <Button
            variant="contained"
            startIcon={<RefreshIcon />}
            onClick={onRetry}
            sx={{ mt: 2 }}
          >
            Retry
          </Button>
        </>
      ) : (
        <Box sx={{ textAlign: 'center' }}>
          <Typography sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
            {children || 'No data available'}
          </Typography>
          <Tooltip title="Refresh">
            <IconButton onClick={onRetry} sx={{ color: 'white' }}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      )}
    </Paper>
  );
};

export default FallbackComponent; 