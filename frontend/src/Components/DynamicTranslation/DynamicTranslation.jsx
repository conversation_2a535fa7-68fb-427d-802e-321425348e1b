import React, { useState, useEffect } from 'react';
import { CircularProgress, Box } from '@mui/material';
import { useTranslation } from '../../hooks/useTranslation';

/**
 * Component that dynamically translates content that isn't in static translation files
 * 
 * @param {Object} props
 * @param {String} props.text - The text to translate
 * @param {Boolean} props.showLoading - Whether to show loading indicator
 * @param {Object} props.sx - MUI styling overrides
 * @param {String} props.component - The component to render (default: span)
 * @param {Function} props.onTranslated - Callback when translation is complete
 * @param {Boolean} props.skipCache - Whether to skip cached translations
 * @param {Object} props.rest - Other props to pass to the component
 */
const DynamicTranslation = ({
  text,
  showLoading = false,
  sx = {},
  component = 'span',
  onTranslated = null,
  skipCache = false,
  ...rest
}) => {
  const { translateDynamic, language } = useTranslation();
  const [translatedText, setTranslatedText] = useState(text);
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    let isMounted = true;
    
    const translateText = async () => {
      // No need to translate if text is empty or language is English
      if (!text || language === 'en' || language === 'en-IN' || !language) {
        if (isMounted) setTranslatedText(text || '');
        return;
      }
      
      try {
        setIsLoading(true);
        const translated = await translateDynamic(text, { skipCache });
        
        if (isMounted) {
          setTranslatedText(translated);
          if (onTranslated) onTranslated(translated);
        }
      } catch (error) {
        console.error('Translation error:', error);
        if (isMounted) setTranslatedText(text);
      } finally {
        if (isMounted) setIsLoading(false);
      }
    };
    
    translateText();
    
    return () => {
      isMounted = false;
    };
  }, [text, language, translateDynamic, onTranslated, skipCache]);
  
  // Display loading indicator if needed
  if (isLoading && showLoading) {
    return (
      <Box component={component} sx={{ display: 'inline-flex', alignItems: 'center', ...sx }} {...rest}>
        <CircularProgress size={16} sx={{ mr: 1 }} />
        {text}
      </Box>
    );
  }
  
  // Display the translated text
  return (
    <Box component={component} sx={sx} {...rest}>
      {translatedText || text || ''}
    </Box>
  );
};

export default DynamicTranslation; 