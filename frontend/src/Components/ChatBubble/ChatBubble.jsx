import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Fab,
  Tooltip,
  Badge
} from '@mui/material';
import chatbotIcon from '../../assets/chatbot.png';
import { useLanguage } from '../../contexts/LanguageContext';
import { getTranslation } from '../../translations';

const ChatBubble = () => {
  const navigate = useNavigate();
  const [unreadCount, setUnreadCount] = useState(0);
  const { selectedLanguage } = useLanguage();

  const handleChatOpen = () => {
    setUnreadCount(0);
    navigate('/chatbot');
  };

  return (
    <Tooltip title={getTranslation('chatbot.tooltip', selectedLanguage)}>
      <Badge badgeContent={unreadCount} color="error" overlap="circular">
        <Fab
          color="primary"
          aria-label="chat"
          onClick={handleChatOpen}
          sx={{
            position: 'fixed',
            bottom: 20,
            right: 20,
            zIndex: 1000,
            width: 60,
            height: 60,
            backgroundImage: `url(${chatbotIcon})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
            '&:hover': {
              transform: 'scale(1.05)',
              boxShadow: '0 6px 12px rgba(0,0,0,0.3)',
            },
            transition: 'all 0.3s ease'
          }}
        />
      </Badge>
    </Tooltip>
  );
};

export default ChatBubble;
