import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const Login = () => {
    const [phoneNumber, setPhoneNumber] = useState('');
    const [otp, setOtp] = useState('');
    const [showOTP, setShowOTP] = useState(false);
    const [confirmationResult, setConfirmationResult] = useState(null);
    const [loading, setLoading] = useState(false);
    const { signInWithPhone, verifyOTP, error, currentUser } = useAuth();
    const navigate = useNavigate();

    useEffect(() => {
        if (currentUser) {
            navigate('/dashboard');
        }
    }, [currentUser, navigate]);

    const handleSendOTP = async (e) => {
        e.preventDefault();
        setLoading(true);
        try {
            // Format phone number to E.164 format
            let formattedPhone = phoneNumber;
            if (!phoneNumber.startsWith('+')) {
                formattedPhone = phoneNumber.startsWith('91') 
                    ? `+${phoneNumber}` 
                    : `+91${phoneNumber}`;
            }
            
            console.log('Sending OTP to:', formattedPhone);
            const result = await signInWithPhone(formattedPhone);
            setConfirmationResult(result);
            setShowOTP(true);
        } catch (err) {
            console.error('Error sending OTP:', err);
            // Error will be shown through the AuthContext error state
        }
        setLoading(false);
    };

    const handleVerifyOTP = async (e) => {
        e.preventDefault();
        if (!otp || otp.length !== 6) {
            return;
        }
        setLoading(true);
        try {
            await verifyOTP(confirmationResult, otp);
            // Navigation will happen automatically through the useEffect
        } catch (err) {
            console.error('Error verifying OTP:', err);
            // Error will be shown through the AuthContext error state
        }
        setLoading(false);
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div>
                    <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        Welcome to AgriCare
                    </h2>
                    <p className="mt-2 text-center text-sm text-gray-600">
                        {showOTP ? 'Enter the OTP sent to your phone' : 'Login with your phone number'}
                    </p>
                </div>

                {error && (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                        <span className="block sm:inline">{error}</span>
                    </div>
                )}

                {!showOTP ? (
                    <form className="mt-8 space-y-6" onSubmit={handleSendOTP}>
                        <div>
                            <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                                Phone Number
                            </label>
                            <div className="mt-1">
                                <input
                                    id="phone"
                                    name="phone"
                                    type="tel"
                                    required
                                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                    placeholder="Enter your phone number"
                                    value={phoneNumber}
                                    onChange={(e) => setPhoneNumber(e.target.value.replace(/\D/g, ''))}
                                    pattern="[0-9]{10}"
                                    maxLength="10"
                                />
                            </div>
                            <p className="mt-2 text-sm text-gray-500">
                                Enter 10 digit number without country code
                            </p>
                        </div>

                        <div id="recaptcha-container" className="flex justify-center"></div>

                        <div>
                            <button
                                type="submit"
                                disabled={loading || phoneNumber.length !== 10}
                                className={`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white ${
                                    loading || phoneNumber.length !== 10
                                        ? 'bg-green-400 cursor-not-allowed'
                                        : 'bg-green-600 hover:bg-green-700'
                                } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500`}
                            >
                                {loading ? 'Sending...' : 'Send OTP'}
                            </button>
                        </div>
                    </form>
                ) : (
                    <form className="mt-8 space-y-6" onSubmit={handleVerifyOTP}>
                        <div>
                            <label htmlFor="otp" className="block text-sm font-medium text-gray-700">
                                OTP
                            </label>
                            <div className="mt-1">
                                <input
                                    id="otp"
                                    name="otp"
                                    type="text"
                                    required
                                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                    placeholder="Enter 6-digit OTP"
                                    value={otp}
                                    onChange={(e) => setOtp(e.target.value.replace(/\D/g, ''))}
                                    pattern="[0-9]{6}"
                                    maxLength="6"
                                />
                            </div>
                        </div>

                        <div>
                            <button
                                type="submit"
                                disabled={loading || otp.length !== 6}
                                className={`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white ${
                                    loading || otp.length !== 6
                                        ? 'bg-green-400 cursor-not-allowed'
                                        : 'bg-green-600 hover:bg-green-700'
                                } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500`}
                            >
                                {loading ? 'Verifying...' : 'Verify OTP'}
                            </button>
                        </div>

                        <div className="text-center">
                            <button
                                type="button"
                                onClick={() => {
                                    setShowOTP(false);
                                    setOtp('');
                                    setConfirmationResult(null);
                                }}
                                className="text-green-600 hover:text-green-500"
                            >
                                Try different number
                            </button>
                        </div>
                    </form>
                )}
            </div>
        </div>
    );
};

export default Login; 