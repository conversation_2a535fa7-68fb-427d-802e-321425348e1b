import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const LoadingSpinner = () => (
    <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
    }}>
        <div style={{ 
            width: '50px', 
            height: '50px', 
            border: '5px solid #f3f3f3',
            borderTop: '5px solid #3498db',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
        }} />
    </div>
);

export default function ProtectedRoute({ children }) {
    const { currentUser, loading } = useAuth();
    const location = useLocation();
    
    console.log('ProtectedRoute Debug:', { 
        loading, 
        currentUser,
        path: location.pathname,
        hasUser: !!currentUser,
        userDetails: currentUser ? {
            uid: currentUser.uid,
            role: currentUser.role,
            isEmulator: currentUser.isEmulator
        } : null
    });

    if (loading) {
        console.log('ProtectedRoute: Loading state, showing spinner');
        return <LoadingSpinner />;
    }

    if (!currentUser) {
        console.log('ProtectedRoute: No user found, redirecting to login');
        return <Navigate to="/login" state={{ from: location }} replace />;
    }

    console.log('ProtectedRoute: User authenticated, rendering children');
    return children;
} 