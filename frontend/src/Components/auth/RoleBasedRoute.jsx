import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const LoadingSpinner = () => (
    <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
    }}>
        <div style={{ 
            width: '50px', 
            height: '50px', 
            border: '5px solid #f3f3f3',
            borderTop: '5px solid #3498db',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
        }} />
    </div>
);

function RoleBasedRoute({ children, allowedRoles }) {
    const { currentUser, loading } = useAuth();
    const location = useLocation();

    if (loading) {
        return <LoadingSpinner />;
    }

    if (!currentUser) {
        // Save the attempted URL for redirecting after login
        return <Navigate to="/login" state={{ from: location }} replace />;
    }

    if (!allowedRoles.includes(currentUser.role)) {
        // Redirect to the appropriate dashboard based on user's role
        let redirectPath = '/login';
        switch (currentUser.role) {
            case 'Farmer':
                redirectPath = '/farmer-dashboard';
                break;
            case 'TM':
                redirectPath = '/tm';
                break;
            case 'HR':
                redirectPath = '/hrdash';
                break;
            default:
                redirectPath = '/dashboard';
        }
        return <Navigate to={redirectPath} replace />;
    }

    return children;
}

export default RoleBasedRoute; 