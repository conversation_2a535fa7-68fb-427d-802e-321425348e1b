/* AIAgent.css — AI-themed updates */

.ai-agent-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  gap: 16px;
  box-sizing: border-box;
}

.language-selector {
  min-width: 200px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.status-indicator {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  background-color: #eeeeee;
  color: #444;
}
.status-indicator.recording {
  background-color: #ffebee;
  color: #d32f2f;
}
.status-indicator.speaking {
  background-color: #e3f2fd;
  color: #1976d2;
}

.messages-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 50vh;
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}
.messages-container::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 6px;
}

.message {
  padding: 14px 18px;
  border-radius: 16px;
  max-width: 85%;
  font-size: 0.95rem;
  line-height: 1.5;
  word-wrap: break-word;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  color: #212121;
}

.user-message {
  align-self: flex-end;
  background-color: #f5f5f5;
  color: #212121 !important;
}
.assistant-message {
  align-self: flex-start;
  background-color: #f0f0f0;
  color: #222 !important;
}

.message * {
  color: inherit !important;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f5f5f5;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.input-container .MuiTextField-root {
  flex: 1;
  border-radius: 8px;
  background-color: white !important;
}

.input-container input,
.input-container .MuiInputBase-input,
.input-container .MuiOutlinedInput-input {
  background-color: white !important;
  color: #212121 !important;
  caret-color: #212121 !important;
  font-size: 1rem;
}

input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: #212121 !important;
  transition: background-color 5000s ease-in-out 0s;
}

.input-container .MuiIconButton-root {
  width: 44px;
  height: 44px;
  background-color: #1976d2;
  color: white;
  transition: background 0.3s ease;
  border-radius: 8px;
}

.input-container .MuiIconButton-root:hover {
  background-color: #1565c0;
}

/* Modern AI loader */
.loading-message {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: #e0f7fa;
  border-left: 4px solid #00acc1;
  border-radius: 12px;
  font-size: 0.9rem;
  color: #006064;
  font-weight: 500;
  font-family: 'Segoe UI', sans-serif;
}

.loading-message::before {
  content: '🤖 Quamin AI Agent thinking...';
  font-style: italic;
  animation: pulseText 1.5s infinite ease-in-out;
}

@keyframes pulseText {
  0% { opacity: 1; }
  50% { opacity: 0.4; }
  100% { opacity: 1; }
}

.message h1,
.message h2,
.message h3 {
  font-weight: 600;
  margin-bottom: 0.5rem;
}
.message ul {
  padding-left: 1.5rem;
}
.message li {
  margin-bottom: 4px;
}
.message strong {
  font-weight: 600;
}
.message em {
  font-style: italic;
}

@media (max-width: 768px) {
  .ai-agent-container {
    padding: 12px;
  }

  .messages-container {
    max-height: 300px;
    min-height: 200px;
    padding: 12px;
  }

  .message {
    font-size: 0.9rem;
    padding: 10px 12px;
  }

  .input-container {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
    padding: 12px;
  }

  .input-container .MuiIconButton-root {
    width: 100%;
    height: 40px;
  }

  .language-selector {
    width: 100%;
  }

  .status-indicator {
    font-size: 0.8rem;
    padding: 6px 10px;
  }
}

