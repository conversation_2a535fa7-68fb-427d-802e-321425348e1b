import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  TextField,
  IconButton,
  Typography,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  useTheme
} from '@mui/material';
import {
  Send as SendIcon,
  Mic as MicIcon,
  Stop as StopIcon,
  SmartToy as SmartToyIcon
} from '@mui/icons-material';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import * as SpeechSDK from 'microsoft-cognitiveservices-speech-sdk';
import './AIAgent.css';

const getVoiceName = (lang) => {
  const voiceMapping = {
    "en-IN": "en-US-JennyNeural",
    "hi-IN": "hi-IN-SwaraNeural",
    "bn-IN": "bn-IN-SumonaNeural",
    "gu-IN": "gu-IN-NehaNeural",
    "kn-IN": "kn-IN-ChitraNeural",
    "ml-IN": "ml-IN-SobhaNeural",
    "mr-IN": "mr-IN-AarohiNeural",
    "pa-IN": "pa-IN-ManeetNeural",
    "ta-IN": "ta-IN-VaniNeural",
    "te-IN": "te-IN-ShrutiNeural",
    "ur-IN": "ur-IN-ZaraNeural",
    "or-IN": "en-US-JennyNeural",
    "as-IN": "en-US-JennyNeural",
    "bho-IN": "en-US-JennyNeural",
    "ma-IN": "en-US-JennyNeural",
    "sa-IN": "en-US-JennyNeural",
    "ks-IN": "en-US-JennyNeural",
    "mni-IN": "en-US-JennyNeural",
    "ne-IN": "en-US-JennyNeural",
    "sd-IN": "en-US-JennyNeural"
  };
  return voiceMapping[lang] || 'en-US-JennyNeural';
};

const cleanTextForSpeech = (text) => {
  if (!text) {
    console.warn('No text provided to cleanTextForSpeech');
    return '';
  }
  return text
    .replace(/[#*_`~>\-]+/g, '')
    .replace(/\[(.*?)\]\((.*?)\)/g, '$1')
    .replace(/\!\[(.*?)\]\((.*?)\)/g, '')
    .replace(/\n{2,}/g, '. ')
    .replace(/\n/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
};

const stripMarkdown = (text) => {
  if (!text) return '';
  return text
    .replace(/[#*_`~>\-]+/g, '')
    .replace(/\[(.*?)\]\((.*?)\)/g, '$1')
    .replace(/\!\[(.*?)\]\((.*?)\)/g, '')
    .replace(/\n{2,}/g, '. ')
    .replace(/\n/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
};

const AIAgent = ({ weatherData, soilData, marketData, alerts, schedule, onRefresh }) => {
  const theme = useTheme();
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  // Track user interaction to enable speech synthesis
  const [userInteracted, setUserInteracted] = useState(false);
  // Track if we've shown the not-allowed alert
  const [shownNotAllowedAlert, setShownNotAllowedAlert] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('en-IN');
  const recognitionRef = useRef(null);
  const messagesEndRef = useRef(null);

  const languageMap = {
    'en-IN': 'English (India)',
    'hi-IN': 'हिंदी (Hindi)',
    'bn-IN': 'বাংলা (Bengali)',
    'te-IN': 'తెలుగు (Telugu)',
    'ta-IN': 'தமிழ் (Tamil)',
    'mr-IN': 'मराठी (Marathi)',
    'gu-IN': 'ગુજરાતી (Gujarati)',
    'kn-IN': 'ಕನ್ನಡ (Kannada)',
    'ml-IN': 'മലയാളം (Malayalam)',
    'pa-IN': 'ਪੰਜਾਬੀ (Punjabi)',
    'ur-IN': 'اردو (Urdu)'
  };

  useEffect(() => {
    setupSpeechRecognition();
    return () => recognitionRef.current?.stop();
  }, [selectedLanguage]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Add event listener for user interaction
  useEffect(() => {
    const handleUserInteraction = () => {
      if (!userInteracted) {
        console.log('User interaction detected, enabling speech synthesis');
        setUserInteracted(true);

        // Try to initialize speech synthesis with a silent utterance
        if (window.speechSynthesis) {
          try {
            const silentUtterance = new SpeechSynthesisUtterance(' ');
            silentUtterance.volume = 0;
            window.speechSynthesis.speak(silentUtterance);
            window.speechSynthesis.cancel(); // Cancel it immediately
          } catch (error) {
            console.warn('Could not initialize speech synthesis:', error);
          }
        }
      }
    };

    // Add event listeners for common user interactions
    document.addEventListener('click', handleUserInteraction);
    document.addEventListener('keydown', handleUserInteraction);
    document.addEventListener('touchstart', handleUserInteraction);

    return () => {
      // Clean up
      document.removeEventListener('click', handleUserInteraction);
      document.removeEventListener('keydown', handleUserInteraction);
      document.removeEventListener('touchstart', handleUserInteraction);
    };
  }, [userInteracted]);

  const setupSpeechRecognition = () => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (SpeechRecognition) {
      const recognition = new SpeechRecognition();
      recognition.lang = selectedLanguage;
      recognition.interimResults = false;
      recognition.continuous = false;

      recognition.onstart = () => setIsRecording(true);
      recognition.onend = () => setIsRecording(false);
      recognition.onerror = () => setIsRecording(false);
      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        handleVoiceCommand(transcript);
      };

      recognitionRef.current = recognition;
    }
  };

  const speak = (text) => {
    const cleanedText = cleanTextForSpeech(text);
    const voiceName = getVoiceName(selectedLanguage);
    const key = import.meta.env.VITE_AZURE_SPEECH_KEY;
    const region = import.meta.env.VITE_AZURE_SPEECH_REGION;

    if (!key || !region) {
      console.error('Azure Speech key or region is missing.');
      return;
    }

    const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(key, region);
    speechConfig.speechSynthesisVoiceName = voiceName;
    speechConfig.speechSynthesisLanguage = selectedLanguage;

    const player = new SpeechSDK.SpeakerAudioDestination();
    const audioConfig = SpeechSDK.AudioConfig.fromSpeakerOutput(player);
    const synthesizer = new SpeechSDK.SpeechSynthesizer(speechConfig, audioConfig);

    setIsSpeaking(true);

    synthesizer.speakTextAsync(
      cleanedText,
      result => {
        if (result.reason === SpeechSDK.ResultReason.SynthesizingAudioCompleted) {
          console.log("✅ Azure TTS completed");
        } else {
          console.error("Azure TTS failed:", result.errorDetails);
        }
        setIsSpeaking(false);
        synthesizer.close();
        recognitionRef.current?.start();
      },
      error => {
        console.error("Speech synthesis error:", error);

        // Check if this is a 'not-allowed' error
        if (error && error.message && error.message.includes('not-allowed')) {
          console.warn('Speech synthesis not allowed. User interaction may be required first.');
          // Don't show alert if we already know user hasn't interacted or if we've already shown it
          if (userInteracted && !shownNotAllowedAlert) {
            // Display a message to the user only if they've already interacted and we haven't shown it yet
            alert('Please click anywhere on the page to enable text-to-speech functionality.');
            setShownNotAllowedAlert(true);
          }
        }

        setIsSpeaking(false);
        synthesizer.close();

        // Try browser TTS as fallback
        if (window.speechSynthesis && cleanedText) {
          try {
            const utterance = new SpeechSynthesisUtterance(cleanedText);
            utterance.lang = selectedLanguage.substring(0, 2);
            utterance.onend = () => setIsSpeaking(false);
            utterance.onerror = (event) => {
              console.error('Browser speech synthesis error:', event);
              setIsSpeaking(false);
            };
            window.speechSynthesis.speak(utterance);
          } catch (browserError) {
            console.error('Browser speech synthesis error:', browserError);
          }
        }
      }
    );
  };

  const handleVoiceCommand = (transcript) => {
    const command = transcript.toLowerCase().trim();
    if (["stop", "cancel"].includes(command)) {
      setIsSpeaking(false);
    } else {
      setInput((prev) => (prev ? prev + ' ' + transcript : transcript));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim()) return;

    const userInput = input.trim();
    setMessages((prev) => [...prev, { role: 'user', content: userInput }]);
    setInput('');
    setIsLoading(true);

    try {
      // Check if farm data is available
      if (!weatherData || !soilData || !marketData || !alerts || !schedule) {
        throw new Error('Farm data is not available. Please refresh the dashboard.');
      }

      const res = await fetch(import.meta.env.VITE_API_URL + '/ai/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: userInput,
          farmData: {
            weather: weatherData,
            soilHealth: soilData,
            marketData: marketData,
            alerts: alerts,
            schedule: schedule
          },
          language: selectedLanguage
        })
      });

      if (!res.ok) {
        throw new Error('Failed to get response from AI');
      }

      const data = await res.json();
      console.log('Chat API Response:', data);

      if (!data.success) {
        throw new Error(data.message || 'Failed to get response from AI');
      }

      const reply = data.response?.trim() || "Sorry, I couldn't understand that.";
      console.log('AI Reply:', reply);

      // Store the markdown-formatted reply for display
      setMessages((prev) => [...prev, { role: 'assistant', content: reply }]);

      // Strip markdown before speaking
      const plainText = stripMarkdown(reply);
      console.log('Plain text for speech:', plainText);
      speak(plainText);
    } catch (err) {
      console.error('Chat error:', err);
      const errorMessage = err.message === 'Farm data is not available. Please refresh the dashboard.'
        ? err.message
        : 'Something went wrong. Please try again.';
      setMessages((prev) => [...prev, { role: 'assistant', content: errorMessage }]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box className="ai-agent-container">
      <Stack direction="row" spacing={2} alignItems="center">
        <FormControl size="small" className="language-selector">
          <InputLabel sx={{ color: 'white' }}>Voice Language</InputLabel>
          <Select
            value={selectedLanguage}
            label="Voice Language"
            onChange={(e) => setSelectedLanguage(e.target.value)}
            sx={{
              color: 'white',
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: 'rgba(255, 255, 255, 0.3)',
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: 'rgba(255, 255, 255, 0.5)',
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: 'white',
              },
              '& .MuiSvgIcon-root': {
                color: 'white',
              }
            }}
          >
            {Object.entries(languageMap).map(([code, label]) => (
              <MenuItem key={code} value={code}>{label}</MenuItem>
            ))}
          </Select>
        </FormControl>
        <Box className={`status-indicator ${isRecording ? 'recording' : isSpeaking ? 'speaking' : ''}`}>
          {isRecording ? '🎤 Recording...' : isSpeaking ? '🔊 Speaking...' : 'Ready'}
        </Box>
      </Stack>

      <Box className="messages-container">
        {messages.map((m, i) => (
          <Box key={i} className={`message ${m.role === 'user' ? 'user-message' : 'assistant-message'}`}>
            <ReactMarkdown remarkPlugins={[remarkGfm]}>{m.content}</ReactMarkdown>
          </Box>
        ))}
        {isLoading && (
          <Box className="loading-message">
            <CircularProgress size={18} sx={{ color: 'white' }} />
            <Typography variant="body2" sx={{ color: 'white' }}>Thinking...</Typography>
          </Box>
        )}
        <div ref={messagesEndRef} />
      </Box>

      <form onSubmit={handleSubmit} className="input-container">
        <TextField
          key={input.length}
          placeholder={`Type in ${languageMap[selectedLanguage]} or speak...`}
          value={input}
          onChange={(e) => setInput(e.target.value)}
          variant="outlined"
          fullWidth
          InputProps={{
            sx: {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '8px',
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: 'rgba(255, 255, 255, 0.3)',
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: 'rgba(255, 255, 255, 0.5)',
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: 'white',
              }
            },
            inputProps: {
              style: {
                color: 'white',
                fontSize: '1rem',
                caretColor: 'white'
              }
            }
          }}
        />
        <IconButton
          onClick={() => {
            if (isRecording) recognitionRef.current?.stop();
            else recognitionRef.current?.start();
            setIsRecording(!isRecording);
          }}
          sx={{
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)'
            }
          }}
        >
          {isRecording ? <StopIcon /> : <MicIcon />}
        </IconButton>
        <IconButton
          type="submit"
          disabled={!input.trim() || isLoading}
          sx={{
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)'
            }
          }}
        >
          <SendIcon />
        </IconButton>
      </form>
    </Box>
  );
};

export default AIAgent;

