// ✅ AIAgent.jsx with natural voice mapping for speech synthesis
import React, { useState, useEffect, useRef } from 'react';
import {
  Box, TextField, IconButton, Typography, CircularProgress, FormControl, InputLabel, Select, MenuItem, Stack
} from '@mui/material';
import {
  Send as SendIcon, Mic as MicIcon, Stop as StopIcon
} from '@mui/icons-material';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import './AIAgent.css';

const getVoiceName = (lang) => {
  const voiceMapping = {
    "en-IN": "en-US-JennyNeural",
    "hi-IN": "hi-IN-SwaraNeural",
    "bn-IN": "bn-IN-SumonaNeural",
    "gu-IN": "gu-IN-NehaNeural",
    "kn-IN": "kn-IN-ChitraNeural",
    "ml-IN": "ml-IN-SobhaNeural",
    "mr-IN": "mr-IN-AarohiNeural",
    "pa-IN": "pa-IN-ManeetNeural",
    "ta-IN": "ta-IN-VaniNeural",
    "te-IN": "te-IN-ShrutiNeural",
    "ur-IN": "ur-IN-ZaraNeural",
    "or-IN": "en-US-JennyNeural",
    "as-IN": "en-US-JennyNeural",
    "bho-IN": "en-US-JennyNeural",
    "ma-IN": "en-US-JennyNeural",
    "sa-IN": "en-US-JennyNeural",
    "ks-IN": "en-US-JennyNeural",
    "mni-IN": "en-US-JennyNeural",
    "ne-IN": "en-US-JennyNeural",
    "sd-IN": "en-US-JennyNeural"
  };
  return voiceMapping[lang] || 'en-US-JennyNeural';
};

const AIAgent = ({ farmData }) => {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('en-IN');
  const recognitionRef = useRef(null);
  const synthesisRef = useRef(null);
  const messagesEndRef = useRef(null);

  const languageMap = {
    'en-IN': 'English', 'hi-IN': 'हिंदी', 'bn-IN': 'বাংলা', 'te-IN': 'తెలుగు',
    'ta-IN': 'தமிழ்', 'mr-IN': 'मराठी', 'gu-IN': 'ગુજરાતી', 'kn-IN': 'ಕನ್ನಡ',
    'ml-IN': 'മലയാളം', 'pa-IN': 'ਪੰਜਾਬੀ'
  }; 

  useEffect(() => {
    setupSpeechServices();
    return () => {
      recognitionRef.current?.stop();
      window.speechSynthesis.cancel();
    };
  }, [selectedLanguage]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const setupSpeechServices = () => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (SpeechRecognition) {
      const recognition = new SpeechRecognition();
      recognition.lang = selectedLanguage;
      recognition.interimResults = false;
      recognition.continuous = false;

      recognition.onstart = () => setIsRecording(true);
      recognition.onend = () => setIsRecording(false);
      recognition.onerror = () => setIsRecording(false);
      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        handleVoiceCommand(transcript);
      };

      recognitionRef.current = recognition;
    }
    synthesisRef.current = window.speechSynthesis;
  };

  const handleVoiceCommand = (transcript) => {
    const command = transcript.toLowerCase().trim();

    if (['stop', 'cancel'].includes(command)) {
      synthesisRef.current.cancel();
      setIsSpeaking(false);
    } else if (command === 'wait') {
      // Wait and let user keep speaking
    } else {
      setInput((prev) => (prev ? prev + ' ' + transcript : transcript));
    }
  };

  const speak = (text) => {
    const voices = window.speechSynthesis.getVoices();
    const preferredVoice = getVoiceName(selectedLanguage);
    const matchedVoice = voices.find(v => v.name === preferredVoice);

    const utterance = new SpeechSynthesisUtterance(text.replace(/[\-*#]/g, '').trim());
    utterance.lang = selectedLanguage;
    if (matchedVoice) utterance.voice = matchedVoice;

    utterance.onstart = () => setIsSpeaking(true);
    utterance.onend = () => {
      setIsSpeaking(false);
      recognitionRef.current?.start();
    };
    synthesisRef.current.speak(utterance);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim()) return;

    const userInput = input.trim();
    setMessages((prev) => [...prev, { role: 'user', content: userInput }]);
    setInput('');
    setIsLoading(true);

    try {
      const res = await fetch('http://localhost:8000/api/ai/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: userInput,
          farmData: farmData || {},
          language: selectedLanguage
        })
      });
      const data = await res.json();
      const reply = data.response?.trim() || 'Sorry, I couldn\'t understand that.';
      setMessages((prev) => [...prev, { role: 'assistant', content: reply }]);
      speak(reply);
    } catch (err) {
      setMessages((prev) => [...prev, { role: 'assistant', content: 'Something went wrong. Please try again.' }]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box className="ai-agent-container">
      <Stack direction="row" spacing={2} alignItems="center">
        <FormControl size="small" className="language-selector">
          <InputLabel>Voice Language</InputLabel>
          <Select value={selectedLanguage} label="Voice Language" onChange={(e) => setSelectedLanguage(e.target.value)}>
            {Object.entries(languageMap).map(([code, label]) => (
              <MenuItem key={code} value={code}>{label}</MenuItem>
            ))}
          </Select>
        </FormControl>
        <Box className={`status-indicator ${isRecording ? 'recording' : isSpeaking ? 'speaking' : ''}`}>
          {isRecording ? '🎤 Recording...' : isSpeaking ? '🔊 Speaking...' : 'Ready'}
        </Box>
      </Stack>

      <Box className="messages-container">
        {messages.map((m, i) => (
          <Box key={i} className={`message ${m.role === 'user' ? 'user-message' : 'assistant-message'}`}>
            <ReactMarkdown remarkPlugins={[remarkGfm]}>{m.content}</ReactMarkdown>
          </Box>
        ))}
        {isLoading && (
          <Box className="loading-message">
            <CircularProgress size={18} />
            <Typography variant="body2">Thinking...</Typography>
          </Box>
        )}
        <div ref={messagesEndRef} />
      </Box>

      <form onSubmit={handleSubmit} className="input-container">

<TextField
  key={input.length}
  placeholder={`Type in ${languageMap[selectedLanguage]} or speak...`}
  value={input}
  onChange={(e) => setInput(e.target.value)}
  variant="outlined"
  fullWidth
  InputProps={{
    sx: {
      backgroundColor: '#ffffff',
      borderRadius: '8px',
    },
    inputProps: {
      style: {
        color: '#212121',
        fontSize: '1rem',
        caretColor: '#212121',
      },
    }
  }}
/>

        <IconButton
          onClick={() => {
            if (isRecording) recognitionRef.current?.stop();
            else recognitionRef.current?.start();
            setIsRecording(!isRecording);
          }}
        >
          {isRecording ? <StopIcon /> : <MicIcon />}
        </IconButton>
        <IconButton type="submit" disabled={!input.trim() || isLoading}>
          <SendIcon />
        </IconButton>
      </form>
    </Box>
  );
};

export default AIAgent;

