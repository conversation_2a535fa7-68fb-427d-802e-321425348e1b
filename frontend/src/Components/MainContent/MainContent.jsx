import React from "react";
import titleSection from "../../assets/smartFarming.jpeg";
import { useState, useEffect } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { Link } from "react-router-dom";
import "../../App.css";
import LanguageSelector from "../LanguageSelector/LanguageSelector";
import { Box } from "@mui/material";
import { useTranslation } from "react-i18next";

const MainContent = () => {
  const { t } = useTranslation();
  const words = [
    t("innov"),
    t("machi"),
    t("artif"),
    t("block"),
    t("iot"),
    t("dron"),
  ];

  const [index, setIndex] = useState(0);

  useEffect(() => {
    const id = setInterval(() => {
      setIndex((state) => {
        if (state >= words.length - 1) {
          return 0;
        }
        return state + 1;
      });
    }, 2000);

    return () => clearInterval(id);
  }, []);

  return (
    <>
      <Box
        sx={{ display: "flex", justifyContent: "center", width: "100%", m: 4 }}
      >
        <LanguageSelector />
      </Box>
      <div className="main-content-container" id="home">
        <div className="heading-container">
          <h2>{t("revo")}</h2>
          <h2>{t("agri")}</h2>
          <h2>{t("thru")}</h2>
          <div className="animate-heading">
            <AnimatePresence>
              <motion.div
                className="last-heading-animate"
                key={index}
                initial={{ y: 20, opacity: 0, scale: 0.8 }}
                animate={{ y: 0, opacity: 1, scale: 1 }}
                exit={{ y: -20, opacity: 0, scale: 0.8 }}
                transition={{
                  ease: "easeInOut",
                  delay: 0.2,
                  duration: 0.5,
                  type: "spring",
                  stiffness: 80,
                }}
                whileHover={{ scale: 1.1 }}
              >
                {words[index]}
              </motion.div>
            </AnimatePresence>
          </div>
          <div className="desc-container">
            <p>{t("home-desc")}</p>
            <Link to="/dashboard" className="login-button-container">
              <i className="fa-solid fa-arrow-right-long"></i>
              <h1>{t("start")}</h1>
            </Link>
          </div>
        </div>
        <div className="image-container">
          <div className="tech-dots"></div>
          <div className="scan-line"></div>
          <div className="data-stream"></div>
          <div className="tech-circle-1"></div>
          <div className="tech-circle-2"></div>
          <div className="connection-lines"></div>
          <div className="drone-path"></div>
          <div className="field-grid"></div>

          {/* Agricultural data panels */}
          <div className="agri-data-panel temp-panel">
            <div className="panel-icon">°C</div>
            <div className="panel-value">27.4</div>
            <div className="panel-label">{t("temp")}</div>
            <div className="panel-indicator"></div>
          </div>

          <div className="agri-data-panel humidity-panel">
            <div className="panel-icon">
              <i className="fa-solid fa-droplet"></i>
            </div>
            <div className="panel-value">68%</div>
            <div className="panel-label">{t("humid")}</div>
            <div className="panel-indicator"></div>
          </div>

          <div className="agri-data-panel soil-panel">
            <div className="panel-icon">
              <i className="fa-solid fa-seedling"></i>
            </div>
            <div className="panel-value">6.8</div>
            <div className="panel-label">{t("soil")}</div>
            <div className="panel-indicator"></div>
          </div>

          <div className="agri-data-panel light-panel">
            <div className="panel-icon">
              <i className="fa-solid fa-sun"></i>
            </div>
            <div className="panel-value">842</div>
            <div className="panel-label">{t("light")}</div>
            <div className="panel-indicator"></div>
          </div>

          <div className="agri-data-panel yield-panel">
            <div className="panel-icon">
              <i className="fa-solid fa-wheat-awn"></i>
            </div>
            <div className="panel-value">4.8</div>
            <div className="panel-label">{t("yield")}</div>
            <div className="panel-indicator"></div>
          </div>

          <img
            src={titleSection}
            alt="Smart Farming visualization"
            loading="eager"
            fetchpriority="high"
          />
        </div>
      </div>
    </>
  );
};

export default MainContent;
