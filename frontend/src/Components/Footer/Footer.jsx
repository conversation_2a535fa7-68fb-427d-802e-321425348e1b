import React from "react";
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Link,
  IconButton,
} from "@mui/material";
import {
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  Instagram as InstagramIcon,
  LinkedIn as LinkedInIcon,
  YouTube as YouTubeIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Google as GoogleIcon,
} from "@mui/icons-material";
import { useTranslation } from "react-i18next";

const Footer = () => {
  const { t } = useTranslation();
  return (
    <Box
      component="footer"
      sx={{
        py: 6,
        px: 2,
        mt: "auto",
        backgroundColor: (theme) =>
          theme.palette.mode === "light"
            ? theme.palette.grey[200]
            : theme.palette.grey[800],
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          <Grid item xs={12} sm={4}>
            <Typography variant="h6" color="text.primary" gutterBottom>
              {t("footer_label")}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {t("footer_desc")}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Typography variant="h6" color="text.primary" gutterBottom>
              {t("nav_supp_heading")}
            </Typography>
            <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
              <Link href="/" color="text.secondary" underline="hover">
                {t("nav_supp_sub1")}
              </Link>
              <Link href="/dashboard" color="text.secondary" underline="hover">
                {t("nav_supp_sub2")}
              </Link>
              <Link href="/farm" color="text.secondary" underline="hover">
                {t("nav_supp_sub3")}
              </Link>
              <Link
                href="/market-analysis"
                color="text.secondary"
                underline="hover"
              >
                {t("nav_supp_sub4")}
              </Link>
              <Link
                href="mailto:<EMAIL>"
                color="text.secondary"
                underline="hover"
              >
                {t("nav_supp_sub5")}: <EMAIL>
              </Link>
              <Link
                href="mailto:<EMAIL>"
                color="text.secondary"
                underline="hover"
              >
                {t("nav_supp_sub6")}: <EMAIL>
              </Link>
            </Box>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Typography variant="h6" color="text.primary" gutterBottom>
              {t("connect_us")}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {t("connect_us_sub1")}:
            </Typography>
            <Box
              sx={{ display: "flex", flexDirection: "column", gap: 1, mb: 2 }}
            >
              <Link
                href="https://www.linkedin.com/company/quamin/?viewAsMember=true"
                target="_blank"
                rel="noopener noreferrer"
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  textDecoration: "none",
                }}
              >
                <LinkedInIcon color="primary" />
                <Typography variant="body2" color="text.secondary">
                  {t("connect_us_sub2")}: Quamin
                </Typography>
              </Link>
              <Link
                href="https://www.youtube.com/@quamintech"
                target="_blank"
                rel="noopener noreferrer"
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  textDecoration: "none",
                }}
              >
                <YouTubeIcon color="error" />
                <Typography variant="body2" color="text.secondary">
                  {t("connect_us_sub3")}: @quamintech
                </Typography>
              </Link>
              <Link
                href="https://www.google.com/search?q=quamin&sca_esv=7c1d8de2d50eae47&sxsrf=AHTn8zqOOlLCl6QABl-2yPq09LuEcHpmMQ%3A1744029547148&source=hp&ei=a8fzZ6D5BY6Mvr0P3eCb8A0&iflsig=ACkRmUkAAAAAZ_PVe5QRginrm0eIBPDURf_lZh_NjHSy&ved=0ahUKEwjg2oet-MWMAxUOhq8BHV3wBt4Q4dUDCBo&uact=5&oq=quamin&gs_lp=Egdnd3Mtd2l6IgZxdWFtaW4yChAjGIAEGCcYigUyExAuGIAEGMcBGCcYigUYjgUYrwEyBBAjGCcyBBAjGCcyBRAAGIAEMgUQLhiABDIFEC4YgAQyBRAAGIAEMgUQABiABDIFEC4YgARI8AlQAFiiB3AAeACQAQCYAbgBoAHVB6oBAzAuNrgBA8gBAPgBAZgCBqAC6gfCAgsQABiABBixAxiDAcICCBAuGIAEGLEDwgIIEAAYgAQYsQPCAhEQLhiABBixAxjRAxiDARjHAcICDhAuGIAEGLEDGNEDGMcBwgILEC4YgAQYxwEYrwHCAgcQABiABBgKmAMAkgcDMC42oAe8ZrIHAzAuNrgH6gc&sclient=gws-wiz"
                target="_blank"
                rel="noopener noreferrer"
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  textDecoration: "none",
                }}
              >
                <GoogleIcon color="primary" />
                <Typography variant="body2" color="text.secondary">
                  {t("connect_us_sub4")}: Quamin
                </Typography>
              </Link>
            </Box>
            <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <PhoneIcon fontSize="small" color="primary" />
                <Typography variant="body2" color="text.secondary">
                  {t("connect_us_sub5")}: 080-49574944
                </Typography>
              </Box>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <EmailIcon fontSize="small" color="primary" />
                <Link
                  href="mailto:<EMAIL>"
                  color="text.secondary"
                  underline="hover"
                >
                  <EMAIL>
                </Link>
              </Box>
            </Box>
          </Grid>
        </Grid>
        <Box sx={{ mt: 4, textAlign: "center" }}>
          <Typography variant="body2" color="text.secondary">
            © 2025 {t("connect_us_sub6")}
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
