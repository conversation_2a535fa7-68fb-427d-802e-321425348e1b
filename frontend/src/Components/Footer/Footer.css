.footer-container {
    background-color: black;
    width: 100%;
    height: 24rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.footer-sub-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    height: 100%;
}

.footer-sub-container2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
    padding-top: 5rem;
    padding-left: 5rem;
    padding-right: 5rem;
}

.footer-sub-section {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 1rem;
}

.icons-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.fa-brands.fa-instagram,
.fa-brands.fa-x-twitter,
.fa-brands.fa-linkedin {
    color: white;
    font-size: 1.5rem;
    line-height: 2rem;
    cursor: pointer;
    text-decoration: none;
}

.fa-brands.fa-instagram:hover,
.fa-brands.fa-x-twitter:hover,
.fa-brands.fa-linkedin:hover {
    color: #ffe400;
}

.footer-text-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 2rem;
}

.footer-text-subcontainer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 1rem;
}

.footer-text-subcontainer h6 {
    color: white;
    font-weight: 700;
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.footer-link {
    color: #9ca3af;
    text-decoration: none;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
}

.footer-link:hover {
    text-decoration: underline;
}

.copyright-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.copyright-container hr {
    border-top-width: 1px;
    border-color: #374151;
    width: 91.666667%;
}

.copyright-container div {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

.copyright-container div p {
    color: #9ca3af;
    font-size: 0.875rem;
    line-height: 1.25rem;
    text-align: center;
}

@media (max-width: 800px) {
    .footer-container {
        background-color: black;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .footer-sub-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 0.5rem;
        width: 100%;
        height: 100%;
    }

    .footer-sub-container2 {
        display: flex;
        flex-direction: column;
        gap: 4rem;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
    }

    .footer-sub-section {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        /* gap: 1rem; */
    }

    .icons-container {
        display: flex;
        align-items: center;
        /* gap: 1rem; */
    }

    .link-icon {
        color: white;
        font-size: 1.5rem;
        line-height: 2rem;
        cursor: pointer;
    }

    .link-icon:hover {
        color: #ffe400;
    }

    .footer-text-container {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        flex-wrap: wrap;
        box-sizing: border-box;
        width: 100%;
    }

    .footer-text-subcontainer {
        flex: 1 1 calc(60% - 2rem);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .footer-text-subcontainer h6 {
        color: white;
        font-weight: 700;
        font-size: 0.875rem;
        line-height: 1.25rem;
    }

    .footer-link {
        color: #9ca3af;
        text-decoration: none;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s;
    }

    .copyright-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 2rem;
    }
}
