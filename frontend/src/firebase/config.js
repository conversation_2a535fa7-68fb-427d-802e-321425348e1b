import { initializeApp } from 'firebase/app';
import { getAuth, RecaptchaVerifier, signInWithPhoneNumber } from 'firebase/auth';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyB5H-B3cMYxXA1x4AWqM-AVHYN1ad1vNgM",
  authDomain: "quamin-agricare-3c370.firebaseapp.com",
  projectId: "quamin-agricare-3c370",
  storageBucket: "quamin-agricare-3c370.firebasestorage.app",
  messagingSenderId: "485775317650",
  appId: "1:485775317650:web:c80f410da3593c1a00644b"
};

// Initialize Firebase
let app;
let auth;

try {
  console.log("Initializing Firebase with config:", {
    apiKey: firebaseConfig.apiKey.substring(0, 10) + "...",
    authDomain: firebaseConfig.authDomain,
    projectId: firebaseConfig.projectId
  });
  
  app = initializeApp(firebaseConfig);
  auth = getAuth(app);
  
  console.log("Firebase initialized successfully");
} catch (error) {
  console.error("Error initializing Firebase:", error);
  
  // Fallback to a mock implementation for development
  console.warn("Using mock Firebase implementation");
  auth = {
    // Mock implementation
    currentUser: null,
    onAuthStateChanged: (callback) => {
      callback(null);
      return () => {};
    }
  };
}

export { auth, RecaptchaVerifier, signInWithPhoneNumber }; 