import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import enLang from "../i18n/locales/en/en.json";
import hiLang from "../i18n/locales/hi/hi.json";
import taLang from "../i18n/locales/ta/ta.json";
import mrLang from "../i18n/locales/mr/mr.json";

// Get the language from localStorage or use "en" as default
const savedLanguage = localStorage.getItem("selectedLanguage");
const defaultLanguage = savedLanguage || "en";

const resources = {
  en: {
    translation: enLang,
  },

  hi: {
    translation: hiLang,
  },

  ta: {
    translation: taLang,
  },
  mr: {
    translation: mrLang,
  },
};

i18n
  .use(initReactI18next) // Connects i18next to React
  .init({
    resources,
    lng: defaultLanguage,
    debug: false,
    interpolation: { escapeValue: false },
    fallbackLng: "en",
  });

export default i18n;
