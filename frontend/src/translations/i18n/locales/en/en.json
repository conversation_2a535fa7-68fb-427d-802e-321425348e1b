{"home": "home", "services": "services", "about": "about", "faq": "faq", "login": "<PERSON><PERSON>", "lang": "Language", "revo": "Revolutionizing", "agri": "Agriculture", "thru": "Through", "innov": "Innovation.", "machi": "Machine Learning.", "artif": "Artificial Intelligence.", "block": "Blockchain.", "iot": "Internet of Things (IoT).", "dron": "Drone in Agriculture.", "home-desc": "Revolutionize Your Harvesting with Smart, Easy Solutions—Empowering the Next Generation of Farmers and Agricultural Innovators.", "start": "Get Started", "temp": "Temperature", "humid": "<PERSON><PERSON><PERSON><PERSON>", "soil": "Soil pH", "light": "Light (lux)", "yield": "Yield (t/ha)", "our_services_heading": "Our Services", "our_services_sub1": "AI-Powered Recommendations", "our_services_desc1": "Get personalized crop recommendations based on your soil, climate, and market conditions.", "our_services_sub2": "Weather Intelligence", "our_services_desc2": "Access accurate weather forecasts and alerts specifically tailored for agricultural planning.", "our_services_sub3": "Sustainable Farming", "our_services_desc3": "Implement eco-friendly practices that reduce environmental impact while maintaining productivity.", "our_services_sub4": "Supply Chain Management", "our_services_desc4": "Streamline your agricultural supply chain from farm to market with integrated logistics solutions.", "learn": "Learn More", "why_us_heading": "Why Choose AgriCare?", "why_us_sub1": "Farm Management", "why_us_desc1": "Comprehensive tools to manage your farm operations, crops, and resources efficiently.", "why_us_sub2": "Livestock Tracking", "why_us_desc2": "Monitor and manage your livestock with advanced tracking and health management features.", "why_us_sub3": "Drone Monitoring", "why_us_desc3": "Utilize drone technology for field mapping, crop health monitoring, and precision agriculture.", "why_us_sub4": "Market Analysis", "why_us_desc4": "Access real-time market data and trends to make informed decisions about crop sales.", "why_us_sub5": "Credit Score", "why_us_desc5": "Track your financial health and access agricultural loans with our credit scoring system.", "why_us_sub6": "AgriXpert Connect", "why_us_desc6": "Connect with agricultural experts and institutions for professional advice and support.", "why_us_sub7": "Data Analytics", "why_us_desc7": "Powerful analytics tools to gain insights from your farm data and optimize operations.", "why_us_sub8": "Efficient Operations", "why_us_desc8": "Streamline your farming operations with our advanced management tools.", "faq_heading": "Frequently Asked Questions", "faq_subheading": "Everything you need to know about Smart Farming", "faq1": "What is Smart Farming technology?", "faq1_ans": "Smart Farming is the application of modern Information and Communication Technologies (ICT) into agriculture. It incorporates IoT sensors, drones, AI, and data analytics to optimize farming operations, increase yields, and reduce resource usage while minimizing environmental impact.", "faq2": "How does soil monitoring technology work?", "faq2_ans": "Soil monitoring technology uses sensors placed in the soil to continuously measure parameters like moisture levels, temperature, pH, and nutrient content. This data is transmitted wirelessly to a central system where it is analyzed to provide farmers with real-time insights for optimal irrigation and fertilization decisions.", "faq3": "What benefits do agricultural drones provide?", "faq3_ans": "Agricultural drones provide numerous benefits including crop monitoring, precise spraying of fertilizers and pesticides, plant counting, yield estimation, and creating detailed field maps. They can cover large areas quickly, access hard-to-reach places, and collect high-resolution imagery that helps identify issues before they become serious problems.", "faq4": "How does weather forecasting help in agriculture?", "faq4_ans": "Advanced weather forecasting helps farmers make informed decisions about planting, harvesting, irrigation, and pest control. By integrating hyperlocal weather data with farm management systems, farmers can optimize operations, reduce crop damage from adverse weather, and improve resource efficiency.", "faq5": "What is precision agriculture?", "faq5_ans": "Precision agriculture is a farming management concept that uses digital techniques to monitor and optimize agricultural production processes. It relies on GPS, sensors, drones, and software to measure variations in the field and adapt strategies accordingly, allowing farmers to apply the right treatment in the right place at the right time.", "footer_label": "Quamin AgriCare 1.0", "footer_desc": "Empowering farmers with smart technology and expert knowledge for better agricultural outcomes.", "nav_supp_heading": "Navigation & Support", "nav_supp_sub1": "Home", "nav_supp_sub2": "Dashboard", "nav_supp_sub3": "Farm Management", "nav_supp_sub4": "Market Analysis", "nav_supp_sub5": "Support", "nav_supp_sub6": "Contact", "connect_us": "Connect With Us", "connect_us_sub1": "Follow us on social media", "connect_us_sub2": "LinkedIn", "connect_us_sub3": "Youtube", "connect_us_sub4": "Google", "connect_us_sub5": "Desk", "connect_us_sub6": "Quamin Tech Solutions LLP. All rights reserved.", "login_welcome_msg": "Welcome Back", "login_select_role": "Select Role", "login_farmer_role": "<PERSON>", "login_tm_role": "Territory Manager", "login_phone": "Phone Number", "login_send_otp": "Sending OTP...", "login_get_otp": "Get OTP", "login_selected_role": "Selected Role:", "login_not_select": "Role not selected. Please go back and select a role.", "login_enter_otp": "Enter OTP", "login_six_digit": "Enter 6-digit OTP", "login_verify_otp": "Verify OTP", "login_verify": "Verifying...", "login_change_phone": "Change Phone Number", "hr_login": "HR Login", "back_home": "Back to Home", "login_otp_success": "OTP sent successfully!", "phone_no_req": "Phone number is required", "phone_no_digits": "Phone number must be 10 digits", "role_req": "Please select a role first. Go back and select <PERSON> or Territory Manager.", "otp_six_req": "OTP must be 6 digits", "chatbot": {"newChat": "New Chat", "back": "Back", "chat": "Cha<PERSON>", "history": "History", "analysis": "Analysis", "searchChatHistory": "Search Chat History", "searchPlaceholder": "Search for keywords...", "search": "Search", "conversationsByDate": "Conversations by Date", "viewConversation": "VIEW CONVERSATION", "delete": "Delete", "messages": "messages", "imageAnalysisHistory": "Image Analysis History", "filter": "Filter", "uploadImage": "Upload Image", "startRecording": "Start Recording", "typeMessage": "Type your message...", "language": "Language", "send": "Send", "suggestedQuestions": "Suggested Questions", "botGreeting": "Hello! How can I assist you with your farming or agricultural needs today? 😊", "you": "You", "bot": "Bot", "noConversations": "No conversations found", "noAnalysisHistory": "No analysis history found", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "close": "Close", "save": "Save", "cancel": "Cancel", "edit": "Edit", "add": "Add", "update": "Update", "refresh": "Refresh", "sort": "Sort", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload", "reset": "Reset", "applyFilters": "Apply Filters"}, "no_reg_already": "This phone number is already registered with a different role. Please select the correct role or contact support.", "net_issue": "Network connection issue. Please check your internet connection and try again.", "ver_fail": "Verification failed. Please try again.", "pls_sel_role": "Please select your role", "none_msg": "None", "farmer_role": "<PERSON>", "tm_role": "Territory Manager", "invalid_format": "Invalid phone number format. Please check and try again.", "too_many_req": "Too many requests. Please try again later.", "recap_error": "reCAPTCHA verification failed. Please try again.", "test_user_success": "OTP sent successfully! Use 123456 as OTP.", "verify_redirect": "Verification successful! Redirecting...", "invalid_otp_msg": "Invalid OTP. Please check and try again.", "otp_expired_msg": "OTP has expired. Please request a new one.", "missing_otp_msg": "Please enter the OTP you received.", "failed_verify_msg": "Failed to verify <PERSON><PERSON>. Please try again.", "no_otp_sent_msg": "No OTP was sent. Please request OTP first.", "back_to_dash_msg": "Back to Dashboard", "farm_manage_msg": "Farm Management", "add_farm_msg": "Add Farm", "livestock_msg": "Livestock Monitoring System", "drone_monitoring_msg": "Drone Monitoring & Control", "market_analysis_msg": "Market Analysis", "farm_profile_msg": "Farm Financial Profile", "agri_expert_msg": "AgriXpert Connect", "crop_demand_msg": "Crop Demand & Supply Analysis", "dashboard": {"welcome": "Welcome", "title": "Dashboard", "cropStatus": "How are your crops doing today?", "updateSuccess": "Dashboard updated successfully", "fetchError": "Failed to fetch dashboard data. Please try again."}, "weather": "Weather", "weather.current": "Current Weather", "weather.lastUpdated": "Last updated", "weather.humidity": "<PERSON><PERSON><PERSON><PERSON>", "weather.wind": "Wind", "weather.feelsLike": "Feels Like", "weather.bulletin": "Weather Bulletin", "weather.forecast": "Forecast", "weather.forecast.unavailable": "Forecast data not available", "weather.noData": "No weather data available", "weather.rain": "Rain", "weather.fiveDayForecast": "5-Day Forecast", "soilHealth": "Soil Health", "soilHealth.moisture": "Moisture", "soilHealth.phLevel": "pH Level", "soilHealth.organicMatter": "Organic Matter", "soilHealth.temperature": "Temperature", "soilHealth.nitrogen": "Nitrogen (N)", "soilHealth.phosphorus": "Phosphorus (P)", "soilHealth.potassium": "Potassium (K)", "soilHealth.ec": "EC", "soilHealth.recommendations": "Recommendations", "soilHealth.weeklyTrends": "Weekly Soil Health Trends", "soilHealth.recommendations.ph": "Soil pH is slightly acidic. Consider adding lime to raise pH.", "soilHealth.recommendations.organic": "Organic matter content is low. Add compost or organic fertilizers.", "soilHealth.recommendations.nitrogen": "Nitrogen levels are below optimal. Apply nitrogen-rich fertilizer.", "soilHealth.recommendations.moisture": "Maintain consistent moisture levels between 40-60%.", "soilHealth.status.low": "Low", "soilHealth.status.optimal": "Optimal", "soilHealth.status.high": "High", "soilHealth.noData": "No soil data available", "loading": "Loading...", "weeklySchedule": "Weekly Schedule", "weeklySchedule.fieldInspection": "Field Inspection", "weeklySchedule.pestMonitoring": "Pest Monitoring", "weeklySchedule.equipmentMaintenance": "Equipment Maintenance", "weeklySchedule.weatherAlert": "<PERSON> Alert", "weeklySchedule.optimalWeather": "Optimal Weather", "weeklySchedule.irrigation": "Irrigation Needed", "weeklySchedule.fertilizer": "Fertilizer Application", "weeklySchedule.noTasks": "No scheduled tasks available", "weeklySchedule.noScheduledTasks": "No scheduled tasks", "weeklySchedule.priority.high": "High Priority", "weeklySchedule.priority.medium": "Medium Priority", "weeklySchedule.priority.low": "Low Priority", "weeklySchedule.status.completed": "Completed", "weeklySchedule.status.pending": "Pending", "weeklySchedule.status.overdue": "Overdue", "weeklySchedule.dialog.editTask": "Edit Task", "weeklySchedule.dialog.addNewTask": "Add New Task", "weeklySchedule.dialog.taskTitle": "Task Title", "weeklySchedule.dialog.taskType": "Task Type", "weeklySchedule.dialog.status": "Status", "weeklySchedule.dialog.cancel": "Cancel", "weeklySchedule.dialog.save": "Save", "weeklySchedule.taskType.farming": "Farming", "weeklySchedule.taskType.irrigation": "Irrigation", "weeklySchedule.taskType.pestControl": "Pest Control", "weeklySchedule.taskType.harvest": "Harvest", "weeklySchedule.weeklyFieldHealthCheck": "Weekly field health check", "weeklySchedule.checkPestInfestation": "Check for pest infestation", "weeklySchedule.maintainEquipment": "Check and maintain farming equipment", "weeklySchedule.expectedRainfall": "Expected rainfall. Plan indoor activities.", "weeklySchedule.goodDayForFieldwork": "Good day for field work", "weeklySchedule.lowSoilMoisture": "Low soil moisture detected", "weeklySchedule.applyNitrogenFertilizer": "Apply nitrogen-rich fertilizer", "marketAnalysis": "Market Analysis", "marketAnalysis.up": "Up", "marketAnalysis.down": "Down", "marketAnalysis.stable": "Stable", "marketAnalysis.crop": "Crop", "marketAnalysis.price": "Price", "marketAnalysis.trend": "Trend", "marketAnalysis.volume": "Volume", "marketAnalysis.market": "Market", "marketAnalysis.noData": "No market data available", "marketDataUpdated": "Market data is updated every 24 hours", "recentAlerts": "Recent Alerts", "recentAlerts.title": "<PERSON><PERSON>", "recentAlerts.noAlerts": "No alerts to display", "recentAlerts.lastUpdated": "Last updated", "recentAlerts.severity": {"high": "HIGH", "medium": "MEDIUM", "low": "LOW", "info": "INFO", "warning": "WARNING", "error": "ERROR"}, "farmFinancial": "Farm Financial", "refresh": "Refresh data", "totalInvestment": "Total Investment", "lastUpdated": "Last updated", "viewCreditScore": "View Credit Score", "cancel": "Cancel", "save": "Save", "menuItems": {"dashboard": "Dashboard", "myFarm": "My Farm", "livestock": "Livestock", "droneMonitoring": "Drone Monitoring", "marketAnalysis": "Market Analysis", "creditScore": "Credit Score", "soilAnalysis": "Soil Analysis", "agriXpertConnect": "AgriXpert Connect", "analysis": "Analysis"}, "userMenu": {"profile": "Profile", "settings": "Settings", "logout": "Logout"}, "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "numbers": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9"}, "units": {"tons": "tons", "kg": "kg", "mm": "mm", "%": "%", "°c": "°C", "km/h": "km/h", "ms/cm": "mS/cm", "mg/kg": "mg/kg"}, "aiAgent": "AI Assistant", "aiAgent.title": "Quamin AI Farmer Assistant", "aiAgent.stopRecording": "Stop Recording", "aiAgent.startRecording": "Start Recording", "aiAgent.stopSpeaking": "Stop Speaking", "aiAgent.startSpeaking": "Start Speaking", "aiAgent.pauseSpeaking": "<PERSON>use Speaking", "aiAgent.resumeSpeaking": "Resume Speaking", "aiAgent.clearChat": "Clear Chat", "aiAgent.askAnything": "Ask me anything about your farm, crops, weather, or agricultural practices.", "aiAgent.latestWeatherBulletin": "Latest Weather Bulletin:", "aiAgent.soilHealthAvailable": "Soil Health Data Available", "aiAgent.askAboutSoil": "You can ask me about your soil health, including pH levels, nutrient content, and recommendations.", "aiAgent.currentPh": "Current pH:", "aiAgent.recentAlert": "Recent Alert", "aiAgent.pluralS": "s", "aiAgent.askAboutAlerts": "You can ask me about your recent alerts and recommendations.", "aiAgent.alertDefault": "<PERSON><PERSON>", "aiAgent.moreAlerts": "more alerts...", "aiAgent.inputPlaceholder": "Ask me anything about your farm...", "languageSelector.label": "Language"}