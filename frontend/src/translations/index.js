export const translations = {
  "en-IN": {
    // Navigation
    home: "Home",
    services: "Services",
    about: "About",
    faq: "FAQ",
    login: "Login",
    logout: "Logout",
    dashboard: "Dashboard",

    // Dashboard
    "dashboard.welcome": "Welcome",

    // Login Page
    welcomeToAgriCare: "Welcome to AgriCare",
    selectRole: "Select Role",
    farmer: "Farmer",
    territoryManager: "Territory Manager",
    phoneNumber: "Phone Number",
    enterPhoneNumber: "Enter 10-digit phone number",
    getOTP: "Get OTP",
    enterOTP: "Enter OTP",
    verifyOTP: "Verify OTP",
    tryDifferentNumber: "Try different number",
    hrLogin: "HR Login",
    pleaseWait: "Please wait...",
    enter6DigitOTP: "Enter 6-digit OTP",

    // Dashboard
    weather: "Weather",
    soilHealth: "Soil Health",
    weeklySchedule: "Weekly Schedule",
    marketAnalysis: "Market Analysis",
    recentAlerts: "Recent Alerts",
    aiAgent: "AI Assistant",
    refresh: "Refresh Data",
    loading: "Loading...",
    error: "Error",
    success: "Success",
    save: "Save",
    cancel: "Cancel",
    delete: "Delete",
    edit: "Edit",
    view: "View",
    search: "Search",
    filter: "Filter",
    sort: "Sort",
    noData: "No data available",
  },
  "hi-IN": {
    // Navigation
    home: "होम",
    services: "सेवाएं",
    about: "हमारे बारे में",
    faq: "सामान्य प्रश्न",
    login: "लॉग इन",
    logout: "लॉग आउट",
    dashboard: "डैशबोर्ड",

    // Dashboard
    "dashboard.welcome": "स्वागत है",

    // Login Page
    welcomeToAgriCare: "एग्रीकेयर में आपका स्वागत है",
    selectRole: "भूमिका चुनें",
    farmer: "किसान",
    territoryManager: "क्षेत्र प्रबंधक",
    phoneNumber: "फोन नंबर",
    enterPhoneNumber: "10 अंकों का फोन नंबर दर्ज करें",
    getOTP: "OTP प्राप्त करें",
    enterOTP: "OTP दर्ज करें",
    verifyOTP: "OTP सत्यापित करें",
    tryDifferentNumber: "अलग नंबर आज़माएं",
    hrLogin: "HR लॉगिन",
    pleaseWait: "कृपया प्रतीक्षा करें...",
    enter6DigitOTP: "6 अंकों का OTP दर्ज करें",

    // Dashboard
    weather: "मौसम",
    soilHealth: "मृदा स्वास्थ्य",
    weeklySchedule: "साप्ताहिक कार्यक्रम",
    marketAnalysis: "बाजार विश्लेषण",
    recentAlerts: "हाल की सूचनाएं",
    aiAgent: "एआई सहायक",
    refresh: "डेटा रीफ्रेश करें",
    loading: "लोड हो रहा है...",
    error: "त्रुटि",
    success: "सफल",
    save: "सहेजें",
    cancel: "रद्द करें",
    delete: "हटाएं",
    edit: "संपादित करें",
    view: "देखें",
    search: "खोजें",
    filter: "फ़िल्टर",
    sort: "क्रमबद्ध करें",
    noData: "कोई डेटा उपलब्ध नहीं है",
  },
  "ta-IN": {
    // Navigation
    home: "முகப்பு",
    services: "சேவைகள்",
    about: "எங்களை பற்றி",
    faq: "அடிக்கடி கேட்கப்படும் கேள்விகள்",
    login: "உள்நுழைய",
    logout: "வெளியேற",
    dashboard: "டாஷ்போர்டு",

    // Login Page
    welcomeToAgriCare: "அக்ரிகேர்-க்கு வரவேற்கிறோம்",
    selectRole: "பாத்திரத்தை தேர்வு செய்யவும்",
    farmer: "விவசாயி",
    territoryManager: "பிரதேச மேலாளர்",
    phoneNumber: "தொலைபேசி எண்",
    enterPhoneNumber: "10 இலக்க தொலைபேசி எண்ணை உள்ளிடவும்",
    getOTP: "OTP பெற",
    enterOTP: "OTP உள்ளிடவும்",
    verifyOTP: "OTP சரிபார்க்க",
    tryDifferentNumber: "வேறு எண்ணை முயற்சிக்கவும்",
    hrLogin: "HR உள்நுழைவு",
    pleaseWait: "காத்திருக்கவும்...",
    enter6DigitOTP: "6 இலக்க OTP உள்ளிடவும்",

    // Dashboard
    weather: "வானிலை",
    soilHealth: "மண் ஆரோக்கியம்",
    weeklySchedule: "வாராந்திர அட்டவணை",
    marketAnalysis: "சந்தை பகுப்பாய்வு",
    recentAlerts: "சமீபத்திய எச்சரிக்கைகள்",
    aiAgent: "AI உதவியாளர்",
    refresh: "தரவை புதுப்பிக்க",
    loading: "ஏற்றுகிறது...",
    error: "பிழை",
    success: "வெற்றி",
    save: "சேமி",
    cancel: "ரத்து செய்",
    delete: "நீக்கு",
    edit: "திருத்து",
    view: "காண்க",
    search: "தேடு",
    filter: "வடிகட்டு",
    sort: "வரிசைப்படுத்து",
    noData: "தரவு இல்லை",
  },
  "te-IN": {
    // Navigation
    home: "హోమ్",
    services: "సేవలు",
    about: "మా గురించి",
    faq: "తరచుగా అడిగే ప్రశ్నలు",
    login: "ప్రవేశించండి",
    logout: "నిష్క్రమించండి",
    dashboard: "డాష్‌బోర్డ్",

    // Login Page
    welcomeToAgriCare: "అగ్రికేర్‌కి స్వాగతం",
    selectRole: "పాత్రను ఎంచుకోండి",
    farmer: "రైతు",
    territoryManager: "ప్రాంత మేనేజర్",
    phoneNumber: "ఫోన్ నంబర్",
    enterPhoneNumber: "10 అంకెల ఫోన్ నంబర్‌ని నమోదు చేయండి",
    getOTP: "OTP పొందండి",
    enterOTP: "OTP నమోదు చేయండి",
    verifyOTP: "OTP ధృవీకరించండి",
    tryDifferentNumber: "వేరే నంబర్‌ని ప్రయత్నించండి",
    hrLogin: "HR లాగిన్",
    pleaseWait: "దయచేసి వేచి ఉండండి...",
    enter6DigitOTP: "6 అంకెల OTP నమోదు చేయండి",

    // Dashboard
    weather: "వాని సాధారణత్వం",
    soilHealth: "మణి ఆరోగ్యం",
    weeklySchedule: "వారాన్ని పాల్గొనిస్తున్న ప్రమేయాలు",
    marketAnalysis: "మార్పు పరిగణన",
    recentAlerts: "సమീపించిన సిగ్నల్లు",
    aiAgent: "AI సహాయక అధికారి",
    refresh: "డేటా పునరావర్తనం",
    loading: "లోడ్ అవుతోంది...",
    error: "దోషం",
    success: "విజయం",
    save: "సేవ్",
    cancel: "రద్దు",
    delete: "తొలగించు",
    edit: "సవరించు",
    view: "చూడండి",
    search: "వెతకండి",
    filter: "ఫిల్టర్",
    sort: "క్రమబద్ధీకరించు",
    noData: "డేటా లేదు",
  },
  "kn-IN": {
    // Navigation
    home: "ಮುಖಪುಟ",
    services: "ಸೇವೆಗಳು",
    about: "ನಮ್ಮ ಬಗ್ಗೆ",
    faq: "ಸಾಮಾನ್ಯ ಪ್ರಶ್ನೆಗಳು",
    login: "ಲಾಗಿನ್",
    logout: "ಲಾಗ್ ಔಟ್",
    dashboard: "ಡ್ಯಾಶ್‌ಬೋರ್ಡ್",

    // Login Page
    welcomeToAgriCare: "ಅಗ್ರಿಕೇರ್‌ಗೆ ಸ್ವಾಗತ",
    selectRole: "ಪಾತ್ರವನ್ನು ಆಯ್ಕೆಮಾಡಿ",
    farmer: "ರೈತ",
    territoryManager: "ಪ್ರದೇಶ ವ್ಯವಸ್ಥಾಪಕ",
    phoneNumber: "ಫೋನ್ ಸಂಖ್ಯೆ",
    enterPhoneNumber: "10 ಅಂಕಿಯ ಫೋನ್ ಸಂಖ್ಯೆಯನ್ನು ನಮೂದಿಸಿ",
    getOTP: "OTP ಪಡೆಯಿರಿ",
    enterOTP: "OTP ನಮೂದಿಸಿ",
    verifyOTP: "OTP ಪರಿಶೀಲಿಸಿ",
    tryDifferentNumber: "ವಿಭಿನ್ನ ಸಂಖ್ಯೆಯನ್ನು ಪ್ರಯತ್ನಿಸಿ",
    hrLogin: "HR ಲಾಗಿನ್",
    pleaseWait: "ದಯವಿಟ್ಟು ಕಾಯಿರಿ...",
    enter6DigitOTP: "6 ಅಂಕಿಯ OTP ನಮೂದಿಸಿ",

    // Dashboard
    weather: "ವಾನಿ ಸಾಧಾರಣತ್ವ",
    soilHealth: "ಮಣಿ ಆರೋಗ್ಯ",
    weeklySchedule: "ವಾರಾನ್ನು ಪಾಲ್ಗೊನಿಸುವ ಪ್ರಮೇಯಗಳು",
    marketAnalysis: "ಮಾರ್ಪು ಪರಿಗಣನೆ",
    recentAlerts: "ಸಮೀಪಿಂಚಿನ ಸಿಗ್ನಲ್ಗಳು",
    aiAgent: "AI ಸಹಾಯಕ ಅಧಿಕಾರಿ",
    refresh: "ಡೇಟಾ ಪುನರಾವರ್ತನ",
    loading: "ಲೋಡ್ ಅವుತ್ತಿದೆ...",
    error: "ದೋಷ",
    success: "ಯಶಸ್ವಿ",
    save: "ಸೇವ್",
    cancel: "ರದ್ದುಮಾಡಿ",
    delete: "ಅಳಿಸಿ",
    edit: "ಸಂಪಾದಿಸಿ",
    view: "ನೋಡಿ",
    search: "ಹುಡುಕಿ",
    filter: "ಫಿಲ್ಟರ್",
    sort: "ವಿಂಗಡಿಸಿ",
    noData: "ಡೇಟಾ ಇಲ್ಲ",
  },
  "ml-IN": {
    // Navigation
    home: "ഹോം",
    services: "സേവനങ്ങൾ",
    about: "ഞങ്ങളെ കുറിച്ച്",
    faq: "പതിവായി ചോദിക്കുന്ന ചോദ്യങ്ങൾ",
    login: "ലോഗിൻ",
    logout: "ലോഗ് ഔട്ട്",
    dashboard: "ഡാഷ്‌ബോർഡ്",

    // Login Page
    welcomeToAgriCare: "അഗ്രികെയർ‌-ലേക്ക് സ്വാഗതം",
    selectRole: "റോൾ തിരഞ്ഞെടുക്കുക",
    farmer: "കർഷകൻ",
    territoryManager: "പ്രദേശ മാനേജർ",
    phoneNumber: "ഫോൺ നമ്പർ",
    enterPhoneNumber: "10 അക്ക ഫോൺ നമ്പർ നൽകുക",
    getOTP: "OTP നേടുക",
    enterOTP: "OTP നൽകുക",
    verifyOTP: "OTP പരിശോധിക്കുക",
    tryDifferentNumber: "വ്യത്യസ്ത നമ്പർ പ്രയത്നിക്കുക",
    hrLogin: "HR ലോഗിൻ",
    pleaseWait: "ദയവായി കാത്തിരിക്കുക...",
    enter6DigitOTP: "6 അക്ക OTP നൽകുക",

    // Dashboard
    weather: "വാനി സാധാരണത്വം",
    soilHealth: "മണി ആരോഗ്യം",
    weeklySchedule: "വാരാന്നി പാല്ഗൊനിസ്തുന്ന പ്രമേയാലുകൾ",
    marketAnalysis: "മാര്പ്പു പരിഗണന",
    recentAlerts: "സമീപിംചിന സിഗ്നലുകൾ",
    aiAgent: "AI സഹായക അധികാരി",
    refresh: "ഡേറ്റാ പുനരാവര്ത്തനം",
    loading: "ലോഡ് ചെയ്യുന്നു...",
    error: "പിശക്",
    success: "വിജയം",
    save: "സേവ്",
    cancel: "റദ്ദാക്കുക",
    delete: "ഇല്ലാതാക്കുക",
    edit: "എഡിറ്റ്",
    view: "കാണുക",
    search: "തിരയുക",
    filter: "ഫിൽട്ടർ",
    sort: "വർഗ്ഗീകരിക്കുക",
    noData: "ഡേറ്റ ഇല്ല",
  },
  "mr-IN": {
    // Navigation
    home: "होम",
    services: "सेवा",
    about: "आमच्याबद्दल",
    faq: "वारंवार विचारले जाणारे प्रश्न",
    login: "लॉगिन",
    logout: "लॉगआउट",
    dashboard: "डॅशबोर्ड",

    // Login Page
    welcomeToAgriCare: "अग्रिकेअर मध्ये आपले स्वागत आहे",
    selectRole: "भूमिका निवडा",
    farmer: "शेतकरी",
    territoryManager: "प्रदेश व्यवस्थापक",
    phoneNumber: "फोन नंबर",
    enterPhoneNumber: "10 अंकी फोन नंबर टाका",
    getOTP: "OTP मिळवा",
    enterOTP: "OTP टाका",
    verifyOTP: "OTP तपासा",
    tryDifferentNumber: "वेगळा नंबर वापरून पहा",
    hrLogin: "HR लॉगिन",
    pleaseWait: "कृपया थांबा...",
    enter6DigitOTP: "6 अंकी OTP टाका",

    // Dashboard
    weather: "मौसम",
    soilHealth: "मृदा स्वास्थ्य",
    weeklySchedule: "साप्ताहिक कार्यक्रम",
    marketAnalysis: "बाजार विश्लेषण",
    recentAlerts: "हाल की सूचनाएं",
    aiAgent: "एआई सहायक",
    refresh: "डेटा रीफ्रेश करें",
    loading: "लोड होत आहे...",
    error: "त्रुटी",
    success: "यश",
    save: "जतन करा",
    cancel: "रद्द करा",
    delete: "हटवा",
    edit: "संपादित करा",
    view: "पहा",
    search: "शोधा",
    filter: "फिल्टर",
    sort: "क्रमवारी लावा",
    noData: "डेटा नाही",
  },
  "gu-IN": {
    // Navigation
    home: "હોમ",
    services: "સેવાઓ",
    about: "અમારા વિશે",
    faq: "વારંવાર પૂછાતા પ્રશ્નો",
    login: "લૉગિન",
    logout: "લૉગઆઉટ",
    dashboard: "ડેશબોર્ડ",

    // Login Page
    welcomeToAgriCare: "અગ્રિકેરમાં તમારું સ્વાગત છે",
    selectRole: "ભૂમિકા પસંદ કરો",
    farmer: "કૃષિકાર",
    territoryManager: "પ્રદેશ વ્યવસ્થાપક",
    phoneNumber: "ફોન નંબર",
    enterPhoneNumber: "10 અંકનો ફોન નંબર દાખલ કરો",
    getOTP: "OTP મેળવો",
    enterOTP: "OTP દાખલ કરો",
    verifyOTP: "OTP ચકાસો",
    tryDifferentNumber: "વિવિધ નંબર વાપરીને જુઓ",
    hrLogin: "HR લૉગિન",
    pleaseWait: "કૃપા કરી રાહ જુઓ...",
    enter6DigitOTP: "6 અંકનો OTP દાખલ કરો",

    // Dashboard
    weather: "વાનિ સાધારણત્વ",
    soilHealth: "મણિ આરોગ્ય",
    weeklySchedule: "વારાન્નિ પાલ્ગોનિસ્ત પ્રમેયાલુકો",
    marketAnalysis: "માર્પુ પરિગણન",
    recentAlerts: "સમીપિંચિન સિગ્નલો",
    aiAgent: "AI સહાયક અધિકારી",
    refresh: "ડેટા પુનરાવર્તન",
    loading: "લોડ થઈ રહ્યું છે...",
    error: "ભૂલ",
    success: "સફળ",
    save: "સેવ",
    cancel: "રદ કરો",
    delete: "કાઢી નાખો",
    edit: "સંપાદિત કરો",
    view: "જુઓ",
    search: "શોધો",
    filter: "ફિલ્ટર",
    sort: "ક્રમમાં ગોઠવો",
    noData: "ડેટા નથી",
  },
  "pa-IN": {
    // Navigation
    home: "ਹੋਮ",
    services: "ਸੇਵਾਵਾਂ",
    about: "ਸਾਡੇ ਬਾਰੇ",
    faq: "ਅਕਸਰ ਪੁੱਛੇ ਜਾਣ ਵਾਲੇ ਪ੍ਰਸ਼ਨ",
    login: "ਲੌਗ ਇਨ",
    logout: "ਲੌਗ ਆਉਟ",
    dashboard: "ਡੈਸ਼ਬੋਰਡ",

    // Login Page
    welcomeToAgriCare: "ਅਗ੍ਰੀਕੇਅਰ ਵਿੱਚ ਤੁਹਾਡਾ ਸਵਾਗਤ ਹੈ",
    selectRole: "ਭੂਮਿਕਾ ਚੁਣੋ",
    farmer: "ਕਿਸਾਨ",
    territoryManager: "ਖੇਤਰ ਪ੍ਰਬੰਧਕ",
    phoneNumber: "ਫੋਨ ਨੰਬਰ",
    enterPhoneNumber: "10 ਅੰਕਾਂ ਦਾ ਫੋਨ ਨੰਬਰ ਦਰਜ ਕਰੋ",
    getOTP: "OTP ਪ੍ਰਾਪਤ ਕਰੋ",
    enterOTP: "OTP ਦਰਜ ਕਰੋ",
    verifyOTP: "OTP ਦੀ ਪੁਸ਼ਟੀ ਕਰੋ",
    tryDifferentNumber: "ਵੱਖ-ਵੱਖ ਨੰਬਰ ਅਜ਼ਮਾਓ",
    hrLogin: "HR ਲੌਗ ਇਨ",
    pleaseWait: "ਕਿਰਪਾ ਕਰਕੇ ਉਡੀਕ ਕਰੋ...",
    enter6DigitOTP: "6 ਅੰਕਾਂ ਦਾ OTP ਦਰਜ ਕਰੋ",

    // Dashboard
    weather: "ਵਾਨੀ ਸਾਧਾਰਣਤਾ",
    soilHealth: "ਮਰਦਾ ਸਵਾਸਥਾ",
    weeklySchedule: "ਸਾਪਤਾਹਿਕ ਕਾਰਯਕਰਮ",
    marketAnalysis: "ਬਾਜ਼ਾਰ ਵਿਸ਼ਲੇਸ਼",
    recentAlerts: "ਤਾਜ਼ਾ ਸੂਚਨਾਵਾਂ",
    aiAgent: "AI ਸਹਾਯਕ ਅਧਿਕਾਰੀ",
    refresh: "ਡੇਟਾ ਪੁਨਰਾਵਰਤਨ",
    loading: "ਲੋਡ ਹੋ ਰਿਹਾ ਹੈ...",
    error: "ਗਲਤੀ",
    success: "ਸਫਲ",
    save: "ਸੇਵ",
    cancel: "ਰੱਦ ਕਰੋ",
    delete: "ਹਟਾਓ",
    edit: "ਸੰਪਾਦਿਤ ਕਰੋ",
    view: "ਵੇਖੋ",
    search: "ਖੋਜੋ",
    filter: "ਫਿਲਟਰ",
    sort: "ਕ੍ਰਮਬੱਧ ਕਰੋ",
    noData: "ਡੇਟਾ ਨਹੀਂ ਹੈ",
  },
};

export const getTranslation = (key, language = "en-IN") => {
  return translations[language]?.[key] || translations["en-IN"][key] || key;
};
