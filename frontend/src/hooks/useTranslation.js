import { useState, useEffect, useCallback } from 'react';
import { useTranslation as useI18nTranslation } from 'react-i18next';
import { useLanguage } from '../contexts/LanguageContext';
import axios from 'axios';

/**
 * Custom hook that extends i18next translation with support for dynamic content translation
 * @returns {Object} Translation utilities
 */
export const useTranslation = () => {
  const { t, i18n } = useI18nTranslation();
  const { selectedLanguage } = useLanguage();
  const [translationCache, setTranslationCache] = useState({});

  /**
   * Translates dynamic content that isn't in the static translation files
   * @param {string} text - Text to translate 
   * @param {object} options - Options object
   * @param {boolean} options.skipCache - Whether to skip the cache check
   * @returns {Promise<string>} Translated text
   */
  const translateDynamic = useCallback(async (text, options = {}) => {
    if (!text) return '';
    
    // Don't translate if language is English or not specified
    if (selectedLanguage === 'en' || selectedLanguage === 'en-IN' || !selectedLanguage) {
      return text;
    }
    
    // Check local cache first unless skipCache is true
    const cacheKey = `${text}:${selectedLanguage}`;
    if (!options.skipCache && translationCache[cacheKey]) {
      return translationCache[cacheKey];
    }
    
    // Check global preloaded cache
    if (!options.skipCache && window.translationCache && window.translationCache[cacheKey]) {
      return window.translationCache[cacheKey];
    }
    
    try {
      const response = await axios.post('/api/translate', {
        text,
        targetLanguage: selectedLanguage.split('-')[0]
      });
      
      if (response.data && response.data.translation) {
        // Update cache with the new translation
        setTranslationCache(prev => ({
          ...prev,
          [cacheKey]: response.data.translation
        }));
        
        return response.data.translation;
      }
      return text;
    } catch (error) {
      console.error('Error translating dynamic content:', error);
      return text;
    }
  }, [selectedLanguage, translationCache]);

  /**
   * Translates multiple items in a batch
   * @param {Object} items - Object with keys to translate
   * @returns {Promise<Object>} Object with translated values
   */
  const translateBatch = useCallback(async (items) => {
    if (!items || selectedLanguage === 'en' || selectedLanguage === 'en-IN' || !selectedLanguage) {
      return items;
    }
    
    const results = {};
    const promises = [];
    const keys = [];
    
    // Create promises for each item
    for (const key in items) {
      if (typeof items[key] === 'string') {
        keys.push(key);
        promises.push(translateDynamic(items[key]));
      } else {
        // Keep non-string values as is
        results[key] = items[key];
      }
    }
    
    // Wait for all translations to complete
    const translations = await Promise.all(promises);
    
    // Map back to original keys
    translations.forEach((translation, index) => {
      results[keys[index]] = translation;
    });
    
    return results;
  }, [selectedLanguage, translateDynamic]);

  /**
   * Clear the translation cache
   */
  const clearCache = useCallback(() => {
    setTranslationCache({});
  }, []);

  // Clear cache when language changes
  useEffect(() => {
    clearCache();
  }, [selectedLanguage, clearCache]);

  return {
    // Original i18next functions
    t,
    i18n,
    // Enhanced functions
    translateDynamic,
    translateBatch,
    clearCache,
    language: selectedLanguage
  };
}; 