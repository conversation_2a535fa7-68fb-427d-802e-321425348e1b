import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import "./HRLogin.css";

const HRSignup = () => {
    const navigate = useNavigate();
    const [formData, setFormData] = useState({
        name: "",
        email: "",
        phoneNumber: "",
        countryCode: "+91", // Default country code for India
        password: "",
        confirmPassword: "",
        secretKey: "" // Admin secret key for authorization
    });
    const [error, setError] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleChange = (e) => {
        const { name, value } = e.target;
        if (name === 'phoneNumber') {
            // Only allow numbers and limit to 10 digits
            const cleaned = value.replace(/\D/g, '').slice(0, 10);
            setFormData(prev => ({
                ...prev,
                [name]: cleaned
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }
        setError(""); // Clear error when user types
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);
        setError("");

        // Basic validation
        if (formData.password !== formData.confirmPassword) {
            setError("Passwords do not match");
            setIsSubmitting(false);
            return;
        }

        // Phone number validation
        if (formData.phoneNumber.length !== 10) {
            setError("Phone number must be 10 digits");
            setIsSubmitting(false);
            return;
        }

        try {
            const response = await fetch('/api/hr/signup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: formData.name,
                    email: formData.email,
                    phoneNumber: `${formData.countryCode}${formData.phoneNumber}`,
                    password: formData.password,
                    secretKey: formData.secretKey
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Signup failed');
            }

            if (data.success) {
                alert("HR account created successfully!");
                navigate("/hrlogin");
            } else {
                throw new Error(data.message || 'Signup failed');
            }
        } catch (error) {
            setError(error.message || "Failed to create account. Please try again.");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleBack = () => {
        navigate("/hrlogin");
    };

    return (
        <div className="hrlogin-container">
            <div className="hrlogin-form">
                <h1 className="hrlogin-title">HR Signup</h1>
                {error && <div className="error-message">{error}</div>}
                <form onSubmit={handleSubmit}>
                    <div className="hrlogin-field">
                        <label htmlFor="name">Full Name:</label>
                        <input
                            type="text"
                            id="name"
                            name="name"
                            className="hrlogin-input"
                            value={formData.name}
                            onChange={handleChange}
                            placeholder="Enter your full name"
                            required
                        />
                    </div>
                    <div className="hrlogin-field">
                        <label htmlFor="email">Email:</label>
                        <input
                            type="email"
                            id="email"
                            name="email"
                            className="hrlogin-input"
                            value={formData.email}
                            onChange={handleChange}
                            placeholder="Enter your email"
                            required
                        />
                    </div>
                    <div className="hrlogin-field">
                        <label htmlFor="phoneNumber">Phone Number:</label>
                        <div className="phone-input-wrapper">
                            <select
                                className="country-code-select"
                                name="countryCode"
                                value={formData.countryCode}
                                onChange={handleChange}
                            >
                                <option value="+91">+91 (India)</option>
                                <option value="+1">+1 (USA)</option>
                                <option value="+44">+44 (UK)</option>
                            </select>
                            <div className="phone-input-container">
                                <input
                                    type="tel"
                                    id="phoneNumber"
                                    name="phoneNumber"
                                    className="hrlogin-input phone-input"
                                    value={formData.phoneNumber}
                                    onChange={handleChange}
                                    placeholder="Enter 10-digit number"
                                    required
                                />
                                <div className="phone-input-length">{formData.phoneNumber.length}/10</div>
                            </div>
                        </div>
                    </div>
                    <div className="hrlogin-field">
                        <label htmlFor="password">Password:</label>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            className="hrlogin-input"
                            value={formData.password}
                            onChange={handleChange}
                            placeholder="Enter password"
                            required
                        />
                    </div>
                    <div className="hrlogin-field">
                        <label htmlFor="confirmPassword">Confirm Password:</label>
                        <input
                            type="password"
                            id="confirmPassword"
                            name="confirmPassword"
                            className="hrlogin-input"
                            value={formData.confirmPassword}
                            onChange={handleChange}
                            placeholder="Confirm password"
                            required
                        />
                    </div>
                    <div className="hrlogin-field">
                        <label htmlFor="secretKey">Admin Secret Key:</label>
                        <input
                            type="password"
                            id="secretKey"
                            name="secretKey"
                            className="hrlogin-input"
                            value={formData.secretKey}
                            onChange={handleChange}
                            placeholder="Enter admin secret key"
                            required
                        />
                        <small className="input-hint">Contact administrator for the secret key</small>
                    </div>
                    <div className="hrlogin-buttons">
                        <button
                            type="button"
                            className="hrlogin-btn-back"
                            onClick={handleBack}
                        >
                            Back
                        </button>
                        <button 
                            type="submit" 
                            className="hrlogin-btn"
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? "Creating Account..." : "Sign Up"}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default HRSignup; 