import React from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON>, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { Box, Typography, Paper } from '@mui/material';

const ManagerPerformanceChart = () => {
  // Sample data for manager performance comparison
  // In a real application, this would come from an API
  const performanceData = [
    {
      name: '<PERSON><PERSON>',
      farmersOnboarded: 32,
      activeFarmers: 30,
      retentionRate: 94,
    },
    {
      name: '<PERSON><PERSON>',
      farmersOnboarded: 28,
      activeFarmers: 25,
      retentionRate: 89,
    },
    {
      name: '<PERSON><PERSON> <PERSON>',
      farmersOnboarded: 24,
      activeFarmers: 20,
      retentionRate: 83,
    },
    {
      name: '<PERSON><PERSON>',
      farmersOnboarded: 22,
      activeFarmers: 17,
      retentionRate: 77,
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      farmersOnboarded: 20,
      activeFarmers: 15,
      retentionRate: 75,
    }
  ];

  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: '8px' }}>
      <Typography variant="h6" gutterBottom sx={{ color: '#1e4d2b', fontWeight: 600 }}>
        Manager Performance Comparison
      </Typography>
      <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
        Comparing key performance metrics across territory managers
      </Typography>
      
      <Box sx={{ width: '100%', height: 400 }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={performanceData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 70
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="name" 
              angle={-45} 
              textAnchor="end"
              height={70}
              tick={{ fontSize: 12 }}
            />
            <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
            <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'rgba(255, 255, 255, 0.9)', 
                borderRadius: '8px', 
                border: 'none', 
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)' 
              }}
            />
            <Legend wrapperStyle={{ paddingTop: 20 }} />
            <Bar yAxisId="left" dataKey="farmersOnboarded" name="Farmers Onboarded" fill="#8884d8" radius={[4, 4, 0, 0]} />
            <Bar yAxisId="left" dataKey="activeFarmers" name="Active Farmers" fill="#82ca9d" radius={[4, 4, 0, 0]} />
            <Bar yAxisId="right" dataKey="retentionRate" name="Retention Rate (%)" fill="#ffc658" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </Box>
      
      <Typography variant="caption" sx={{ display: 'block', textAlign: 'center', mt: 2, color: '#666' }}>
        Data represents performance metrics for the last 30 days
      </Typography>
    </Paper>
  );
};

export default ManagerPerformanceChart;
