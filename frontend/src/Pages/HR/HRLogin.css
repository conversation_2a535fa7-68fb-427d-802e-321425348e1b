.hrlogin-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f5f5f5;
    padding: 20px;
}

.hrlogin-form {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.hrlogin-title {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    font-size: 24px;
}

.hrlogin-field {
    margin-bottom: 20px;
}

.hrlogin-field label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: 500;
}

.hrlogin-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.hrlogin-input:focus {
    border-color: #4CAF50;
    outline: none;
}

/* Phone input styles */
.phone-input-wrapper {
    display: flex;
    gap: 10px;
    align-items: flex-start;
}

.country-code-select {
    width: 120px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    background-color: white;
}

.phone-input-container {
    flex: 1;
    position: relative;
}

.phone-input {
    padding-right: 60px; /* Make room for the counter */
}

.phone-input-length {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 12px;
    pointer-events: none;
}

.hrlogin-buttons {
    display: flex;
    gap: 10px;
    margin-top: 30px;
}

.hrlogin-btn,
.hrlogin-btn-back {
    padding: 12px 20px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
    flex: 1;
}

.hrlogin-btn {
    background-color: #4CAF50;
    color: white;
}

.hrlogin-btn:hover {
    background-color: #45a049;
}

.hrlogin-btn:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.hrlogin-btn-back {
    background-color: #4caf50;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hrlogin-btn-back:hover {
    background-color: #388e3c;
}

.error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
    text-align: center;
}

.hrlogin-signup-link {
    text-align: center;
    margin-top: 20px;
    color: #666;
}

.hrlogin-signup-link a {
    color: #4CAF50;
    text-decoration: none;
    font-weight: 500;
}

.hrlogin-signup-link a:hover {
    text-decoration: underline;
}

.input-hint {
    display: block;
    color: #666;
    font-size: 12px;
    margin-top: 5px;
    font-style: italic;
}

@media (max-width: 480px) {
    .hrlogin-form {
        padding: 20px;
    }

    .phone-input-wrapper {
        flex-direction: column;
        gap: 5px;
    }

    .country-code-select {
        width: 100%;
    }
}
