.hrdash-container {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.hrdash-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.hrdash-title {
    color: #1e4d2b;
    margin: 0;
}

.hrdash-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.hrdash-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.hrdash-tab-btn {
    padding: 0.75rem 1.5rem;
    background: #f5f5f5;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    color: #555;
    transition: all 0.2s ease;
}

.hrdash-tab-btn:hover {
    background: #e0e0e0;
}

.hrdash-tab-btn.active {
    background: #1e4d2b;
    color: white;
}

.hrdash-tab-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.hrdash-managers-list {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hrdash-section-title {
    color: #1e4d2b;
    margin-bottom: 1rem;
}

.hrdash-managers-ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.hrdash-manager-item {
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.hrdash-manager-item:hover {
    background: #f5f5f5;
}

.hrdash-manager-item.inactive {
    opacity: 0.7;
    background: #f5f5f5;
}

.status-indicator {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
}

.status-indicator.active {
    background: #e8f5e9;
    color: #2e7d32;
}

.status-indicator.inactive {
    background: #ffebee;
    color: #c62828;
}

.hrdash-manager-actions {
    margin-top: 1rem;
    display: flex;
    gap: 1rem;
}

.hrdash-btn {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 6px;
    background: #1e4d2b;
    color: white;
    cursor: pointer;
    transition: background 0.3s ease;
}

.hrdash-btn:hover {
    background: #2c6d3f;
}

.hrdash-btn-logout {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    background: #dc3545;
    color: white;
    cursor: pointer;
}

.hrdash-details-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    width: 100%;
    max-width: 500px;
}

.modal-field {
    margin-bottom: 1rem;
}

.modal-field label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
}

.modal-field input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.modal-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
}

.modal-btn.submit {
    background: #1e4d2b;
    color: white;
}

.modal-btn.cancel {
    background: #dc3545;
    color: white;
}

.error-message {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 15px;
    text-align: center;
}

.phone-input-container {
    display: flex;
    gap: 8px;
    align-items: center;
}

.country-code-select {
    width: 120px;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
}

.country-code-select:focus {
    outline: none;
    border-color: #4a90e2;
}

#newManagerPhone {
    flex: 1;
}

@media (max-width: 768px) {
    .hrdash-content {
        grid-template-columns: 1fr;
    }

    .hrdash-container {
        padding: 1rem;
    }

    .hrdash-tab-content {
        grid-template-columns: 1fr;
    }

    .hrdash-performance-summary {
        grid-template-columns: 1fr 1fr;
    }

    .hrdash-reports-filters {
        flex-direction: column;
    }
}

/* Performance Tab Styles */
.hrdash-performance-tab,
.hrdash-reports-tab {
    grid-column: 1 / -1;
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hrdash-performance-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.hrdash-performance-card {
    background: #f9f9f9;
    padding: 1.25rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.hrdash-performance-card h3 {
    margin: 0 0 0.5rem;
    font-size: 1rem;
    color: #555;
}

.hrdash-performance-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1e4d2b;
    margin-bottom: 0.5rem;
}

.hrdash-performance-trend {
    font-size: 0.85rem;
    font-weight: 500;
}

.hrdash-performance-trend.positive {
    color: #2e7d32;
}

.hrdash-performance-trend.negative {
    color: #d32f2f;
}

.hrdash-subsection-title {
    margin: 1.5rem 0 1rem;
    color: #1e4d2b;
}

.hrdash-performance-table table {
    width: 100%;
    border-collapse: collapse;
}

.hrdash-performance-table th,
.hrdash-performance-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.hrdash-performance-table th {
    background: #f5f5f5;
    font-weight: 600;
    color: #333;
}

.hrdash-performance-table tr:hover {
    background: #f9f9f9;
}

/* Reports Tab Styles */
.hrdash-reports-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f5f5f5;
    border-radius: 8px;
}

.hrdash-filter {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.hrdash-filter label {
    font-weight: 500;
    font-size: 0.9rem;
}

.hrdash-filter select {
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    min-width: 200px;
}

.hrdash-report-preview {
    background: white;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

.hrdash-report-date {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

.hrdash-report-summary {
    margin-bottom: 1.5rem;
}

.hrdash-report-summary ul {
    padding-left: 1.5rem;
}

.hrdash-report-chart-placeholder {
    text-align: center;
    padding: 2rem;
    background: #f9f9f9;
    border-radius: 8px;
}

.chart-placeholder {
    height: 200px;
    background: #eee;
    border-radius: 4px;
    margin-top: 1rem;
    position: relative;
}

.chart-placeholder::after {
    content: 'Chart will appear here';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #999;
}

.chart-loading {
    text-align: center;
    padding: 2rem;
    background: #f9f9f9;
    border-radius: 8px;
    color: #666;
    font-style: italic;
}

.hrdash-report-chart {
    margin-top: 1.5rem;
}

/* Performance Metrics in Manager Details */
.hrdash-performance-metrics {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e0e0e0;
}

.hrdash-performance-metrics h3 {
    margin: 0 0 1rem;
    color: #1e4d2b;
}

.hrdash-metric {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.hrdash-metric-value {
    font-weight: 600;
    color: #1e4d2b;
}
