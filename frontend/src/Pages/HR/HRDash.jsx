import React, { useState, useEffect, lazy, Suspense } from "react";
import ManagerPerformanceChart from './components/ManagerPerformanceChart';
import { useNavigate } from "react-router-dom";
import "./HRDash.css";

const HRDash = () => {
    const navigate = useNavigate();
    const [managers, setManagers] = useState([]);
    const [selectedManager, setSelectedManager] = useState(null);
    const [showAddPopup, setShowAddPopup] = useState(false);
    const [showEditPopup, setShowEditPopup] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [addError, setAddError] = useState("");
    const [editError, setEditError] = useState("");
    const [activeTab, setActiveTab] = useState(0); // 0: Managers, 1: Performance, 2: Reports

    // New manager form fields
    const [newManagerName, setNewManagerName] = useState("");
    const [newManagerPhone, setNewManagerPhone] = useState("");
    const [newManagerRegion, setNewManagerRegion] = useState("");
    const [countryCode, setCountryCode] = useState("91"); // Default to India's code

    // Edit manager form fields
    const [editManagerName, setEditManagerName] = useState("");
    const [editManagerPhone, setEditManagerPhone] = useState("");
    const [editManagerRegion, setEditManagerRegion] = useState("");
    const [editCountryCode, setEditCountryCode] = useState("91");

    // Country codes for dropdown
    const countryCodes = [
        { code: "91", country: "India" },
        { code: "1", country: "USA" },
        { code: "44", country: "UK" },
        { code: "61", country: "Australia" },
        { code: "86", country: "China" }
    ];

    // Load TMs from MongoDB
    useEffect(() => {
        const loadTMs = async () => {
            try {
                setIsLoading(true);
                const response = await fetch('/api/tm/list');
                const data = await response.json();

                if (data.success) {
                    setManagers(data.territoryManagers || []);
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                console.error("Error loading TMs:", error);
                setManagers([]);
            } finally {
                setIsLoading(false);
            }
        };
        loadTMs();
    }, []);

    const handleSelectManager = (manager) => {
        setSelectedManager(manager);
    };

    const handleAddManagerClick = () => {
        setShowAddPopup(true);
    };

    const handleAddManagerSubmit = async (e) => {
        e.preventDefault();
        setAddError("");
        try {
            const response = await fetch('/api/tm/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: newManagerName,
                    phoneNumber: `${countryCode}${newManagerPhone}`,
                    region: newManagerRegion,
                    isActive: true
                })
            });

            const data = await response.json();

            if (!response.ok || !data.success) {
                throw new Error(data.message || 'Failed to add Territory Manager');
            }

            if (!data.territoryManager) {
                throw new Error('Invalid response from server');
            }

            // Add new manager to the list
            setManagers(prevManagers => [...prevManagers, data.territoryManager]);
            setShowAddPopup(false);
            setNewManagerName("");
            setNewManagerPhone("");
            setNewManagerRegion("");
            alert("Territory Manager added successfully!");
        } catch (error) {
            console.error("Error adding TM:", error);
            setAddError(error.message);
        }
    };

    const handleEditManagerClick = () => {
        if (selectedManager) {
            // Extract country code and phone number from the selected manager's phone number
            let phoneWithoutCode = selectedManager.phoneNumber;
            let countryCode = "91"; // Default to India

            // Check if the phone number starts with a country code
            const countryCodeMatch = selectedManager.phoneNumber.match(/^(\d{1,3})(\d{10})$/);
            if (countryCodeMatch) {
                countryCode = countryCodeMatch[1];
                phoneWithoutCode = countryCodeMatch[2];
            }

            // Set the edit form fields
            setEditManagerName(selectedManager.name);
            setEditManagerPhone(phoneWithoutCode);
            setEditManagerRegion(selectedManager.region);
            setEditCountryCode(countryCode);
            setEditError("");
            setShowEditPopup(true);
        }
    };

    const handleEditManagerSubmit = async (e) => {
        e.preventDefault();
        setEditError("");
        try {
            const response = await fetch(`/api/tm/update/${selectedManager._id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: editManagerName,
                    phoneNumber: `${editCountryCode}${editManagerPhone}`,
                    region: editManagerRegion
                })
            });

            const data = await response.json();

            if (!response.ok || !data.success) {
                throw new Error(data.message || 'Failed to update Territory Manager');
            }

            // Update the manager in the list
            setManagers(prevManagers =>
                prevManagers.map(manager =>
                    manager._id === selectedManager._id ? data.territoryManager : manager
                )
            );

            // Update the selected manager
            setSelectedManager(data.territoryManager);

            setShowEditPopup(false);
            alert("Territory Manager updated successfully!");
        } catch (error) {
            console.error("Error updating TM:", error);
            setEditError(error.message);
        }
    };

    const handleToggleStatus = async (managerId, isActive) => {
        try {
            const response = await fetch(`/api/tm/toggle/${managerId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message);
            }

            // Update local state
            setManagers(managers.map(m =>
                m._id === managerId ? { ...m, isActive } : m
            ));

            if (selectedManager?._id === managerId) {
                setSelectedManager({ ...selectedManager, isActive });
            }

            alert(`Territory Manager ${isActive ? 'activated' : 'deactivated'} successfully!`);
        } catch (error) {
            console.error("Error updating TM status:", error);
            alert("Failed to update Territory Manager status");
        }
    };

    const handleLogout = () => {
        // Clear any session data or tokens
        localStorage.removeItem('hrToken');
        sessionStorage.removeItem('hrToken');
        // Redirect to HR login page instead of home
        navigate("/hrlogin");
    };

    return (
        <div className="hrdash-container">
            <div className="hrdash-header">
                <h1 className="hrdash-title">HR Dashboard</h1>
                <button className="hrdash-btn-logout" onClick={handleLogout}>Logout</button>
            </div>
            <div className="hrdash-content">
                <div className="hrdash-tabs">
                    <button
                        className={`hrdash-tab-btn ${activeTab === 0 ? 'active' : ''}`}
                        onClick={() => setActiveTab(0)}
                    >
                        Managers
                    </button>
                    <button
                        className={`hrdash-tab-btn ${activeTab === 1 ? 'active' : ''}`}
                        onClick={() => setActiveTab(1)}
                    >
                        Performance
                    </button>
                    <button
                        className={`hrdash-tab-btn ${activeTab === 2 ? 'active' : ''}`}
                        onClick={() => setActiveTab(2)}
                    >
                        Reports
                    </button>
                </div>

                <div className="hrdash-tab-content">
                    {activeTab === 0 && (
                    <>
                    <div className="hrdash-managers-list">
                        <h2 className="hrdash-section-title">Territory Managers</h2>
                        {isLoading ? (
                            <p>Loading territory managers...</p>
                        ) : managers && managers.length > 0 ? (
                            <ul className="hrdash-managers-ul">
                                {managers.map((manager) => (
                                    <li
                                        key={manager._id}
                                        className={`hrdash-manager-item ${!(manager.isActive ?? true) ? 'inactive' : ''}`}
                                        onClick={() => handleSelectManager(manager)}
                                    >
                                        {manager.name} - {manager.phoneNumber}
                                        <span className={`status-indicator ${(manager.isActive ?? true) ? 'active' : 'inactive'}`}>
                                            {(manager.isActive ?? true) ? 'Active' : 'Inactive'}
                                        </span>
                                    </li>
                                ))}
                            </ul>
                        ) : (
                            <p>No territory managers found.</p>
                        )}
                        <div className="hrdash-manager-actions">
                            <button className="hrdash-btn" onClick={handleAddManagerClick}>
                                Add Manager
                            </button>
                            {selectedManager && (
                                <>
                                    <button
                                        className="hrdash-btn"
                                        onClick={() => handleToggleStatus(selectedManager._id, !selectedManager.isActive)}
                                    >
                                        {selectedManager.isActive ? 'Deactivate' : 'Activate'} Manager
                                    </button>
                                    <button
                                        className="hrdash-btn"
                                        onClick={handleEditManagerClick}
                                    >
                                        Edit Manager
                                    </button>
                                </>
                            )}
                        </div>
                    </div>
                    <div className="hrdash-manager-details">
                        <h2 className="hrdash-section-title">Manager Details</h2>
                        {selectedManager ? (
                            <div className="hrdash-details-card">
                                <p><strong>Name:</strong> {selectedManager.name}</p>
                                <p><strong>Phone:</strong> {selectedManager.phoneNumber}</p>
                                <p><strong>Region:</strong> {selectedManager.region}</p>
                                <p><strong>Status:</strong> {selectedManager.isActive ? 'Active' : 'Inactive'}</p>
                                <p><strong>Added on:</strong> {new Date(selectedManager.createdAt).toLocaleDateString()}</p>

                                <div className="hrdash-performance-metrics">
                                    <h3>Performance Metrics</h3>
                                    <div className="hrdash-metric">
                                        <span>Farmers Onboarded:</span>
                                        <span className="hrdash-metric-value">24</span>
                                    </div>
                                    <div className="hrdash-metric">
                                        <span>Active Farmers:</span>
                                        <span className="hrdash-metric-value">18</span>
                                    </div>
                                    <div className="hrdash-metric">
                                        <span>Retention Rate:</span>
                                        <span className="hrdash-metric-value">75%</span>
                                    </div>
                                    <div className="hrdash-metric">
                                        <span>Last Active:</span>
                                        <span className="hrdash-metric-value">Today</span>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            <p>Please select a Territory Manager from the list.</p>
                        )}
                    </div>
                    </>
                    )}

                    {activeTab === 1 && (
                    <div className="hrdash-performance-tab">
                        <h2 className="hrdash-section-title">Team Performance Overview</h2>
                        <div className="hrdash-performance-summary">
                            <div className="hrdash-performance-card">
                                <h3>Total Farmers</h3>
                                <div className="hrdash-performance-value">156</div>
                                <div className="hrdash-performance-trend positive">+12% from last month</div>
                            </div>
                            <div className="hrdash-performance-card">
                                <h3>Active Farmers</h3>
                                <div className="hrdash-performance-value">124</div>
                                <div className="hrdash-performance-trend positive">+8% from last month</div>
                            </div>
                            <div className="hrdash-performance-card">
                                <h3>Retention Rate</h3>
                                <div className="hrdash-performance-value">79%</div>
                                <div className="hrdash-performance-trend negative">-2% from last month</div>
                            </div>
                            <div className="hrdash-performance-card">
                                <h3>Average Engagement</h3>
                                <div className="hrdash-performance-value">4.2/5</div>
                                <div className="hrdash-performance-trend positive">+0.3 from last month</div>
                            </div>
                        </div>

                        <h3 className="hrdash-subsection-title">Top Performing Managers</h3>
                        <div className="hrdash-performance-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Rank</th>
                                        <th>Manager</th>
                                        <th>Region</th>
                                        <th>Farmers</th>
                                        <th>Retention</th>
                                        <th>Last Active</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>Rajesh Kumar</td>
                                        <td>Punjab</td>
                                        <td>32</td>
                                        <td>94%</td>
                                        <td>Today</td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>Priya Singh</td>
                                        <td>Haryana</td>
                                        <td>28</td>
                                        <td>89%</td>
                                        <td>Today</td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>Amit Sharma</td>
                                        <td>Uttar Pradesh</td>
                                        <td>24</td>
                                        <td>83%</td>
                                        <td>Yesterday</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    )}

                    {activeTab === 2 && (
                    <div className="hrdash-reports-tab">
                        <h2 className="hrdash-section-title">Reports & Analytics</h2>

                        <div className="hrdash-reports-filters">
                            <div className="hrdash-filter">
                                <label>Report Type:</label>
                                <select>
                                    <option>Manager Performance</option>
                                    <option>Farmer Engagement</option>
                                    <option>Regional Analysis</option>
                                    <option>Retention Report</option>
                                </select>
                            </div>
                            <div className="hrdash-filter">
                                <label>Time Period:</label>
                                <select>
                                    <option>Last 30 Days</option>
                                    <option>Last Quarter</option>
                                    <option>Last 6 Months</option>
                                    <option>Year to Date</option>
                                    <option>Custom Range</option>
                                </select>
                            </div>
                            <button className="hrdash-btn">Generate Report</button>
                            <button className="hrdash-btn">Export to Excel</button>
                        </div>

                        <div className="hrdash-report-preview">
                            <h3>Manager Performance Report</h3>
                            <p className="hrdash-report-date">Generated for: Last 30 Days (May 1, 2025 - May 31, 2025)</p>

                            <div className="hrdash-report-summary">
                                <p>This report provides an overview of Territory Manager performance metrics including farmer onboarding, retention rates, and engagement scores.</p>
                                <p><strong>Key Findings:</strong></p>
                                <ul>
                                    <li>Overall farmer onboarding increased by 12% compared to previous period</li>
                                    <li>Punjab region shows highest retention rate at 92%</li>
                                    <li>Managers with regular check-ins show 24% higher farmer engagement</li>
                                </ul>
                            </div>

                            <div className="hrdash-report-chart">
                                <Suspense fallback={<div className="chart-loading">Loading performance chart...</div>}>
                                    <ManagerPerformanceChart />
                                </Suspense>
                            </div>
                        </div>
                    </div>
                    )}
                </div>
            </div>

            {showAddPopup && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <h2>Add New Territory Manager</h2>
                        {addError && <div className="error-message">{addError}</div>}
                        <form onSubmit={handleAddManagerSubmit}>
                            <div className="modal-field">
                                <label htmlFor="newManagerName">Name:</label>
                                <input
                                    type="text"
                                    id="newManagerName"
                                    value={newManagerName}
                                    onChange={(e) => setNewManagerName(e.target.value)}
                                    required
                                />
                            </div>
                            <div className="modal-field">
                                <label htmlFor="newManagerPhone">Phone Number:</label>
                                <div className="phone-input-container">
                                    <select
                                        id="countryCode"
                                        value={countryCode}
                                        onChange={(e) => setCountryCode(e.target.value)}
                                        className="country-code-select"
                                    >
                                        {countryCodes.map(({ code, country }) => (
                                            <option key={code} value={code}>
                                                +{code} ({country})
                                            </option>
                                        ))}
                                    </select>
                                    <input
                                        type="tel"
                                        id="newManagerPhone"
                                        value={newManagerPhone}
                                        onChange={(e) => setNewManagerPhone(e.target.value.replace(/\D/g, ''))}
                                        pattern="[0-9]{10}"
                                        placeholder="10-digit phone number"
                                        required
                                    />
                                </div>
                            </div>
                            <div className="modal-field">
                                <label htmlFor="newManagerRegion">Region:</label>
                                <input
                                    type="text"
                                    id="newManagerRegion"
                                    value={newManagerRegion}
                                    onChange={(e) => setNewManagerRegion(e.target.value)}
                                    required
                                />
                            </div>
                            <div className="modal-actions">
                                <button type="submit" className="modal-btn submit">Add Manager</button>
                                <button type="button" className="modal-btn cancel" onClick={() => setShowAddPopup(false)}>
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}

            {showEditPopup && selectedManager && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <h2>Edit Territory Manager</h2>
                        {editError && <div className="error-message">{editError}</div>}
                        <form onSubmit={handleEditManagerSubmit}>
                            <div className="modal-field">
                                <label htmlFor="editManagerName">Name:</label>
                                <input
                                    type="text"
                                    id="editManagerName"
                                    value={editManagerName}
                                    onChange={(e) => setEditManagerName(e.target.value)}
                                    required
                                />
                            </div>
                            <div className="modal-field">
                                <label htmlFor="editManagerPhone">Phone Number:</label>
                                <div className="phone-input-container">
                                    <select
                                        id="editCountryCode"
                                        value={editCountryCode}
                                        onChange={(e) => setEditCountryCode(e.target.value)}
                                        className="country-code-select"
                                    >
                                        {countryCodes.map(({ code, country }) => (
                                            <option key={code} value={code}>
                                                +{code} ({country})
                                            </option>
                                        ))}
                                    </select>
                                    <input
                                        type="tel"
                                        id="editManagerPhone"
                                        value={editManagerPhone}
                                        onChange={(e) => setEditManagerPhone(e.target.value.replace(/\D/g, ''))}
                                        pattern="[0-9]{10}"
                                        placeholder="10-digit phone number"
                                        required
                                    />
                                </div>
                            </div>
                            <div className="modal-field">
                                <label htmlFor="editManagerRegion">Region:</label>
                                <input
                                    type="text"
                                    id="editManagerRegion"
                                    value={editManagerRegion}
                                    onChange={(e) => setEditManagerRegion(e.target.value)}
                                    required
                                />
                            </div>
                            <div className="modal-actions">
                                <button type="submit" className="modal-btn submit">Update Manager</button>
                                <button type="button" className="modal-btn cancel" onClick={() => setShowEditPopup(false)}>
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default HRDash;
