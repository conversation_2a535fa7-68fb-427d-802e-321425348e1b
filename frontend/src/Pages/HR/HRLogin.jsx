import React, { useState } from "react";
import { useN<PERSON>gate, Link } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { makeApiCall, ENDPOINTS } from "../../config/api";
import HomeIcon from '@mui/icons-material/Home';
import "./HRLogin.css";

const HRLogin = () => {
    const navigate = useNavigate();
    const { login } = useAuth();
    const [formData, setFormData] = useState({
        email: "",
        password: ""
    });
    const [error, setError] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        setError(""); // Clear error when user types
    };

    const handleLogin = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);
        setError("");

        try {
            const data = await makeApiCall(ENDPOINTS.HR.LOGIN, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            if (data.success) {
                // Set the user in auth context with HR role
                await login({
                    ...data.hr,
                    role: 'HR'
                });
                navigate("/hrdash");
            } else {
                throw new Error(data.message || 'Login failed');
            }
        } catch (error) {
            console.error('HR Login error:', error);
            setError(error.message || "Failed to login. Please try again.");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleBack = () => {
        navigate("/");
    };

    return (
        <div className="hrlogin-container">
            <div className="hrlogin-form">
                <h1 className="hrlogin-title">HR Login</h1>
                {error && <div className="error-message">{error}</div>}
                <form onSubmit={handleLogin}>
                    <div className="hrlogin-field">
                        <label htmlFor="email">Email:</label>
                        <input
                            type="email"
                            id="email"
                            name="email"
                            className="hrlogin-input"
                            value={formData.email}
                            onChange={handleChange}
                            placeholder="Enter your email"
                            required
                        />
                    </div>
                    <div className="hrlogin-field">
                        <label htmlFor="password">Password:</label>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            className="hrlogin-input"
                            value={formData.password}
                            onChange={handleChange}
                            placeholder="Enter your password"
                            required
                        />
                    </div>
                    <div className="hrlogin-buttons">
                        <button
                            type="button"
                            className="hrlogin-btn-back"
                            onClick={handleBack}
                        >
                            <HomeIcon style={{ marginRight: '5px' }} /> Back to Home
                        </button>
                        <button
                            type="submit"
                            className="hrlogin-btn"
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? "Logging in..." : "Login"}
                        </button>
                    </div>
                </form>
                <div className="hrlogin-signup-link">
                    Don't have an account? <Link to="/hrsignup">Sign up here</Link>
                </div>
            </div>
        </div>
    );
};

export default HRLogin;
