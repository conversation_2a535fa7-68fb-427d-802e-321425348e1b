import React, { useState, useEffect } from 'react';
import {
  getFarmerFinancialData,
  getYieldHistory,
  getPaymentHistory,
  getLoanDetails,
  generateFinancialReport
} from '../../services/financialService';
import { API_BASE_URL } from '../../config/api';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Button,
  Chip,
  CircularProgress,
  AppBar,
  Toolbar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  FormGroup,
  FormControlLabel,
  Switch,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  AccountBalance as BankIcon,
  Payment as PaymentIcon,
  Assessment as AssessmentIcon,
  Landscape as LandIcon,
  Agriculture as AgricultureIcon,
  LocalShipping as EquipmentIcon,
  Timeline as YieldIcon,
  ArrowBack as ArrowBackIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Download as DownloadIcon,
  Share as ShareIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from 'react-i18next';

// TabPanel component for tab content
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`credit-score-tabpanel-${index}`}
      aria-labelledby={`credit-score-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const CreditScore = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [assetSubTab, setAssetSubTab] = useState(0);
  const [creditData, setCreditData] = useState(null);

  // Mock credit score data
  const mockCreditData = {
    creditScore: {
      score: 720,
      scoreCategory: 'Good',
      lastUpdated: '2024-05-15'
    },
    scoreRange: { min: 300, max: 900 },
    loans: [
      {
        id: 'L001',
        type: 'Crop Loan',
        amount: 200000,
        remainingAmount: 120000,
        interestRate: 7.5,
        startDate: '2023-06-10',
        endDate: '2026-06-10',
        status: 'Active',
        bankName: 'State Bank of India',
        emiAmount: 6500,
        emiStatus: 'On Time',
        purpose: 'Wheat cultivation',
        collateral: 'Land (3 acres)',
        guarantor: 'Rajesh Kumar (Brother)',
        loanOfficer: 'Mr. Sharma',
        branchCode: 'SBI-AMR-1205'
      },
      {
        id: 'L002',
        type: 'Equipment Loan',
        amount: 500000,
        remainingAmount: 350000,
        interestRate: 8.2,
        startDate: '2022-03-15',
        endDate: '2027-03-15',
        status: 'Active',
        bankName: 'Punjab National Bank',
        emiAmount: 12000,
        emiStatus: 'Delayed',
        purpose: 'Tractor purchase',
        collateral: 'Existing equipment',
        guarantor: 'Suresh Singh (Father)',
        loanOfficer: 'Mr. Patel',
        branchCode: 'PNB-AMR-0305'
      },
      {
        id: 'L003',
        type: 'Kisan Credit Card',
        amount: 150000,
        remainingAmount: 75000,
        interestRate: 4.0,
        startDate: '2024-01-10',
        endDate: '2025-01-10',
        status: 'Active',
        bankName: 'NABARD',
        emiAmount: 0,
        emiStatus: 'Revolving Credit',
        purpose: 'Seasonal expenses',
        collateral: 'None',
        guarantor: 'None',
        loanOfficer: 'Ms. Gupta',
        branchCode: 'NABARD-AMR-0112'
      }
    ],
    assets: {
      land: [
        {
          id: 'LD001',
          area: 5.5,
          unit: 'Acres',
          location: 'Amritsar, Punjab',
          value: 2500000,
          cropType: 'Wheat',
          ownership: 'Owned',
          documentNumber: 'AMR-LD-2345-A',
          purchaseYear: 2015,
          irrigationSource: 'Canal',
          soilType: 'Alluvial'
        },
        {
          id: 'LD002',
          area: 3.2,
          unit: 'Acres',
          location: 'Amritsar, Punjab',
          value: 1500000,
          cropType: 'Rice',
          ownership: 'Leased',
          documentNumber: 'AMR-LD-3456-B',
          leaseExpiryDate: '2026-05-30',
          irrigationSource: 'Tube Well',
          soilType: 'Clay Loam'
        },
        {
          id: 'LD003',
          area: 2.8,
          unit: 'Acres',
          location: 'Tarn Taran, Punjab',
          value: 1200000,
          cropType: 'Sugarcane',
          ownership: 'Owned',
          documentNumber: 'TT-LD-4567-C',
          purchaseYear: 2018,
          irrigationSource: 'Drip Irrigation',
          soilType: 'Sandy Loam'
        }
      ],
      equipment: [
        {
          id: 'EQ001',
          name: 'Tractor',
          model: 'Mahindra 575 DI',
          purchaseYear: 2022,
          value: 650000,
          registrationNumber: 'PB-02-AB-1234',
          insuranceExpiryDate: '2025-03-15',
          maintenanceStatus: 'Good',
          fuelType: 'Diesel'
        },
        {
          id: 'EQ002',
          name: 'Harvester',
          model: 'John Deere X350R',
          purchaseYear: 2023,
          value: 850000,
          registrationNumber: 'PB-02-CD-5678',
          insuranceExpiryDate: '2025-06-20',
          maintenanceStatus: 'Excellent',
          fuelType: 'Diesel'
        },
        {
          id: 'EQ003',
          name: 'Rotavator',
          model: 'Sonalika 42 Blade',
          purchaseYear: 2023,
          value: 120000,
          registrationNumber: 'N/A',
          insuranceExpiryDate: '2025-04-10',
          maintenanceStatus: 'Good',
          fuelType: 'N/A'
        },
        {
          id: 'EQ004',
          name: 'Sprayer',
          model: 'HTP 4-Stroke',
          purchaseYear: 2024,
          value: 35000,
          registrationNumber: 'N/A',
          insuranceExpiryDate: 'N/A',
          maintenanceStatus: 'Excellent',
          fuelType: 'Petrol'
        }
      ],
      livestock: [
        {
          id: 'LS001',
          type: 'Cow',
          breed: 'Holstein',
          count: 5,
          value: 250000,
          purchaseYear: 2022,
          insuranceStatus: 'Insured',
          healthStatus: 'Healthy'
        },
        {
          id: 'LS002',
          type: 'Buffalo',
          breed: 'Murrah',
          count: 3,
          value: 180000,
          purchaseYear: 2023,
          insuranceStatus: 'Insured',
          healthStatus: 'Healthy'
        }
      ],
      structures: [
        {
          id: 'ST001',
          type: 'Storage Silo',
          capacity: '50 tons',
          value: 120000,
          constructionYear: 2021,
          condition: 'Good'
        },
        {
          id: 'ST002',
          type: 'Barn',
          capacity: '200 sq meters',
          value: 350000,
          constructionYear: 2020,
          condition: 'Good'
        }
      ]
    },
    yieldHistory: [
      {
        year: 2022,
        crop: 'Wheat',
        area: 5.5,
        production: 22000,
        unit: 'kg',
        revenue: 440000,
        expenses: 180000,
        profit: 260000,
        yieldPerAcre: 4000,
        marketPrice: 20
      },
      {
        year: 2023,
        crop: 'Wheat',
        area: 5.5,
        production: 24000,
        unit: 'kg',
        revenue: 504000,
        expenses: 195000,
        profit: 309000,
        yieldPerAcre: 4364,
        marketPrice: 21
      },
      {
        year: 2023,
        crop: 'Rice',
        area: 3.2,
        production: 12800,
        unit: 'kg',
        revenue: 384000,
        expenses: 150000,
        profit: 234000,
        yieldPerAcre: 4000,
        marketPrice: 30
      },
      {
        year: 2024,
        crop: 'Rice',
        area: 3.2,
        production: 13500,
        unit: 'kg',
        revenue: 432000,
        expenses: 160000,
        profit: 272000,
        yieldPerAcre: 4219,
        marketPrice: 32
      }
    ],
    estimatedYield: {
      crop: 'Wheat',
      year: 2025,
      estimatedProduction: 26000,
      unit: 'kg',
      estimatedRevenue: 598000,
      estimatedExpenses: 210000,
      estimatedProfit: 388000,
      estimatedYieldPerAcre: 4727,
      estimatedMarketPrice: 23,
      growthFactors: [
        'Improved seed variety',
        'Better irrigation system',
        'Favorable weather forecast'
      ],
      riskFactors: [
        'Potential pest issues',
        'Market price volatility',
        'Delayed monsoon possibility'
      ]
    },
    paymentHistory: [
      {
        date: '2024-04-15',
        amount: 6500,
        loanId: 'L001',
        status: 'Paid',
        dueDate: '2024-04-15',
        transactionId: 'TXN-45678',
        paymentMethod: 'Bank Transfer'
      },
      {
        date: '2024-03-15',
        amount: 6500,
        loanId: 'L001',
        status: 'Paid',
        dueDate: '2024-03-15',
        transactionId: 'TXN-34567',
        paymentMethod: 'Bank Transfer'
      },
      {
        date: '2024-04-25',
        amount: 12000,
        loanId: 'L002',
        status: 'Delayed',
        dueDate: '2024-04-15',
        paidDate: '2024-04-25',
        transactionId: 'TXN-56789',
        paymentMethod: 'Cash',
        delayReason: 'Market payment delay'
      },
      {
        date: '2024-05-15',
        amount: 6500,
        loanId: 'L001',
        status: 'Upcoming',
        dueDate: '2024-05-15'
      }
    ],
    creditHistory: {
      cibilScore: 750,
      lastCibilUpdate: '2024-04-01',
      creditUtilization: 65,
      accountAgeYears: 5,
      inquiriesLast6Months: 1,
      delinquencies: 0,
      creditMix: 'Good',
      paymentHistory: 'Excellent',
      remarks: 'Good repayment history with minimal delays'
    },
    insuranceDetails: [
      {
        id: 'INS001',
        type: 'Crop Insurance',
        provider: 'Agriculture Insurance Company',
        coverageAmount: 300000,
        premium: 15000,
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        crops: ['Wheat', 'Rice'],
        policyNumber: 'AIC-CR-12345',
        status: 'Active'
      },
      {
        id: 'INS002',
        type: 'Equipment Insurance',
        provider: 'National Insurance',
        coverageAmount: 500000,
        premium: 25000,
        startDate: '2024-02-15',
        endDate: '2025-02-14',
        equipment: ['Tractor', 'Harvester'],
        policyNumber: 'NI-EQ-67890',
        status: 'Active'
      },
      {
        id: 'INS003',
        type: 'Livestock Insurance',
        provider: 'United India Insurance',
        coverageAmount: 200000,
        premium: 10000,
        startDate: '2024-03-10',
        endDate: '2025-03-09',
        livestock: ['Cow', 'Buffalo'],
        policyNumber: 'UII-LS-54321',
        status: 'Active'
      }
    ],
    subsidies: [
      {
        id: 'SUB001',
        scheme: 'PM-KISAN',
        amount: 6000,
        frequency: 'Annual',
        lastReceived: '2024-02-10',
        nextExpected: '2025-02-10',
        status: 'Active',
        registrationNumber: 'PMKS-12345-6789'
      },
      {
        id: 'SUB002',
        scheme: 'Fertilizer Subsidy',
        amount: 8500,
        frequency: 'Seasonal',
        lastReceived: '2024-03-15',
        nextExpected: '2024-09-15',
        status: 'Active',
        registrationNumber: 'FERT-45678-9012'
      }
    ],
    bankAccounts: [
      {
        id: 'ACC001',
        bankName: 'State Bank of India',
        accountType: 'Savings',
        accountNumber: 'XXXX5678',
        branch: 'Amritsar Main',
        ifscCode: 'SBIN0001234',
        balance: 85000,
        lastTransaction: '2024-05-10'
      },
      {
        id: 'ACC002',
        bankName: 'Punjab National Bank',
        accountType: 'Current',
        accountNumber: 'XXXX9012',
        branch: 'Amritsar Agricultural Branch',
        ifscCode: 'PUNB0056789',
        balance: 120000,
        lastTransaction: '2024-05-12'
      }
    ]
  };

  useEffect(() => {
    const fetchFinancialData = async () => {
      try {
        // Check if backend is running by making a simple request
        const isBackendRunning = await checkBackendStatus();

        if (!isBackendRunning) {
          console.warn('Backend not available, using mock data');
          setCreditData(mockCreditData);
          setLoading(false);
          return;
        }

        // In a real app, we would get the current user's ID
        // For now, use a mock farmer ID or get it from auth context
        const farmerId = currentUser?.uid || '64f5e5e5e5e5e5e5e5e5e5e5';

        try {
          const response = await getFarmerFinancialData(farmerId);

          if (response.success) {
            setCreditData(response.data);
          } else {
            // If API fails, use mock data as fallback
            console.warn('Using mock data as fallback');
            setCreditData(mockCreditData);
          }
        } catch (error) {
          console.error('Error fetching financial data:', error);
          // Use mock data as fallback
          setCreditData(mockCreditData);
        }
      } catch (error) {
        console.error('Error checking backend status:', error);
        // Use mock data as fallback
        setCreditData(mockCreditData);
      } finally {
        setLoading(false);
      }
    };

    // Helper function to check if backend is running
    const checkBackendStatus = async () => {
      // Skip the actual check and always return false to use mock data
      // This avoids unnecessary network requests that will fail
      console.warn('Using mock data by default');
      return false;

      // In a production environment, you would uncomment this code:
      /*
      try {
        // Try to make a simple request to the backend
        // We'll use a timeout to avoid waiting too long
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 1000);

        // Try to connect to the base URL instead of a health endpoint
        await fetch(`${API_BASE_URL}`, {
          signal: controller.signal
        });

        clearTimeout(timeoutId);
        return true;
      } catch (error) {
        console.warn('Backend health check failed:', error.message);
        return false;
      }
      */
    };

    fetchFinancialData();
  }, [currentUser]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleAssetSubTabChange = (event, newValue) => {
    setAssetSubTab(newValue);
  };

  const getScoreColor = (score) => {
    if (!score) return '#2196f3'; // Default blue
    if (score >= 750) return '#4caf50'; // Green
    if (score >= 650) return '#2196f3'; // Blue
    if (score >= 550) return '#ff9800'; // Orange
    return '#f44336'; // Red
  };

  const getStatusIcon = (status) => {
    switch (status.toLowerCase()) {
      case 'paid':
      case 'on time':
        return <CheckCircleIcon color="success" />;
      case 'delayed':
        return <WarningIcon color="warning" />;
      case 'missed':
        return <ErrorIcon color="error" />;
      default:
        return <InfoIcon color="info" />;
    }
  };

  // Function to generate a financial report with date range
  const generateReport = async (reportType = 'comprehensive') => {
    setLoading(true);

    try {
      // Get current year for default date range
      const currentYear = new Date().getFullYear();
      const startDate = new Date(currentYear, 0, 1); // Jan 1 of current year
      const endDate = new Date(currentYear, 11, 31); // Dec 31 of current year

      // In a real app, we would get the current user's ID
      const farmerId = currentUser?.uid || '64f5e5e5e5e5e5e5e5e5e5e5';

      const response = await generateFinancialReport(farmerId, {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        reportType
      });

      if (response.success) {
        // Here you would handle the report data, perhaps open a modal or download it
        console.log('Report generated successfully:', response.data);

        // For demonstration, we'll just show an alert
        alert(`${reportType.charAt(0).toUpperCase() + reportType.slice(1)} report generated successfully!`);
      }
    } catch (error) {
      console.error('Error generating report:', error);
      alert('Failed to generate report. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (loading || !creditData || !creditData.creditScore) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ position: 'relative', pb: 4 }}>
      <AppBar
        position="static"
        color="transparent"
        elevation={0}
        sx={{
          borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
          bgcolor: 'background.paper',
          mb: 3
        }}
      >
        <Toolbar variant="dense" sx={{ minHeight: 48 }}>
          <Button
            onClick={() => navigate('/dashboard')}
            startIcon={<ArrowBackIcon fontSize="small" />}
            size="small"
            sx={{
              mr: 2,
              color: 'primary.main',
              '&:hover': { bgcolor: 'primary.light', color: 'primary.contrastText' },
              fontSize: '0.75rem',
              py: 0.5,
              minWidth: 'auto'
            }}
          >
            {t("back_to_dash_msg")}
          </Button>
          <Typography variant="h6" component="h1" sx={{ fontWeight: 'medium' }}>
            {t("farm_profile_msg")}
          </Typography>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mb: 4 }}>

      {/* Credit Score Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: { xs: 2, md: 3 }, height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
            <Typography variant="h6" gutterBottom sx={{ alignSelf: 'flex-start', mb: 2 }}>
              Credit Score
            </Typography>
            <Box sx={{ position: 'relative', display: 'inline-flex', mb: 2, alignSelf: 'center' }}>
              <CircularProgress
                variant="determinate"
                value={(creditData.creditScore.score / (creditData.scoreRange?.max || 900)) * 100}
                size={130}
                thickness={4}
                sx={{
                  color: getScoreColor(creditData.creditScore.score),
                  '@media (min-width: 600px)': {
                    '& .MuiCircularProgress-svg': {
                      width: 150,
                      height: 150
                    }
                  }
                }}
              />
              <Box
                sx={{
                  top: 0,
                  left: 0,
                  bottom: 0,
                  right: 0,
                  position: 'absolute',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexDirection: 'column',
                  textAlign: 'center',
                  p: 1
                }}
              >
                <Typography
                  variant="h3"
                  component="div"
                  color="text.primary"
                  sx={{
                    fontSize: { xs: '1.75rem', sm: '2.5rem' },
                    lineHeight: 1.2,
                    mb: 0.5
                  }}
                >
                  {creditData.creditScore.score}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.7rem', sm: '0.875rem' },
                    lineHeight: 1
                  }}
                >
                  out of {creditData.scoreRange?.max || 900}
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%', mb: 2 }}>
              <Chip
                label={creditData.creditScore.scoreCategory}
                color={
                  creditData.creditScore.scoreCategory === 'Excellent' ? 'success' :
                  creditData.creditScore.scoreCategory === 'Good' ? 'primary' :
                  creditData.creditScore.scoreCategory === 'Fair' ? 'warning' : 'error'
                }
              />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ alignSelf: 'center' }}>
              Last updated: {new Date(creditData.creditScore.lastUpdated).toLocaleDateString()}
            </Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={8}>
          <Paper sx={{ p: { xs: 2, md: 3 }, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Loan Eligibility Summary
            </Typography>
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={4} sm={4}>
                <Card sx={{ bgcolor: 'success.light', color: 'success.contrastText' }}>
                  <CardContent sx={{ p: { xs: 1, sm: 2 } }}>
                    <Typography variant="h5" sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }}>₹500,000</Typography>
                    <Typography variant="body2" sx={{ fontSize: { xs: '0.7rem', sm: '0.875rem' } }}>Eligible Amount</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={4} sm={4}>
                <Card sx={{ bgcolor: 'info.light', color: 'info.contrastText' }}>
                  <CardContent sx={{ p: { xs: 1, sm: 2 } }}>
                    <Typography variant="h5" sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }}>8.5%</Typography>
                    <Typography variant="body2" sx={{ fontSize: { xs: '0.7rem', sm: '0.875rem' } }}>Interest Rate</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={4} sm={4}>
                <Card sx={{ bgcolor: 'warning.light', color: 'warning.contrastText' }}>
                  <CardContent sx={{ p: { xs: 1, sm: 2 } }}>
                    <Typography variant="h5" sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }}>5 Years</Typography>
                    <Typography variant="body2" sx={{ fontSize: { xs: '0.7rem', sm: '0.875rem' } }}>Maximum Term</Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
            <Typography variant="body2" paragraph>
              Based on your credit score, land holdings, and payment history, you are eligible for agricultural loans up to ₹500,000.
              Your good repayment history has qualified you for preferential interest rates.
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, justifyContent: 'space-between', gap: 1 }}>
              <Button variant="contained" color="primary" fullWidth={false} sx={{ width: { xs: '100%', sm: 'auto' } }}>
                Apply for Loan
              </Button>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={() => generateReport('comprehensive')}
                disabled={loading}
                fullWidth={false}
                sx={{ width: { xs: '100%', sm: 'auto' } }}
              >
                {loading ? <CircularProgress size={24} /> : "Download Report"}
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Detailed Information Tabs */}
      <Paper sx={{ mb: 3, overflow: 'hidden' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            aria-label="credit score tabs"
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile
            sx={{
              '.MuiTabs-scrollButtons': {
                '&.Mui-disabled': { opacity: 0.3 },
              },
              '.MuiTab-root': {
                minWidth: { xs: '100px', sm: '120px' },
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                py: 1.5
              }
            }}
          >
            <Tab icon={<BankIcon />} label="Loans" />
            <Tab icon={<LandIcon />} label="Assets" />
            <Tab icon={<YieldIcon />} label="Yield" />
            <Tab icon={<PaymentIcon />} label="Payments" />
            <Tab icon={<AssessmentIcon />} label="Credit" />
            <Tab icon={<InfoIcon />} label="Insurance" />
            <Tab icon={<AgricultureIcon />} label="Subsidies" />
          </Tabs>
        </Box>

        {/* Loans Tab */}
        <TabPanel value={activeTab} index={0}>
          <Typography variant="h6" gutterBottom>
            Active Loans
          </Typography>
          <TableContainer sx={{ overflowX: 'auto' }}>
            <Table>
              <TableHead>
                <TableRow key="loan-header-row">
                  <TableCell>Loan Type</TableCell>
                  <TableCell>Bank</TableCell>
                  <TableCell>Amount</TableCell>
                  <TableCell>Remaining</TableCell>
                  <TableCell>Interest</TableCell>
                  <TableCell>EMI</TableCell>
                  <TableCell>Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {(creditData.loans || []).map((loan) => (
                  <TableRow key={loan.id}>
                    <TableCell>{loan.type}</TableCell>
                    <TableCell>{loan.bankName}</TableCell>
                    <TableCell>₹{loan.amount.toLocaleString()}</TableCell>
                    <TableCell>₹{loan.remainingAmount.toLocaleString()}</TableCell>
                    <TableCell>{loan.interestRate}%</TableCell>
                    <TableCell>₹{loan.emiAmount.toLocaleString()}</TableCell>
                    <TableCell>
                      <Chip
                        icon={getStatusIcon(loan.emiStatus)}
                        label={loan.emiStatus}
                        color={
                          loan.emiStatus === 'On Time' ? 'success' :
                          loan.emiStatus === 'Delayed' ? 'warning' : 'error'
                        }
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Assets Tab */}
        <TabPanel value={activeTab} index={1}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Total Asset Value: ₹{(
                creditData.assets.land.reduce((sum, land) => sum + land.value, 0) +
                creditData.assets.equipment.reduce((sum, eq) => sum + eq.value, 0) +
                (creditData.assets.livestock ? creditData.assets.livestock.reduce((sum, ls) => sum + ls.value, 0) : 0) +
                (creditData.assets.structures ? creditData.assets.structures.reduce((sum, st) => sum + st.value, 0) : 0)
              ).toLocaleString()}
            </Typography>
          </Box>

          <Box sx={{ mb: 3 }}>
            <Tabs
              value={assetSubTab}
              onChange={handleAssetSubTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
            >
              <Tab label="Land Holdings" />
              <Tab label="Equipment" />
              <Tab label="Livestock" />
              <Tab label="Structures" />
            </Tabs>

            {/* Land Holdings Sub-Tab */}
            {assetSubTab === 0 && (
              <TableContainer sx={{ overflowX: 'auto' }}>
                <Table>
                  <TableHead>
                    <TableRow key="land-header-row">
                      <TableCell>Area</TableCell>
                      <TableCell>Location</TableCell>
                      <TableCell>Crop</TableCell>
                      <TableCell>Ownership</TableCell>
                      <TableCell>Soil Type</TableCell>
                      <TableCell>Irrigation</TableCell>
                      <TableCell>Value</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {(creditData.assets?.land || []).map((land) => (
                      <TableRow key={land.id}>
                        <TableCell>{land.area} {land.unit}</TableCell>
                        <TableCell>{land.location}</TableCell>
                        <TableCell>{land.cropType}</TableCell>
                        <TableCell>
                          <Chip
                            label={land.ownership}
                            color={land.ownership === 'Owned' ? 'success' : 'primary'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{land.soilType}</TableCell>
                        <TableCell>{land.irrigationSource}</TableCell>
                        <TableCell>₹{land.value.toLocaleString()}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* Equipment Sub-Tab */}
            {assetSubTab === 1 && (
              <TableContainer sx={{ overflowX: 'auto' }}>
                <Table>
                  <TableHead>
                    <TableRow key="equipment-header-row">
                      <TableCell>Equipment</TableCell>
                      <TableCell>Model</TableCell>
                      <TableCell>Year</TableCell>
                      <TableCell>Registration</TableCell>
                      <TableCell>Maintenance</TableCell>
                      <TableCell>Insurance Expiry</TableCell>
                      <TableCell>Value</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {(creditData.assets?.equipment || []).map((equipment) => (
                      <TableRow key={equipment.id}>
                        <TableCell>{equipment.name}</TableCell>
                        <TableCell>{equipment.model}</TableCell>
                        <TableCell>{equipment.purchaseYear}</TableCell>
                        <TableCell>{equipment.registrationNumber}</TableCell>
                        <TableCell>
                          <Chip
                            label={equipment.maintenanceStatus}
                            color={
                              equipment.maintenanceStatus === 'Excellent' ? 'success' :
                              equipment.maintenanceStatus === 'Good' ? 'primary' : 'warning'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{equipment.insuranceExpiryDate}</TableCell>
                        <TableCell>₹{equipment.value.toLocaleString()}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* Livestock Sub-Tab */}
            {assetSubTab === 2 && (
              <TableContainer sx={{ overflowX: 'auto' }}>
                <Table>
                  <TableHead>
                    <TableRow key="livestock-header-row">
                      <TableCell>Type</TableCell>
                      <TableCell>Breed</TableCell>
                      <TableCell>Count</TableCell>
                      <TableCell>Purchase Year</TableCell>
                      <TableCell>Insurance Status</TableCell>
                      <TableCell>Health Status</TableCell>
                      <TableCell>Value</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {(creditData.assets?.livestock || []).map((livestock) => (
                      <TableRow key={livestock.id}>
                        <TableCell>{livestock.type}</TableCell>
                        <TableCell>{livestock.breed}</TableCell>
                        <TableCell>{livestock.count}</TableCell>
                        <TableCell>{livestock.purchaseYear}</TableCell>
                        <TableCell>
                          <Chip
                            label={livestock.insuranceStatus}
                            color={livestock.insuranceStatus === 'Insured' ? 'success' : 'error'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={livestock.healthStatus}
                            color={
                              livestock.healthStatus === 'Healthy' ? 'success' :
                              livestock.healthStatus === 'Fair' ? 'warning' : 'error'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>₹{livestock.value.toLocaleString()}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* Structures Sub-Tab */}
            {assetSubTab === 3 && (
              <TableContainer sx={{ overflowX: 'auto' }}>
                <Table>
                  <TableHead>
                    <TableRow key="structures-header-row">
                      <TableCell>Type</TableCell>
                      <TableCell>Capacity</TableCell>
                      <TableCell>Construction Year</TableCell>
                      <TableCell>Condition</TableCell>
                      <TableCell>Value</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {(creditData.assets?.structures || []).map((structure) => (
                      <TableRow key={structure.id}>
                        <TableCell>{structure.type}</TableCell>
                        <TableCell>{structure.capacity}</TableCell>
                        <TableCell>{structure.constructionYear}</TableCell>
                        <TableCell>
                          <Chip
                            label={structure.condition}
                            color={
                              structure.condition === 'Excellent' ? 'success' :
                              structure.condition === 'Good' ? 'primary' :
                              structure.condition === 'Fair' ? 'warning' : 'error'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>₹{structure.value.toLocaleString()}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Box>
        </TabPanel>

        {/* Yield History Tab */}
        <TabPanel value={activeTab} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Typography variant="h6" gutterBottom>
                Past Yield Performance
              </Typography>
              <TableContainer sx={{ overflowX: 'auto' }}>
                <Table>
                  <TableHead>
                    <TableRow key="yield-header-row">
                      <TableCell>Year</TableCell>
                      <TableCell>Crop</TableCell>
                      <TableCell>Area</TableCell>
                      <TableCell>Production</TableCell>
                      <TableCell>Revenue</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {(creditData.yieldHistory || []).map((yield_, index) => (
                      <TableRow key={index}>
                        <TableCell>{yield_.year}</TableCell>
                        <TableCell>{yield_.crop}</TableCell>
                        <TableCell>{yield_.area} Acres</TableCell>
                        <TableCell>{yield_.production} {yield_.unit}</TableCell>
                        <TableCell>₹{yield_.revenue.toLocaleString()}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Grid>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: { xs: 1.5, sm: 2 }, bgcolor: 'success.light', color: 'success.contrastText' }}>
                <Typography variant="h6" gutterBottom sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
                  Estimated Yield for {creditData.estimatedYield?.year || 2025}
                </Typography>
                <Typography variant="body1" paragraph sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                  Crop: {creditData.estimatedYield?.crop || 'Wheat'}
                </Typography>
                <Typography variant="h4" gutterBottom sx={{ fontSize: { xs: '1.5rem', sm: '2.125rem' } }}>
                  {creditData.estimatedYield?.estimatedProduction || '25,000'} {creditData.estimatedYield?.unit || 'kg'}
                </Typography>
                <Typography variant="body1" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                  Estimated Revenue: ₹{(creditData.estimatedYield?.estimatedRevenue || 550000).toLocaleString()}
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Payment History Tab */}
        <TabPanel value={activeTab} index={3}>
          <Typography variant="h6" gutterBottom>
            Recent Payments
          </Typography>
          <TableContainer sx={{ overflowX: 'auto' }}>
            <Table>
              <TableHead>
                <TableRow key="payment-header-row">
                  <TableCell>Date</TableCell>
                  <TableCell>Amount</TableCell>
                  <TableCell>Loan</TableCell>
                  <TableCell>Due Date</TableCell>
                  <TableCell>Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {(creditData.paymentHistory || []).map((payment, index) => (
                  <TableRow key={index}>
                    <TableCell>{payment.date}</TableCell>
                    <TableCell>₹{payment.amount.toLocaleString()}</TableCell>
                    <TableCell>{(creditData.loans || []).find(loan => loan.id === payment.loanId)?.type || 'Unknown'}</TableCell>
                    <TableCell>{payment.dueDate}</TableCell>
                    <TableCell>
                      <Chip
                        icon={getStatusIcon(payment.status)}
                        label={payment.status}
                        color={
                          payment.status === 'Paid' ? 'success' :
                          payment.status === 'Delayed' ? 'warning' : 'error'
                        }
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Credit History Tab */}
        <TabPanel value={activeTab} index={4}>
          <Typography variant="h6" gutterBottom>
            Credit Profile
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Typography variant="subtitle1" gutterBottom>
                  CIBIL Score Details
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ position: 'relative', display: 'inline-flex', mr: 3 }}>
                    <CircularProgress
                      variant="determinate"
                      value={(creditData.creditHistory.cibilScore / 900) * 100}
                      size={100}
                      thickness={5}
                      sx={{ color: getScoreColor(creditData.creditHistory.cibilScore) }}
                    />
                    <Box
                      sx={{
                        top: 0,
                        left: 0,
                        bottom: 0,
                        right: 0,
                        position: 'absolute',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant="h5" component="div">
                        {creditData.creditHistory.cibilScore}
                      </Typography>
                    </Box>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Last Updated: {creditData.creditHistory.lastCibilUpdate}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Account Age: {creditData.creditHistory.accountAgeYears} years
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Credit Mix: {creditData.creditHistory.creditMix}
                    </Typography>
                  </Box>
                </Box>

                <Typography variant="subtitle2" gutterBottom>
                  Credit Utilization
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box sx={{ width: '100%', mr: 1 }}>
                    <LinearProgress
                      variant="determinate"
                      value={creditData.creditHistory.creditUtilization}
                      sx={{
                        height: 10,
                        borderRadius: 5,
                        bgcolor: 'background.paper',
                        '& .MuiLinearProgress-bar': {
                          bgcolor: creditData.creditHistory.creditUtilization > 80 ? 'error.main' :
                                  creditData.creditHistory.creditUtilization > 60 ? 'warning.main' : 'success.main'
                        }
                      }}
                    />
                  </Box>
                  <Box sx={{ minWidth: 35 }}>
                    <Typography variant="body2" color="text.secondary">{creditData.creditHistory.creditUtilization}%</Typography>
                  </Box>
                </Box>

                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      {creditData.creditHistory.delinquencies === 0 ? <CheckCircleIcon color="success" /> : <ErrorIcon color="error" />}
                    </ListItemIcon>
                    <ListItemText
                      primary="Delinquencies"
                      secondary={creditData.creditHistory.delinquencies === 0 ? "No delinquencies reported" : `${creditData.creditHistory.delinquencies} delinquencies reported`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      {creditData.creditHistory.inquiriesLast6Months <= 2 ? <CheckCircleIcon color="success" /> : <WarningIcon color="warning" />}
                    </ListItemIcon>
                    <ListItemText
                      primary="Recent Inquiries"
                      secondary={`${creditData.creditHistory.inquiriesLast6Months} inquiries in last 6 months`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <InfoIcon color="info" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Remarks"
                      secondary={creditData.creditHistory.remarks}
                    />
                  </ListItem>
                </List>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Typography variant="subtitle1" gutterBottom>
                  Bank Accounts
                </Typography>
                <List>
                  {creditData.bankAccounts.map((account) => (
                    <ListItem key={account.id} divider>
                      <ListItemIcon>
                        <BankIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="subtitle2">{account.bankName}</Typography>
                            <Typography variant="subtitle2">₹{account.balance.toLocaleString()}</Typography>
                          </Box>
                        }
                        secondary={
                          <>
                            <Typography variant="body2" component="span">
                              {account.accountType} Account: {account.accountNumber}
                            </Typography>
                            <Typography variant="body2" component="div" display="block" color="text.secondary">
                              Branch: {account.branch} | IFSC: {account.ifscCode}
                            </Typography>
                            <Typography variant="caption" component="div" display="block" color="text.secondary">
                              Last Transaction: {account.lastTransaction}
                            </Typography>
                          </>
                        }
                      />
                    </ListItem>
                  ))}
                </List>

                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button variant="outlined" size="small">
                    Link New Account
                  </Button>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Insurance Tab */}
        <TabPanel value={activeTab} index={5}>
          <Typography variant="h6" gutterBottom>
            Insurance Coverage
          </Typography>
          <Grid container spacing={3}>
            {creditData.insuranceDetails.map((insurance) => (
              <Grid item xs={12} md={4} key={insurance.id}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="h6">{insurance.type}</Typography>
                      <Chip
                        label={insurance.status}
                        color={insurance.status === 'Active' ? 'success' : 'error'}
                        size="small"
                      />
                    </Box>

                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Provider: {insurance.provider}
                    </Typography>

                    <Divider sx={{ my: 1 }} />

                    <Typography variant="subtitle2" gutterBottom>
                      Coverage Details
                    </Typography>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Coverage Amount:</Typography>
                      <Typography variant="body2">₹{insurance.coverageAmount.toLocaleString()}</Typography>
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Annual Premium:</Typography>
                      <Typography variant="body2">₹{insurance.premium.toLocaleString()}</Typography>
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Valid Period:</Typography>
                      <Typography variant="body2">{insurance.startDate} to {insurance.endDate}</Typography>
                    </Box>

                    <Divider sx={{ my: 1 }} />

                    <Typography variant="subtitle2" gutterBottom>
                      Covered Items
                    </Typography>

                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
                      {insurance.crops && insurance.crops.map((crop, index) => (
                        <Chip key={index} label={crop} size="small" variant="outlined" />
                      ))}
                      {insurance.equipment && insurance.equipment.map((equip, index) => (
                        <Chip key={index} label={equip} size="small" variant="outlined" />
                      ))}
                      {insurance.livestock && insurance.livestock.map((animal, index) => (
                        <Chip key={index} label={animal} size="small" variant="outlined" />
                      ))}
                    </Box>

                    <Typography variant="caption" display="block" color="text.secondary">
                      Policy Number: {insurance.policyNumber}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Subsidies Tab */}
        <TabPanel value={activeTab} index={6}>
          <Typography variant="h6" gutterBottom>
            Government Subsidies & Schemes
          </Typography>
          <Grid container spacing={3}>
            {creditData.subsidies.map((subsidy) => (
              <Grid item xs={12} md={6} key={subsidy.id}>
                <Paper sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="h6">{subsidy.scheme}</Typography>
                    <Chip
                      label={subsidy.status}
                      color={subsidy.status === 'Active' ? 'success' : 'error'}
                      size="small"
                    />
                  </Box>

                  <Divider sx={{ my: 1 }} />

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Amount:</Typography>
                    <Typography variant="body2">₹{subsidy.amount.toLocaleString()}</Typography>
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Frequency:</Typography>
                    <Typography variant="body2">{subsidy.frequency}</Typography>
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Last Received:</Typography>
                    <Typography variant="body2">{subsidy.lastReceived}</Typography>
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Next Expected:</Typography>
                    <Typography variant="body2">{subsidy.nextExpected}</Typography>
                  </Box>

                  <Typography variant="caption" display="block" color="text.secondary">
                    Registration Number: {subsidy.registrationNumber}
                  </Typography>
                </Paper>
              </Grid>
            ))}

            <Grid item xs={12}>
              <Paper sx={{ p: 2, bgcolor: 'info.light', color: 'info.contrastText' }}>
                <Typography variant="subtitle1" gutterBottom>
                  Available Schemes You May Qualify For
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={4}>
                    <Card variant="outlined" sx={{ bgcolor: 'background.paper' }}>
                      <CardContent>
                        <Typography variant="subtitle1">Soil Health Card Scheme</Typography>
                        <Typography variant="body2" color="text.secondary">
                          Get detailed soil analysis and recommendations for your farm.
                        </Typography>
                        <Button size="small" sx={{ mt: 1 }}>Apply Now</Button>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Card variant="outlined" sx={{ bgcolor: 'background.paper' }}>
                      <CardContent>
                        <Typography variant="subtitle1">Micro Irrigation Fund</Typography>
                        <Typography variant="body2" color="text.secondary">
                          Subsidies for drip and sprinkler irrigation systems.
                        </Typography>
                        <Button size="small" sx={{ mt: 1 }}>Apply Now</Button>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <Card variant="outlined" sx={{ bgcolor: 'background.paper' }}>
                      <CardContent>
                        <Typography variant="subtitle1">Farm Mechanization Subsidy</Typography>
                        <Typography variant="body2" color="text.secondary">
                          Get up to 50% subsidy on purchase of new farm equipment.
                        </Typography>
                        <Button size="small" sx={{ mt: 1 }}>Apply Now</Button>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>

      {/* API Integration Section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Third-Party API Integration
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom>
              Financial Data Sharing
            </Typography>
            <Typography variant="body2" paragraph>
              This financial profile can be securely shared with banks and financial institutions for loan approval processes.
              The system integrates with third-party services for EMI tracking and civil score verification.
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 3 }}>
              <Button variant="outlined" startIcon={<ShareIcon />}>
                Share with Bank
              </Button>
              <Button variant="outlined" color="secondary">
                Connect to CIBIL
              </Button>
              <Button variant="outlined" color="warning">
                Connect EMI Tracker
              </Button>
            </Box>

            <Typography variant="subtitle2" gutterBottom>
              Connected Services
            </Typography>
            <List dense>
              <ListItem>
                <ListItemIcon>
                  <CheckCircleIcon color="success" />
                </ListItemIcon>
                <ListItemText
                  primary="State Bank of India"
                  secondary="Connected on 15 Mar 2024"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircleIcon color="success" />
                </ListItemIcon>
                <ListItemText
                  primary="NABARD Kisan Credit"
                  secondary="Connected on 10 Jan 2024"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <InfoIcon color="disabled" />
                </ListItemIcon>
                <ListItemText
                  primary="CIBIL Score Tracker"
                  secondary="Not connected"
                />
              </ListItem>
            </List>
          </Grid>

          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom>
              Data Privacy & Permissions
            </Typography>
            <Typography variant="body2" paragraph>
              Control which data is shared with financial institutions and third-party services.
            </Typography>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Data Sharing Permissions
              </Typography>
              <FormGroup>
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Basic Profile Information"
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Asset Details"
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Loan History"
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Payment History"
                />
                <FormControlLabel
                  control={<Switch />}
                  label="Bank Account Details"
                />
                <FormControlLabel
                  control={<Switch />}
                  label="Yield Predictions"
                />
              </FormGroup>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button variant="contained" color="primary">
                Save Preferences
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Container>
    </Box>
  );
};

export default CreditScore;
