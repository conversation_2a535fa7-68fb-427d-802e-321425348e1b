import React, { useState, useEffect } from "react";
import {
  Container,
  Typography,
  Grid,
  Paper,
  Box,
  CircularProgress,
  Button,
  Tabs,
  Tab,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
} from "@mui/material";
import {
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";

// Import services
import {
  getMarketOverview as getMockMarketOverview,
  getAvailableStates as getMockAvailableStates,
  getMandisByState as getMockMandisByState,
  getAvailableCrops as getMockAvailableCrops,
  getMarketData as getMockMarketData,
} from "../../services/marketAnalysisService";

// Import real market data service
import {
  getMarketOverview,
  getAvailableStates,
  getMandisByState,
  getAvailableCrops,
  getMarketData,
} from "../../services/realMarketAnalysisService";

// Import components
import MarketOverview from "./components/MarketOverview";
import MandiPrices from "./components/MandiPrices";
import QualityAnalysis from "./components/QualityAnalysis";
import ExportAnalysis from "./components/ExportAnalysis";
import { useTranslation } from "react-i18next";

const MarketAnalysis = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState(0);

  // Selection states
  const [states, setStates] = useState([]);
  const [selectedState, setSelectedState] = useState("");
  const [mandis, setMandis] = useState([]);
  const [selectedMandi, setSelectedMandi] = useState("");
  const [crops, setCrops] = useState([]);
  const [selectedCrop, setSelectedCrop] = useState("");

  // Data states
  const [overviewData, setOverviewData] = useState(null);
  const [marketData, setMarketData] = useState(null);

  // Fetch initial data
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoading(true);

        // Fetch market overview
        let overview;
        try {
          // Try to get real data first
          overview = await getMarketOverview();
          console.log("Using real market overview data");
        } catch (error) {
          // Fall back to mock data
          console.warn(
            "Failed to fetch real market overview data, using mock data:",
            error
          );
          overview = await getMockMarketOverview();
        }
        setOverviewData(overview);

        // Fetch available states
        let statesData;
        try {
          // Try to get real data first
          statesData = await getAvailableStates();
          console.log("Using real states data");
        } catch (error) {
          // Fall back to mock data
          console.warn(
            "Failed to fetch real states data, using mock data:",
            error
          );
          statesData = await getMockAvailableStates();
        }
        setStates(statesData);

        // Set default state
        if (statesData.length > 0) {
          const defaultState = "Maharashtra";
          setSelectedState(defaultState);

          // Fetch mandis for default state
          let mandisData;
          try {
            // Try to get real data first
            mandisData = await getMandisByState(defaultState);
            console.log(`Using real mandis data for ${defaultState}`);
          } catch (error) {
            // Fall back to mock data
            console.warn(
              `Failed to fetch real mandis data for ${defaultState}, using mock data:`,
              error
            );
            mandisData = await getMockMandisByState(defaultState);
          }
          setMandis(mandisData);

          // Set default mandi
          if (mandisData.length > 0) {
            setSelectedMandi(mandisData[0]);
          }
        }

        // Fetch available crops
        let cropsData;
        try {
          // Try to get real data first
          cropsData = await getAvailableCrops();
          console.log("Using real crops data");
        } catch (error) {
          // Fall back to mock data
          console.warn(
            "Failed to fetch real crops data, using mock data:",
            error
          );
          cropsData = await getMockAvailableCrops();
        }
        setCrops(cropsData);

        // Set default crop
        if (cropsData.length > 0) {
          setSelectedCrop(cropsData[0]);
        }

        setLoading(false);
      } catch (err) {
        console.error("Failed to load initial data:", err);
        setError("Failed to load initial data. Please try again.");
        setLoading(false);
      }
    };

    fetchInitialData();
  }, []);

  // Fetch market data when selection changes
  useEffect(() => {
    if (selectedState && selectedMandi && selectedCrop) {
      fetchMarketData();
    }
  }, [selectedState, selectedMandi, selectedCrop]);

  // Fetch market data
  const fetchMarketData = async () => {
    try {
      setLoading(true);
      setError(null);

      let data;
      try {
        // Try to get real data first
        data = await getMarketData(selectedCrop, selectedState, selectedMandi);
        console.log(
          `Using real market data for ${selectedCrop} in ${selectedMandi}, ${selectedState}`
        );
      } catch (error) {
        // Fall back to mock data
        console.warn(
          `Failed to fetch real market data for ${selectedCrop} in ${selectedMandi}, ${selectedState}, using mock data:`,
          error
        );
        data = await getMockMarketData(
          selectedCrop,
          selectedState,
          selectedMandi
        );
      }

      setMarketData(data);

      setLoading(false);
    } catch (err) {
      console.error("Failed to fetch market data:", err);
      setError("Failed to fetch market data. Please try again.");
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle state change
  const handleStateChange = async (event) => {
    const state = event.target.value;
    setSelectedState(state);

    try {
      // Fetch mandis for selected state
      let mandisData;
      try {
        // Try to get real data first
        mandisData = await getMandisByState(state);
        console.log(`Using real mandis data for ${state}`);
      } catch (error) {
        // Fall back to mock data
        console.warn(
          `Failed to fetch real mandis data for ${state}, using mock data:`,
          error
        );
        mandisData = await getMockMandisByState(state);
      }

      setMandis(mandisData);

      // Set default mandi
      if (mandisData.length > 0) {
        setSelectedMandi(mandisData[0]);
      }
    } catch (err) {
      console.error(`Failed to fetch mandis for ${state}:`, err);
      setError("Failed to fetch mandis. Please try again.");
    }
  };

  // Handle mandi change
  const handleMandiChange = (event) => {
    setSelectedMandi(event.target.value);
  };

  // Handle crop change
  const handleCropChange = (event) => {
    setSelectedCrop(event.target.value);
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchMarketData();
  };

  // Handle back
  const handleBack = () => {
    navigate("/dashboard");
  };

  // Loading state
  if (loading && !overviewData) {
    return (
      <Container>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{
            mt: 2,
            mb: 2,
            bgcolor: "#2e7d32",
            "&:hover": {
              bgcolor: "#1b5e20",
            },
          }}
        >
          {t("back_to_dash_msg")}
        </Button>
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="70vh"
        >
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // Error state
  if (error && !overviewData) {
    return (
      <Container>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{
            mt: 2,
            mb: 2,
            bgcolor: "#2e7d32",
            "&:hover": {
              bgcolor: "#1b5e20",
            },
          }}
        >
          {t("back_to_dash_msg")}
        </Button>
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
        <Button
          variant="outlined"
          onClick={fetchMarketData}
          startIcon={<RefreshIcon />}
          sx={{ mt: 2 }}
        >
          Retry
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{
            mr: 2,
            bgcolor: "#2e7d32",
            "&:hover": {
              bgcolor: "#1b5e20",
            },
          }}
        >
          {t("back_to_dash_msg")}
        </Button>
        <Typography variant="h4" component="h1">
          {t("market_analysis_msg")}
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Selection Controls */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel id="state-select-label">State</InputLabel>
              <Select
                labelId="state-select-label"
                id="state-select"
                value={selectedState}
                label="State"
                onChange={handleStateChange}
              >
                {states.map((state) => (
                  <MenuItem key={state} value={state}>
                    {state}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel id="mandi-select-label">Mandi</InputLabel>
              <Select
                labelId="mandi-select-label"
                id="mandi-select"
                value={selectedMandi}
                label="Mandi"
                onChange={handleMandiChange}
                disabled={!selectedState || mandis.length === 0}
              >
                {mandis.map((mandi) => (
                  <MenuItem key={mandi} value={mandi}>
                    {mandi}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel id="crop-select-label">Crop</InputLabel>
              <Select
                labelId="crop-select-label"
                id="crop-select"
                value={selectedCrop}
                label="Crop"
                onChange={handleCropChange}
              >
                {crops.map((crop) => (
                  <MenuItem key={crop} value={crop}>
                    {crop}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
                disabled={loading}
              >
                {loading ? "Updating..." : "Refresh Data"}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {loading && (
        <Box sx={{ display: "flex", justifyContent: "center", my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Tabs */}
      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          indicatorColor="primary"
          textColor="primary"
          sx={{
            "& .MuiTab-root": {
              fontWeight: "bold",
              py: 2,
            },
          }}
        >
          <Tab label="Market Overview" />
          <Tab label="Mandi Prices" />
          <Tab label="Quality Analysis" />
          <Tab label="Export Analysis" />
        </Tabs>
      </Paper>

      {/* Tab Panels */}
      <Box sx={{ mt: 2 }}>
        {activeTab === 0 && (
          <MarketOverview data={overviewData} loading={loading} />
        )}
        {activeTab === 1 && <MandiPrices data={marketData} loading={loading} />}
        {activeTab === 2 && (
          <QualityAnalysis data={marketData} loading={loading} />
        )}
        {activeTab === 3 && (
          <ExportAnalysis data={marketData} loading={loading} />
        )}
      </Box>
    </Container>
  );
};

export default MarketAnalysis;
