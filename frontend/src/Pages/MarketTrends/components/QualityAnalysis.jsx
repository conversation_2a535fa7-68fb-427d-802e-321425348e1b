import React from 'react';
import {
  Typography,
  Grid,
  Card,
  CardContent,
  Paper,
  Box,
  Divider,
  Skeleton,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon
} from '@mui/icons-material';

const QualityAnalysis = ({ data, loading }) => {
  // If no data or loading, show skeleton
  if (!data && loading) {
    return <QualitySkeleton />;
  }
  
  // If no data and not loading, show error
  if (!data && !loading) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          Please select a state, mandi, and crop to view quality data
        </Typography>
      </Box>
    );
  }
  
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };
  
  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'Excellent':
        return <CheckCircleIcon sx={{ color: 'success.main' }} />;
      case 'Good':
        return <CheckCircleIcon sx={{ color: 'success.main' }} />;
      case 'Average':
        return <WarningIcon sx={{ color: 'warning.main' }} />;
      case 'Poor':
        return <ErrorIcon sx={{ color: 'error.main' }} />;
      default:
        return <InfoIcon sx={{ color: 'info.main' }} />;
    }
  };
  
  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'Excellent':
        return 'success';
      case 'Good':
        return 'success';
      case 'Average':
        return 'warning';
      case 'Poor':
        return 'error';
      default:
        return 'info';
    }
  };
  
  // Get progress color
  const getProgressColor = (score) => {
    if (score >= 90) return 'success.main';
    if (score >= 80) return 'success.main';
    if (score >= 70) return 'warning.main';
    return 'error.main';
  };
  
  // Calculate overall quality score
  const calculateOverallScore = () => {
    if (!data.qualityData || data.qualityData.length === 0) return 0;
    
    const sum = data.qualityData.reduce((acc, item) => acc + item.score, 0);
    return Math.round(sum / data.qualityData.length);
  };
  
  // Get overall status
  const getOverallStatus = (score) => {
    if (score >= 90) return 'Excellent';
    if (score >= 80) return 'Good';
    if (score >= 70) return 'Average';
    return 'Poor';
  };
  
  const overallScore = calculateOverallScore();
  const overallStatus = getOverallStatus(overallScore);
  
  return (
    <Grid container spacing={3}>
      {/* Quality Overview */}
      <Grid item xs={12} md={4}>
        <Card sx={{ height: '100%' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Quality Overview: {data.crop}
            </Typography>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              {data.mandi} Mandi, {data.state}
            </Typography>
            
            <Box sx={{ mt: 4, mb: 2, textAlign: 'center' }}>
              <Box sx={{ position: 'relative', display: 'inline-block' }}>
                <Box
                  sx={{
                    position: 'relative',
                    width: 150,
                    height: 150,
                    borderRadius: '50%',
                    background: `conic-gradient(${getProgressColor(overallScore)} ${overallScore}%, #f5f5f5 0)`,
                    transform: 'rotate(-90deg)',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      width: '70%',
                      height: '70%',
                      borderRadius: '50%',
                      background: 'white'
                    }
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    textAlign: 'center'
                  }}
                >
                  <Typography variant="h3" fontWeight="bold">
                    {overallScore}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    out of 100
                  </Typography>
                </Box>
              </Box>
            </Box>
            
            <Box sx={{ textAlign: 'center', mt: 2 }}>
              <Chip
                label={overallStatus}
                color={getStatusColor(overallStatus)}
                sx={{ fontWeight: 'bold' }}
              />
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Typography variant="subtitle2" gutterBottom>
              Quality Summary
            </Typography>
            
            <Typography variant="body2" paragraph>
              The overall quality of {data.crop} in {data.mandi} mandi is rated as <strong>{overallStatus.toLowerCase()}</strong> with a score of {overallScore} out of 100.
            </Typography>
            
            <Typography variant="body2">
              {overallScore >= 80 
                ? `This ${data.crop} meets high quality standards and is suitable for premium markets and export.` 
                : overallScore >= 70 
                  ? `This ${data.crop} meets standard quality requirements for domestic markets.` 
                  : `This ${data.crop} has some quality issues that may affect market value.`
              }
            </Typography>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="caption" color="text.secondary">
                Last updated: {formatDate(data.lastUpdated)}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>
      
      {/* Quality Parameters */}
      <Grid item xs={12} md={8}>
        <Paper sx={{ p: 3, height: '100%' }}>
          <Typography variant="h6" gutterBottom>
            Quality Parameters
          </Typography>
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Parameter</TableCell>
                  <TableCell>Score</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Rating</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data.qualityData.map((param, index) => (
                  <TableRow key={index}>
                    <TableCell>{param.parameter}</TableCell>
                    <TableCell>{param.score}/100</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {getStatusIcon(param.status)}
                        <Typography variant="body2" sx={{ ml: 1 }}>
                          {param.status}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ width: '100%' }}>
                        <LinearProgress 
                          variant="determinate" 
                          value={param.score} 
                          sx={{ 
                            height: 8, 
                            borderRadius: 5,
                            bgcolor: 'rgba(0,0,0,0.1)',
                            '& .MuiLinearProgress-bar': {
                              bgcolor: getProgressColor(param.score)
                            }
                          }}
                        />
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </Grid>
      
      {/* Quality Analysis */}
      <Grid item xs={12}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Quality Analysis & Recommendations
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Market Impact
                  </Typography>
                  
                  <Typography variant="body2" paragraph>
                    {overallScore >= 90 
                      ? `The excellent quality of this ${data.crop} can command premium prices in both domestic and export markets. Buyers typically pay 10-15% higher prices for such high-quality produce.` 
                      : overallScore >= 80 
                        ? `The good quality of this ${data.crop} is suitable for standard market requirements and can fetch competitive prices. Consider targeting quality-conscious buyers.` 
                        : overallScore >= 70 
                          ? `The average quality of this ${data.crop} meets basic market requirements but may not command premium prices. Focus on improving key parameters for better returns.` 
                          : `The below-average quality of this ${data.crop} may result in lower market prices. Immediate attention to quality improvement is recommended.`
                    }
                  </Typography>
                  
                  <Typography variant="body2">
                    {data.qualityData.some(param => param.score < 70) 
                      ? `Key areas for improvement: ${data.qualityData.filter(param => param.score < 70).map(param => param.parameter).join(', ')}.` 
                      : `All quality parameters are at satisfactory levels, continue maintaining current practices.`
                    }
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Quality Improvement Recommendations
                  </Typography>
                  
                  <Box sx={{ mt: 1 }}>
                    {data.qualityData.filter(param => param.score < 80).length > 0 ? (
                      data.qualityData.filter(param => param.score < 80).map((param, index) => (
                        <Box key={index} sx={{ mb: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <WarningIcon color="warning" sx={{ mr: 1 }} />
                            <Typography variant="body2" fontWeight="medium">
                              {param.parameter} ({param.score}/100)
                            </Typography>
                          </Box>
                          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5, ml: 4 }}>
                            {param.parameter === 'Moisture Content' 
                              ? `Ensure proper drying before storage and transportation. Maintain moisture levels between 12-14% for optimal quality.` 
                              : param.parameter === 'Foreign Matter' 
                                ? `Improve cleaning and sorting processes to remove impurities, stones, and other foreign materials.` 
                                : param.parameter === 'Damaged Grains' 
                                  ? `Improve harvesting techniques and post-harvest handling to minimize physical damage to grains.` 
                                  : param.parameter === 'Broken Grains' 
                                    ? `Adjust milling equipment and handle produce carefully during processing to reduce breakage.` 
                                    : `Implement better quality control measures to improve ${param.parameter.toLowerCase()}.`
                            }
                          </Typography>
                        </Box>
                      ))
                    ) : (
                      <Typography variant="body2" color="success.main">
                        All quality parameters are at good or excellent levels. Continue maintaining current practices.
                      </Typography>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Paper>
      </Grid>
    </Grid>
  );
};

// Skeleton for loading state
const QualitySkeleton = () => {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={4}>
        <Card sx={{ height: '100%' }}>
          <CardContent>
            <Skeleton variant="text" width="60%" height={32} />
            <Skeleton variant="text" width="40%" height={24} sx={{ mb: 3 }} />
            
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <Skeleton variant="circular" width={150} height={150} />
            </Box>
            
            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
              <Skeleton variant="rectangular" width={100} height={32} sx={{ borderRadius: 16 }} />
            </Box>
            
            <Divider sx={{ my: 2 }} />
            
            <Skeleton variant="text" width="40%" height={24} />
            <Skeleton variant="text" width="100%" height={20} sx={{ mt: 1 }} />
            <Skeleton variant="text" width="100%" height={20} sx={{ mt: 1 }} />
            <Skeleton variant="text" width="100%" height={20} sx={{ mt: 1 }} />
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} md={8}>
        <Paper sx={{ p: 3, height: '100%' }}>
          <Skeleton variant="text" width="40%" height={32} />
          <Skeleton variant="rectangular" width="100%" height={250} sx={{ mt: 2 }} />
        </Paper>
      </Grid>
      
      <Grid item xs={12}>
        <Paper sx={{ p: 3 }}>
          <Skeleton variant="text" width="50%" height={32} />
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <Skeleton variant="rectangular" width="100%" height={200} />
            </Grid>
            <Grid item xs={12} md={6}>
              <Skeleton variant="rectangular" width="100%" height={200} />
            </Grid>
          </Grid>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default QualityAnalysis;
