import React from 'react';
import {
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Paper,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Divider,
  Skeleton,
  LinearProgress
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Info as InfoIcon
} from '@mui/icons-material';

const MandiPrices = ({ data, loading }) => {
  // If no data or loading, show skeleton
  if (!data && loading) {
    return <PricesSkeleton />;
  }
  
  // If no data and not loading, show error
  if (!data && !loading) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          Please select a state, mandi, and crop to view price data
        </Typography>
      </Box>
    );
  }
  
  // Get trend icon and color
  const getTrendIcon = (value) => {
    const numValue = parseFloat(value);
    if (numValue > 0) {
      return <TrendingUpIcon sx={{ color: 'success.main' }} />;
    } else if (numValue < 0) {
      return <TrendingDownIcon sx={{ color: 'error.main' }} />;
    }
    return <TrendingFlatIcon sx={{ color: 'info.main' }} />;
  };
  
  // Get trend color
  const getTrendColor = (value) => {
    const numValue = parseFloat(value);
    if (numValue > 0) return 'success';
    if (numValue < 0) return 'error';
    return 'info';
  };
  
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };
  
  // Format number with commas
  const formatNumber = (num) => {
    return new Intl.NumberFormat('en-IN').format(num);
  };
  
  return (
    <Grid container spacing={3}>
      {/* Price Overview */}
      <Grid item xs={12} md={6}>
        <Card sx={{ height: '100%' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Price Overview: {data.crop}
            </Typography>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              {data.mandi} Mandi, {data.state}
            </Typography>
            
            <Box sx={{ mt: 3, mb: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Current Price:
                  </Typography>
                  <Typography variant="h4" color="primary" sx={{ mt: 1 }}>
                    ₹{data.priceData.currentPrice}
                    <Typography component="span" variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                      per {data.priceData.unit}
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Previous Price:
                  </Typography>
                  <Typography variant="h5" color="text.secondary" sx={{ mt: 1 }}>
                    ₹{data.priceData.previousPrice}
                    <Typography component="span" variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                      per {data.priceData.unit}
                    </Typography>
                  </Typography>
                </Grid>
              </Grid>
            </Box>
            
            <Divider sx={{ my: 2 }} />
            
            <Typography variant="subtitle2" gutterBottom>
              Price Trends
            </Typography>
            
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Weekly
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      {getTrendIcon(data.priceData.weeklyChange)}
                      <Typography 
                        variant="body1" 
                        fontWeight="bold"
                        color={parseFloat(data.priceData.weeklyChange) > 0 ? 'success.main' : parseFloat(data.priceData.weeklyChange) < 0 ? 'error.main' : 'info.main'}
                        sx={{ ml: 0.5 }}
                      >
                        {parseFloat(data.priceData.weeklyChange) > 0 ? '+' : ''}{data.priceData.weeklyChange}%
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Monthly
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      {getTrendIcon(data.priceData.monthlyChange)}
                      <Typography 
                        variant="body1" 
                        fontWeight="bold"
                        color={parseFloat(data.priceData.monthlyChange) > 0 ? 'success.main' : parseFloat(data.priceData.monthlyChange) < 0 ? 'error.main' : 'info.main'}
                        sx={{ ml: 0.5 }}
                      >
                        {parseFloat(data.priceData.monthlyChange) > 0 ? '+' : ''}{data.priceData.monthlyChange}%
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Yearly
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      {getTrendIcon(data.priceData.yearlyChange)}
                      <Typography 
                        variant="body1" 
                        fontWeight="bold"
                        color={parseFloat(data.priceData.yearlyChange) > 0 ? 'success.main' : parseFloat(data.priceData.yearlyChange) < 0 ? 'error.main' : 'info.main'}
                        sx={{ ml: 0.5 }}
                      >
                        {parseFloat(data.priceData.yearlyChange) > 0 ? '+' : ''}{data.priceData.yearlyChange}%
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Box>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="caption" color="text.secondary">
                Last updated: {formatDate(data.lastUpdated)}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>
      
      {/* MSP and Arrival Data */}
      <Grid item xs={12} md={6}>
        <Card sx={{ height: '100%' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              MSP & Arrival Data
            </Typography>
            
            {/* MSP Data */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Minimum Support Price (MSP)
              </Typography>
              
              {data.mspData.available ? (
                <Box>
                  <Grid container spacing={2} sx={{ mt: 1 }}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Current MSP:
                      </Typography>
                      <Typography variant="h5" color="primary" sx={{ mt: 0.5 }}>
                        ₹{data.mspData.currentMSP}
                        <Typography component="span" variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                          per {data.priceData.unit}
                        </Typography>
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Previous MSP:
                      </Typography>
                      <Typography variant="body1" color="text.secondary" sx={{ mt: 0.5 }}>
                        ₹{data.mspData.previousMSP}
                        <Typography component="span" variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                          per {data.priceData.unit}
                        </Typography>
                      </Typography>
                    </Grid>
                  </Grid>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                    <Chip 
                      label={`${parseFloat(data.mspData.change) > 0 ? '+' : ''}${data.mspData.change}% from previous year`}
                      color={parseFloat(data.mspData.change) > 0 ? 'success' : 'error'}
                      size="small"
                    />
                    <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                      Effective from: {formatDate(data.mspData.effectiveFrom)}
                    </Typography>
                  </Box>
                </Box>
              ) : (
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    {data.mspData.message}
                  </Typography>
                </Box>
              )}
            </Box>
            
            <Divider sx={{ my: 2 }} />
            
            {/* Arrival Data */}
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Arrival Data (in quintals)
              </Typography>
              
              <TableContainer component={Paper} elevation={0} sx={{ mt: 2 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Period</TableCell>
                      <TableCell align="right">Quantity</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <TableRow>
                      <TableCell>Today</TableCell>
                      <TableCell align="right">{formatNumber(data.arrivalData.today)}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Yesterday</TableCell>
                      <TableCell align="right">{formatNumber(data.arrivalData.yesterday)}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Last Week</TableCell>
                      <TableCell align="right">{formatNumber(data.arrivalData.lastWeek)}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Last Month</TableCell>
                      <TableCell align="right">{formatNumber(data.arrivalData.lastMonth)}</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          </CardContent>
        </Card>
      </Grid>
      
      {/* Price Comparison */}
      <Grid item xs={12}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Price Analysis
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Market Price vs MSP
                </Typography>
                
                {data.mspData.available ? (
                  <Box sx={{ mt: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Market Price</Typography>
                      <Typography variant="body2">₹{data.priceData.currentPrice}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">MSP</Typography>
                      <Typography variant="body2">₹{data.mspData.currentMSP}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Difference</Typography>
                      <Typography 
                        variant="body2"
                        color={(data.priceData.currentPrice - data.mspData.currentMSP) > 0 ? 'success.main' : 'error.main'}
                      >
                        ₹{data.priceData.currentPrice - data.mspData.currentMSP}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Percentage</Typography>
                      <Typography 
                        variant="body2"
                        color={(data.priceData.currentPrice - data.mspData.currentMSP) > 0 ? 'success.main' : 'error.main'}
                      >
                        {(((data.priceData.currentPrice - data.mspData.currentMSP) / data.mspData.currentMSP) * 100).toFixed(1)}%
                      </Typography>
                    </Box>
                    
                    <Box sx={{ mt: 2, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Market Price vs MSP
                      </Typography>
                      <LinearProgress 
                        variant="determinate" 
                        value={Math.min(100, (data.priceData.currentPrice / data.mspData.currentMSP) * 100)}
                        sx={{ 
                          height: 10, 
                          borderRadius: 5,
                          mt: 1,
                          bgcolor: 'rgba(0,0,0,0.1)',
                          '& .MuiLinearProgress-bar': {
                            bgcolor: data.priceData.currentPrice > data.mspData.currentMSP ? 'success.main' : 'error.main'
                          }
                        }}
                      />
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                      {data.priceData.currentPrice > data.mspData.currentMSP 
                        ? `Current market price is ${(((data.priceData.currentPrice - data.mspData.currentMSP) / data.mspData.currentMSP) * 100).toFixed(1)}% higher than MSP, indicating good market conditions.`
                        : `Current market price is ${(((data.mspData.currentMSP - data.priceData.currentPrice) / data.mspData.currentMSP) * 100).toFixed(1)}% lower than MSP, consider selling to government procurement agencies.`
                      }
                    </Typography>
                  </Box>
                ) : (
                  <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      MSP comparison not available for this crop
                    </Typography>
                  </Box>
                )}
              </Box>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Price Recommendation
                </Typography>
                
                <Box sx={{ p: 2, bgcolor: 'rgba(46, 125, 50, 0.1)', borderRadius: 2, mt: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                    <InfoIcon color="primary" sx={{ mr: 1, mt: 0.5 }} />
                    <Box>
                      <Typography variant="body1" fontWeight="medium" gutterBottom>
                        {data.priceData.weeklyChange > 0 
                          ? "Price is trending upward" 
                          : data.priceData.weeklyChange < 0 
                            ? "Price is trending downward" 
                            : "Price is stable"
                        }
                      </Typography>
                      
                      <Typography variant="body2" paragraph>
                        {data.priceData.weeklyChange > 3 
                          ? `The price of ${data.crop} has increased by ${data.priceData.weeklyChange}% in the last week. Consider holding your produce for a short period if storage is available.` 
                          : data.priceData.weeklyChange < -3 
                            ? `The price of ${data.crop} has decreased by ${Math.abs(data.priceData.weeklyChange)}% in the last week. If you need to sell immediately, consider exploring other nearby mandis or wait if possible.`
                            : `The price of ${data.crop} has been relatively stable (${data.priceData.weeklyChange}%) over the last week. Current market conditions are favorable for regular trading.`
                        }
                      </Typography>
                      
                      <Typography variant="body2">
                        {data.mspData.available && data.priceData.currentPrice < data.mspData.currentMSP
                          ? `Since the market price (₹${data.priceData.currentPrice}) is below MSP (₹${data.mspData.currentMSP}), consider selling to government procurement agencies.`
                          : `Current market price is ₹${data.priceData.currentPrice} per ${data.priceData.unit}, which is a good price based on historical trends.`
                        }
                      </Typography>
                    </Box>
                  </Box>
                </Box>
                
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Best Time to Sell
                  </Typography>
                  
                  <Typography variant="body2" paragraph>
                    Based on historical data and current trends, the best time to sell {data.crop} in {data.mandi} mandi is typically during the 
                    {data.priceData.monthlyChange > 0 ? " current month" : " next month"} when prices tend to be higher due to 
                    {data.priceData.monthlyChange > 0 ? " increasing demand" : " seasonal factors"}.
                  </Typography>
                  
                  <Typography variant="body2">
                    Monitor price trends closely and consider selling when the price reaches ₹{Math.round(data.priceData.currentPrice * 1.05)} per {data.priceData.unit} or higher.
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Grid>
    </Grid>
  );
};

// Skeleton for loading state
const PricesSkeleton = () => {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card sx={{ height: '100%' }}>
          <CardContent>
            <Skeleton variant="text" width="60%" height={32} />
            <Skeleton variant="text" width="40%" height={24} sx={{ mb: 3 }} />
            
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Skeleton variant="text" width="80%" height={24} />
                <Skeleton variant="text" width="100%" height={40} sx={{ mt: 1 }} />
              </Grid>
              <Grid item xs={6}>
                <Skeleton variant="text" width="80%" height={24} />
                <Skeleton variant="text" width="100%" height={40} sx={{ mt: 1 }} />
              </Grid>
            </Grid>
            
            <Divider sx={{ my: 2 }} />
            
            <Skeleton variant="text" width="40%" height={24} />
            <Skeleton variant="rectangular" width="100%" height={80} sx={{ mt: 2 }} />
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <Card sx={{ height: '100%' }}>
          <CardContent>
            <Skeleton variant="text" width="60%" height={32} />
            <Skeleton variant="text" width="40%" height={24} sx={{ mb: 1 }} />
            <Skeleton variant="rectangular" width="100%" height={120} sx={{ mt: 2 }} />
            
            <Divider sx={{ my: 2 }} />
            
            <Skeleton variant="text" width="50%" height={24} />
            <Skeleton variant="rectangular" width="100%" height={120} sx={{ mt: 2 }} />
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12}>
        <Paper sx={{ p: 3 }}>
          <Skeleton variant="text" width="30%" height={32} />
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <Skeleton variant="rectangular" width="100%" height={200} />
            </Grid>
            <Grid item xs={12} md={6}>
              <Skeleton variant="rectangular" width="100%" height={200} />
            </Grid>
          </Grid>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default MandiPrices;
