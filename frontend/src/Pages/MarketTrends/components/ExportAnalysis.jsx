import React from 'react';
import {
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Paper,
  Box,
  Divider,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Public as PublicIcon,
  LocalShipping as LocalShippingIcon,
  VerifiedUser as VerifiedUserIcon,
  AttachMoney as AttachMoneyIcon
} from '@mui/icons-material';

const ExportAnalysis = ({ data, loading }) => {
  // If no data or loading, show skeleton
  if (!data && loading) {
    return <ExportSkeleton />;
  }
  
  // If no data and not loading, show error
  if (!data && !loading) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          Please select a state, mandi, and crop to view export data
        </Typography>
      </Box>
    );
  }
  
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };
  
  // Format number with commas
  const formatNumber = (num) => {
    return new Intl.NumberFormat('en-IN').format(num);
  };
  
  // Get trend icon and color
  const getTrendIcon = (value) => {
    const numValue = parseFloat(value);
    if (numValue > 0) {
      return <TrendingUpIcon sx={{ color: 'success.main' }} />;
    } else if (numValue < 0) {
      return <TrendingDownIcon sx={{ color: 'error.main' }} />;
    }
    return <TrendingFlatIcon sx={{ color: 'info.main' }} />;
  };
  
  // Get trend color
  const getTrendColor = (value) => {
    const numValue = parseFloat(value);
    if (numValue > 0) return 'success';
    if (numValue < 0) return 'error';
    return 'info';
  };
  
  return (
    <Grid container spacing={3}>
      {/* Export Overview */}
      <Grid item xs={12} md={4}>
        <Card sx={{ height: '100%' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Export Overview: {data.crop}
            </Typography>
            
            <Box sx={{ mt: 3, mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Total Export Volume
              </Typography>
              <Typography variant="h4" color="primary" sx={{ mt: 1 }}>
                {formatNumber(data.exportData.totalExport)}
                <Typography component="span" variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  {data.exportData.unit}
                </Typography>
              </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {getTrendIcon(data.exportData.yearOnYearGrowth)}
                <Typography 
                  variant="body2" 
                  color={parseFloat(data.exportData.yearOnYearGrowth) > 0 ? 'success.main' : parseFloat(data.exportData.yearOnYearGrowth) < 0 ? 'error.main' : 'info.main'}
                  sx={{ ml: 0.5 }}
                >
                  {parseFloat(data.exportData.yearOnYearGrowth) > 0 ? '+' : ''}{data.exportData.yearOnYearGrowth}% year-on-year
                </Typography>
              </Box>
            </Box>
            
            <Divider sx={{ my: 2 }} />
            
            <Typography variant="subtitle2" gutterBottom>
              Export Requirements
            </Typography>
            
            <List dense>
              <ListItem>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <VerifiedUserIcon fontSize="small" color="primary" />
                </ListItemIcon>
                <ListItemText 
                  primary="Quality Certification" 
                  secondary="AGMARK, FSSAI, Organic (if applicable)"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <LocalShippingIcon fontSize="small" color="primary" />
                </ListItemIcon>
                <ListItemText 
                  primary="Packaging Requirements" 
                  secondary="Food-grade, moisture-resistant packaging"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <AttachMoneyIcon fontSize="small" color="primary" />
                </ListItemIcon>
                <ListItemText 
                  primary="Export Incentives" 
                  secondary="MEIS, duty drawback schemes available"
                />
              </ListItem>
            </List>
            
            <Box sx={{ mt: 2 }}>
              <Typography variant="caption" color="text.secondary">
                Last updated: {formatDate(data.lastUpdated)}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>
      
      {/* Export Destinations */}
      <Grid item xs={12} md={8}>
        <Paper sx={{ p: 3, height: '100%' }}>
          <Typography variant="h6" gutterBottom>
            Top Export Destinations
          </Typography>
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Country</TableCell>
                  <TableCell align="right">Volume ({data.exportData.unit})</TableCell>
                  <TableCell align="right">Share (%)</TableCell>
                  <TableCell align="right">Growth</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data.exportData.destinations.map((destination, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <PublicIcon sx={{ mr: 1, color: 'primary.main', fontSize: 20 }} />
                        {destination.country}
                      </Box>
                    </TableCell>
                    <TableCell align="right">{formatNumber(destination.volume)}</TableCell>
                    <TableCell align="right">
                      {((destination.volume / data.exportData.totalExport) * 100).toFixed(1)}%
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                        {getTrendIcon(destination.growth)}
                        <Chip
                          label={`${parseFloat(destination.growth) > 0 ? '+' : ''}${destination.growth}%`}
                          size="small"
                          color={getTrendColor(destination.growth)}
                          sx={{ ml: 1 }}
                        />
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </Grid>
      
      {/* Export Analysis */}
      <Grid item xs={12}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Export Market Analysis
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Market Opportunities
                  </Typography>
                  
                  <Typography variant="body2" paragraph>
                    {parseFloat(data.exportData.yearOnYearGrowth) > 10 
                      ? `The export market for ${data.crop} is growing rapidly with a ${data.exportData.yearOnYearGrowth}% increase year-on-year. This presents significant opportunities for farmers and exporters.` 
                      : parseFloat(data.exportData.yearOnYearGrowth) > 0 
                        ? `The export market for ${data.crop} is showing steady growth with a ${data.exportData.yearOnYearGrowth}% increase year-on-year. This indicates stable international demand.` 
                        : `The export market for ${data.crop} has seen a ${Math.abs(parseFloat(data.exportData.yearOnYearGrowth))}% decline year-on-year. Farmers should focus on quality improvement and exploring new markets.`
                    }
                  </Typography>
                  
                  <Typography variant="body2">
                    {data.exportData.destinations.some(dest => parseFloat(dest.growth) > 15) 
                      ? `Emerging markets with high growth potential: ${data.exportData.destinations.filter(dest => parseFloat(dest.growth) > 15).map(dest => dest.country).join(', ')}.` 
                      : `Focus on established markets with stable demand patterns for reliable export opportunities.`
                    }
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Export Recommendations
                  </Typography>
                  
                  <List>
                    <ListItem>
                      <ListItemIcon>
                        <VerifiedUserIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Quality Standards" 
                        secondary={`Ensure ${data.crop} meets international quality standards. Focus on parameters like size uniformity, color, and moisture content.`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <LocalShippingIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Packaging & Logistics" 
                        secondary="Use export-grade packaging that preserves quality during transportation. Consider temperature-controlled logistics for perishable items."
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <PublicIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Market Diversification" 
                        secondary={`Consider exploring ${data.exportData.destinations.filter(dest => parseFloat(dest.growth) > 0).length > 0 ? data.exportData.destinations.filter(dest => parseFloat(dest.growth) > 0).map(dest => dest.country).join(', ') : 'new markets'} for better price realization.`}
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
          
          <Box sx={{ mt: 3, p: 2, bgcolor: 'rgba(46, 125, 50, 0.1)', borderRadius: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Export Potential Assessment
            </Typography>
            
            <Typography variant="body2" paragraph>
              {parseFloat(data.exportData.yearOnYearGrowth) > 5 
                ? `${data.crop} from ${data.state} has high export potential with growing international demand. Farmers producing high-quality ${data.crop} can benefit from premium export prices.` 
                : parseFloat(data.exportData.yearOnYearGrowth) > -5 
                  ? `${data.crop} from ${data.state} has moderate export potential with stable international demand. Focus on quality improvement to access premium export markets.` 
                  : `${data.crop} from ${data.state} currently faces challenges in export markets. Consider focusing on domestic markets or improving quality to meet international standards.`
              }
            </Typography>
            
            <Typography variant="body2">
              For export assistance, contact Agricultural and Processed Food Products Export Development Authority (APEDA) or your state agricultural export promotion agency.
            </Typography>
          </Box>
        </Paper>
      </Grid>
    </Grid>
  );
};

// Skeleton for loading state
const ExportSkeleton = () => {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={4}>
        <Card sx={{ height: '100%' }}>
          <CardContent>
            <Skeleton variant="text" width="60%" height={32} />
            
            <Box sx={{ mt: 3, mb: 2 }}>
              <Skeleton variant="text" width="40%" height={24} />
              <Skeleton variant="text" width="70%" height={40} sx={{ mt: 1 }} />
              <Skeleton variant="text" width="50%" height={24} sx={{ mt: 1 }} />
            </Box>
            
            <Divider sx={{ my: 2 }} />
            
            <Skeleton variant="text" width="50%" height={24} />
            <Skeleton variant="rectangular" width="100%" height={120} sx={{ mt: 1 }} />
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} md={8}>
        <Paper sx={{ p: 3, height: '100%' }}>
          <Skeleton variant="text" width="40%" height={32} />
          <Skeleton variant="rectangular" width="100%" height={250} sx={{ mt: 2 }} />
        </Paper>
      </Grid>
      
      <Grid item xs={12}>
        <Paper sx={{ p: 3 }}>
          <Skeleton variant="text" width="30%" height={32} />
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <Skeleton variant="rectangular" width="100%" height={180} />
            </Grid>
            <Grid item xs={12} md={6}>
              <Skeleton variant="rectangular" width="100%" height={180} />
            </Grid>
          </Grid>
          <Skeleton variant="rectangular" width="100%" height={100} sx={{ mt: 3 }} />
        </Paper>
      </Grid>
    </Grid>
  );
};

export default ExportAnalysis;
