import React from 'react';
import {
  Typography,
  Grid,
  Card,
  CardContent,
  Paper,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Skeleton
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Newspaper as NewspaperIcon
} from '@mui/icons-material';

const MarketOverview = ({ data, loading }) => {
  // If no data or loading, show skeleton
  if (!data && loading) {
    return <OverviewSkeleton />;
  }
  
  // If no data and not loading, show error
  if (!data && !loading) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          Market overview data not available
        </Typography>
      </Box>
    );
  }
  
  // Get trend icon and color
  const getTrendIcon = (value) => {
    const numValue = parseFloat(value);
    if (numValue > 0) {
      return <TrendingUpIcon sx={{ color: 'success.main' }} />;
    } else if (numValue < 0) {
      return <TrendingDownIcon sx={{ color: 'error.main' }} />;
    }
    return <TrendingFlatIcon sx={{ color: 'info.main' }} />;
  };
  
  // Get trend color
  const getTrendColor = (value) => {
    const numValue = parseFloat(value);
    if (numValue > 0) return 'success';
    if (numValue < 0) return 'error';
    return 'info';
  };
  
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };
  
  return (
    <Grid container spacing={3}>
      {/* Market Trends */}
      <Grid item xs={12} md={4}>
        <Card sx={{ height: '100%' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Market Trends
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Weekly Change:
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {getTrendIcon(data.trends.weeklyChange)}
                <Typography 
                  variant="body2" 
                  color={parseFloat(data.trends.weeklyChange) > 0 ? 'success.main' : parseFloat(data.trends.weeklyChange) < 0 ? 'error.main' : 'info.main'}
                  sx={{ ml: 0.5 }}
                >
                  {parseFloat(data.trends.weeklyChange) > 0 ? '+' : ''}{data.trends.weeklyChange}%
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Monthly Change:
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {getTrendIcon(data.trends.monthlyChange)}
                <Typography 
                  variant="body2" 
                  color={parseFloat(data.trends.monthlyChange) > 0 ? 'success.main' : parseFloat(data.trends.monthlyChange) < 0 ? 'error.main' : 'info.main'}
                  sx={{ ml: 0.5 }}
                >
                  {parseFloat(data.trends.monthlyChange) > 0 ? '+' : ''}{data.trends.monthlyChange}%
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2" color="text.secondary">
                Yearly Change:
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {getTrendIcon(data.trends.yearlyChange)}
                <Typography 
                  variant="body2" 
                  color={parseFloat(data.trends.yearlyChange) > 0 ? 'success.main' : parseFloat(data.trends.yearlyChange) < 0 ? 'error.main' : 'info.main'}
                  sx={{ ml: 0.5 }}
                >
                  {parseFloat(data.trends.yearlyChange) > 0 ? '+' : ''}{data.trends.yearlyChange}%
                </Typography>
              </Box>
            </Box>
            
            <Divider sx={{ my: 2 }} />
            
            <Typography variant="subtitle2" gutterBottom>
              Market News
            </Typography>
            <List dense sx={{ mt: 1 }}>
              {data.news.map((item, index) => (
                <ListItem key={index} alignItems="flex-start" sx={{ px: 0 }}>
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <NewspaperIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary={item.title}
                    secondary={
                      <>
                        <Typography component="span" variant="caption" color="text.secondary">
                          {formatDate(item.date)} • {item.source}
                        </Typography>
                      </>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      </Grid>

      {/* Crop Prices */}
      <Grid item xs={12} md={8}>
        <Paper sx={{ p: 2, height: '100%' }}>
          <Typography variant="h6" gutterBottom>
            Top Crop Prices
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Crop</TableCell>
                  <TableCell align="right">Current Price</TableCell>
                  <TableCell align="right">Previous Price</TableCell>
                  <TableCell align="right">Weekly Change</TableCell>
                  <TableCell align="right">Monthly Change</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data.crops.map((crop) => (
                  <TableRow key={crop.name}>
                    <TableCell component="th" scope="row">
                      {crop.name}
                    </TableCell>
                    <TableCell align="right">₹{crop.currentPrice}/{crop.unit}</TableCell>
                    <TableCell align="right">₹{crop.previousPrice}/{crop.unit}</TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                        {getTrendIcon(crop.weeklyChange)}
                        <Chip
                          label={`${parseFloat(crop.weeklyChange) > 0 ? '+' : ''}${crop.weeklyChange}%`}
                          size="small"
                          color={getTrendColor(crop.weeklyChange)}
                          sx={{ ml: 1 }}
                        />
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                        {getTrendIcon(crop.monthlyChange)}
                        <Chip
                          label={`${parseFloat(crop.monthlyChange) > 0 ? '+' : ''}${crop.monthlyChange}%`}
                          size="small"
                          color={getTrendColor(crop.monthlyChange)}
                          sx={{ ml: 1 }}
                        />
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Typography variant="caption" color="text.secondary">
              Last updated: {formatDate(data.lastUpdated)}
            </Typography>
          </Box>
        </Paper>
      </Grid>
      
      {/* Market Analysis */}
      <Grid item xs={12}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Market Analysis
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Supply & Demand
                  </Typography>
                  <Typography variant="body2" paragraph>
                    The current market shows a balanced supply and demand for most crops. Wheat and rice prices have stabilized after the recent harvest season.
                  </Typography>
                  <Typography variant="body2">
                    Farmers are advised to monitor market trends closely as the monsoon season approaches, which may affect crop yields and prices.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Price Forecast
                  </Typography>
                  <Typography variant="body2" paragraph>
                    Based on current trends, prices for staple crops are expected to remain stable in the short term. However, specialty crops may see price increases due to growing export demand.
                  </Typography>
                  <Typography variant="body2">
                    Government MSP (Minimum Support Price) announcements are expected in the coming weeks, which may influence market dynamics.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Paper>
      </Grid>
    </Grid>
  );
};

// Skeleton for loading state
const OverviewSkeleton = () => {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={4}>
        <Card sx={{ height: '100%' }}>
          <CardContent>
            <Skeleton variant="text" width="60%" height={32} />
            <Skeleton variant="text" width="100%" height={24} sx={{ my: 1 }} />
            <Skeleton variant="text" width="100%" height={24} sx={{ my: 1 }} />
            <Skeleton variant="text" width="100%" height={24} sx={{ my: 1 }} />
            <Divider sx={{ my: 2 }} />
            <Skeleton variant="text" width="40%" height={24} />
            <Skeleton variant="rectangular" width="100%" height={120} sx={{ mt: 1 }} />
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={8}>
        <Paper sx={{ p: 2, height: '100%' }}>
          <Skeleton variant="text" width="40%" height={32} />
          <Skeleton variant="rectangular" width="100%" height={250} sx={{ mt: 2 }} />
        </Paper>
      </Grid>
      <Grid item xs={12}>
        <Paper sx={{ p: 3 }}>
          <Skeleton variant="text" width="30%" height={32} />
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <Skeleton variant="rectangular" width="100%" height={150} />
            </Grid>
            <Grid item xs={12} md={6}>
              <Skeleton variant="rectangular" width="100%" height={150} />
            </Grid>
          </Grid>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default MarketOverview;
