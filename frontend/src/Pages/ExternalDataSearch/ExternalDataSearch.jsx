import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Grid,
  Box,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  CardActions,
  Divider,
  CircularProgress,
  Alert,
  Chip,
  Pagination,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Search as SearchIcon,
  ArrowBack as ArrowBackIcon,
  FilterList as FilterListIcon,
  Clear as ClearIcon,
  OpenInNew as OpenInNewIcon,
  LocationOn as LocationIcon,
  Grass as CropIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { 
  searchAgriculturalData, 
  getAvailableStates, 
  getAvailableCrops 
} from '../../services/azureSearchService';

const ExternalDataSearch = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedState, setSelectedState] = useState('');
  const [selectedCrop, setSelectedCrop] = useState('');
  const [states, setStates] = useState([]);
  const [crops, setCrops] = useState([]);
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showFilters, setShowFilters] = useState(false);
  const [page, setPage] = useState(1);
  const [resultsPerPage] = useState(5);

  // Fetch available states and crops
  useEffect(() => {
    const fetchFilters = async () => {
      try {
        const [statesData, cropsData] = await Promise.all([
          getAvailableStates(),
          getAvailableCrops()
        ]);
        
        setStates(statesData);
        setCrops(cropsData);
      } catch (err) {
        console.error('Error fetching filters:', err);
        setError('Failed to load filter options. Please try again.');
      }
    };
    
    fetchFilters();
  }, []);

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setError('Please enter a search query');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      const results = await searchAgriculturalData(
        searchQuery,
        selectedState || null,
        selectedCrop || null,
        20 // Fetch more results for pagination
      );
      
      setSearchResults(results);
      setPage(1);
      setLoading(false);
    } catch (err) {
      console.error('Error searching data:', err);
      setError('Failed to search. Please try again.');
      setLoading(false);
    }
  };

  const handleClearFilters = () => {
    setSelectedState('');
    setSelectedCrop('');
  };

  const handleBack = () => {
    navigate('/dashboard');
  };

  const handlePageChange = (event, value) => {
    setPage(value);
  };

  // Get current page results
  const getCurrentResults = () => {
    const startIndex = (page - 1) * resultsPerPage;
    const endIndex = startIndex + resultsPerPage;
    return searchResults.slice(startIndex, endIndex);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'N/A';
    
    return date.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Truncate text
  const truncateText = (text, maxLength = 200) => {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{
            mr: 2,
            bgcolor: '#2e7d32',
            '&:hover': {
              bgcolor: '#1b5e20'
            }
          }}
        >
          Back to Dashboard
        </Button>
        <Typography variant="h4" component="h1">
          Quamin AI Search
        </Typography>
      </Box>

      {/* Search Bar */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <TextField
                fullWidth
                label="Search agricultural data"
                variant="outlined"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                placeholder="E.g., organic farming techniques, pest control for rice, soil health management"
                InputProps={{
                  endAdornment: (
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleSearch}
                      disabled={loading}
                      startIcon={<SearchIcon />}
                      sx={{
                        bgcolor: '#2e7d32',
                        '&:hover': {
                          bgcolor: '#1b5e20'
                        }
                      }}
                    >
                      Search
                    </Button>
                  )
                }}
              />
              <Tooltip title="Toggle Filters">
                <IconButton 
                  onClick={() => setShowFilters(!showFilters)}
                  sx={{ ml: 1 }}
                  color={showFilters ? 'primary' : 'default'}
                >
                  <FilterListIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>

          {showFilters && (
            <Grid item xs={12}>
              <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Filter Results
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={5}>
                    <FormControl fullWidth>
                      <InputLabel id="state-select-label">State</InputLabel>
                      <Select
                        labelId="state-select-label"
                        id="state-select"
                        value={selectedState}
                        label="State"
                        onChange={(e) => setSelectedState(e.target.value)}
                      >
                        <MenuItem value="">All States</MenuItem>
                        {states.map((state) => (
                          <MenuItem key={state} value={state}>{state}</MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={5}>
                    <FormControl fullWidth>
                      <InputLabel id="crop-select-label">Crop</InputLabel>
                      <Select
                        labelId="crop-select-label"
                        id="crop-select"
                        value={selectedCrop}
                        label="Crop"
                        onChange={(e) => setSelectedCrop(e.target.value)}
                      >
                        <MenuItem value="">All Crops</MenuItem>
                        {crops.map((crop) => (
                          <MenuItem key={crop} value={crop}>{crop}</MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <Button
                      fullWidth
                      variant="outlined"
                      onClick={handleClearFilters}
                      startIcon={<ClearIcon />}
                      sx={{ height: '100%' }}
                    >
                      Clear
                    </Button>
                  </Grid>
                </Grid>
              </Box>
            </Grid>
          )}
        </Grid>
      </Paper>

      {/* Loading State */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Error Message */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Search Results */}
      {!loading && searchResults.length > 0 && (
        <>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              {searchResults.length} results found
            </Typography>
            {(selectedState || selectedCrop) && (
              <Box>
                {selectedState && (
                  <Chip
                    icon={<LocationIcon />}
                    label={selectedState}
                    onDelete={() => setSelectedState('')}
                    sx={{ mr: 1 }}
                  />
                )}
                {selectedCrop && (
                  <Chip
                    icon={<CropIcon />}
                    label={selectedCrop}
                    onDelete={() => setSelectedCrop('')}
                  />
                )}
              </Box>
            )}
          </Box>

          {getCurrentResults().map((result, index) => (
            <Card key={index} sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {result.title}
                </Typography>
                
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {result.state && (
                    <Chip
                      size="small"
                      icon={<LocationIcon />}
                      label={result.state}
                      variant="outlined"
                    />
                  )}
                  {result.crop && (
                    <Chip
                      size="small"
                      icon={<CropIcon />}
                      label={result.crop}
                      variant="outlined"
                    />
                  )}
                  {result.source && (
                    <Chip
                      size="small"
                      label={result.source}
                      variant="outlined"
                    />
                  )}
                  {result.publishedDate && (
                    <Chip
                      size="small"
                      label={formatDate(result.publishedDate)}
                      variant="outlined"
                    />
                  )}
                </Box>
                
                <Typography variant="body1" paragraph>
                  {truncateText(result.description || result.content)}
                </Typography>
              </CardContent>
              
              <Divider />
              
              <CardActions>
                <Button
                  size="small"
                  endIcon={<OpenInNewIcon />}
                  href={result.url}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  View Source
                </Button>
              </CardActions>
            </Card>
          ))}

          {/* Pagination */}
          {searchResults.length > resultsPerPage && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={Math.ceil(searchResults.length / resultsPerPage)}
                page={page}
                onChange={handlePageChange}
                color="primary"
              />
            </Box>
          )}
        </>
      )}

      {/* No Results */}
      {!loading && searchQuery && searchResults.length === 0 && (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            No results found
          </Typography>
          <Typography variant="body1">
            Try adjusting your search query or filters.
          </Typography>
        </Paper>
      )}

      {/* Initial State */}
      {!loading && !searchQuery && searchResults.length === 0 && (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Search for agricultural information
          </Typography>
          <Typography variant="body1">
            Enter a search query to find information about crops, farming techniques, market trends, and more.
          </Typography>
        </Paper>
      )}
    </Container>
  );
};

export default ExternalDataSearch;
