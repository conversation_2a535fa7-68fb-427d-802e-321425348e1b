import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Alert,
  useTheme
} from '@mui/material';
import {
  CheckCircle,
  Warning,
  Info,
  ExpandMore,
  Psychology,
  Timeline,
  Insights,
  Nature,
  WbSunny,
  TrendingUp,
  ReportProblem,
  CheckCircleOutline,
  ErrorOutline
} from '@mui/icons-material';

/**
 * AI-powered comparison section component
 * Displays enhanced analysis generated by GPT-4o
 */
const AIComparisonSection = ({
  comparisonResult,
  isLoading,
  error,
  useAI = true, // Default to true, no longer toggleable
  translatedLabels = {},
  translatedContent = {}
}) => {
  const theme = useTheme();

  // Always show AI analysis, toggle removed
  // if (!useAI) return null;

  // Handle loading state
  if (isLoading) {
    return (
      <Paper variant="outlined" sx={{ p: 3, mt: 3, textAlign: 'center' }}>
        <CircularProgress size={40} sx={{ mb: 2 }} />
        <Typography>{translatedLabels.generatingAIAnalysis || 'Generating AI analysis...'}</Typography>
      </Paper>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 3 }}>
        {error}
      </Alert>
    );
  }

  // If no AI analysis is available
  if (!comparisonResult?.aiAnalysis) {
    return (
      <Paper variant="outlined" sx={{ p: 3, mt: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Psychology sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6">{translatedLabels.aiAnalysis || 'AI Analysis'}</Typography>
        </Box>
        <Alert severity="info">
          {translatedLabels.aiAnalysisNotAvailable || 'AI-powered analysis is not available for this comparison.'}
        </Alert>
      </Paper>
    );
  }

  const {
    growthAnalysis,
    healthAssessment,
    resolvedIssues,
    newIssues,
    persistentIssues,
    predictions,
    recommendations,
    environmentalFactors,
    rawContent
  } = comparisonResult.aiAnalysis;

  return (
    <Paper variant="outlined" sx={{ p: 3, mt: 3 }}>
      {/* Header - removed toggle button */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Psychology sx={{ mr: 1, color: 'primary.main' }} />
        <Typography variant="h6">{translatedLabels.aiPoweredAnalysis || 'AI-Powered Analysis'}</Typography>
      </Box>

      <Divider sx={{ my: 2 }} />

      {/* Growth Analysis */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Timeline sx={{ mr: 1, color: theme.palette.success.main }} />
          <Typography variant="subtitle1" fontWeight="medium">{translatedLabels.growthAnalysis || 'Growth Analysis'}</Typography>
        </Box>
        <Typography variant="body2" sx={{ ml: 4 }}>
          {translatedContent.growthAnalysis || growthAnalysis}
        </Typography>
      </Box>

      {/* Health Assessment */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Insights sx={{ mr: 1, color: theme.palette.info.main }} />
          <Typography variant="subtitle1" fontWeight="medium">{translatedLabels.healthAssessment || 'Health Assessment'}</Typography>
        </Box>
        <Typography variant="body2" sx={{ ml: 4 }}>
          {translatedContent.healthAssessment || healthAssessment}
        </Typography>
      </Box>

      {/* Issues Analysis */}
      <Accordion defaultExpanded sx={{ mb: 2 }}>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ReportProblem sx={{ mr: 1, color: theme.palette.warning.main }} />
            <Typography variant="subtitle1" fontWeight="medium">{translatedLabels.issuesAnalysis || 'Issues Analysis'}</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          {/* Resolved Issues */}
          {resolvedIssues && resolvedIssues.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {translatedLabels.resolvedIssues || 'Resolved Issues:'}
              </Typography>
              <List dense disablePadding>
                {resolvedIssues.map((issue, index) => (
                  <ListItem key={index} sx={{ py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <CheckCircleOutline fontSize="small" color="success" />
                    </ListItemIcon>
                    <ListItemText 
                      primaryTypographyProps={{ component: 'span' }}
                      primary={translatedContent.resolvedIssues?.[index] || issue} 
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}

          {/* New Issues */}
          {newIssues && newIssues.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {translatedLabels.newIssues || 'New Issues:'}
              </Typography>
              <List dense disablePadding>
                {newIssues.map((issue, index) => (
                  <ListItem key={index} sx={{ py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <ErrorOutline fontSize="small" color="error" />
                    </ListItemIcon>
                    <ListItemText 
                      primaryTypographyProps={{ component: 'span' }}
                      primary={translatedContent.newIssues?.[index] || issue} 
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}

          {/* Persistent Issues */}
          {persistentIssues && persistentIssues.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {translatedLabels.persistentIssues || 'Persistent Issues:'}
              </Typography>
              <List dense disablePadding>
                {persistentIssues.map((issue, index) => (
                  <ListItem key={index} sx={{ py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <Warning fontSize="small" color="warning" />
                    </ListItemIcon>
                    <ListItemText 
                      primaryTypographyProps={{ component: 'span' }}
                      primary={translatedContent.persistentIssues?.[index] || issue} 
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}

          {/* No Issues */}
          {(!resolvedIssues || resolvedIssues.length === 0) &&
           (!newIssues || newIssues.length === 0) &&
           (!persistentIssues || persistentIssues.length === 0) && (
            <Typography variant="body2" color="text.secondary">
              {translatedLabels.noSpecificIssues || 'No specific issues detected in this analysis.'}
            </Typography>
          )}
        </AccordionDetails>
      </Accordion>

      {/* Predictions */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <TrendingUp sx={{ mr: 1, color: theme.palette.primary.main }} />
          <Typography variant="subtitle1" fontWeight="medium">{translatedLabels.predictions || 'Predictions'}</Typography>
        </Box>
        <Typography variant="body2" sx={{ ml: 4 }}>
          {translatedContent.predictions || predictions}
        </Typography>
      </Box>

      {/* Recommendations */}
      <Accordion defaultExpanded sx={{ mb: 2 }}>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Nature sx={{ mr: 1, color: theme.palette.success.main }} />
            <Typography variant="subtitle1" fontWeight="medium">{translatedLabels.recommendations || 'Recommendations'}</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          {recommendations && recommendations.length > 0 ? (
            <List dense disablePadding>
              {recommendations.map((rec, index) => (
                <ListItem key={index} sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <CheckCircle fontSize="small" color="success" />
                  </ListItemIcon>
                  <ListItemText 
                    primaryTypographyProps={{ component: 'span' }}
                    primary={translatedContent.recommendations?.[index] || rec} 
                  />
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography variant="body2" color="text.secondary">
              {translatedLabels.noSpecificRecommendations || 'No specific recommendations available.'}
            </Typography>
          )}
        </AccordionDetails>
      </Accordion>

      {/* Environmental Factors */}
      <Box sx={{ mb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <WbSunny sx={{ mr: 1, color: theme.palette.warning.main }} />
          <Typography variant="subtitle1" fontWeight="medium">{translatedLabels.environmentalFactors || 'Environmental Factors'}</Typography>
        </Box>
        <Typography variant="body2" sx={{ ml: 4 }}>
          {translatedContent.environmentalFactors || environmentalFactors}
        </Typography>
      </Box>

      {/* Raw Content (if available) */}
      {rawContent && (
        <Accordion sx={{ mt: 2, mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMore />}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Info sx={{ mr: 1, color: theme.palette.info.main }} />
            <Typography variant="subtitle1" fontWeight="medium">{translatedLabels.rawAIResponse || 'Raw AI Response'}</Typography>
          </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box
              sx={{
                p: 2,
                bgcolor: 'rgba(0,0,0,0.04)',
                borderRadius: 1,
                maxHeight: '300px',
                overflow: 'auto',
                whiteSpace: 'pre-wrap',
                fontFamily: 'monospace',
                fontSize: '0.8rem'
              }}
            >
              {translatedContent.rawContent || rawContent}
            </Box>
          </AccordionDetails>
        </Accordion>
      )}

      {/* AI Generated Badge */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
        <Chip
          icon={<Psychology fontSize="small" />}
          label={translatedLabels.aiGeneratedAnalysis || "AI Generated Analysis"}
          size="small"
          color="primary"
          variant="outlined"
        />
      </Box>
    </Paper>
  );
};

export default AIComparisonSection;
