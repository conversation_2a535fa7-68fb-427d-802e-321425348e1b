import React, { useState, useEffect, useCallback } from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';
import { fetchAnalysesByCropType } from './mockAnalysisApi';
import { generateAIComparisonAnalysis, isAIAnalysisAvailable } from '../../../services/aiAnalysisService';
import { AI_FEATURES, MOCK_AI_ANALYSIS } from '../../../config/aiConfig';
import AIComparisonSection from './AIComparisonSection';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Grid,
  Button,
  IconButton,
  Divider,
  Paper,
  Chip,
  CircularProgress,
  useTheme,
  useMediaQuery,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tooltip,
  Card,
  CardActionArea,
  CardMedia,
  CardContent,
  RadioGroup,
  FormControlLabel,
  Radio,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Tabs,
  Tab
} from '@mui/material';
import {
  Close,
  CompareArrows,
  CalendarMonth,
  ArrowUpward,
  ArrowDownward,
  CheckCircle,
  Warning,
  Error,
  Info,
  Timeline,
  History,
  Today,
  DateRange,
  Image,
  ImageSearch,
  Agriculture,
  FilterAlt,
  Sort,
  Translate as TranslateIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import translationService from '../../../utils/TranslationService';

const AnalysisComparisonDialog = ({ open, onClose, currentAnalysis, cropType }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [historicalAnalyses, setHistoricalAnalyses] = useState([]);
  const [selectedAnalysis, setSelectedAnalysis] = useState(null);
  const [selectedAnalysisId, setSelectedAnalysisId] = useState(null);
  const [comparisonResult, setComparisonResult] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'grid'
  const [sortOrder, setSortOrder] = useState('newest'); // 'newest' or 'oldest'
  const [dateGroups, setDateGroups] = useState({});

  // AI analysis states
  const [useAI] = useState(true);
  const [isAILoading, setIsAILoading] = useState(false);
  const [aiError, setAIError] = useState(null);

  // Translation states
  // Use global language context instead of local state
  const { selectedLanguage, setSelectedLanguage } = useLanguage();
  const [translatedLabels, setTranslatedLabels] = useState({});
  const [translatedContent, setTranslatedContent] = useState({});
  const [isTranslating, setIsTranslating] = useState(false);

  // Fetch historical analyses for the same crop type
  useEffect(() => {
    if (open && currentAnalysis) {
      fetchHistoricalAnalyses();
    }
  }, [open, currentAnalysis]);

  // Trigger translation when comparison result changes
  useEffect(() => {
    if (selectedLanguage && selectedLanguage !== 'en-IN' && comparisonResult) {
      performTranslation(selectedLanguage);
    }
  }, [comparisonResult, selectedLanguage]);

  const fetchHistoricalAnalyses = async () => {
    try {
      setIsLoading(true);
      setError(null);
      setSelectedAnalysis(null); // Clear any previous selection
      setComparisonResult(null); // Clear any previous comparison result

      // Get the crop type from the current analysis
      const currentCropType = currentAnalysis.analysis?.identifiedAs || cropType;

      if (!currentCropType) {
        setError('No crop type identified in the current analysis');
        setIsLoading(false);
        return;
      }

      console.log('Current analysis:', currentAnalysis);
      console.log('Fetching historical analyses for crop type:', currentCropType);

      // Use our mock API implementation
      const data = await fetchAnalysesByCropType(currentCropType, currentAnalysis.id);

      // Filter analyses by crop type and exclude current analysis
      const filteredAnalyses = data.data.analyses || [];

      console.log(`Found ${filteredAnalyses.length} analyses for crop type: ${currentCropType}`);
      console.log('Historical analyses:', filteredAnalyses);

      // Group analyses by date
      const groups = {};
      filteredAnalyses.forEach(analysis => {
        const date = new Date(analysis.timestamp);
        const dateKey = format(date, 'yyyy-MM-dd');
        const formattedDate = format(date, 'MMMM d, yyyy');

        if (!groups[dateKey]) {
          groups[dateKey] = {
            date: formattedDate,
            dateKey,
            analyses: []
          };
        }

        groups[dateKey].analyses.push(analysis);
      });

      // Sort analyses within each group by time
      Object.values(groups).forEach(group => {
        group.analyses.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      });

      setDateGroups(groups);

      // Sort all analyses by date (newest first)
      const sortedAnalyses = filteredAnalyses.sort((a, b) =>
        sortOrder === 'newest'
          ? new Date(b.timestamp) - new Date(a.timestamp)
          : new Date(a.timestamp) - new Date(b.timestamp)
      );

      setHistoricalAnalyses(sortedAnalyses);

      // Select the most recent analysis by default if available
      if (sortedAnalyses.length > 0) {
        console.log('Setting default selected analysis:', sortedAnalyses[0]);
        setSelectedAnalysis(sortedAnalyses[0]);
        generateComparisonResult(sortedAnalyses[0]);
      } else {
        console.log('No historical analyses found for comparison');
      }
    } catch (err) {
      console.error('Error fetching historical analyses:', err);
      setError(err?.message || 'Failed to fetch historical analyses');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleViewModeChange = (mode) => {
    setViewMode(mode);
  };

  const handleSortOrderChange = (order) => {
    setSortOrder(order);

    // Re-sort the analyses based on the new order
    const newSortedAnalyses = [...historicalAnalyses].sort((a, b) =>
      order === 'newest'
        ? new Date(b.timestamp) - new Date(a.timestamp)
        : new Date(a.timestamp) - new Date(b.timestamp)
    );

    setHistoricalAnalyses(newSortedAnalyses);
  };

  // Handle language change for translation
  const handleLanguageChange = async (event) => {
    const newLanguage = event.target.value;
    setSelectedLanguage(newLanguage);
    
    if (newLanguage === 'en-IN') {
      // Reset translations when switching back to English
      setTranslatedLabels({});
      setTranslatedContent({});
      return;
    }
    
    await performTranslation(newLanguage);
  };

  // Perform translation for the current language
  const performTranslation = async (newLanguage) => {
    try {
      setIsTranslating(true);
      
      // Static labels to translate
      const staticLabels = {
        // Dialog title and main sections
        cropGrowthComparison: 'Crop Growth Comparison',
        selectAnyImage: 'Select Any Image to Compare',
        selectPreviousImage: 'Please select any previous image to compare with the current one. You can compare with any crop type to see differences over time.',
        selectPreviousImageShort: 'Select any previous image to compare',
        byDate: 'BY DATE',
        allImages: 'ALL IMAGES',
        currentAnalysis: 'Current Analysis',
        previousAnalysis: 'Previous Analysis',
        currentStatus: 'Current Status',
        previousStatus: 'Previous Status',
        healthStatus: 'Health Status:',
        confidenceLevel: 'Confidence Level:',
        highConfidence: 'High confidence',
        mediumConfidence: 'Medium confidence',
        lowConfidence: 'Low confidence',
        issues: 'Issues:',
        noIssuesDetected: 'No issues detected',
        growthAnalysis: 'Growth Analysis',
        selectPreviousImageToCompare: 'Select a previous image to see the growth comparison analysis',
        newIssues: 'New issues:',
        persistentIssues: 'Persistent issues:',
        resolvedIssues: 'Resolved issues:',
        close: 'Close',
        translating: 'Translating...',
        
        // AI Analysis section
        aiPoweredAnalysis: 'AI-Powered Analysis',
        aiAnalysis: 'AI Analysis',
        aiAnalysisNotAvailable: 'AI-powered analysis is not available for this comparison.',
        healthAssessment: 'Health Assessment',
        issuesAnalysis: 'Issues Analysis',
        predictions: 'Predictions',
        recommendations: 'Recommendations',
        environmentalFactors: 'Environmental Factors',
        
        // Additional missing labels
        selectPreviousImageDescription: 'Please select an image from above to compare with the current analysis',
        generatingAIAnalysis: 'Generating AI analysis...',
        noSpecificIssues: 'No specific issues detected in this analysis.',
        noSpecificRecommendations: 'No specific recommendations available.',
        rawAIResponse: 'Raw AI Response',
        aiGeneratedAnalysis: 'AI Generated Analysis',
        
        // Additional labels that were missing
        timeBetweenAnalyses: 'Time between analyses:',
        days: 'days',
        healthStatusChange: 'Health status change:',
        improved: 'Improved',
        declined: 'Declined',
        noChange: 'No change',
        confidence: 'Confidence:',
        unknown: 'Unknown',
        excellent: 'Excellent',
        good: 'Good',
        fair: 'Fair',
        poor: 'Poor',
        moderate: 'Moderate'
      };
      
      // Content to translate from current and selected analysis
      const content = {};
      
      // Translate current analysis content
      if (currentAnalysis) {
        if (currentAnalysis.analysis?.identifiedAs) {
          content.currentCropType = currentAnalysis.analysis.identifiedAs;
        }
        if (currentAnalysis.analysis?.issues) {
          content.currentIssues = currentAnalysis.analysis.issues;
        }
      }
      
      // Translate selected analysis content
      if (selectedAnalysis) {
        if (selectedAnalysis.analysis?.identifiedAs) {
          content.selectedCropType = selectedAnalysis.analysis.identifiedAs;
        }
        if (selectedAnalysis.analysis?.issues) {
          content.selectedIssues = selectedAnalysis.analysis.issues;
        }
      }
      
      // Translate comparison result content
      if (comparisonResult) {
        // Basic comparison data
        if (comparisonResult.newIssues) {
          content.newIssues = comparisonResult.newIssues;
        }
        if (comparisonResult.persistentIssues) {
          content.persistentIssues = comparisonResult.persistentIssues;
        }
        if (comparisonResult.resolvedIssues) {
          content.resolvedIssues = comparisonResult.resolvedIssues;
        }
        
        // AI analysis data (stored in aiAnalysis object)
        if (comparisonResult.aiAnalysis) {
          if (comparisonResult.aiAnalysis.growthAnalysis) {
            content.growthAnalysis = comparisonResult.aiAnalysis.growthAnalysis;
          }
          if (comparisonResult.aiAnalysis.healthAssessment) {
            content.healthAssessment = comparisonResult.aiAnalysis.healthAssessment;
          }
          if (comparisonResult.aiAnalysis.predictions) {
            content.predictions = comparisonResult.aiAnalysis.predictions;
          }
          if (comparisonResult.aiAnalysis.recommendations) {
            content.recommendations = comparisonResult.aiAnalysis.recommendations;
          }
          if (comparisonResult.aiAnalysis.environmentalFactors) {
            content.environmentalFactors = comparisonResult.aiAnalysis.environmentalFactors;
          }
          if (comparisonResult.aiAnalysis.rawContent) {
            content.rawContent = comparisonResult.aiAnalysis.rawContent;
          }
        }
      }
      
      // Translate static labels
      const translatedLabelsResult = {};
      for (const [key, value] of Object.entries(staticLabels)) {
        translatedLabelsResult[key] = await translationService.translateText(value, newLanguage);
      }
      
      // Translate content
      const translatedContentResult = {};
      for (const [key, value] of Object.entries(content)) {
        if (Array.isArray(value)) {
          // Handle arrays (like issues)
          translatedContentResult[key] = [];
          for (const item of value) {
            const translatedItem = await translationService.translateText(item, newLanguage);
            translatedContentResult[key].push(translatedItem);
          }
        } else {
          translatedContentResult[key] = await translationService.translateText(value, newLanguage);
        }
      }
      
      setTranslatedLabels(translatedLabelsResult);
      setTranslatedContent(translatedContentResult);
      console.log('Translation complete:', { translatedLabelsResult, translatedContentResult });
    } catch (error) {
      console.error('Translation error:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  const handleAnalysisSelect = (analysis) => {
    setSelectedAnalysis(analysis);
    generateComparisonResult(analysis);
  };

  const handleAnalysisChange = (event) => {
    const analysisId = event.target.value;
    const analysis = historicalAnalyses.find(a => a.id === analysisId);
    setSelectedAnalysis(analysis);
    generateComparisonResult(analysis);
  };

  const generateComparisonResult = async (historicalAnalysis) => {
    if (!historicalAnalysis || !currentAnalysis) return;

    const current = currentAnalysis.analysis || {};
    const historical = historicalAnalysis.analysis || {};

    // Calculate days between analyses
    const currentDate = new Date(currentAnalysis.timestamp);
    const historicalDate = new Date(historicalAnalysis.timestamp);
    const daysDifference = Math.round((currentDate - historicalDate) / (1000 * 60 * 60 * 24));

    // Compare health status
    let healthChange = 'same';
    if (current.healthStatus !== historical.healthStatus) {
      const healthRanking = { 'excellent': 4, 'good': 3, 'fair': 2, 'poor': 1 };
      const currentRank = healthRanking[current.healthStatus?.toLowerCase()] || 0;
      const historicalRank = healthRanking[historical.healthStatus?.toLowerCase()] || 0;

      healthChange = currentRank > historicalRank ? 'improved' : 'declined';
    }

    // Compare issues
    const currentIssues = current.issues || [];
    const historicalIssues = historical.issues || [];

    const resolvedIssues = historicalIssues.filter(issue =>
      !currentIssues.some(currentIssue =>
        currentIssue.toLowerCase().includes(issue.toLowerCase())
      )
    );

    const newIssues = currentIssues.filter(issue =>
      !historicalIssues.some(historicalIssue =>
        historicalIssue.toLowerCase().includes(issue.toLowerCase())
      )
    );

    const persistentIssues = currentIssues.filter(issue =>
      historicalIssues.some(historicalIssue =>
        historicalIssue.toLowerCase().includes(issue.toLowerCase())
      )
    );

    // Create basic comparison result
    const basicResult = {
      daysDifference,
      healthChange,
      resolvedIssues,
      newIssues,
      persistentIssues
    };

    // Set the basic result immediately
    setComparisonResult(basicResult);

    // Always generate AI analysis if the feature is enabled
    if (AI_FEATURES.enableAIComparison) {
      try {
        setIsAILoading(true);
        setAIError(null);

        console.log('Generating AI comparison analysis with real data');
        console.log('Current analysis:', currentAnalysis);
        console.log('Historical analysis:', historicalAnalysis);

        // Use Azure OpenAI for analysis
        const aiResult = await generateAIComparisonAnalysis(currentAnalysis, historicalAnalysis);
        console.log('AI analysis result:', aiResult);

        // Update the comparison result with AI analysis
        if (aiResult) {
          setComparisonResult(aiResult);
        }
      } catch (err) {
        console.error('Error generating AI analysis:', err);
        setAIError(err.message || 'Failed to generate AI analysis');
      } finally {
        setIsAILoading(false);
      }
    }
  };

  const renderHealthStatusChip = (status) => {
    if (!status) return <span><Chip label={translatedLabels.unknown || "Unknown"} size="small" /></span>;

    const statusLower = status.toLowerCase();
    let color = 'default';
    let icon = <Info />;
    let translatedStatus = status;

    if (statusLower.includes('excellent')) {
      color = 'success';
      icon = <CheckCircle />;
      translatedStatus = translatedLabels.excellent || status;
    } else if (statusLower.includes('good')) {
      color = 'success';
      icon = <CheckCircle />;
      translatedStatus = translatedLabels.good || status;
    } else if (statusLower.includes('fair')) {
      color = 'warning';
      icon = <Warning />;
      translatedStatus = translatedLabels.fair || status;
    } else if (statusLower.includes('poor')) {
      color = 'error';
      icon = <Error />;
      translatedStatus = translatedLabels.poor || status;
    } else if (statusLower.includes('moderate')) {
      color = 'warning';
      icon = <Warning />;
      translatedStatus = translatedLabels.moderate || status;
    }

    return (
      <span>
        <Chip
          label={translatedStatus}
          size="small"
          color={color}
          icon={icon}
        />
      </span>
    );
  };

  const renderConfidenceLevel = (confidence) => {
    if (!confidence && confidence !== 0) return null;

    const confidencePercent = Math.round(confidence * 100);

    return (
      <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
        <Typography variant="body2" sx={{ mr: 1 }}>
          {translatedLabels.confidence || 'Confidence:'}
        </Typography>
        <Chip
          label={`${confidencePercent}%`}
          size="small"
          color={confidencePercent > 80 ? 'success' : confidencePercent > 60 ? 'warning' : 'error'}
        />
      </Box>
    );
  };

  const renderHealthChangeIndicator = (change) => {
    if (change === 'improved') {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', color: 'success.main' }}>
          <ArrowUpward fontSize="small" sx={{ mr: 0.5 }} />
          <Typography variant="body2">{translatedLabels.improved || 'Improved'}</Typography>
        </Box>
      );
    } else if (change === 'declined') {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', color: 'error.main' }}>
          <ArrowDownward fontSize="small" sx={{ mr: 0.5 }} />
          <Typography variant="body2">{translatedLabels.declined || 'Declined'}</Typography>
        </Box>
      );
    } else {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', color: 'text.secondary' }}>
          <CompareArrows fontSize="small" sx={{ mr: 0.5 }} />
          <Typography variant="body2">{translatedLabels.noChange || 'No change'}</Typography>
        </Box>
      );
    }
  };

  const renderAnalysisImage = (analysis, title) => {
    if (!analysis) return null;

    const date = new Date(analysis.timestamp);
    const formattedDate = format(date, 'MMMM d, yyyy');
    const formattedTime = format(date, 'h:mm a');

    return (
      <Box sx={{ position: 'relative', mb: 2 }}>
        <Typography variant="subtitle1" gutterBottom>
          {title}
        </Typography>
        <img
          src={analysis.imageUrl}
          alt={analysis.analysis?.identifiedAs || 'Crop analysis'}
          style={{
            width: '100%',
            borderRadius: theme.shape.borderRadius,
            maxHeight: '250px',
            objectFit: 'contain'
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            bgcolor: 'rgba(0,0,0,0.6)',
            color: 'white',
            p: 1,
            borderBottomLeftRadius: theme.shape.borderRadius,
            borderBottomRightRadius: theme.shape.borderRadius,
          }}
        >
          <Typography variant="body2">
            {formattedDate} at {formattedTime}
          </Typography>
        </Box>
      </Box>
    );
  };

  const renderComparisonSection = () => {
    if (!comparisonResult || !selectedAnalysis) {
      return (
        <Paper variant="outlined" sx={{ p: 2, mt: 3, textAlign: 'center', bgcolor: 'action.hover' }}>
          <Typography variant="body1" color="text.secondary">
            {translatedLabels.selectPreviousImageToCompare || 'Select a previous image to see the growth comparison analysis'}
          </Typography>
        </Paper>
      );
    }

    return (
      <Paper variant="outlined" sx={{ p: 2, mt: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Timeline sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6">{translatedLabels.growthAnalysis || 'Growth Analysis'}</Typography>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            {translatedLabels.timeBetweenAnalyses || 'Time between analyses:'}
          </Typography>
          <Typography variant="body1" fontWeight="medium">
            {comparisonResult.daysDifference} {translatedLabels.days || 'days'}
          </Typography>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            {translatedLabels.healthStatusChange || 'Health status change:'}
          </Typography>
          {renderHealthChangeIndicator(comparisonResult.healthChange)}
        </Box>

        {comparisonResult.resolvedIssues.length > 0 && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {translatedLabels.resolvedIssues || 'Resolved issues:'}
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {comparisonResult.resolvedIssues.map((issue, index) => (
                <Chip
                  key={index}
                  label={translatedContent.resolvedIssues?.[index] || issue}
                  size="small"
                  color="success"
                  variant="outlined"
                  icon={<CheckCircle fontSize="small" />}
                />
              ))}
            </Box>
          </Box>
        )}

        {comparisonResult.newIssues.length > 0 && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {translatedLabels.newIssues || 'New issues:'}
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {comparisonResult.newIssues.map((issue, index) => (
                <Chip
                  key={index}
                  label={translatedContent.newIssues?.[index] || issue}
                  size="small"
                  color="error"
                  variant="outlined"
                  icon={<Warning fontSize="small" />}
                />
              ))}
            </Box>
          </Box>
        )}

        {comparisonResult.persistentIssues.length > 0 && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {translatedLabels.persistentIssues || 'Persistent issues:'}
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {comparisonResult.persistentIssues.map((issue, index) => (
                <Chip
                  key={index}
                  label={translatedContent.persistentIssues?.[index] || issue}
                  size="small"
                  color="warning"
                  variant="outlined"
                  icon={<Info fontSize="small" />}
                />
              ))}
            </Box>
          </Box>
        )}
      </Paper>
    );
  };

  // Render the image selection UI
  const renderImageSelectionUI = () => {
    if (historicalAnalyses.length === 0) {
      return (
        <Alert severity="info" sx={{ mb: 3 }}>
          No previous analyses found for this crop type. Upload more images over time to track growth.
        </Alert>
      );
    }

    return (
      <Paper variant="outlined" sx={{ width: '100%', mb: 3, p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <CompareArrows sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6">{translatedLabels.selectAnyImage || 'Select Any Image to Compare'}</Typography>
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {translatedLabels.selectPreviousImage || 'Please select any previous image to compare with the current one. You can compare with any crop type to see differences over time.'}
        </Typography>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="image selection tabs"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab
              icon={<DateRange />}
              iconPosition="start"
              label={translatedLabels.byDate || "By Date"}
              id="tab-0"
              aria-controls="tabpanel-0"
            />
            <Tab
              icon={<Timeline />}
              iconPosition="start"
              label={translatedLabels.allImages || "All Images"}
              id="tab-1"
              aria-controls="tabpanel-1"
            />
          </Tabs>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="subtitle1">
            {translatedLabels.selectPreviousImageShort || 'Select any previous image to compare'}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title="Toggle view mode">
              <Box sx={{ display: 'flex', mr: 2 }}>
                <IconButton
                  size="small"
                  color={viewMode === 'list' ? 'primary' : 'default'}
                  onClick={() => handleViewModeChange('list')}
                >
                  <List fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  color={viewMode === 'grid' ? 'primary' : 'default'}
                  onClick={() => handleViewModeChange('grid')}
                >
                  <Image fontSize="small" />
                </IconButton>
              </Box>
            </Tooltip>

            <Tooltip title="Sort order">
              <IconButton
                size="small"
                onClick={() => handleSortOrderChange(sortOrder === 'newest' ? 'oldest' : 'newest')}
                color="primary"
              >
                <Sort fontSize="small" />
                {sortOrder === 'newest' ? (
                  <ArrowDownward fontSize="small" sx={{ ml: 0.5 }} />
                ) : (
                  <ArrowUpward fontSize="small" sx={{ ml: 0.5 }} />
                )}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Box role="tabpanel" hidden={tabValue !== 0} id="tabpanel-0" aria-labelledby="tab-0">
          {tabValue === 0 && (
            <Box sx={{ maxHeight: '300px', overflowY: 'auto', pr: 1 }}>
              {Object.keys(dateGroups).length > 0 ? (
                Object.keys(dateGroups)
                  .sort((a, b) => sortOrder === 'newest' ? b.localeCompare(a) : a.localeCompare(b))
                  .map(dateKey => {
                    const group = dateGroups[dateKey];
                    return (
                      <Box key={dateKey} sx={{ mb: 3 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <CalendarMonth fontSize="small" sx={{ mr: 1, color: 'primary.main' }} />
                          <Typography variant="subtitle2">{group.date}</Typography>
                        </Box>

                        {viewMode === 'grid' ? (
                          <Grid container spacing={2}>
                            {group.analyses.map(analysis => {
                              const time = format(new Date(analysis.timestamp), 'h:mm a');
                              const isSelected = selectedAnalysis?.id === analysis.id;

                              return (
                                <Grid item xs={6} sm={4} md={3} key={analysis.id}>
                                  <Card
                                    variant={isSelected ? 'elevation' : 'outlined'}
                                    elevation={isSelected ? 4 : 0}
                                    sx={{
                                      border: isSelected ? `2px solid ${theme.palette.primary.main}` : undefined,
                                      transition: 'all 0.2s'
                                    }}
                                  >
                                    <CardActionArea onClick={() => handleAnalysisSelect(analysis)}>
                                      <CardMedia
                                        component="img"
                                        height="120"
                                        image={analysis.imageUrl}
                                        alt={analysis.result?.identifiedAs || 'Crop analysis'}
                                      />
                                      <CardContent sx={{ p: 1 }}>
                                        <Typography variant="caption" color="text.secondary">
                                          {time}
                                        </Typography>
                                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                                          {renderHealthStatusChip(analysis.result?.healthStatus)}
                                        </Box>
                                      </CardContent>
                                    </CardActionArea>
                                  </Card>
                                </Grid>
                              );
                            })}
                          </Grid>
                        ) : (
                          <List dense disablePadding>
                            {group.analyses.map(analysis => {
                              const time = format(new Date(analysis.timestamp), 'h:mm a');
                              const isSelected = selectedAnalysis?.id === analysis.id;

                              return (
                                <ListItem
                                  key={analysis.id}
                                  button
                                  onClick={() => handleAnalysisSelect(analysis)}
                                  selected={isSelected}
                                  sx={{
                                    borderRadius: 1,
                                    mb: 0.5,
                                    border: isSelected ? `1px solid ${theme.palette.primary.main}` : undefined,
                                    bgcolor: isSelected ? 'rgba(0, 0, 0, 0.04)' : undefined
                                  }}
                                >
                                  <ListItemAvatar>
                                    <Avatar
                                      variant="rounded"
                                      src={analysis.imageUrl}
                                      alt={analysis.analysis?.identifiedAs || 'Crop'}
                                    />
                                  </ListItemAvatar>
                                  <ListItemText
                                    primary={time}
                                    secondary={
                                      <>
                                        {analysis.analysis?.healthStatus && (
                                          <Box component="span" sx={{ mt: 0.5, display: 'inline-block' }}>
                                            {renderHealthStatusChip(analysis.analysis?.healthStatus)}
                                          </Box>
                                        )}
                                      </>
                                    }
                                  />
                                </ListItem>
                              );
                            })}
                          </List>
                        )}
                      </Box>
                    );
                  })
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ py: 2, textAlign: 'center' }}>
                  No historical data available for this crop
                </Typography>
              )}
            </Box>
          )}
        </Box>

        <Box role="tabpanel" hidden={tabValue !== 1} id="tabpanel-1" aria-labelledby="tab-1">
          {tabValue === 1 && (
            <Box sx={{ maxHeight: '300px', overflowY: 'auto', pr: 1 }}>
              {viewMode === 'grid' ? (
                <Grid container spacing={2}>
                  {historicalAnalyses.map(analysis => {
                    const date = new Date(analysis.timestamp);
                    const formattedDate = format(date, 'MMM d, yyyy');
                    const time = format(date, 'h:mm a');
                    const isSelected = selectedAnalysis?.id === analysis.id;

                    return (
                      <Grid item xs={6} sm={4} md={3} key={analysis.id}>
                        <Card
                          variant={isSelected ? 'elevation' : 'outlined'}
                          elevation={isSelected ? 4 : 0}
                          sx={{
                            border: isSelected ? `2px solid ${theme.palette.primary.main}` : undefined,
                            transition: 'all 0.2s'
                          }}
                        >
                          <CardActionArea onClick={() => handleAnalysisSelect(analysis)}>
                            <CardMedia
                              component="img"
                              height="120"
                              image={analysis.imageUrl}
                              alt={analysis.analysis?.identifiedAs || 'Crop analysis'}
                            />
                            <CardContent sx={{ p: 1 }}>
                              <Typography variant="caption" color="text.secondary" display="block">
                                {formattedDate} {time}
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                                {renderHealthStatusChip(analysis.analysis?.healthStatus)}
                              </Box>
                            </CardContent>
                          </CardActionArea>
                        </Card>
                      </Grid>
                    );
                  })}
                </Grid>
              ) : (
                <List dense disablePadding>
                  {historicalAnalyses.map(analysis => {
                    const date = new Date(analysis.timestamp);
                    const formattedDate = format(date, 'MMM d, yyyy');
                    const time = format(date, 'h:mm a');
                    const isSelected = selectedAnalysis?.id === analysis.id;

                    return (
                      <ListItem
                        key={analysis.id}
                        button
                        onClick={() => handleAnalysisSelect(analysis)}
                        selected={isSelected}
                        sx={{
                          borderRadius: 1,
                          mb: 0.5,
                          border: isSelected ? `1px solid ${theme.palette.primary.main}` : undefined,
                          bgcolor: isSelected ? 'rgba(0, 0, 0, 0.04)' : undefined
                        }}
                      >
                        <ListItemAvatar>
                          <Avatar
                            variant="rounded"
                            src={analysis.imageUrl}
                            alt={analysis.analysis?.identifiedAs || 'Crop'}
                          />
                        </ListItemAvatar>
                        <ListItemText
                          primary={`${formattedDate} at ${time}`}
                          secondary={
                            <>
                              {analysis.analysis?.healthStatus && (
                                <Box component="span" sx={{ mt: 0.5, display: 'inline-block' }}>
                                  {renderHealthStatusChip(analysis.analysis?.healthStatus)}
                                </Box>
                              )}
                            </>
                          }
                        />
                      </ListItem>
                    );
                  })}
                </List>
              )}
            </Box>
          )}
        </Box>
      </Paper>
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      fullScreen={isMobile}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <CompareArrows sx={{ mr: 1 }} />
          <span style={{ fontSize: '1.25rem', fontWeight: 500 }}>
            {translatedLabels.cropGrowthComparison || 'Crop Growth Comparison'}
          </span>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box sx={{ position: 'relative' }}>
            <FormControl size="small" sx={{ width: 100 }}>
              <Select
                value={selectedLanguage}
                onChange={handleLanguageChange}
                displayEmpty
                variant="outlined"
                disabled={isTranslating}
                startAdornment={<TranslateIcon fontSize="small" sx={{ ml: 0.5, mr: -0.5, color: 'action.active' }} />}
                sx={{ 
                  height: 32,
                  '& .MuiSelect-select': {
                    py: 0.5,
                    pr: 2,
                    pl: 2.5
                  }
                }}
              >
                <MenuItem value="en-IN">English</MenuItem>
                <MenuItem value="hi-IN">हिंदी</MenuItem>
                <MenuItem value="bn-IN">বাংলা</MenuItem>
                <MenuItem value="te-IN">తెలుగు</MenuItem>
                <MenuItem value="ta-IN">தமிழ்</MenuItem>
                <MenuItem value="kn-IN">ಕನ್ನಡ</MenuItem>
                <MenuItem value="ml-IN">മലയാളം</MenuItem>
                <MenuItem value="mr-IN">मराठी</MenuItem>
                <MenuItem value="gu-IN">ગુજરાતી</MenuItem>
                <MenuItem value="pa-IN">ਪੰਜਾਬੀ</MenuItem>
              </Select>
            </FormControl>
            {isTranslating && (
              <CircularProgress 
                size={20} 
                sx={{ 
                  position: 'absolute',
                  top: '50%',
                  right: 8,
                  marginTop: '-10px'
                }}
              />
            )}
          </Box>
          <IconButton onClick={onClose}>
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {/* Translation Loading Overlay */}
        {isTranslating && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgcolor: 'rgba(255, 255, 255, 0.8)',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              zIndex: 1000,
              borderRadius: 1
            }}
          >
            <CircularProgress size={40} sx={{ mb: 2 }} />
            <Typography variant="body2" color="text.secondary">
              {translatedLabels.translating || 'Translating...'}
            </Typography>
          </Box>
        )}
        
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        ) : (
          <>
            {renderImageSelectionUI()}

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                {renderAnalysisImage(currentAnalysis, translatedLabels.currentAnalysis || "Current Analysis")}

                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    {translatedLabels.currentStatus || 'Current Status'}
                  </Typography>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography component="span" variant="body2" sx={{ mr: 1 }}>
                      {translatedLabels.healthStatus || 'Health Status:'}
                    </Typography>
                    {renderHealthStatusChip(currentAnalysis.analysis?.healthStatus)}
                  </Box>

                  {renderConfidenceLevel(currentAnalysis.analysis?.confidence)}

                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" gutterBottom>
                      {translatedLabels.issues || 'Issues:'}
                    </Typography>
                    {currentAnalysis.analysis?.issues && currentAnalysis.analysis.issues.length > 0 ? (
                      <Box component="ul" sx={{ pl: 2, mt: 0 }}>
                        {currentAnalysis.analysis.issues.map((issue, index) => (
                          <Typography component="li" key={index} variant="body2">
                            {translatedContent.currentIssues?.[index] || issue}
                          </Typography>
                        ))}
                      </Box>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        {translatedLabels.noIssuesDetected || 'No issues detected'}
                      </Typography>
                    )}
                  </Box>
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                {selectedAnalysis ? (
                  <>
                    {renderAnalysisImage(selectedAnalysis, translatedLabels.previousAnalysis || "Previous Analysis")}

                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        {translatedLabels.previousStatus || 'Previous Status'}
                      </Typography>

                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography component="span" variant="body2" sx={{ mr: 1 }}>
                          {translatedLabels.healthStatus || 'Health Status:'}
                        </Typography>
                        {renderHealthStatusChip(selectedAnalysis.analysis?.healthStatus)}
                      </Box>

                      {renderConfidenceLevel(selectedAnalysis.analysis?.confidence)}

                      <Box sx={{ mt: 2 }}>
                        <Typography variant="body2" gutterBottom>
                          {translatedLabels.issues || 'Issues:'}
                        </Typography>
                        {selectedAnalysis.analysis?.issues && selectedAnalysis.analysis.issues.length > 0 ? (
                          <Box component="ul" sx={{ pl: 2, mt: 0 }}>
                                                      {selectedAnalysis.analysis.issues.map((issue, index) => (
                            <Typography component="li" key={index} variant="body2">
                              {translatedContent.selectedIssues?.[index] || issue}
                            </Typography>
                          ))}
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            {translatedLabels.noIssuesDetected || 'No issues detected'}
                          </Typography>
                        )}
                      </Box>
                    </Paper>
                  </>
                ) : (
                  <Paper variant="outlined" sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', bgcolor: 'action.hover' }}>
                    <CompareArrows sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="h6" color="text.secondary" align="center" gutterBottom>
                      {translatedLabels.selectPreviousImage || 'Please select any previous image to compare with the current one. You can compare with any crop type to see differences over time.'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" align="center">
                      {translatedLabels.selectPreviousImageDescription || 'Please select an image from above to compare with the current analysis'}
                    </Typography>
                  </Paper>
                )}
              </Grid>

              <Grid item xs={12}>
                {renderComparisonSection()}

                {/* AI-powered analysis section */}
                {selectedAnalysis && (
                  <AIComparisonSection
                    comparisonResult={comparisonResult}
                    isLoading={isAILoading}
                    error={aiError}
                    useAI={useAI}
                    translatedLabels={translatedLabels}
                    translatedContent={translatedContent}
                  />
                )}
              </Grid>
            </Grid>
          </>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>{translatedLabels.close || 'Close'}</Button>
      </DialogActions>
    </Dialog>
  );
};

export default AnalysisComparisonDialog;
