// This file provides a mock implementation of the missing API endpoint
// for crop growth comparison functionality

/**
 * Filters analysis history by crop type
 * @param {Array} analyses - The full list of analyses
 * @param {string} cropType - The crop type to filter by
 * @param {string} excludeId - Optional ID to exclude from results
 * @returns {Array} - Filtered analyses
 */
export const filterAnalysesByCropType = (analyses, cropType, excludeId = '') => {
  if (!analyses || !Array.isArray(analyses) || analyses.length === 0) {
    return [];
  }

  console.log('Filtering analyses by crop type:', cropType);
  console.log('Total analyses before filtering:', analyses.length);
  console.log('Excluding ID:', excludeId);

  // Normalize the crop type for comparison
  const normalizedCropType = cropType?.toLowerCase() || '';

  // First, try to find exact matches
  let filtered = analyses.filter(analysis => {
    // Skip the current analysis if excludeId is provided
    if (excludeId && analysis.id === excludeId) {
      return false;
    }

    // Get the crop type from the analysis
    const analysisCropType = analysis.result?.identifiedAs?.toLowerCase() || '';

    // For debugging
    console.log(`Analysis ID: ${analysis.id}, Crop Type: ${analysisCropType}`);

    // Check for exact match first
    return analysisCropType === normalizedCropType;
  });

  // If no exact matches, try partial matches
  if (filtered.length === 0) {
    filtered = analyses.filter(analysis => {
      // Skip the current analysis if excludeId is provided
      if (excludeId && analysis.id === excludeId) {
        return false;
      }

      // Get the crop type from the analysis
      const analysisCropType = analysis.result?.identifiedAs?.toLowerCase() || '';

      // Check for partial match
      return normalizedCropType && (
        analysisCropType.includes(normalizedCropType) ||
        normalizedCropType.includes(analysisCropType)
      );
    });
  }

  // If still no matches, return all analyses except the current one
  // This is a fallback to ensure users can compare with something
  if (filtered.length === 0) {
    console.log('No matches found, returning all analyses except current');
    filtered = analyses.filter(analysis => analysis.id !== excludeId);
  }

  console.log('Filtered analyses count:', filtered.length);
  return filtered;
};

/**
 * Mock implementation of the missing API endpoint
 * @param {string} cropType - The crop type to filter by
 * @param {string} excludeId - Optional ID to exclude from results
 * @returns {Promise} - Promise that resolves with filtered analyses
 */
export const fetchAnalysesByCropType = async (cropType, excludeId = '') => {
  try {
    console.log(`Fetching all analyses, excluding ID: ${excludeId}`);

    // Fetch all analyses from the regular endpoint with a high limit to get all records
    const response = await fetch('/api/chat/analysis-history?limit=1000', {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("token")}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch analysis history');
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch analysis history');
    }

    console.log('Total analyses fetched:', data.data.analyses?.length || 0);

    // Get all analyses except the current one
    const allExceptCurrent = data.data.analyses?.filter(a => a.id !== excludeId) || [];

    console.log('All analyses except current:', allExceptCurrent.length);

    // Return in the same format as the API would
    return {
      success: true,
      data: {
        analyses: allExceptCurrent,
        total: allExceptCurrent.length,
        page: 1,
        limit: allExceptCurrent.length,
        totalPages: 1
      }
    };
  } catch (error) {
    console.error('Error in fetchAnalysesByCropType:', error);
    throw error;
  }
};
