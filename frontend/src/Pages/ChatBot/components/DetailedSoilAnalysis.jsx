import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Chip,
  Grid,
  LinearProgress,
} from '@mui/material';
import { ExpandMore, Info, Warning } from '@mui/icons-material';

/**
 * Detailed Soil Analysis Component
 * Provides comprehensive AI-generated insights about soil properties
 * 
 * @param {Object} props - Component props
 * @param {Object} props.analysis - The soil analysis data
 */
const DetailedSoilAnalysis = ({ analysis }) => {
  // Determine if we're dealing with soil or another type of analysis
  const isSoil = analysis?.identifiedAs?.toLowerCase().includes('soil');
  
  // Extract soil type from the analysis or default to "loamy soil"
  const soilType = analysis?.identifiedAs || 'loamy soil';
  
  // Extract health status or default to "moderate"
  const healthStatus = analysis?.healthStatus?.toLowerCase() || 'moderate';
  
  // Determine soil characteristics based on soil type
  const getSoilCharacteristics = () => {
    const type = soilType.toLowerCase();
    
    if (type.includes('clay')) {
      return {
        texture: 'Clay-dominant',
        structure: 'Dense, compact structure with small pores',
        waterRetention: 'High water retention (holds 2-2.5 inches of water per foot)',
        drainage: 'Poor drainage with slow infiltration rate',
        aeration: 'Limited aeration due to small pore spaces',
        nutrientRetention: 'Excellent nutrient retention due to high cation exchange capacity',
        workability: 'Difficult to work when wet or dry; narrow optimal moisture window',
      };
    } else if (type.includes('sandy')) {
      return {
        texture: 'Sand-dominant',
        structure: 'Loose structure with large pores',
        waterRetention: 'Low water retention (holds 0.5-1 inch of water per foot)',
        drainage: 'Excellent drainage with rapid infiltration rate',
        aeration: 'Excellent aeration due to large pore spaces',
        nutrientRetention: 'Poor nutrient retention due to low cation exchange capacity',
        workability: 'Easy to work across a wide range of moisture conditions',
      };
    } else if (type.includes('silt')) {
      return {
        texture: 'Silt-dominant',
        structure: 'Moderate structure with medium pores',
        waterRetention: 'Moderate to high water retention (holds 1.5-2 inches of water per foot)',
        drainage: 'Moderate drainage with medium infiltration rate',
        aeration: 'Moderate aeration',
        nutrientRetention: 'Good nutrient retention',
        workability: 'Moderately easy to work when moisture conditions are optimal',
      };
    } else {
      // Default to loamy characteristics
      return {
        texture: 'Balanced mixture of sand, silt, and clay particles',
        structure: 'Moderately aggregated with medium crumb structure',
        waterRetention: 'Medium capacity (holds approximately 1.5-2 inches of water per foot)',
        drainage: 'Good natural drainage with moderate water infiltration rate',
        aeration: 'Good aeration with balanced pore sizes',
        nutrientRetention: 'Good nutrient retention with moderate cation exchange capacity',
        workability: 'Relatively easy to work across a range of moisture conditions',
      };
    }
  };
  
  // Get nutrient levels based on health status
  const getNutrientLevels = () => {
    if (healthStatus === 'good' || healthStatus === 'excellent') {
      return {
        organicMatter: { level: 'Adequate (3-5%)', value: 75 },
        nitrogen: { level: 'Good availability', value: 70 },
        phosphorus: { level: 'Good availability', value: 75 },
        potassium: { level: 'Good availability', value: 80 },
        calcium: { level: 'Adequate levels', value: 85 },
        magnesium: { level: 'Adequate levels', value: 80 },
        sulfur: { level: 'Adequate levels', value: 75 },
        micronutrients: { level: 'Balanced levels', value: 75 }
      };
    } else if (healthStatus === 'fair' || healthStatus === 'moderate') {
      return {
        organicMatter: { level: 'Low to moderate (1-3%)', value: 45 },
        nitrogen: { level: 'Low to moderate availability', value: 50 },
        phosphorus: { level: 'Moderate availability', value: 60 },
        potassium: { level: 'Moderate to high availability', value: 65 },
        calcium: { level: 'Adequate levels', value: 70 },
        magnesium: { level: 'Adequate levels', value: 65 },
        sulfur: { level: 'Slightly deficient', value: 50 },
        micronutrients: { level: 'Some imbalances likely', value: 55 }
      };
    } else {
      return {
        organicMatter: { level: 'Very low (<1%)', value: 25 },
        nitrogen: { level: 'Deficient', value: 30 },
        phosphorus: { level: 'Low availability', value: 35 },
        potassium: { level: 'Low availability', value: 40 },
        calcium: { level: 'Potentially deficient', value: 45 },
        magnesium: { level: 'Potentially deficient', value: 40 },
        sulfur: { level: 'Deficient', value: 30 },
        micronutrients: { level: 'Likely imbalanced or deficient', value: 35 }
      };
    }
  };
  
  // Get soil health indicators
  const getSoilHealthIndicators = () => {
    if (healthStatus === 'good' || healthStatus === 'excellent') {
      return {
        pH: '6.5-7.0 (neutral, optimal for most crops)',
        cec: 'Medium to high (15-25 meq/100g)',
        biologicalActivity: 'High (abundant earthworm and microbial activity)',
        compaction: 'Minimal compaction risk',
        erosionRisk: 'Low erosion risk with good aggregate stability'
      };
    } else if (healthStatus === 'fair' || healthStatus === 'moderate') {
      return {
        pH: '6.2-6.8 (slightly acidic to neutral)',
        cec: 'Medium (10-15 meq/100g)',
        biologicalActivity: 'Moderate (some evidence of earthworm activity)',
        compaction: 'Moderate compaction risk',
        erosionRisk: 'Moderate erosion risk'
      };
    } else {
      return {
        pH: '5.5-6.2 or 7.2-8.0 (moderately acidic or alkaline)',
        cec: 'Low to medium (5-10 meq/100g)',
        biologicalActivity: 'Low (limited signs of soil life)',
        compaction: 'High compaction risk or already compacted',
        erosionRisk: 'High erosion risk with poor aggregate stability'
      };
    }
  };
  
  // Get agricultural potential based on soil type and health
  const getAgriculturalPotential = () => {
    const type = soilType.toLowerCase();
    let cropSuitability = '';
    
    if (type.includes('clay')) {
      cropSuitability = 'Well-suited for wheat, rice, certain vegetables, and many perennial crops';
    } else if (type.includes('sandy')) {
      cropSuitability = 'Well-suited for root vegetables, melons, strawberries, and drought-tolerant crops';
    } else if (type.includes('silt')) {
      cropSuitability = 'Well-suited for vegetables, berries, and many tree fruits';
    } else {
      cropSuitability = 'Well-suited for a wide range of crops including corn, wheat, soybeans, vegetables, and many fruit trees';
    }
    
    if (healthStatus === 'good' || healthStatus === 'excellent') {
      return {
        cropSuitability,
        irrigationNeeds: 'Low to moderate - efficient water use with good retention',
        fertilityManagement: 'Maintenance fertilization sufficient; focus on sustaining organic matter',
        yieldPotential: 'High yield potential with proper management'
      };
    } else if (healthStatus === 'fair' || healthStatus === 'moderate') {
      return {
        cropSuitability,
        irrigationNeeds: 'Moderate - requires regular irrigation during dry periods',
        fertilityManagement: 'Will respond well to organic matter additions and balanced fertilization',
        yieldPotential: 'Moderate yield potential with significant improvement possible through management'
      };
    } else {
      return {
        cropSuitability: 'Limited crop selection recommended until soil health improves',
        irrigationNeeds: 'High - careful irrigation management required',
        fertilityManagement: 'Requires comprehensive soil building program with organic amendments and balanced nutrients',
        yieldPotential: 'Currently limited, but substantial improvement possible with remediation'
      };
    }
  };
  
  // Get management recommendations based on issues
  const getManagementRecommendations = () => {
    const issues = analysis?.issues || [];
    const baseRecommendations = [];
    
    if (healthStatus !== 'good' && healthStatus !== 'excellent') {
      baseRecommendations.push('Conduct a comprehensive soil test to identify specific nutrient deficiencies');
    }
    
    if (issues.some(issue => issue.toLowerCase().includes('organic'))) {
      baseRecommendations.push('Add organic matter through compost, cover crops, or crop residues');
      baseRecommendations.push('Implement reduced tillage practices to preserve soil structure');
    }
    
    if (issues.some(issue => issue.toLowerCase().includes('compact'))) {
      baseRecommendations.push('Use mechanical aeration or deep tillage to break up compacted layers');
      baseRecommendations.push('Avoid working soil when wet to prevent further compaction');
      baseRecommendations.push('Consider deep-rooted cover crops to naturally break up compaction');
    }
    
    if (issues.some(issue => issue.toLowerCase().includes('drain'))) {
      baseRecommendations.push('Improve drainage through installation of drainage tiles or ditches');
      baseRecommendations.push('Create raised beds for better drainage in problem areas');
    }
    
    // Add general recommendations if we don't have specific ones
    if (baseRecommendations.length === 0) {
      if (healthStatus === 'good' || healthStatus === 'excellent') {
        baseRecommendations.push('Maintain current soil management practices');
        baseRecommendations.push('Consider crop rotation to maintain soil health and prevent pest buildup');
        baseRecommendations.push('Monitor nutrient levels through regular soil testing');
      } else {
        baseRecommendations.push('Incorporate organic matter to improve soil structure and fertility');
        baseRecommendations.push('Consider cover crops to protect soil and add organic matter');
        baseRecommendations.push('Implement balanced fertilization based on soil test results');
      }
    }
    
    return baseRecommendations;
  };
  
  // Get characteristics based on soil type
  const soilCharacteristics = getSoilCharacteristics();
  const nutrientLevels = getNutrientLevels();
  const soilHealthIndicators = getSoilHealthIndicators();
  const agriculturalPotential = getAgriculturalPotential();
  const managementRecommendations = getManagementRecommendations();
  
  return (
    <Box sx={{ mt: 2 }}>
      <Typography variant="h6" gutterBottom>
        Comprehensive Soil Analysis
      </Typography>
      
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Typography>Physical Properties</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                Soil Texture
              </Typography>
              <Typography variant="body2" paragraph>
                {soilCharacteristics.texture}
              </Typography>
              
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                Soil Structure
              </Typography>
              <Typography variant="body2" paragraph>
                {soilCharacteristics.structure}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                Water Retention
              </Typography>
              <Typography variant="body2" paragraph>
                {soilCharacteristics.waterRetention}
              </Typography>
              
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                Drainage & Aeration
              </Typography>
              <Typography variant="body2" paragraph>
                {soilCharacteristics.drainage}. {soilCharacteristics.aeration}.
              </Typography>
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>
      
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Typography>Nutrient Analysis</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            {Object.entries(nutrientLevels).map(([nutrient, data]) => (
              <Grid item xs={12} sm={6} key={nutrient}>
                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                    <Typography variant="subtitle2" sx={{ textTransform: 'capitalize' }}>
                      {nutrient.replace(/([A-Z])/g, ' $1').trim()}
                    </Typography>
                    <Typography variant="body2">{data.level}</Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={data.value} 
                    sx={{ 
                      height: 8, 
                      borderRadius: 2,
                      bgcolor: 'grey.200',
                      '& .MuiLinearProgress-bar': {
                        bgcolor: data.value > 70 ? 'success.main' : data.value > 40 ? 'warning.main' : 'error.main',
                      }
                    }} 
                  />
                </Box>
              </Grid>
            ))}
          </Grid>
        </AccordionDetails>
      </Accordion>
      
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Typography>Soil Health Indicators</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            {Object.entries(soilHealthIndicators).map(([indicator, value]) => (
              <Grid item xs={12} sm={6} key={indicator}>
                <Box sx={{ mb: 1.5 }}>
                  <Typography variant="subtitle2" sx={{ textTransform: 'capitalize', mb: 0.5 }}>
                    {indicator.replace(/([A-Z])/g, ' $1').trim()}:
                  </Typography>
                  <Typography variant="body2">
                    {value}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </AccordionDetails>
      </Accordion>
      
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Typography>Agricultural Potential</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            {Object.entries(agriculturalPotential).map(([aspect, value]) => (
              <Grid item xs={12} key={aspect}>
                <Box sx={{ mb: 1.5 }}>
                  <Typography variant="subtitle2" sx={{ textTransform: 'capitalize', mb: 0.5 }}>
                    {aspect.replace(/([A-Z])/g, ' $1').trim()}:
                  </Typography>
                  <Typography variant="body2">
                    {value}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </AccordionDetails>
      </Accordion>
      
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Typography>Management Recommendations</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box component="ul" sx={{ pl: 2, mt: 0 }}>
            {managementRecommendations.map((recommendation, index) => (
              <Typography component="li" key={index} variant="body2" sx={{ mb: 1 }}>
                {recommendation}
              </Typography>
            ))}
          </Box>
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default DetailedSoilAnalysis; 