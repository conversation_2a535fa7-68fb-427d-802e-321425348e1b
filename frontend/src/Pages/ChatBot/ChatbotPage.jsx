import React, { useState, useEffect, useRef, useCallback } from "react";
import ReactMarkdown from "react-markdown";
import { useNavigate } from "react-router-dom";
import SpeechService from "../../utils/SpeechService";
import ImageAnalysisHistory from "./ImageAnalysisHistory";
import axios from "axios";
import AddIcon from "@mui/icons-material/Add";
import {
  Box,
  Container,
  AppBar,
  Toolbar,
  IconButton,
  Typography,
  TextField,
  Button,
  List,
  ListItem,
  Paper,
  CircularProgress,
  Menu,
  MenuItem,
  Grid,
  Divider,
  Card,
  CardContent,
  CardMedia,
  Tab,
  Tabs,
  Chip,
  Tooltip,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import SendIcon from "@mui/icons-material/Send";
import ImageIcon from "@mui/icons-material/Image";
import LanguageIcon from "@mui/icons-material/Language";
import HistoryIcon from "@mui/icons-material/History";
import DeleteIcon from "@mui/icons-material/Delete";
import HelpOutlineIcon from "@mui/icons-material/HelpOutline";
import SearchIcon from "@mui/icons-material/Search";
import AnalysisIcon from "@mui/icons-material/BiotechOutlined";
import ChatIcon from "@mui/icons-material/ChatOutlined";
import HistoryToggleOffIcon from "@mui/icons-material/HistoryToggleOff";
import MicIcon from "@mui/icons-material/Mic";
import StopIcon from "@mui/icons-material/Stop";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import PauseIcon from "@mui/icons-material/Pause";
import VolumeUpIcon from "@mui/icons-material/VolumeUp";
import DeleteForeverIcon from "@mui/icons-material/DeleteForever";
import VisibilityIcon from "@mui/icons-material/Visibility";
import CalendarMonth from "@mui/icons-material/CalendarMonth";
import Download from "@mui/icons-material/Download";
import chatbotIcon from "../../assets/chatbot.png";
import { useAuth } from "../../contexts/AuthContext";
import { useLanguage } from "../../contexts/LanguageContext";
import { getTranslation } from "../../translations";
import { useTranslation } from "react-i18next";

const ChatbotPage = () => {
  const navigate = useNavigate();
  const { currentUser, loading: authLoading } = useAuth();
  
  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !currentUser) {
      console.log("User not authenticated, redirecting to login");
      navigate("/login");
    }
  }, [currentUser, authLoading, navigate]);
  
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [imageUpload, setImageUpload] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [languageMenuAnchor, setLanguageMenuAnchor] = useState(null);
  const [followupMenuAnchor, setFollowupMenuAnchor] = useState(null);
  const [followupQuestions, setFollowupQuestions] = useState([]);
  const [contextId, setContextId] = useState(Date.now().toString());
  const [activeTab, setActiveTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [chatHistory, setChatHistory] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [analysisHistory, setAnalysisHistory] = useState([]);
  const [isRecording, setIsRecording] = useState(false);
  const [audioChunks, setAudioChunks] = useState([]);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [messageToRead, setMessageToRead] = useState(null);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 600);
  const [userState, setUserState] = useState("");
  // Use global language context instead of local state
  const { selectedLanguage, setSelectedLanguage } = useLanguage();
  const [currentUserDetails, setCurrentUserDetails] = useState({}); //Here userPhoneNumber and userId is stored
  const [isNewChat, setIsNewChat] = useState(false);

  const chatContainerRef = useRef(null);
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);
  const shouldAutoScrollRef = useRef(true); // New ref to track if auto-scrolling should happen
  const backupRestoredRef = useRef(false); // Ref to track if backup has been restored
  const { t } = useTranslation();

  useEffect(() => {
    if (currentUser?.phoneNumber && currentUser?.id) {
      setCurrentUserDetails({
        phoneNumber: currentUser.phoneNumber,
        id: currentUser.id,
      });
    }
  }, [currentUser]);
  
  // Enhanced scrollToBottom function that respects user scrolling
  const scrollToBottom = (immediate = false) => {
    // Only auto-scroll if the flag is true
    if (shouldAutoScrollRef.current) {
      // Use an immediate scroll behavior for initial load to prevent flickering
      const behavior = immediate ? "auto" : "smooth";
      
      if (chatContainerRef.current) {
        // Use direct DOM scrolling as the primary method - more reliable
        chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
      }
      
      // As a backup, also try the scrollIntoView if the element exists
      if (messagesEndRef.current) {
        try {
          messagesEndRef.current.scrollIntoView({ behavior });
        } catch (err) {
          console.error('Error with scrollIntoView:', err);
          // Fallback to direct scrolling again if needed
          if (chatContainerRef.current) {
            chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
          }
        }
      }
    }
  };
  
  // Add handler for when user scrolls manually
  const handleScroll = () => {
    if (!chatContainerRef.current) return;
    
    const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
    const isScrolledNearBottom = scrollHeight - scrollTop - clientHeight < 100;
    
    // If user scrolled up, disable auto-scrolling
    // If user scrolled to near bottom, enable auto-scrolling again
    shouldAutoScrollRef.current = isScrolledNearBottom;
  };
  
  // Add a function to restore messages from backup if needed, with strict checking
  const restoreMessagesFromBackup = useCallback(() => {
    try {
      // First, clear localStorage to prevent persistent image issues
      localStorage.removeItem('chatMessagesBackup');
      
      // Then, restore only explicitly saved image messages if needed
      const backupMessages = localStorage.getItem('chatMessagesBackup');
      if (!backupMessages) {
        console.log('No backup messages found');
        return false;
      }
      
      let parsedMessages = [];
      try {
        parsedMessages = JSON.parse(backupMessages);
        
        // Check if backup is corrupt or invalid
        if (!Array.isArray(parsedMessages)) {
          console.log('Invalid backup format, clearing backup');
          localStorage.removeItem('chatMessagesBackup');
          return false;
        }
      } catch (e) {
        console.error('Failed to parse backup messages, clearing backup');
        localStorage.removeItem('chatMessagesBackup');
        return false;
      }
      
      // Return false if we don't have enough context to restore properly
      if (!parsedMessages.length || !messages.length) {
        return false;
      }
      
      // Only proceed if current messages don't already have attachments
      const currentHasAttachments = messages.some(msg => msg.attachment && msg.attachment.url);
      if (currentHasAttachments) {
        return false;
      }
      
      // Don't restore anything, it's better to start fresh
      return false;
    } catch (err) {
      console.error('Error restoring messages from backup:', err);
      return false;
    }
  }, [messages]);

  // Add this new useEffect hook
  useEffect(() => {
    const fetchFarmerState = async () => {
      if (currentUserDetails?.phoneNumber) {
        try {
          const response = await axios.get(
            `http://localhost:8000/api/auth/farmer/${currentUserDetails.phoneNumber}`
          );
          if (response.data && response.data.state) {
            setUserState(response.data.state);
            console.log(
              "Updated userState from farmer API:",
              response.data.state
            );
          }
        } catch (error) {
          console.error("Error fetching farmer state:", error);
        }
      }
    };

    fetchFarmerState();
  }, [currentUserDetails?.phoneNumber]);

  // useEffect(() => {
  //   if(currentUserDetails?.phoneNumber && currentUserDetails?.id)
  //   {
  //     console.log("currentUserDetails:", currentUserDetails);
  //   }
  // }, [currentUserDetails]);

  const supportedLanguages = [
    "english",
    "hindi",
    "telugu",
    "tamil",
    "kannada",
    "malayalam",
    "punjabi",
    "bengali",
    "gujarati",
    "marathi",
  ];

  const fullLanguages = [
    { code: "en", name: "English" },
    { code: "hi", name: "हिंदी (Hindi)" },
    { code: "te", name: "తెలుగు (Telugu)" },
    { code: "ta", name: "தமிழ் (Tamil)" },
    { code: "kn", name: "ಕನ್ನಡ (Kannada)" },
    { code: "ml", name: "മലയാളം (Malayalam)" },
    { code: "pa", name: "ਪੰਜਾਬੀ (Punjabi)" },
    { code: "bn", name: "বাংলা (Bengali)" },
    { code: "gu", name: "ગુજરાતી (Gujarati)" },
    { code: "mr", name: "मराठी (Marathi)" },
  ];

  const stateLanguageMap = {
    "andhra pradesh": "te",
    "tamil nadu": "ta",
    karnataka: "kn",
    kerala: "ml",
    " punjab": "pa",
    "west bengal": "bn",
    " gujarat": "gu",
    maharashtra: "mr",
    // Add more mappings as needed
  };

  const getLanguageOptionsByState = (userState) => {
    const defaultLangCodes = ["en", "hi"];
    const stateLangCode = stateLanguageMap[userState?.toLowerCase()];
    const uniqueLangCodes = stateLangCode
      ? [...new Set([...defaultLangCodes, stateLangCode])]
      : defaultLangCodes;
    return fullLanguages.filter((lang) => uniqueLangCodes.includes(lang.code));
  };

  //languageOptions state
  const [languageOptions, setLanguageOptions] = useState(
    getLanguageOptionsByState("")
  );

  // Then add a useEffect that updates when userState changes
  useEffect(() => {
    // Calculate language options based on current userState
    const options = getLanguageOptionsByState(userState);

    // Update languageOptions state
    setLanguageOptions(options);

    // No longer auto-override selectedLanguage - use global language context instead
    console.log("Language options are: ", options);
  }, [userState]);
  
  // Add a useEffect to check for and restore messages with attachments ONLY on component mount
  useEffect(() => {
    // Only try to restore once on initial mount, not on message changes
    if (!backupRestoredRef.current && messages.length > 0 && !messages.some(msg => msg.attachment)) {
      restoreMessagesFromBackup();
      backupRestoredRef.current = true;
    }
  }, [messages, restoreMessagesFromBackup]); // Run on mount and when messages change, but internal check prevents multiple runs

  // Fetch chat history when user is authenticated
  useEffect(() => {
    console.log("useEffect triggered - currentUser:", currentUser);
    console.log("authLoading:", authLoading);
    console.log("currentUser?.id:", currentUser?.id);
    console.log("currentUser?.phoneNumber:", currentUser?.phoneNumber);
    
    // Only proceed if authentication is complete
    if (authLoading) {
      console.log("Authentication still loading, waiting...");
      return;
    }
    
    if (currentUser?.id && currentUser?.phoneNumber) {
      console.log("User authenticated, fetching chat history and analysis history");
      fetchChatHistory();
      fetchImageAnalysisHistory();
    } else {
      console.log("User not authenticated yet, skipping data fetch");
      console.log("Available localStorage data:");
      console.log("user:", localStorage.getItem("user"));
      console.log("token:", localStorage.getItem("token") ? "Present" : "Missing");
    }
  }, [currentUser?.id, currentUser?.phoneNumber, authLoading]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Handle window resize for responsive design
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 600);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Add keyboard shortcut for back button (Escape key)
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape") {
        navigate("/dashboard");
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [navigate]);

  // Helper function to process messages with attachments
  const processMessagesWithAttachments = (messages) => {
    if (!messages || !Array.isArray(messages)) return [];
    
    return messages.map(msg => {
      // If message has attachments array (server format)
      if (msg.attachments && Array.isArray(msg.attachments) && msg.attachments.length > 0) {
        // Convert server format to client format
        return {
          ...msg,
          attachment: {
            type: msg.attachments[0].type,
            url: msg.attachments[0].url,
            name: msg.attachments[0].name,
            analysisResult: msg.attachments[0].analysisResult
          }
        };
      }
      // If message has attachment object already but might need url preservation
      else if (msg.attachment && msg.attachment.type === "image") {
        // Ensure the URL is preserved correctly
        const imageUrl = msg.attachment.url;
        
        // Keep object reference intact for other properties
        return {
          ...msg,
          attachment: {
            ...msg.attachment,
            url: imageUrl // Ensure URL is maintained exactly as is
          }
        };
      }
      return msg;
    });
  };
  
  // Messages are now stored in database, no need for localStorage backup restoration
  // const tryRestoreAttachmentsFromBackup = () => {
  //   try {
  //     const backupMessages = localStorage.getItem('chatMessagesBackup');
  //     if (backupMessages && messages.length > 0) {
  //       const parsedBackup = JSON.parse(backupMessages);
  //       
  //       // Check if backup has same number of messages and same text content
  //       // If so, we'll use attachment data from backup where needed
  //       if (parsedBackup.length === messages.length) {
  //         const updatedMessages = messages.map((msg, index) => {
  //           const backupMsg = parsedBackup[index];
  //           // If both have same text and timestamp but current message is missing attachment
  //           if (msg.text === backupMsg.text && !msg.attachment && backupMsg.attachment) {
  //             console.log('Restoring attachment from backup for message:', index);
  //             return {
  //               ...msg,
  //               attachment: backupMsg.attachment
  //             };
  //           }
  //           return msg;
  //         });
  //         
  //         // Only update if we restored some attachments
  //         if (JSON.stringify(updatedMessages) !== JSON.stringify(messages)) {
  //           console.log('Restored attachments from backup');
  //           setMessages(updatedMessages);
  //         }
  //       }
  //     }
  //   } catch (err) {
  //     console.error('Error trying to restore attachments from backup:', err);
  //   }
  // };
  
  // Messages are now stored in database, no need for localStorage backup
  // const saveMessagesToLocalStorage = (msgs) => {
  //   try {
  //     if (msgs && msgs.length > 0 && msgs.some(msg => msg.attachment)) {
  //       console.log('Saving messages with attachments to localStorage');
  //       
  //       // Process the messages to ensure they're serializable
  //       const messagesToSave = msgs.map(msg => {
  //         // Deep clone message first
  //         const clonedMsg = JSON.parse(JSON.stringify({
  //           ...msg,
  //           // Remove any circular references or non-serializable data
  //           _id: undefined,
  //           __v: undefined
  //         }));
  //           
  //           // Handle attachments specifically since they might contain DataURLs
  //           if (msg.attachment && msg.attachment.type === 'image') {
  //             clonedMsg.attachment = {
  //               ...clonedMsg.attachment,
  //               // Ensure URL is preserved as is
  //               url: msg.attachment.url
  //           }
  //         });
  //           
  //         // Save to localStorage
  //         localStorage.setItem('chatMessagesBackup', JSON.stringify(messagesToSave));
  //       }
  //     } catch (err) {
  //       console.error('Error saving messages to localStorage:', err);
  //     }
  //   };

  const fetchChatHistory = async () => {
    try {
      setIsLoading(true);
      console.log("Fetching chat history...");
      console.log("Current user:", currentUser);
      console.log("Token:", localStorage.getItem("token") ? "Present" : "Missing");

      const response = await fetch("/api/chat/history", {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log("Chat history response:", data);
        console.log("Response status:", response.status);

        if (data.success) {
          // Check if we have the new date-organized format
          if (data.data.byDate) {
            console.log("Using date-organized chat history");
            // Store the organized chat history
            setChatHistory(data.data.byDate);

            // Get today's date
            // const today = new Date().toISOString().split("T")[0];
            // Get today's date with IST adjustment
            const today = new Date();
            today.setMinutes(today.getMinutes() + 330); // Add 5 hours and 30 minutes for IST
            const todayStr = today.toISOString().split("T")[0];

            // Get today's chats if available
            const todayChats = data.data.byDate[todayStr] || [];

            // Only load messages if we're not starting a new chat
            if (!isNewChat) {
              // If we have chats for today, use the most recent one
              if (todayChats.length > 0) {
                // Process messages to ensure image attachments are properly formatted
                const processedMessages = processMessagesWithAttachments(todayChats[0].messages);
                setMessages(processedMessages);

                // If there's context data, set it
                if (todayChats[0].contextData) {
                  setContextId(
                    todayChats[0].contextData.contextId || Date.now().toString()
                  );
                }
              } else {
                // If no chats for today, use the most recent chat from any date
                const allChats = data.data.allChats;
                if (allChats && allChats.length > 0) {
                  // Process messages to ensure image attachments are properly formatted
                  const processedMessages = processMessagesWithAttachments(allChats[0].messages);
                  setMessages(processedMessages);

                  // If there's context data, set it
                  if (allChats[0].contextData) {
                    setContextId(
                      allChats[0].contextData.contextId || Date.now().toString()
                    );
                  }
                } else {
                  // If no chat history at all, create a default context ID
                  setContextId(Date.now().toString());
                }
              }
            } else {
              // For new chat, don't load any messages, just ensure we have a context ID
              if (!contextId) {
                setContextId(Date.now().toString());
              }
            }
          } else if (data.data.length > 0) {
            // Legacy format - not date organized
            console.log("Using legacy chat history format");
            // Get the most recent chat history
            const recentChat = data.data[0];
            // Process messages to ensure image attachments are properly formatted
            const processedMessages = processMessagesWithAttachments(recentChat.messages);
            setMessages(processedMessages);
            setChatHistory(data.data);

            // If there's context data, set it
            if (recentChat.contextData) {
              setContextId(
                recentChat.contextData.contextId || Date.now().toString()
              );
            }
          } else {
            // If no chat history, create a default context ID
            setContextId(Date.now().toString());
          }
          
          // Messages are now stored in database, no need for localStorage backup restoration
          // setTimeout(() => {
          //   tryRestoreAttachmentsFromBackup();
          // }, 500);
        } else {
          // If no chat history, create a default context ID
          setContextId(Date.now().toString());
        }
      } else {
        console.error("Failed to fetch chat history:", response.status);
        console.error("Response text:", await response.text());
        // Fallback to empty state
        setContextId(Date.now().toString());
      }
    } catch (error) {
      console.error("Error fetching chat history:", error);
      // Fallback to empty state
      setContextId(Date.now().toString());
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fetch combined chat history by context ID or date
  const fetchCombinedChatHistory = async (contextId, dateKey) => {
    try {
      console.log("Fetching combined chat history for context:", contextId, "date:", dateKey);
      
      let apiUrl = "/api/chat/history";
      
      // Add query parameters if provided
      const params = new URLSearchParams();
      if (contextId) params.append("contextId", contextId);
      if (dateKey) params.append("date", dateKey);
      
      if (params.toString()) {
        apiUrl += `?${params.toString()}`;
      }
      
      const response = await fetch(apiUrl, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log("Combined chat history response:", data);

        if (data.success) {
          // Process messages
          let targetMessages = [];
          let targetHistory = {};
          
          // Handle different response formats
          if (data.data.byDate) {
            targetHistory = data.data.byDate;
            
            // If we have a specific date, get those chats
            if (dateKey && data.data.byDate[dateKey]) {
              const dateChats = data.data.byDate[dateKey];
              
              // Find the specific chat by context ID if provided
              if (contextId) {
                const specificChat = dateChats.find(
                  chat => chat.contextData?.contextId === contextId
                );
                
                if (specificChat) {
                  targetMessages = processMessagesWithAttachments(specificChat.messages);
                }
              } 
              // Otherwise just get the first chat for that date
              else if (dateChats.length > 0) {
                targetMessages = processMessagesWithAttachments(dateChats[0].messages);
              }
            } 
            // If no specific date or context, get the most recent chat
            else if (data.data.allChats && data.data.allChats.length > 0) {
              targetMessages = processMessagesWithAttachments(data.data.allChats[0].messages);
            }
          } 
          // Legacy format handling
          else if (Array.isArray(data.data) && data.data.length > 0) {
            targetHistory = data.data;
            
            // Find by context ID if provided
            if (contextId) {
              const specificChat = data.data.find(
                chat => chat.contextData?.contextId === contextId
              );
              
              if (specificChat) {
                targetMessages = processMessagesWithAttachments(specificChat.messages);
              } else {
                targetMessages = processMessagesWithAttachments(data.data[0].messages);
              }
            } else {
              targetMessages = processMessagesWithAttachments(data.data[0].messages);
            }
          }
          
          // Try to restore missing image attachments from localStorage backup
          try {
            const backupMessages = localStorage.getItem('chatMessagesBackup');
            if (backupMessages && targetMessages.length > 0) {
              const parsedBackup = JSON.parse(backupMessages);
              
              // Check if we have matching messages with attachments in the backup
              const hasMatchingMessages = parsedBackup.some(backupMsg => 
                targetMessages.some(targetMsg => 
                  targetMsg.text === backupMsg.text && 
                  !targetMsg.attachment && 
                  backupMsg.attachment
                )
              );
              
              if (hasMatchingMessages) {
                console.log('Found matching messages with attachments in backup');
                // Restore attachments for matching messages
                targetMessages = targetMessages.map(msg => {
                  // Try to find a matching message in backup with attachment
                  const matchingBackupMsg = parsedBackup.find(bMsg => 
                    bMsg.text === msg.text && bMsg.attachment
                  );
                  
                  if (matchingBackupMsg && !msg.attachment) {
                    console.log('Restoring attachment for message:', msg.text.substring(0, 30));
                    return {
                      ...msg,
                      attachment: matchingBackupMsg.attachment
                    };
                  }
                  return msg;
                });
              }
            }
          } catch (err) {
            console.error('Error trying to restore attachments in fetchCombinedChatHistory:', err);
          }
          
          return {
            messages: targetMessages,
            chatHistory: targetHistory,
          };
        } else {
          console.error("Failed to fetch combined chat history:", data.message);
          return { messages: [], chatHistory: {} };
        }
      } else {
        console.error("Failed to fetch combined chat history:", response.status);
        return { messages: [], chatHistory: {} };
      }
    } catch (error) {
      console.error("Error fetching combined chat history:", error);
      return { messages: [], chatHistory: {} };
    }
  };

  const fetchImageAnalysisHistory = async () => {
    try {
      console.log("Fetching image analysis history");
      console.log("Current user for analysis:", currentUser);
      console.log("Token for analysis:", localStorage.getItem("token") ? "Present" : "Missing");

      // Call the dedicated endpoint for image analysis history
      const response = await fetch("/api/chat/analysis-history", {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log("Image analysis history response:", data);

        if (data.success) {
          // Update the analysis history state
          setAnalysisHistory(data.data.analyses || []);
        }
      } else {
        console.error(
          "Failed to fetch image analysis history:",
          response.status
        );
        // Fallback to empty state if needed
      }
    } catch (error) {
      console.error("Error fetching image analysis history:", error);
    }
  };

  const searchChatHistory = async () => {
    if (!searchQuery.trim()) {
      // If search query is empty, fetch the full chat history
      fetchChatHistory();
      return;
    }

    try {
      setIsSearching(true);
      console.log("Searching chat history");

      // Call the backend API to search chat history
      console.log("My search query: ", searchQuery);
      const response = await fetch(
        `/api/chat/search?query=${encodeURIComponent(searchQuery)}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        console.log("Search results:", data);

        if (data.success) {
          if (data.data.byDate) {
            // If the backend already organized by date
            console.log("Using date-organized search results");
            setChatHistory(data.data.byDate);
          } else if (Array.isArray(data.data) && data.data.length > 0) {
            // If we got an array of results, organize them by date
            console.log("Organizing search results by date");
            const chatsByDate = {};

            data.data.forEach((chat) => {
              // Use chatDate if available, otherwise use createdAt
              const dateObj = chat.chatDate
                ? new Date(chat.chatDate)
                : new Date(chat.createdAt);
              const dateKey = dateObj.toISOString().split("T")[0]; // YYYY-MM-DD format

              if (!chatsByDate[dateKey]) {
                chatsByDate[dateKey] = [];
              }

              chatsByDate[dateKey].push(chat);
            });

            // Sort chats within each date
            Object.keys(chatsByDate).forEach((dateKey) => {
              chatsByDate[dateKey].sort(
                (a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)
              );
            });

            setChatHistory(chatsByDate);
          } else {
            // If search returns no results, show empty results
            setChatHistory({});
          }
        } else {
          // If search fails, show empty results
          setChatHistory({});
        }
      } else {
        console.error("Failed to search chat history:", response.status);
        // Fallback to client-side filtering
        if (typeof chatHistory === "object" && !Array.isArray(chatHistory)) {
          // If chatHistory is already date-organized
          const filteredHistory = {};

          Object.keys(chatHistory).forEach((dateKey) => {
            const filteredChats = chatHistory[dateKey].filter((chat) => {
              return chat.messages.some((msg) =>
                msg.text.toLowerCase().includes(searchQuery.toLowerCase())
              );
            });

            if (filteredChats.length > 0) {
              filteredHistory[dateKey] = filteredChats;
            }
          });

          setChatHistory(filteredHistory);
        } else if (Array.isArray(chatHistory)) {
          // If chatHistory is an array (legacy format)
          const filteredChats = chatHistory.filter((chat) => {
            return chat.messages.some((msg) =>
              msg.text.toLowerCase().includes(searchQuery.toLowerCase())
            );
          });

          // Organize filtered results by date
          const chatsByDate = {};

          filteredChats.forEach((chat) => {
            const dateObj = chat.chatDate
              ? new Date(chat.chatDate)
              : new Date(chat.createdAt);
            const dateKey = dateObj.toISOString().split("T")[0];

            if (!chatsByDate[dateKey]) {
              chatsByDate[dateKey] = [];
            }

            chatsByDate[dateKey].push(chat);
          });

          setChatHistory(chatsByDate);
        }
      }
    } catch (error) {
      console.error("Error searching chat history:", error);
      // Show empty results on error
      setChatHistory({});
    } finally {
      setIsSearching(false);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() && !imageUpload) return;

    // Enable auto-scrolling when user sends a new message
    shouldAutoScrollRef.current = true;
    
    // If there's an image, handle image analysis
    if (imageUpload) {
      await handleImageAnalysis();
      return;
    }
    
    // Messages are now stored in database, no need for localStorage backup restoration
    // const hasAttachments = messages.some(msg => msg.attachment);
    // if (!hasAttachments) {
    //   const restored = restoreMessagesFromBackup();
    //   if (restored) {
    //     console.log('Restored messages with attachments before sending new message');
    //   }
    // }

    const newMessage = {
      text: inputMessage,
      sender: "user",
      timestamp: new Date(),
      language: selectedLanguage,
    };

    setMessages((prev) => [...prev, newMessage]);
    setInputMessage("");
    // Clear image states when sending text messages to prevent images showing with every message
    setImageUpload(null);
    setImagePreview(null);
    // Messages are now stored in database, no need for localStorage cleanup
    setIsLoading(true);

    try {
      console.log("Processing message with backend");

      // Process the message with Azure OpenAI
      const response = await fetch("/api/chat/process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({
          text: inputMessage,
          language: selectedLanguage,
          contextId,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log("Process message response:", data);

        if (data.success) {
          const botResponse = {
            text: data.data.response,
            sender: "bot",
            timestamp: new Date(),
            language: selectedLanguage,
            followupQuestions: data.data.followupQuestions,
          };
          console.log("Bot response:", botResponse);
          // Update state with both messages at once
          setMessages((prev) => [...prev, botResponse]);
          setFollowupQuestions(botResponse.followupQuestions);
          
          // Reset new chat flag after first message
          if (isNewChat) {
            setIsNewChat(false);
          }
          
          // Don't fetch chat history as it might overwrite messages with attachments
          // Instead, we'll let the messages stay in the current state
        } else {
          setMessages((prev) => [...prev, newMessage]);
          fallbackMessageProcessing(newMessage);
        }
          } else {
      console.error("Failed to process message:", response.status);
      // We've already added the user message to the messages array above,
      // so we don't need to add it again here
      fallbackMessageProcessing(newMessage);
    }
  } catch (error) {
    console.error("Error sending message:", error);
    // We've already added the user message to the messages array above,
    // so we don't need to add it again here
    fallbackMessageProcessing(newMessage);
  } finally {
    setIsLoading(false);
  }
  };

  // Fallback function for message processing when API fails
  const fallbackMessageProcessing = (userMessage) => {
    console.log("Using fallback message processing");

    // Generate a response based on the input
    let responseText =
      "I'm your Quamin agricultural assistant. I can help with crop diseases, market prices, farming techniques, and government schemes for agriculture. How can I assist you today?";
    let followups = [
      "How do I identify crop diseases?",
      "What are current market prices for crops?",
      "Tell me about government schemes for farmers",
    ];

    // Check if query is non-agricultural
    const nonAgTopics = [
      "politics",
      "movies",
      "sports",
      "entertainment",
      "celebrity",
      "stock market",
      "crypto",
      "dating",
      "games",
    ];
    const lowerInput = userMessage.text.toLowerCase();

    // Check if asking about who created the chatbot
    if (
      lowerInput.includes("who created you") ||
      lowerInput.includes("who made you") ||
      lowerInput.includes("who developed you") ||
      lowerInput.includes("who built you") ||
      lowerInput.includes("who designed you") ||
      lowerInput.includes("who are you") ||
      lowerInput.includes("about you") ||
      lowerInput.includes("your creator")
    ) {
      responseText =
        "I have been developed by Quamin Tech Solutions LLP, a company focused on creating innovative agricultural technology solutions to empower farmers and agricultural professionals.";
      followups = [
        "What agricultural services do you provide?",
        "How can you help with crop management?",
        "Tell me about crop disease identification",
      ];
    }
    // Check if the query contains non-agricultural topics
    else if (nonAgTopics.some((topic) => lowerInput.includes(topic))) {
      responseText =
        "I'm specialized in agricultural topics only. I can help you with crop diseases, farming techniques, livestock management, market prices, and government schemes for agriculture. Please ask me about these topics instead.";
      followups = [
        "How do I improve crop yield?",
        "Tell me about livestock management",
        "What government schemes are available for farmers?",
      ];
    }
    // Agricultural topic responses
    else if (
      lowerInput.includes("weather") ||
      lowerInput.includes("forecast")
    ) {
      responseText =
        "The weather forecast for your area shows sunny conditions with temperatures around 28\u00b0C for the next 3 days. There is a 20% chance of light rain on Friday. Would you like me to explain how this weather might affect your crops?";
      followups = [
        "How does this weather affect my crops?",
        "What precautions should I take for rain?",
        "Best crops for current weather conditions",
      ];
    } else if (
      lowerInput.includes("soil") ||
      lowerInput.includes("fertilizer")
    ) {
      responseText =
        "Based on soil health data, I recommend using nitrogen-rich fertilizers. Your soil pH is slightly acidic (6.2), which is good for most crops. Consider crop rotation to improve soil health. Would you like me to provide more detailed recommendations or summarize some research on this topic?";
      followups = [
        "Tell me more about nitrogen fertilizers",
        "How do I improve soil pH?",
        "Best crops for acidic soil",
      ];
    } else if (lowerInput.includes("disease") || lowerInput.includes("pest")) {
      responseText =
        "To identify crop diseases, look for discoloration, spots, wilting, or unusual growth patterns. You can upload an image for analysis. Common treatments include organic fungicides like neem oil or chemical options depending on severity. Would you like specific information about a particular crop disease?";
      followups = [
        "How to prevent crop diseases",
        "Organic vs chemical treatments",
        "Upload an image for disease analysis",
      ];
    } else if (
      lowerInput.includes("market") ||
      lowerInput.includes("price") ||
      lowerInput.includes("sell")
    ) {
      responseText =
        "Current market prices for major crops show wheat at ₹2,015/quintal, rice at ₹2,040/quintal, and maize at ₹1,962/quintal. Prices are expected to rise by 5-8% in the next month based on demand forecasts. Would you like me to provide more detailed market analysis or link to official market data sources?";
      followups = [
        "Best time to sell my crops",
        "Market trends for specific crops",
        "How to get better prices",
      ];
    } else if (
      lowerInput.includes("scheme") ||
      lowerInput.includes("government") ||
      lowerInput.includes("subsidy")
    ) {
      responseText =
        "Several government schemes are available for farmers including PM-KISAN (₹6,000 annual income support), Soil Health Card Scheme (free soil testing), and PMFBY (crop insurance). Would you like me to explain how to apply for these schemes or provide more details about a specific program?";
      followups = [
        "How to apply for PM-KISAN",
        "Benefits of Soil Health Card",
        "Eligibility for crop insurance",
      ];
    } else if (
      lowerInput.includes("livestock") ||
      lowerInput.includes("cattle") ||
      lowerInput.includes("animal")
    ) {
      responseText =
        "For livestock management, focus on proper nutrition, regular health check-ups, and appropriate housing. The National Livestock Mission provides subsidies for breed improvement and feed development. Would you like specific information about cattle, poultry, or other livestock?";
      followups = [
        "Cattle disease prevention",
        "Livestock feed recommendations",
        "Government support for animal husbandry",
      ];
    }

    const botResponse = {
      text: responseText,
      sender: "bot",
      timestamp: new Date(),
      language: selectedLanguage,
      followupQuestions: followups,
    };

    setMessages((prev) => [...prev, botResponse]);
    setFollowupQuestions(botResponse.followupQuestions);
    setIsLoading(false);
  };

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      setImageUpload(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleImageAnalysis = async () => {
    if (!imageUpload) return;

    setIsLoading(true);

    // Keep the existing conversation - don't clear messages
    // We'll add the image analysis to the current chat context
    
    // Add user message with image
    const userMessage = {
      text: inputMessage || "Please analyze this image",
      sender: "user",
      timestamp: new Date(),
      language: selectedLanguage,
      attachment: {
        type: "image",
        url: imagePreview, // This is a DataURL from FileReader
        name: imageUpload.name,
        // Store size for reference
        size: imageUpload.size
      },
    };

    // Add the user message to the existing conversation
    setMessages(prevMessages => [...prevMessages, userMessage]);
    setInputMessage("");
    
    // Set up a cleanup function that only clears the image upload controls
    // but preserves the image data in the messages
    const cleanupImageAfterDelay = () => {
      setTimeout(() => {
        // Only clear the image upload control state, not the image data in messages
        setImageUpload(null);
        // Don't clear imagePreview as it might be needed for reference
      }, 2000); // 2-second delay before clearing upload controls
    };

    try {
      console.log(
        "Processing image for analysis:",
        imageUpload.name,
        imageUpload.size,
        "bytes"
      );

      // Create a FormData object to send the image file
      const formData = new FormData();
      formData.append("image", imageUpload);

      if (inputMessage) {
        formData.append("text", inputMessage);
      }

      formData.append("language", selectedLanguage);
      formData.append("contextId", contextId);

      // Call the backend API to analyze the image
      const response = await fetch("/api/chat/analyze-image", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        console.log("Image analysis response:", data);

        if (data.success) {
          const { analysisResult, followupQuestions, message, userMessage } = data.data;
          console.log("Image analysis data:", { analysisResult, message, userMessage });

          // Update the user message to use the server URL for persistence
          const updatedUserMessage = {
            ...userMessage,
            attachment: {
              type: "image",
              url: userMessage.attachments && userMessage.attachments.length > 0 
                ? userMessage.attachments[0].url 
                : imagePreview,
              name: imageUpload.name,
              size: imageUpload.size
            }
          };

          // Create bot response
          const botResponse = {
            text: message.text,
            sender: "bot",
            timestamp: new Date(),
            language: selectedLanguage,
            attachment: {
              type: "image",
              url: message.attachments && message.attachments.length > 0 
                ? message.attachments[0].url 
                : imagePreview,
              analysisResult,
              name: imageUpload.name,
              // Preserve any other attachment data
              ...(message.attachments && message.attachments.length > 0 
                ? message.attachments[0] 
                : {})
            },
            followupQuestions: followupQuestions,
          };

          // Replace the temporary user message with the server version and add bot response
          setMessages((prev) => {
            // Remove the last message (temporary user message) and add both updated messages
            const withoutLastMessage = prev.slice(0, -1);
            return [...withoutLastMessage, updatedUserMessage, botResponse];
          });
          setFollowupQuestions(followupQuestions);

          // Add to analysis history immediately for UI responsiveness
          const newAnalysis = {
              timestamp: new Date(),
            imageUrl: message.attachments && message.attachments.length > 0 
              ? message.attachments[0].url 
              : imagePreview,
            analysis: analysisResult,
            text: message.text
          };
          
          setAnalysisHistory((prev) => [newAnalysis, ...prev]);

          // Also refresh from API to ensure we have the latest data
          await fetchImageAnalysisHistory();
          
          // Reset new chat flag if it was set
          if (isNewChat) {
            setIsNewChat(false);
          }

          // Schedule cleanup after a delay to ensure the image stays visible in chat
          cleanupImageAfterDelay();
          setIsLoading(false);
          return;
        }
      } else {
        // Handle API error responses
        try {
          const errorData = await response.json();
          if (errorData.message && errorData.message.includes("File format not supported")) {
            throw new Error("File format not supported. Please use JPG, JPEG, PNG, or WebP format.");
          } else if (errorData.error && errorData.error.includes("Only image files")) {
            throw new Error("File format not supported. Please use JPG, JPEG, PNG, or WebP format.");
          }
        } catch (jsonError) {
          // If we can't parse the JSON, check the response status
          if (response.status === 500) {
            throw new Error("File format not supported. Please use JPG, JPEG, PNG, or WebP format.");
          }
        }
      }

      // If we get here, the API call failed, so fall back to client-side processing
      console.log("API call failed, using fallback image analysis");

      // Analyze the image to determine crop type and potential diseases
      // This is a mock implementation that would be replaced with actual ML-based analysis
      const cropTypes = [
        "wheat",
        "rice",
        "corn",
        "potato",
        "tomato",
        "cotton",
        "soybean",
        "chickpea",
        "sugarcane",
        "mustard",
      ];
      const diseaseTypes = {
        wheat: [
          "leaf rust",
          "powdery mildew",
          "septoria leaf spot",
          "fusarium head blight",
          "none",
        ],
        rice: [
          "blast",
          "bacterial leaf blight",
          "sheath blight",
          "brown spot",
          "none",
        ],
        corn: [
          "gray leaf spot",
          "northern corn leaf blight",
          "common rust",
          "ear rot",
          "none",
        ],
        potato: [
          "late blight",
          "early blight",
          "black scurf",
          "potato virus Y",
          "none",
        ],
        tomato: [
          "early blight",
          "late blight",
          "septoria leaf spot",
          "bacterial spot",
          "none",
        ],
        cotton: [
          "bacterial blight",
          "verticillium wilt",
          "fusarium wilt",
          "leaf spot",
          "none",
        ],
        soybean: [
          "asian rust",
          "bacterial blight",
          "brown spot",
          "downy mildew",
          "none",
        ],
        chickpea: [
          "ascochyta blight",
          "fusarium wilt",
          "botrytis gray mold",
          "collar rot",
          "none",
        ],
        sugarcane: ["red rot", "smut", "rust", "leaf scald", "none"],
        mustard: [
          "white rust",
          "downy mildew",
          "alternaria blight",
          "powdery mildew",
          "none",
        ],
      };

      // Simulate image analysis by selecting a random crop and disease
      // In a real implementation, this would use computer vision and ML
      const cropIndex = Math.floor(Math.random() * cropTypes.length);
      const identifiedCrop = cropTypes[cropIndex];
      const possibleDiseases = diseaseTypes[identifiedCrop];
      const diseaseIndex = Math.floor(Math.random() * possibleDiseases.length);
      const identifiedDisease = possibleDiseases[diseaseIndex];

      // Generate health status based on disease
      const healthStatus =
        identifiedDisease === "none"
          ? "good"
          : ["leaf spot", "powdery mildew", "bacterial spot"].includes(
              identifiedDisease
            )
          ? "moderate"
          : "poor";

      // Generate confidence level
      const confidence = 0.7 + Math.random() * 0.25;

      // Generate treatment recommendations based on disease
      let recommendations = [];
      let issues = [];

      if (identifiedDisease === "none") {
        issues = ["No significant issues detected"];
        recommendations = [
          "Continue regular monitoring",
          "Maintain current irrigation schedule",
          "Follow standard fertilization program",
        ];
      } else {
        issues = [`${identifiedDisease} infection detected`];

        // Disease-specific recommendations
        if (identifiedDisease.includes("blight")) {
          recommendations = [
            `Apply copper-based fungicide specifically formulated for ${identifiedDisease}`,
            "Improve air circulation by proper spacing between plants",
            "Remove and destroy infected plant parts",
            "Rotate crops to prevent disease buildup in soil",
          ];
        } else if (
          identifiedDisease.includes("rust") ||
          identifiedDisease.includes("mildew")
        ) {
          recommendations = [
            `Apply sulfur or potassium bicarbonate for ${identifiedDisease} control`,
            "Avoid overhead irrigation to keep foliage dry",
            "Ensure proper plant spacing for air circulation",
            "Consider resistant varieties for future planting",
          ];
        } else if (identifiedDisease.includes("wilt")) {
          recommendations = [
            "Remove and destroy infected plants immediately",
            "Implement crop rotation with non-susceptible crops",
            "Use disease-free planting material",
            "Improve soil drainage to prevent root stress",
          ];
        } else {
          recommendations = [
            "Apply appropriate fungicide based on local agricultural extension recommendations",
            "Implement proper crop rotation practices",
            "Improve field sanitation by removing crop debris",
            "Consider resistant varieties for next planting season",
          ];
        }
      }

      // Create analysis result object
      const analysisResult = {
        type: "crop",
        healthStatus: healthStatus,
        identifiedAs: identifiedCrop,
        confidence: confidence,
        disease: identifiedDisease,
        issues: issues,
        recommendations: recommendations,
      };

      // Create intelligent, context-aware followup questions specific to the identified crop and disease
      let followups = [];

      // Disease-specific questions
      if (identifiedDisease !== "none") {
        followups.push(
          `What are the best fungicides for treating ${identifiedDisease} in ${identifiedCrop}?`
        );
        followups.push(
          `How can I prevent ${identifiedDisease} from spreading to other ${identifiedCrop} plants?`
        );
        followups.push(
          `What crop rotation practices help prevent ${identifiedDisease}?`
        );
      } else {
        followups.push(
          `What are common diseases that affect ${identifiedCrop} in my region?`
        );
        followups.push(
          `What preventive measures should I take to keep my ${identifiedCrop} healthy?`
        );
      }

      // Crop-specific questions
      if (identifiedCrop === "wheat") {
        followups.push(
          `What is the optimal fertilizer ratio for ${identifiedCrop}?`
        );
        followups.push(
          `When is the best time to harvest ${identifiedCrop} for maximum yield?`
        );
      } else if (identifiedCrop === "rice") {
        followups.push(
          `What water management practices are best for ${identifiedCrop} cultivation?`
        );
        followups.push(
          `How can I control weeds in my ${identifiedCrop} field without affecting yield?`
        );
      } else if (identifiedCrop === "corn") {
        followups.push(
          `What is the ideal plant spacing for ${identifiedCrop}?`
        );
        followups.push(
          `How can I protect my ${identifiedCrop} from pest damage?`
        );
      } else if (identifiedCrop === "potato") {
        followups.push(
          `What is the best way to store ${identifiedCrop} after harvest?`
        );
        followups.push(
          `How can I improve the size and quality of my ${identifiedCrop} tubers?`
        );
      } else if (identifiedCrop === "tomato") {
        followups.push(
          `What pruning techniques are recommended for ${identifiedCrop} plants?`
        );
        followups.push(
          `How can I prevent blossom end rot in ${identifiedCrop}?`
        );
      } else if (identifiedCrop === "cotton") {
        followups.push(
          `What is the optimal timing for ${identifiedCrop} defoliation?`
        );
        followups.push(
          `How can I manage bollworms in my ${identifiedCrop} field?`
        );
      } else if (identifiedCrop === "soybean") {
        followups.push(
          `What is the optimal planting density for ${identifiedCrop}?`
        );
        followups.push(
          `How can I improve nodulation in my ${identifiedCrop} crop?`
        );
      } else if (identifiedCrop === "chickpea") {
        followups.push(
          `What soil conditions are best for ${identifiedCrop} cultivation?`
        );
        followups.push(
          `How can I manage pod borers in my ${identifiedCrop} crop?`
        );
      } else if (identifiedCrop === "sugarcane") {
        followups.push(
          `What is the best time for ${identifiedCrop} planting in my region?`
        );
        followups.push(
          `How can I improve the sugar content in my ${identifiedCrop}?`
        );
      } else if (identifiedCrop === "mustard") {
        followups.push(
          `What is the optimal fertilizer application for ${identifiedCrop}?`
        );
        followups.push(
          `How can I control aphids in my ${identifiedCrop} crop?`
        );
      }

      // Always add market-related question
      followups.push(
        `What are the current market prices for ${identifiedCrop}?`
      );

      // Select 3 questions randomly to provide variety
      if (followups.length > 3) {
        const selectedQuestions = [];
        while (selectedQuestions.length < 3 && followups.length > 0) {
          const randomIndex = Math.floor(Math.random() * followups.length);
          selectedQuestions.push(followups[randomIndex]);
          followups.splice(randomIndex, 1);
        }
        followups = selectedQuestions;
      }

      // Create detailed analysis text with disease information and research links
      const analysisText = `## Crop Analysis Results

**Crop Identified**: ${analysisResult.identifiedAs} (${Math.round(
        analysisResult.confidence * 100
      )}% confidence)
**Health Status**: ${analysisResult.healthStatus}
**Disease Assessment**: ${
        identifiedDisease === "none"
          ? "No disease detected"
          : identifiedDisease + " detected"
      }

### Issues Detected:
${analysisResult.issues.map((issue) => `- ${issue}`).join("\n")}

### Recommended Actions:
${analysisResult.recommendations.map((rec) => `- ${rec}`).join("\n")}

### Additional Resources:
- [${
        identifiedCrop.charAt(0).toUpperCase() + identifiedCrop.slice(1)
      } Disease Management Guide](https://icar.org.in/crops/${identifiedCrop}/diseases) - [View Summary](#summary-disease)
- [Current Market Prices for ${
        identifiedCrop.charAt(0).toUpperCase() + identifiedCrop.slice(1)
      }](https://agmarknet.gov.in/) - [View Summary](#summary-market)
- [Government Schemes for ${
        identifiedCrop.charAt(0).toUpperCase() + identifiedCrop.slice(1)
      } Farmers](https://agricoop.nic.in/en/schemes) - [View Summary](#summary-schemes)

Would you like me to summarize any specific information from these resources?`;

      // Create bot response
      const botResponse = {
        text: analysisText,
        sender: "bot",
        timestamp: new Date(),
        language: selectedLanguage,
        attachment: {
          type: "image",
          url: imagePreview,
          analysisResult,
        },
        followupQuestions: followups,
      };

      setMessages((prev) => [...prev, botResponse]);
      setFollowupQuestions(followups);

      // Add to analysis history immediately for UI responsiveness
      const newAnalysis = {
          timestamp: new Date(),
        imageUrl: imagePreview,
        analysis: analysisResult,
        text: analysisText
      };
      
      setAnalysisHistory((prev) => [newAnalysis, ...prev]);

      // Also fetch the latest analysis history from the server
      fetchImageAnalysisHistory();
    } catch (error) {
      console.error("Error analyzing image:", error);

      // Check if it's a file format error
      let errorText = "Sorry, I couldn't analyze this image. Please try again with a clearer image.";
      
      if (error.message && error.message.includes("File format not supported")) {
        errorText = "File format not supported. Please use JPG, JPEG, PNG, or WebP format.";
      } else if (error.message && error.message.includes("Only image files")) {
        errorText = "File format not supported. Please use JPG, JPEG, PNG, or WebP format.";
      }

      // Add error message
      const errorMessage = {
        text: errorText,
        sender: "bot",
        timestamp: new Date(),
        language: selectedLanguage,
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      // Schedule cleanup after a delay to ensure the image stays visible in chat
      cleanupImageAfterDelay();
      setIsLoading(false);
      
      // We no longer need this code here since we're using the saveMessagesToLocalStorage 
      // function with the useEffect hook that runs whenever messages change
    }
  };

  const handleFollowupQuestion = (question) => {
    setInputMessage(question);
    setFollowupMenuAnchor(null);
  };

  const handleLanguageChange = (languageCode) => {
    setSelectedLanguage(languageCode);
    setLanguageMenuAnchor(null);
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Function to handle website links and provide summaries
  const handleWebsiteLink = (url, resourceType) => {
    // First, open the URL in a new tab
    window.open(url, "_blank");

    // Then, generate a summary based on the resource type
    let summaryText = "";

    if (url.includes("icar.org.in")) {
      summaryText = `## Disease Management Guide Summary

The Indian Council of Agricultural Research (ICAR) provides comprehensive guides for managing crop diseases. Key points from their guide include:

- Early detection is critical - inspect crops regularly
- Integrated Pest Management (IPM) approaches are recommended
- Both chemical and biological control methods are detailed
- Resistant varieties are listed with their specific traits
- Region-specific recommendations are provided

The full guide contains detailed application rates, timing recommendations, and safety precautions for all treatments.`;
    } else if (url.includes("agmarknet.gov.in")) {
      summaryText = `## Market Price Summary

Agmarknet provides daily price updates for agricultural commodities across India. Current trends show:

- Prices vary significantly by region and quality grade
- Transportation costs impact final prices by 5-15%
- Seasonal variations affect prices by up to 30%
- Direct marketing to consumers can increase profits by 15-25%
- Organic produce commands a 20-40% premium in most markets

The website provides detailed mandi-wise prices, historical trends, and future projections for planning harvests.`;
    } else if (url.includes("agricoop.nic.in")) {
      summaryText = `## Government Schemes Summary

The Ministry of Agriculture & Farmers Welfare offers several beneficial schemes:

- PM-KISAN: ₹6,000 annual income support in three installments
- Soil Health Card: Free soil testing and fertilizer recommendations
- PMFBY: Crop insurance with minimal premium (1.5-5%)
- PMKSY: Irrigation support with subsidies up to 55%
- RKVY: Development funds for infrastructure and capacity building

Eligibility criteria, application procedures, and deadlines for each scheme are available on the website.`;
    }

    if (summaryText) {
      // Add the summary as a new bot message
      const summaryMessage = {
        text: summaryText,
        sender: "bot",
        timestamp: new Date(),
        language: selectedLanguage,
        followupQuestions: [
          "How do I apply for these schemes?",
          "Which scheme is best for small farmers?",
          "Are there any new schemes announced recently?",
        ],
      };

      setMessages((prev) => [...prev, summaryMessage]);
      setFollowupQuestions(summaryMessage.followupQuestions);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    
    // Clear image states when switching tabs
    if (imageUpload || imagePreview) {
      setImageUpload(null);
      setImagePreview(null);
    }
  };

  // Text-to-speech functions using the shared SpeechService
  const speakText = (text, messageId) => {
    // Stop any current speech
    stopSpeaking();

    // Start speaking and update UI state
    const success = SpeechService.speak(text, selectedLanguage, () => {
      // Callback when speech ends
      setIsSpeaking(false);
      setMessageToRead(null);
    });

    if (success) {
      setIsSpeaking(true);
      setMessageToRead(messageId);
    }
  };

  // const pauseSpeaking = () => {
  //   const paused = SpeechService.pause();
  //   if (paused) {
  //     setIsSpeaking(false);
  //     // setIsPaused(true);
  //   }
  // };

  // const resumeSpeaking = () => {
  //   const resumed = SpeechService.resume();
  //   if (resumed) {
  //     setIsSpeaking(true);
  //     // setIsPaused(false);
  //   }
  // };
  const stopSpeaking = async () => {
    SpeechService.stop();
    setIsSpeaking(false);
    setMessageToRead(null);
    console.log("Stop Speaking!");
  };

  useEffect(() => {
    console.log("isSpeaking changed:", isSpeaking);
  }, [isSpeaking]);

  useEffect(() => {
    console.log("messageToRead changed:", messageToRead);
  }, [messageToRead]);

  // Initialize speech synthesis when component mounts
  useEffect(() => {
    // Clean up when component unmounts
    return () => {
      stopSpeaking();
    };
  }, []);

  useEffect(() => {
    console.log("All messages are: ", messages);
  }, [messages]);

  useEffect(() => {
    // Only log chat history in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log("Chat history is: ", chatHistory);
    }
  }, [chatHistory]);
  
  // Remove localStorage dependency - messages are now stored in database
  // useEffect(() => {
  //   if (messages && messages.length > 0) {
  //     // Only save when messages contain attachments
  //     if (messages.some(msg => msg.attachment)) {
  //       saveMessagesToLocalStorage(messages);
  //     }
  //   }
  // }, [messages]);

  // Add effect to scroll to bottom when messages change or tab switches to chat
  useEffect(() => {
    // Only scroll if we're on the chat tab (activeTab === 0) and have messages
    if (activeTab === 0 && messages.length > 0) {
      // When switching tabs or adding new messages, enable auto-scroll
      if (shouldAutoScrollRef.current) {
        // For new messages (not initial load from "View Conversation"), use a smooth scroll
        // with a small delay to let the DOM update
        setTimeout(() => {
          scrollToBottom(false); // Use smooth scrolling for regular messages
        }, 100);
      }
    }
  }, [messages, activeTab]);

  // Delete chat history function
  const deleteChatHistory = async (dateToDelete) => {
    if (
      !window.confirm(
        `Are you sure you want to delete all chat history for ${dateToDelete}?`
      )
    ) {
      return;
    }

    try {
      setIsLoading(true);

      const token = localStorage.getItem("token");
      if (!token) {
        console.error("No token found. User not authenticated.");
        alert("You are not authenticated. Please log in.");
        return;
      }

      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      await axios.delete(
        `http://localhost:8000/api/chat/history/${dateToDelete}`,
        config
      );

      // ✅ Remove the chat entry from chatHistory state
      setChatHistory((prevChatHistory) => {
        if (!prevChatHistory || typeof prevChatHistory !== "object")
          return prevChatHistory;

        const updatedHistory = { ...prevChatHistory };
        delete updatedHistory[dateToDelete];
        return updatedHistory;
      });

      // ✅ Clear messages if current chat is from deleted date
      if (
        messages.length > 0 &&
        messages[0].chatDate &&
        !isNaN(new Date(messages[0].chatDate).getTime())
      ) {
        const currentChatDate = new Date(messages[0].chatDate)
          .toISOString()
          .split("T")[0];

        if (currentChatDate === dateToDelete) {
          console.log("Clearing messages for deleted date.");
          setMessages([]);
          setContextId(Date.now().toString());
        }
      }
      // Safely update local state
      // setChatHistory((prevState) => {
      //   const newState = { ...prevState };
      //   if (newState.byDate && newState.byDate[dateToDelete]) {
      //     delete newState.byDate[dateToDelete];
      //   }
      //   return newState;
      // });

      // Clear messages if deleted date is currently viewed
      // if (currentChatMessages.length > 0) {
      //   const currentChatDate = new Date(currentChatMessages[0].chatDate)
      //     .toISOString()
      //     .split("T")[0];
      //   if (currentChatDate === dateToDelete) {
      //     setCurrentChatMessages([]);
      //     setSelectedChatId(null);
      //   }
      // }

      alert(`Chat history for ${dateToDelete} deleted successfully!`);
    } catch (error) {
      console.error(
        "Error deleting chat history:",
        error?.response?.data || error
      );
      alert("Failed to delete chat history. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Delete analysis history function
  const deleteAnalysis = (index) => {
    setAnalysisHistory((prev) => prev.filter((_, i) => i !== index));
  };

  // Speech-to-text functions using Azure Speech Services
  const startRecording = async () => {
    try {
      // Check if we're in offline mode due to network errors
      if (window.consecutiveNetworkErrors >= 3) {
        console.log("Speech recognition disabled due to network errors");
        if (confirm("Speech recognition is disabled due to network issues. Would you like to try again?")) {
          window.consecutiveNetworkErrors = 0; // Reset counter
          console.log("Network error counter reset, trying speech recognition again");
        } else {
          // Focus on input field for manual typing
          setTimeout(() => {
            const inputField = document.querySelector('input[type="text"], textarea');
            if (inputField) {
              inputField.focus();
            }
          }, 100);
          return;
        }
      }

      // Check if Microsoft Speech SDK is available
      if (typeof SpeechSDK === "undefined") {
        console.log(
          "Microsoft Speech SDK not available, falling back to Web Speech API"
        );
        startBrowserRecording();
        return;
      }

      // Check network connectivity for Azure services
      if (!navigator.onLine) {
        console.log("No internet connection, falling back to Web Speech API");
        startBrowserRecording();
        return;
      }

      // Get Azure credentials from environment variables
      const key = import.meta.env.VITE_AZURE_SPEECH_KEY;
      const region = import.meta.env.VITE_AZURE_SPEECH_REGION;

      if (!key || !region) {
        console.error(
          "Azure Speech key or region is missing, falling back to Web Speech API"
        );
        startBrowserRecording();
        return;
      }

      console.log("Using Azure Speech Services for speech-to-text");
      setIsRecording(true);

      // Create speech configuration
      const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(key, region);

      // Set the language
      const langCode = selectedLanguage.includes("-")
        ? selectedLanguage
        : `${selectedLanguage}-IN`;
      speechConfig.speechRecognitionLanguage = langCode;

      // Create audio configuration
      const audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();

      // Create recognizer
      const recognizer = new SpeechSDK.SpeechRecognizer(
        speechConfig,
        audioConfig
      );

      // Start recognition
      recognizer.recognizeOnceAsync(
        (result) => {
          if (result.reason === SpeechSDK.ResultReason.RecognizedSpeech) {
            console.log(`Azure STT result: ${result.text}`);
            setInputMessage(result.text);
          } else {
            console.error(`Azure STT failed: ${result.reason}`);
            if (result.reason === SpeechSDK.ResultReason.NoMatch) {
              alert("No speech detected. Please try again.");
            } else {
              alert("Speech recognition failed. Please try again.");
            }
          }
          setIsRecording(false);
          recognizer.close();
        },
        (error) => {
          console.error(`Azure STT error: ${error}`);
          alert(`Speech recognition error: ${error}`);
          setIsRecording(false);
          recognizer.close();

          // Fall back to browser speech recognition
          startBrowserRecording();
        }
      );

      // Store the recognizer for stopping later
      setMediaRecorder(recognizer);
    } catch (error) {
      console.error("Error starting Azure speech recognition:", error);
      setIsRecording(false);

      // Fall back to browser speech recognition
      startBrowserRecording();
    }
  };

  // Fallback to browser's Web Speech API
  const startBrowserRecording = (retryCount = 0) => {
    try {
      // Check if browser supports Web Speech API
      if (!(window.SpeechRecognition || window.webkitSpeechRecognition)) {
        throw new Error("Browser does not support Web Speech API");
      }

      // Check if microphone permission is granted
      if (navigator.permissions) {
        navigator.permissions.query({ name: 'microphone' }).then((result) => {
          if (result.state === 'denied') {
            alert("Microphone access is denied. Please enable microphone access in your browser settings.");
            return;
          }
        }).catch(() => {
          // Permission API not supported, continue anyway
        });
      }

      // Don't retry more than 2 times to avoid infinite loops
      if (retryCount > 2) {
        console.log("Max retries reached for speech recognition");
        alert("Speech recognition is not working. Please try typing your message instead.");
        return;
      }

      // Check if we're in offline mode due to network errors
      if (window.consecutiveNetworkErrors >= 3) {
        console.log("In offline mode due to network errors");
        console.log("Speech recognition status: DISABLED (network issues)");
        alert("Speech recognition is disabled due to network issues. Please type your message.");
        return;
      }
      
      console.log("Speech recognition status: ENABLED");

      // Initialize speech recognition
      const SpeechRecognition =
        window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();

      // Try to use a more basic configuration for better compatibility
      recognition.continuous = false;
      recognition.interimResults = false; // Disable interim results for better stability
      recognition.maxAlternatives = 1;

      // Configure recognition with better language mapping
      let recognitionLang = "en-US"; // default
      
      switch (selectedLanguage) {
        case "en-IN":
        case "en":
          recognitionLang = "en-US";
          break;
        case "hi":
          recognitionLang = "hi-IN";
          break;
        case "ta":
          recognitionLang = "ta-IN";
          break;
        case "mr":
          recognitionLang = "mr-IN";
          break;
        default:
          recognitionLang = "en-US";
      }
      
      recognition.lang = recognitionLang;

      // Set up event handlers
      recognition.onstart = () => {
        console.log("Browser speech recognition started");
        setIsRecording(true);
        
        // Show a subtle indicator that recording has started
        // You could add a visual indicator here if needed
        
        // Reset network error counter on successful start
        if (window.consecutiveNetworkErrors) {
          console.log("Resetting network error counter on successful start");
          window.consecutiveNetworkErrors = 0;
        }
        
        // Show success message
        console.log("Speech recognition started successfully");
      };

      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        console.log("Browser speech recognition result:", transcript);

        // Since we disabled interim results, all results are final
        setInputMessage(transcript);
      };

      recognition.onerror = (event) => {
        console.error("Browser speech recognition error:", event.error);
        
        // Clear timeout if it exists
        if (recognition.timeoutId) {
          clearTimeout(recognition.timeoutId);
          recognition.timeoutId = null;
        }
        
        setIsRecording(false);

        // Track consecutive network errors
        if (event.error === "network") {
          if (!window.consecutiveNetworkErrors) {
            window.consecutiveNetworkErrors = 0;
          }
          window.consecutiveNetworkErrors++;
          
          console.log(`Network error count: ${window.consecutiveNetworkErrors}`);
          
          // If we have too many consecutive network errors, switch to offline mode
          if (window.consecutiveNetworkErrors >= 3) {
            console.log("Too many network errors, switching to offline mode");
            alert("Speech recognition is having network issues. Switching to offline mode - please type your message.");
            window.consecutiveNetworkErrors = 0; // Reset counter
            setIsRecording(false);
            
            // Focus on input field for manual typing
            setTimeout(() => {
              const inputField = document.querySelector('input[type="text"], textarea');
              if (inputField) {
                inputField.focus();
              }
            }, 100);
            return;
          }
          
          // For network errors, try to retry with exponential backoff
          console.log("Network error in speech recognition - retrying...");
          
          // Retry after a short delay
          setTimeout(() => {
            if (isRecording) {
              console.log("Retrying speech recognition after network error...");
              startBrowserRecording(window.consecutiveNetworkErrors);
            }
          }, 1000 * Math.pow(2, window.consecutiveNetworkErrors - 1)); // Exponential backoff
          
          return;
        }
        
        // Reset network error counter for non-network errors
        window.consecutiveNetworkErrors = 0;

        // Show error message based on error type
        let errorMessage = "";
        switch (event.error) {
          case "no-speech":
            errorMessage = "No speech detected. Please try again.";
            break;
          case "audio-capture":
            errorMessage = "No microphone detected. Please check your microphone settings.";
            break;
          case "not-allowed":
            errorMessage = "Microphone access denied. Please allow microphone access in your browser settings.";
            break;
          case "aborted":
            errorMessage = "Speech recognition was aborted.";
            break;
          case "service-not-allowed":
            errorMessage = "Speech recognition service not allowed. Please check your browser settings.";
            break;
          default:
            errorMessage = `Speech recognition error: ${event.error}`;
        }
        
        alert(errorMessage);
      };

      recognition.onend = () => {
        console.log("Browser speech recognition ended");
        
        // Clear timeout if it exists
        if (recognition.timeoutId) {
          clearTimeout(recognition.timeoutId);
          recognition.timeoutId = null;
        }
        
        setIsRecording(false);
      };

      // Store the recognition object for stopping later
      setMediaRecorder(recognition);

      // Add timeout to prevent hanging
      const timeoutId = setTimeout(() => {
        if (isRecording) {
          console.log("Speech recognition timeout, stopping...");
          recognition.stop();
          setIsRecording(false);
        }
      }, 30000); // 30 second timeout

      // Start recognition
      recognition.start();
      console.log("Browser recording started");

      // Store timeout ID for cleanup
      recognition.timeoutId = timeoutId;
    } catch (error) {
      console.error("Error starting browser speech recognition:", error);
      
      // If speech recognition fails completely, offer manual input
      if (confirm("Speech recognition is not available. Would you like to type your message instead?")) {
        // Focus on the input field
        const inputField = document.querySelector('input[type="text"], textarea');
        if (inputField) {
          inputField.focus();
        }
      }
      setIsRecording(false);
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && isRecording) {
      try {
        console.log("Stopping speech recognition...");

        // Clear timeout if it exists
        if (mediaRecorder.timeoutId) {
          clearTimeout(mediaRecorder.timeoutId);
          mediaRecorder.timeoutId = null;
        }

        // Check if it's an Azure recognizer or browser recognizer
        if (mediaRecorder.close) {
          // Azure Speech SDK recognizer
          mediaRecorder.close();
          console.log("Azure speech recognition stopped");
        } else if (mediaRecorder.stop) {
          // Browser Web Speech API
          mediaRecorder.stop();
          console.log("Browser speech recognition stopped");
        } else if (mediaRecorder.abort) {
          // Some browser implementations might use abort()
          mediaRecorder.abort();
          console.log("Browser speech recognition aborted");
        }

        setIsRecording(false);
      } catch (error) {
        console.error("Error stopping speech recognition:", error);
        setIsRecording(false);
      }
    }
  };

  // This function is kept for backward compatibility but is no longer used
  // with the direct Web Speech API implementation
  const handleSpeechToText = async (audioBlob) => {
    setIsLoading(true);

    try {
      console.log(
        "Processing audio blob for speech-to-text:",
        audioBlob.size,
        "bytes"
      );

      // Try the backend API for processing the audio blob
      try {
        // Create a FormData object to send the audio file
        const formData = new FormData();
        formData.append("audio", audioBlob, "recording.wav");
        formData.append("language", selectedLanguage);

        // Call the backend API to convert speech to text
        const response = await fetch("/api/chat/speech-to-text", {
          method: "POST",
          body: formData,
        });

        if (response.ok) {
          const data = await response.json();
          console.log("Speech-to-text response:", data);

          if (data.success && data.data.text) {
            setInputMessage(data.data.text);
            return;
          }
        }
      } catch (apiError) {
        console.error("Backend API error:", apiError);
      }

      // If backend API fails, use fallback text
      console.log("Speech recognition failed, using fallback text");

      // Generate fallback transcription based on the selected language
      let fallbackText = "";

      if (selectedLanguage === "en-IN") {
        fallbackText = "What is the weather forecast for tomorrow?";
      } else if (selectedLanguage === "hi") {
        fallbackText = "कल का मौसम कैसा रहेगा?";
      } else {
        fallbackText = "What is the weather forecast for tomorrow?";
      }

      setInputMessage(fallbackText);
    } catch (error) {
      console.error("Error in speech-to-text:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderChatTab = () => (
    <Box
      sx={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
        width: "100%",
        maxWidth: "100%",
        overflow: "hidden",
      }}
    >
      {/* Messages */}
      <Box
        ref={chatContainerRef}
        onScroll={handleScroll}
        sx={{
          flexGrow: 1,
          overflowY: "auto",
          overflowX: "hidden",
          p: { xs: 0.5, sm: 1 },
          pt: { xs: 1, sm: 1.5 },
          pb: { xs: 2, sm: 3 },
          display: "flex",
          flexDirection: "column",
          bgcolor: "#f5f5f5",
          width: "100%",
          height: "calc(100vh - 180px)",
          maxHeight: "calc(100vh - 180px)",
          WebkitOverflowScrolling: "touch", // Smoother scrolling on iOS
          scrollBehavior: "smooth" // Default smooth scrolling
        }}
      >
        {isLoading && messages.length === 0 ? (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "100%",
            }}
          >
            <CircularProgress />
          </Box>
        ) : (
          <List sx={{ width: "100%", p: 0 }}>
            {messages.map((message, index) => (
              <ListItem
                key={index}
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems:
                    message.sender === "user" ? "flex-end" : "flex-start",
                  p: { xs: 0.25, sm: 0.5 },
                  mb: { xs: 0.5, sm: 0.75 },
                  width: "100%",
                }}
              >
                <Paper
                  elevation={1}
                  sx={{
                    p: { xs: 1, sm: 1.5 },
                    maxWidth: { xs: "85%", sm: "80%", md: "70%" },
                    bgcolor:
                      message.sender === "user" ? "primary.light" : "white",
                    color: message.sender === "user" ? "white" : "text.primary",
                    borderRadius:
                      message.sender === "user"
                        ? "16px 16px 4px 16px"
                        : "16px 16px 16px 4px",
                    wordBreak: "break-word",
                  }}
                >
                  {/* Only show image attachment if this specific message has one and it's a valid image URL */}
                  {message.attachment && 
                    message.attachment.type === "image" &&
                    message.attachment.url && (
                      <Box sx={{ mb: 1 }}>
                        <img
                          src={message.attachment.url}
                          alt="Uploaded"
                          style={{
                            width: "100%",
                            maxHeight: 200,
                            borderRadius: 8,
                            objectFit: "cover",
                          }}
                        />
                      </Box>
                    )}
                  <Box sx={{ position: "relative" }}>
                    {message.sender === "bot" ? (
                      <Box
                        sx={{
                          whiteSpace: "pre-wrap",
                          fontSize: { xs: "0.875rem", sm: "1rem" },
                        }}
                      >
                        <ReactMarkdown
                          components={{
                            h1: (props) => (
                              <Typography
                                variant="h4"
                                sx={{
                                  mt: 0.5,
                                  mb: 0,
                                  fontWeight: 600,
                                  lineHeight: 1,
                                  fontSize: "1.2rem",
                                }}
                                {...props}
                              />
                            ),
                            h2: (props) => (
                              <Typography
                                variant="h5"
                                sx={{
                                  mt: 0.5,
                                  mb: 0,
                                  fontWeight: 600,
                                  lineHeight: 1,
                                  fontSize: "1.1rem",
                                }}
                                {...props}
                              />
                            ),
                            h3: (props) => (
                              <Typography
                                variant="h6"
                                sx={{
                                  mt: 0.25,
                                  mb: 0,
                                  fontWeight: 600,
                                  lineHeight: 1,
                                  fontSize: "1rem",
                                }}
                                {...props}
                              />
                            ),
                            h4: (props) => (
                              <Typography
                                variant="subtitle1"
                                sx={{
                                  mt: 0.25,
                                  mb: 0,
                                  fontWeight: 600,
                                  lineHeight: 1,
                                  fontSize: "0.95rem",
                                }}
                                {...props}
                              />
                            ),
                            h5: (props) => (
                              <Typography
                                variant="subtitle2"
                                sx={{
                                  mt: 0.25,
                                  mb: 0,
                                  fontWeight: 600,
                                  lineHeight: 1,
                                  fontSize: "0.9rem",
                                }}
                                {...props}
                              />
                            ),
                            h6: (props) => (
                              <Typography
                                variant="subtitle2"
                                sx={{
                                  mt: 0.25,
                                  mb: 0,
                                  fontWeight: 600,
                                  lineHeight: 1,
                                  fontSize: "0.85rem",
                                }}
                                {...props}
                              />
                            ),
                            p: (props) => (
                              <Typography
                                variant="body1"
                                sx={{ my: 0.125 }}
                                {...props}
                              />
                            ),
                            a: (props) => {
                              // Handle special summary links
                              if (
                                props.href &&
                                props.href.startsWith("#summary-")
                              ) {
                                const resourceType = props.href.replace(
                                  "#summary-",
                                  ""
                                );
                                let url = "";

                                if (resourceType === "disease") {
                                  url = "https://icar.org.in/crops/diseases";
                                } else if (resourceType === "market") {
                                  url = "https://agmarknet.gov.in/";
                                } else if (resourceType === "schemes") {
                                  url = "https://agricoop.nic.in/en/schemes";
                                }

                                return (
                                  <Link
                                    color="primary"
                                    sx={{
                                      textDecoration: "none",
                                      cursor: "pointer",
                                    }}
                                    onClick={(e) => {
                                      e.preventDefault();
                                      handleWebsiteLink(url, resourceType);
                                    }}
                                  >
                                    {props.children}
                                  </Link>
                                );
                              }

                              // Regular links open in new tab
                              return (
                                <Link
                                  color="primary"
                                  sx={{ textDecoration: "none" }}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  {...props}
                                />
                              );
                            },
                            ul: (props) => (
                              <Box
                                component="ul"
                                sx={{
                                  pl: 1.5,
                                  my: 0.125,
                                  "& li:last-child": { mb: 0 },
                                  lineHeight: 1,
                                }}
                                {...props}
                              />
                            ),
                            ol: (props) => (
                              <Box
                                component="ol"
                                sx={{
                                  pl: 1.5,
                                  my: 0.125,
                                  "& li:last-child": { mb: 0 },
                                  lineHeight: 1,
                                }}
                                {...props}
                              />
                            ),
                            li: (props) => (
                              <Box
                                component="li"
                                sx={{ mb: 0, py: 0.125, lineHeight: 1.1 }}
                                {...props}
                              />
                            ),
                            code: (props) => (
                              <Box
                                component="code"
                                sx={{
                                  bgcolor: "rgba(0, 0, 0, 0.05)",
                                  p: 0.3,
                                  borderRadius: 0.5,
                                  fontFamily: "monospace",
                                }}
                                {...props}
                              />
                            ),
                            pre: (props) => (
                              <Box
                                component="pre"
                                sx={{
                                  bgcolor: "rgba(0, 0, 0, 0.05)",
                                  p: 1,
                                  borderRadius: 1,
                                  overflowX: "auto",
                                  "& code": { bgcolor: "transparent", p: 0 },
                                }}
                                {...props}
                              />
                            ),
                            blockquote: (props) => (
                              <Box
                                component="blockquote"
                                sx={{
                                  borderLeft: "2px solid",
                                  borderColor: "primary.main",
                                  pl: 1,
                                  py: 0.125,
                                  my: 0.25,
                                  bgcolor: "rgba(0, 0, 0, 0.02)",
                                  fontStyle: "italic",
                                  lineHeight: 1.1,
                                }}
                                {...props}
                              />
                            ),
                            hr: (props) => (
                              <Divider sx={{ my: 0.75 }} {...props} />
                            ),
                            table: (props) => (
                              <TableContainer
                                component={Paper}
                                elevation={0}
                                sx={{ my: 0.5 }}
                              >
                                <Table
                                  size="small"
                                  sx={{ "& th, & td": { py: 0.5, px: 1 } }}
                                  {...props}
                                />
                              </TableContainer>
                            ),
                            thead: (props) => <TableHead {...props} />,
                            tbody: (props) => <TableBody {...props} />,
                            tr: (props) => <TableRow {...props} />,
                            th: (props) => (
                              <TableCell
                                component="th"
                                sx={{
                                  bgcolor: "rgba(0, 0, 0, 0.04)",
                                  fontWeight: 600,
                                }}
                                {...props}
                              />
                            ),
                            td: (props) => <TableCell {...props} />,
                          }}
                        >
                          {message.text}
                        </ReactMarkdown>
                      </Box>
                    ) : (
                      <Typography
                        variant="body1"
                        sx={{
                          whiteSpace: "pre-wrap",
                          fontSize: { xs: "0.875rem", sm: "1rem" },
                        }}
                      >
                        {message.text}
                      </Typography>
                    )}

                    {/* Text-to-speech controls for bot messages */}
                    {message.sender === "bot" && (
                      <Box
                        sx={{
                          display: "flex",
                          gap: 0.5,
                          mt: 1,
                          justifyContent: "center",
                        }}
                      >
                        {messageToRead === index ? (
                          <IconButton
                            size="small"
                            onClick={stopSpeaking}
                            sx={{ p: 0.5, width: "40px", height: "40px" }}
                          >
                            <StopIcon fontSize="small" />
                          </IconButton>
                        ) : (
                          <IconButton
                            size="small"
                            onClick={() => {
                              stopSpeaking(); // stop any currently playing audio
                              speakText(message.text, index); // start new audio
                            }}
                            sx={{ p: 0.5, width: "40px", height: "40px" }}
                          >
                            <VolumeUpIcon fontSize="small" />
                          </IconButton>
                        )}
                      </Box>
                    )}
                    {/* {message.sender === "bot" && (
                      <Box
                        sx={{
                          display: "flex",
                          gap: 0.5,
                          mt: 1,
                          justifyContent: "center",
                        }}
                      >
                        {messageToRead === index ? (
                          isSpeaking ? (
                            <IconButton
                              size="small"
                              onClick={pauseSpeaking}
                              sx={{ p: 0.5, width: "40px", height: "40px" }}
                            >
                              <PauseIcon fontSize="small" />
                            </IconButton>
                          ) : (
                            <IconButton
                              size="small"
                              onClick={resumeSpeaking}
                              sx={{ p: 0.5, width: "40px", height: "40px" }}
                            >
                              <PlayArrowIcon fontSize="small" />
                            </IconButton>
                          )
                        ) : (
                          <IconButton
                            size="small"
                            onClick={() => speakText(message.text, index)}
                            sx={{ p: 0.5, width: "40px", height: "40px" }}
                          >
                            <VolumeUpIcon fontSize="small" />
                          </IconButton>
                        )}

                        {messageToRead === index && (
                          <IconButton
                            size="small"
                            onClick={stopSpeaking}
                            sx={{ p: 0.5, width: "40px", height: "40px" }}
                          >
                            <StopIcon fontSize="small" />
                          </IconButton>
                        )}
                      </Box>
                    )} */}
                  </Box>
                  <Typography
                    variant="caption"
                    sx={{
                      display: "block",
                      mt: 1,
                      fontSize: { xs: "0.7rem", sm: "0.75rem" },
                      color:
                        message.sender === "user"
                          ? "rgba(255,255,255,0.7)"
                          : "text.secondary",
                    }}
                  >
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </Typography>

                  {message.followupQuestions &&
                    message.followupQuestions.length > 0 && (
                                        <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={(e) => {
                        setFollowupQuestions(message.followupQuestions);
                        setFollowupMenuAnchor(e.currentTarget);
                      }}
                      sx={{
                        color:
                          message.sender === "user"
                            ? "white"
                            : "primary.main",
                        borderColor:
                          message.sender === "user"
                            ? "white"
                            : "primary.main",
                        fontSize: { xs: "0.75rem", sm: "0.875rem" },
                        py: { xs: 0.25, sm: 0.5 },
                        "&:hover": {
                          borderColor:
                            message.sender === "user"
                              ? "white"
                              : "primary.dark",
                          bgcolor:
                            message.sender === "user"
                              ? "rgba(255,255,255,0.1)"
                              : "rgba(0,0,0,0.05)",
                        },
                      }}
                      startIcon={<HelpOutlineIcon fontSize="small" />}
                    >
                      {getTranslation(
                        "chatbot.suggestedQuestions",
                        selectedLanguage
                      )}
                    </Button>
                    
                    {message.attachment && message.attachment.analysisResult && (
                      <Button
                        size="small"
                        variant="outlined"
                        onClick={async () => {
                          try {
                            setIsLoading(true);
                            
                            // Show a message to the user
                            const messageElement = document.createElement('div');
                            messageElement.style.position = 'fixed';
                            messageElement.style.top = '20px';
                            messageElement.style.left = '50%';
                            messageElement.style.transform = 'translateX(-50%)';
                            messageElement.style.backgroundColor = '#4CAF50';
                            messageElement.style.color = 'white';
                            messageElement.style.padding = '10px 20px';
                            messageElement.style.borderRadius = '4px';
                            messageElement.style.zIndex = '9999';
                            messageElement.textContent = 'Generating PDF, please wait...';
                            document.body.appendChild(messageElement);
                            
                            // Import the PDF export service dynamically
                            const PdfExportService = (await import("../../utils/PdfExportService")).default;
                            
                            // Generate the PDF
                            const pdfBlob = await PdfExportService.generateAnalysisPDF(
                              message.attachment.analysisResult,
                              message.attachment.url
                            );
                            
                            // Update message
                            messageElement.textContent = 'PDF generated successfully!';
                            messageElement.style.backgroundColor = '#4CAF50';
                            
                            // Create a download link
                            const url = URL.createObjectURL(pdfBlob);
                            const link = document.createElement('a');
                            link.href = url;
                            link.download = `${message.attachment.analysisResult.identifiedAs || 'crop'}_analysis_${new Date().toISOString().split('T')[0]}.pdf`;
                            document.body.appendChild(link);
                            link.click();
                            
                            // Clean up
                            document.body.removeChild(link);
                            URL.revokeObjectURL(url);
                            
                            // Remove message after a delay
                            setTimeout(() => {
                              try {
                                document.body.removeChild(messageElement);
                              } catch (e) {
                                // Element might have been removed already
                              }
                            }, 3000);
                          } catch (error) {
                            console.error('Error exporting analysis to PDF:', error);
                            
                            // Show error message
                            const errorElement = document.createElement('div');
                            errorElement.style.position = 'fixed';
                            errorElement.style.top = '20px';
                            errorElement.style.left = '50%';
                            errorElement.style.transform = 'translateX(-50%)';
                            errorElement.style.backgroundColor = '#F44336';
                            errorElement.style.color = 'white';
                            errorElement.style.padding = '10px 20px';
                            errorElement.style.borderRadius = '4px';
                            errorElement.style.zIndex = '9999';
                            errorElement.textContent = 'Failed to export analysis. Please try again.';
                            document.body.appendChild(errorElement);
                            
                            // Remove error message after a delay
                            setTimeout(() => {
                              try {
                                document.body.removeChild(errorElement);
                              } catch (e) {
                                // Element might have been removed already
                              }
                            }, 5000);
                          } finally {
                            setIsLoading(false);
                          }
                        }}
                        sx={{
                          color: message.sender === "user" ? "white" : "primary.main",
                          borderColor: message.sender === "user" ? "white" : "primary.main",
                          fontSize: { xs: "0.75rem", sm: "0.875rem" },
                          py: { xs: 0.25, sm: 0.5 },
                          "&:hover": {
                            borderColor: message.sender === "user" ? "white" : "primary.dark",
                            bgcolor: message.sender === "user" ? "rgba(255,255,255,0.1)" : "rgba(0,0,0,0.05)",
                          },
                        }}
                        startIcon={<Download fontSize="small" />}
                      >
                        Export as PDF
                      </Button>
                    )}
                  </Box>
                    )}
                </Paper>
              </ListItem>
            ))}
            <div ref={messagesEndRef} />
          </List>
        )}

        {isLoading && messages.length > 0 && (
          <Box sx={{ display: "flex", p: 1, justifyContent: "center" }}>
            <CircularProgress size={24} />
          </Box>
        )}
      </Box>

      {/* Image Preview */}
      {imagePreview && (
        <Box
          sx={{
            p: { xs: 1, sm: 2 },
            borderTop: "1px solid rgba(0, 0, 0, 0.12)",
            bgcolor: "#f5f5f5",
            width: "100%",
          }}
        >
          <Box
            sx={{
              position: "relative",
              display: "inline-block",
              maxWidth: "100%",
            }}
          >
            <img
              src={imagePreview}
              alt="Preview"
              style={{
                width: "100%",
                maxWidth: "300px",
                maxHeight: 120,
                borderRadius: 8,
                objectFit: "cover",
              }}
            />
            <IconButton
              size="small"
              sx={{
                position: "absolute",
                top: -8,
                right: -8,
                bgcolor: "white",
                padding: "4px",
                "&:hover": { bgcolor: "#f5f5f5" },
              }}
              onClick={() => {
                setImageUpload(null);
                setImagePreview(null);
              }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Box>
        </Box>
      )}

      {/* Input Area with Action Buttons */}
      <Box
        sx={{
          borderTop: "1px solid rgba(0, 0, 0, 0.12)",
          width: "100%",
          bgcolor: "white",
        }}
      >
        {/* Action Buttons Row */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            p: { xs: 0.5, sm: 0.75 },
            mt: { xs: 0.5, sm: 0.75 },
            mb: { xs: 0, sm: 0 },
            borderBottom: "1px solid rgba(0, 0, 0, 0.06)",
            bgcolor: "#f9f9f9",
            borderRadius: 1,
            position: "sticky",
            bottom: 0,
            zIndex: 10,
          }}
        >
          <Box sx={{ display: "flex", gap: { xs: 0.5, sm: 1 } }}>
            <input
              type="file"
              accept="image/*"
              style={{ display: "none" }}
              ref={fileInputRef}
              onChange={handleImageUpload}
            />
            <Button
              variant="outlined"
              color="primary"
              startIcon={<ImageIcon />}
              onClick={() => fileInputRef.current.click()}
              disabled={isLoading || isRecording}
              size="small"
              sx={{
                fontSize: { xs: "0.65rem", sm: "0.75rem" },
                py: { xs: 0.15, sm: 0.35 },
                px: { xs: 0.5, sm: 0.75 },
                borderRadius: 2,
                textTransform: "none",
                minWidth: "auto",
                height: { xs: "28px", sm: "32px" },
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {isMobile ? "Image" : t('chatbot.uploadImage')}
            </Button>

            <Button
              variant="outlined"
              color={isRecording ? "error" : (window.consecutiveNetworkErrors >= 3 ? "warning" : "primary")}
              startIcon={isRecording ? <StopIcon /> : <MicIcon />}
              onClick={isRecording ? stopRecording : startRecording}
              disabled={isLoading}
              size="small"
              sx={{
                fontSize: { xs: "0.65rem", sm: "0.75rem" },
                py: { xs: 0.15, sm: 0.35 },
                px: { xs: 0.5, sm: 0.75 },
                borderRadius: 2,
                textTransform: "none",
                minWidth: "auto",
                height: { xs: "28px", sm: "32px" },
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
                animation: isRecording ? "pulse 1.5s infinite" : "none",
                "@keyframes pulse": {
                  "0%": { opacity: 1 },
                  "50%": { opacity: 0.5 },
                  "100%": { opacity: 1 },
                },
                opacity: window.consecutiveNetworkErrors >= 3 ? 0.6 : 1,
              }}
              title={window.consecutiveNetworkErrors >= 3 ? "Speech recognition disabled due to network issues" : ""}
            >
              {isRecording
                ? isMobile
                  ? "Stop"
                  : "Stop Recording"
                : isMobile
                ? "Record"
                : t('chatbot.startRecording')}
            </Button>
          </Box>

          <Button
            variant="outlined"
            color="primary"
            startIcon={<LanguageIcon />}
            onClick={(e) => {
              e.stopPropagation();
              setLanguageMenuAnchor(e.currentTarget);
            }}
            size="small"
            sx={{
              fontSize: { xs: "0.65rem", sm: "0.75rem" },
              py: { xs: 0.15, sm: 0.35 },
              px: { xs: 0.5, sm: 0.75 },
              borderRadius: 2,
              textTransform: "none",
              minWidth: "auto",
              height: { xs: "28px", sm: "32px" },
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              maxWidth: { xs: "80px", sm: "120px" },
            }}
          >
            {languageOptions.find((lang) => lang.code === selectedLanguage)
              ?.name || t('chatbot.language')}
          </Button>
        </Box>

        {/* Text Input and Send Button */}
        <Box
          sx={{
            p: { xs: 1, sm: 1.5 },
            display: "flex",
            alignItems: "center",
            gap: { xs: 1, sm: 1.5 },
          }}
        >
          <TextField
            fullWidth
            placeholder={t('chatbot.typeMessage')}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isLoading || isRecording}
            multiline
            maxRows={4}
            variant="outlined"
            sx={{
              flexGrow: 1,
              "& .MuiOutlinedInput-root": {
                borderRadius: 8,
                fontSize: { xs: "0.9rem", sm: "1rem" },
                padding: { xs: "8px 12px", sm: "10px 14px" },
                minHeight: { xs: "40px", sm: "46px" },
              },
            }}
          />
          <Button
            variant="contained"
            color="primary"
            size="small"
            onClick={handleSendMessage}
            disabled={
              (!inputMessage.trim() && !imageUpload) || isLoading || isRecording
            }
            sx={{
              height: { xs: "32px", sm: "36px" },
              minWidth: "auto",
              width: "auto",
              borderRadius: 8,
              textTransform: "none",
              fontSize: { xs: "0.8rem", sm: "0.85rem" },
              px: { xs: 1.25, sm: 1.75 },
              py: { xs: 0.25, sm: 0.5 },
              ml: 0.5,
            }}
          >
            {t('chatbot.send')}
          </Button>
        </Box>
      </Box>
    </Box>
  );

  const renderHistoryTab = () => (
    <Box
      sx={{
        p: { xs: 1, sm: 2 },
        width: "100%",
        height: "100%",
        overflow: "auto",
        display: "flex",
        flexDirection: "column",
        "&::-webkit-scrollbar": {
          width: "8px",
          backgroundColor: "rgba(0,0,0,0.05)",
        },
        "&::-webkit-scrollbar-thumb": {
          backgroundColor: "rgba(0,0,0,0.2)",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          backgroundColor: "rgba(0,0,0,0.3)",
        },
      }}
    >
      <Box
        sx={{
          mb: { xs: 2, sm: 3 },
          position: "sticky",
          top: 0,
          zIndex: 20,
          backgroundColor: "background.paper",
          pt: 1,
          pb: 2,
        }}
      >
        <Typography
          variant="h6"
          gutterBottom
          sx={{ fontSize: { xs: "1rem", sm: "1.25rem" } }}
        >
          {t('chatbot.searchChatHistory')}
        </Typography>
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", sm: "row" },
            gap: 1,
          }}
        >
          <TextField
            fullWidth
            placeholder={t('chatbot.searchPlaceholder')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            size="small"
            onKeyPress={(e) => e.key === "Enter" && searchChatHistory()}
            sx={{
              "& .MuiOutlinedInput-root": {
                fontSize: { xs: "0.875rem", sm: "1rem" },
              },
            }}
          />
          <Button
            variant="contained"
            startIcon={<SearchIcon fontSize="small" />}
            onClick={searchChatHistory}
            disabled={isSearching || !searchQuery.trim()}
            sx={{
              minWidth: { xs: "100%", sm: "auto" },
              fontSize: { xs: "0.75rem", sm: "0.875rem" },
              py: { xs: 0.75, sm: 1 },
            }}
          >
            {t('chatbot.search')}
          </Button>
        </Box>
      </Box>

      <Box
        sx={{
          position: "sticky",
          top: { xs: 130, sm: 140 },
          zIndex: 15,
          backgroundColor: "background.paper",
          pb: 1,
        }}
      >
        <Divider sx={{ mb: { xs: 2, sm: 2 } }} />

        <Typography
          variant="h6"
          gutterBottom
          sx={{ fontSize: { xs: "1rem", sm: "1.25rem" } }}
        >
          Conversations by Date
        </Typography>
      </Box>

      {isSearching ? (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            p: { xs: 2, sm: 4 },
          }}
        >
          <CircularProgress size={24} />
        </Box>
      ) : typeof chatHistory === "object" &&
        Object.keys(chatHistory).length === 0 ? (
        <Typography
          color="text.secondary"
          align="center"
          sx={{
            py: { xs: 2, sm: 4 },
            fontSize: { xs: "0.875rem", sm: "1rem" },
          }}
        >
          No chat history found
        </Typography>
      ) : (
        <Box
          sx={{
            flexGrow: 1,
            overflow: "auto",
            "&::-webkit-scrollbar": {
              width: "8px",
              backgroundColor: "rgba(0,0,0,0.05)",
            },
            "&::-webkit-scrollbar-thumb": {
              backgroundColor: "rgba(0,0,0,0.2)",
              borderRadius: "4px",
            },
            "&::-webkit-scrollbar-thumb:hover": {
              backgroundColor: "rgba(0,0,0,0.3)",
            },
          }}
        >
          {/* If chatHistory is an object with date keys (new format) */}
          {typeof chatHistory === "object" && !Array.isArray(chatHistory) ? (
            Object.keys(chatHistory)
              .sort((a, b) => new Date(b) - new Date(a)) // Sort dates in descending order
              .map((dateKey) => {
                const chats = chatHistory[dateKey];
                // console.log("Chat history - chats", chats);
                const formattedDate = new Date(dateKey).toLocaleDateString(
                  "en-US",
                  {
                    weekday: "long",
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  }
                );

                return (
                  <Box key={dateKey} sx={{ mb: 3 }}>
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        mb: 2,
                        bgcolor: "primary.main",
                        color: "white",
                        p: { xs: 1, sm: 1.5 },
                        borderRadius: "8px",
                        boxShadow: 2,
                        position: "sticky",
                        top: 8,
                        zIndex: 10,
                        mt: 1,
                      }}
                    >
                      <CalendarMonth sx={{ mr: 1 }} />
                      <Typography
                        variant="h6"
                        sx={{
                          fontSize: { xs: "0.9rem", sm: "1rem" },
                          fontWeight: "bold",
                        }}
                      >
                        {formattedDate}
                      </Typography>
                    </Box>

                    <List sx={{ p: 0 }}>
                      {chats.map((chat, index) => (
                        <Paper
                          key={`${dateKey}-${index}`}
                          elevation={2}
                          sx={{
                            mb: 2,
                            overflow: "hidden",
                            width: "100%",
                            borderRadius: 2,
                            transition: "transform 0.2s, box-shadow 0.2s",
                            "&:hover": {
                              transform: "translateY(-2px)",
                              boxShadow: 3,
                            },
                          }}
                        >
                          <Box
                            sx={{
                              p: { xs: 1.5, sm: 2 },
                              bgcolor: "primary.light",
                              color: "white",
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              borderTopLeftRadius: 2,
                              borderTopRightRadius: 2,
                            }}
                          >
                            <Box>
                              <Typography
                                variant="subtitle1"
                                sx={{
                                  fontSize: { xs: "0.875rem", sm: "1rem" },
                                }}
                              >
                                {new Date(chat.updatedAt).toLocaleTimeString()}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{
                                  fontSize: { xs: "0.75rem", sm: "0.875rem" },
                                }}
                              >
                                {chat.messages.length} messages
                              </Typography>
                            </Box>
                            <IconButton
                              size="small"
                              onClick={() => deleteChatHistory(dateKey)}
                              sx={{ color: "white" }}
                            >
                              <DeleteForeverIcon />
                            </IconButton>
                          </Box>
                          <Box sx={{ p: { xs: 1.5, sm: 2 } }}>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                mb: 1,
                                fontSize: { xs: "0.75rem", sm: "0.875rem" },
                              }}
                            >
                              Preview:
                            </Typography>
                            {chat.messages.slice(0, 2).map((msg, i) => (
                              <Box key={i} sx={{ mb: 1 }}>
                                <Typography
                                  variant="body2"
                                  component="span"
                                  fontWeight="bold"
                                  sx={{
                                    fontSize: { xs: "0.75rem", sm: "0.875rem" },
                                  }}
                                >
                                  {msg.sender === "user" ? t('chatbot.you') : t('chatbot.bot')}:
                                </Typography>{" "}
                                <Typography
                                  variant="body2"
                                  component="span"
                                  sx={{
                                    fontSize: { xs: "0.75rem", sm: "0.875rem" },
                                  }}
                                >
                                  {msg.text.length > 80
                                    ? `${msg.text.substring(0, 80)}...`
                                    : msg.text}
                                </Typography>
                              </Box>
                            ))}
                            <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                              <Button
                                variant="outlined"
                                size="small"
                                onClick={async () => {
                                  try {
                                    const {
                                      messages: latestMessages,
                                      chatHistory: latestHistory,
                                    } = await fetchCombinedChatHistory(
                                      chat.contextData?.contextId,
                                      dateKey
                                    );

                                    // Enable auto-scroll for this conversation view
                                    shouldAutoScrollRef.current = true;
                                    
                                    setMessages(
                                      latestMessages.length > 0
                                        ? latestMessages
                                        : chat.messages
                                    );
                                    setContextId(
                                      chat.contextData?.contextId ||
                                        Date.now().toString()
                                    );
                                    setChatHistory(latestHistory);
                                    
                                    // Use a single, properly timed scroll approach to prevent flickering
                                    // First switch the tab
                                    setActiveTab(0);
                                    
                                    // Wait for the tab to fully switch and render
                                    setTimeout(() => {
                                      // Messages are now stored in database, no need for localStorage backup restoration
                                      // tryRestoreAttachmentsFromBackup();
                                      
                                      // Wait for DOM to update after restoration, then do a single immediate scroll
                                      // Use immediate scrolling (non-smooth) to prevent flickering
                                      setTimeout(() => {
                                        // Use immediate scrolling for initial load
                                        scrollToBottom(true);
                                      }, 300);
                                    }, 100);
                                  } catch (error) {
                                    console.error(
                                      "Error viewing conversation:",
                                      error
                                    );
                                  }
                                }}
                                sx={{
                                  fontSize: { xs: "0.75rem", sm: "0.875rem" },
                                  py: { xs: 0.5, sm: 0.75 },
                                }}
                              >
                                View Conversation
                              </Button>
                            </Box>
                          </Box>
                        </Paper>
                      ))}
                    </List>
                  </Box>
                );
              })
          ) : (
            // Legacy format (array of chats)
            <Typography
              color="text.secondary"
              align="center"
              sx={{
                py: { xs: 2, sm: 4 },
                fontSize: { xs: "0.875rem", sm: "1rem" },
              }}
            >
              No chat history found
            </Typography>
          )}
        </Box>
      )}
    </Box>
  );

  const renderAnalysisTab = () => (
    <Box sx={{ height: "100%", overflow: "hidden" }}>
      <ImageAnalysisHistory 
        initialAnalyses={analysisHistory}
        onRefresh={fetchImageAnalysisHistory}
        currentUser={currentUser}
      />
    </Box>
  );

  // Fetch chat history when component mounts
  useEffect(() => {
    fetchChatHistory();
    fetchImageAnalysisHistory();
    
    // Also check for saved image attachments after a short delay
    // This ensures we can restore images even if the server data doesn't have them
    setTimeout(() => {
      try {
        const backupMessages = localStorage.getItem('chatMessagesBackup');
        if (backupMessages) {
          const parsedBackup = JSON.parse(backupMessages);
          // Check if we have image attachments in backup
          const hasImageAttachments = parsedBackup.some(msg => 
            msg.attachment && 
            msg.attachment.type === 'image' && 
            msg.attachment.url
          );
          
          if (hasImageAttachments) {
            console.log('Found image attachments in backup, will restore if needed');
            // Messages are now stored in database, no need for localStorage backup restoration
            // if (messages.length > 0 && !messages.some(msg => msg.attachment && msg.attachment.type === 'image')) {
            //   console.log('Current messages missing attachments, attempting to restore');
            //   tryRestoreAttachmentsFromBackup();
            // }
            // If we have no messages yet, load directly from backup
            // else if (messages.length === 0) {
            //   console.log('No current messages, loading from backup');
            //   setMessages(parsedBackup);
            // }
          }
        }
      } catch (err) {
        console.error('Error checking backup on mount:', err);
      }
    }, 2000); // Wait 2 seconds after mount to ensure fetch has completed
  }, []);

  // Add event listener for before page unload to save state
  useEffect(() => {
    const handleBeforeUnload = () => {
      console.log("Page is about to unload - saving current state");
      // Messages are now stored in database, no need for localStorage backup
      // if (messages.length > 0 && messages.some(msg => msg.attachment)) {
      //   saveMessagesToLocalStorage(messages);
      // }
    };
    
    // Add event listener
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    // Cleanup
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [messages]);



  // Show loading state while authentication is in progress
  if (authLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // Show loading state if user is not authenticated
  if (!currentUser) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <Typography>Redirecting to login...</Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100vh",
        width: "100%",
        maxWidth: "100%",
        overflow: "hidden",
      }}
    >
      {/* App Bar */}
      <AppBar position="static" elevation={0}>
        <Toolbar
          sx={{
            minHeight: { xs: "50px", sm: "60px" },
            background: "linear-gradient(90deg, #1a8754 0%, #2e7d32 100%)",
            p: { xs: 1, sm: 1.5 },
          }}
        >
          <Tooltip title="Back to Dashboard (Esc)" arrow placement="bottom">
            <Button
              variant="contained"
              color="primary"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate("/dashboard")}
              sx={{
                mr: { xs: 0.5, sm: 1 },
                bgcolor: "rgba(255,255,255,0.15)",
                "&:hover": { bgcolor: "rgba(255,255,255,0.25)" },
                borderRadius: 2,
                textTransform: "none",
                fontSize: { xs: "0.7rem", sm: "0.8rem" },
                py: 0.25,
                px: { xs: 0.75, sm: 1 },
                minWidth: "auto",
                height: { xs: "28px", sm: "32px" },
              }}
            >
              {t('chatbot.back')}
            </Button>
          </Tooltip>

          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => {
              // Clear all current messages
              setMessages([]);
              // Set new context ID
              setContextId(Date.now().toString());
              // Set new chat flag
              setIsNewChat(true);
              // Switch to chat tab
              setActiveTab(0);
            }}
            sx={{
              mr: { xs: 0.5, sm: 1 },
              bgcolor: "rgba(255,255,255,0.15)",
              "&:hover": { bgcolor: "rgba(255,255,255,0.25)" },
              borderRadius: 2,
              textTransform: "none",
              fontSize: { xs: "0.7rem", sm: "0.8rem" },
              py: 0.25,
              px: { xs: 0.75, sm: 1 },
              minWidth: "auto",
              height: { xs: "28px", sm: "32px" },
            }}
                      >
            {t('chatbot.newChat')}
          </Button>

          <Box sx={{ display: "flex", alignItems: "center", flexGrow: 1 }}>
            <Box
              component="img"
              src={chatbotIcon}
              alt="Quamin SmartBot"
              sx={{
                width: { xs: 28, sm: 36 },
                height: { xs: 28, sm: 36 },
                borderRadius: "50%",
                mr: 1,
                border: "2px solid white",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              }}
            />
            <Box>
              <Typography
                variant="h6"
                component="div"
                sx={{
                  fontWeight: "bold",
                  fontSize: { xs: "0.85rem", sm: "1rem" },
                  lineHeight: 1.1,
                  whiteSpace: "nowrap",
                }}
              >
                Quamin SmartBot
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  fontSize: { xs: "0.65rem", sm: "0.75rem" },
                  opacity: 0.9,
                  mt: 0.1,
                }}
              >
                Powered by Quamin
              </Typography>
            </Box>
          </Box>
        </Toolbar>

        {/* Tabs */}
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            bgcolor: "#f5f5f5",
            minHeight: { xs: "40px", sm: "46px" },
            "& .MuiTab-root": {
              minHeight: { xs: "40px", sm: "46px" },
              py: { xs: 0.25, sm: 0.5 },
              color: "rgba(0, 0, 0, 0.7)",
            },
            "& .Mui-selected": {
              color: "primary.main",
              fontWeight: "bold",
            },
            "& .MuiTabs-indicator": {
              height: 2,
              borderTopLeftRadius: 2,
              borderTopRightRadius: 2,
            },
          }}
        >
          <Tab
            icon={<ChatIcon />}
            label={t('chatbot.chat')}
            sx={{
              fontSize: { xs: "0.7rem", sm: "0.8rem" },
              textTransform: "none",
              "& .MuiSvgIcon-root": {
                fontSize: { xs: "1.2rem", sm: "1.3rem" },
              },
            }}
          />
          <Tab
            icon={<HistoryToggleOffIcon />}
            label={t('chatbot.history')}
            onClick={() => fetchChatHistory()}
            sx={{
              fontSize: { xs: "0.7rem", sm: "0.8rem" },
              textTransform: "none",
              "& .MuiSvgIcon-root": {
                fontSize: { xs: "1.2rem", sm: "1.3rem" },
              },
            }}
          />
          <Tab
            icon={<AnalysisIcon />}
            label={t('chatbot.analysis')}
            sx={{
              fontSize: { xs: "0.7rem", sm: "0.8rem" },
              textTransform: "none",
              "& .MuiSvgIcon-root": {
                fontSize: { xs: "1.2rem", sm: "1.3rem" },
              },
            }}
          />
        </Tabs>
      </AppBar>

      {/* Tab Content */}
      <Box
        sx={{
          flexGrow: 1,
          overflow: "hidden",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          height: "calc(100vh - 146px)", // Further reduced header height to gain more space
        }}
      >
        {activeTab === 0 && renderChatTab()}
        {activeTab === 1 && renderHistoryTab()}
        {activeTab === 2 && renderAnalysisTab()}
      </Box>

      {/* Language Menu */}
      <Menu
        anchorEl={languageMenuAnchor}
        open={Boolean(languageMenuAnchor)}
        onClose={() => setLanguageMenuAnchor(null)}
      >
        {languageOptions.map((lang) => (
          <MenuItem
            key={lang.code}
            onClick={() => handleLanguageChange(lang.code)}
            selected={selectedLanguage === lang.code}
          >
            {lang.name}
          </MenuItem>
        ))}
      </Menu>

      {/* Followup Questions Menu */}
      <Menu
        anchorEl={followupMenuAnchor}
        open={Boolean(followupMenuAnchor)}
        onClose={() => setFollowupMenuAnchor(null)}
        PaperProps={{
          sx: { width: 280, maxWidth: "100%" },
        }}
      >
        {followupQuestions.map((question, index) => (
          <MenuItem
            key={index}
            onClick={() => handleFollowupQuestion(question)}
            sx={{ whiteSpace: "normal" }}
          >
            {question}
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};

export default ChatbotPage;
