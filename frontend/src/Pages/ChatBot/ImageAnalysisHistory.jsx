import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActionArea,
  Chip,
  Divider,
  CircularProgress,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Pagination,
  useTheme,
  useMediaQuery,
  FormControl,
  Select,
  MenuItem,
  InputLabel
} from '@mui/material';
import {
  ExpandMore,
  CalendarMonth,
  FilterList,
  Search,
  Close,
  Download,
  Share,
  ArrowBack,
  ArrowForward,
  CompareArrows,
  AddPhotoAlternate,
  Translate as TranslateIcon
} from '@mui/icons-material';
// Import date-fns directly
import { format } from 'date-fns';
import AnalysisComparisonDialog from './components/AnalysisComparisonDialog';
import translationService from '../../utils/TranslationService';

/**
 * Component for displaying image analysis history with filtering and detailed view
 * @param {Object} props - Component props
 * @param {Array} props.initialAnalyses - Initial analyses data
 * @param {Function} props.onRefresh - Function to refresh analyses data
 * @param {Object} props.currentUser - Current user information
 */
const ImageAnalysisHistory = ({ initialAnalyses = [], onRefresh, currentUser }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // State
  const [analyses, setAnalyses] = useState(initialAnalyses || []);
  const [analysesByDate, setAnalysesByDate] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedAnalysis, setSelectedAnalysis] = useState(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [filterDialogOpen, setFilterDialogOpen] = useState(false);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [comparisonDialogOpen, setComparisonDialogOpen] = useState(false);
  // Use global language context instead of local state
  const { selectedLanguage, setSelectedLanguage } = useLanguage();
  const { t } = useTranslation();
  const [translatedAnalysis, setTranslatedAnalysis] = useState({});
  const [translatedLabels, setTranslatedLabels] = useState({});
  const [isTranslating, setIsTranslating] = useState(false);

  // Log props for debugging
  console.log('ImageAnalysisHistory props:', { initialAnalyses, currentUser });

  // Create refs for debouncing and tracking initial fetch
  const fetchTimeoutRef = useRef(null);
  const initialFetchDoneRef = useRef(false);
  const isFirstRender = useRef(true);

  // Debounced fetch function
  const debouncedFetch = useCallback((pageNum = 1, filters = {}) => {
    // Clear any existing timeout
    if (fetchTimeoutRef.current) {
      clearTimeout(fetchTimeoutRef.current);
    }

    // Set a new timeout
    fetchTimeoutRef.current = setTimeout(() => {
      fetchAnalysisHistory(pageNum, filters);
    }, 300); // 300ms debounce time
  }, []);

  // Fetch image analysis history
  const fetchAnalysisHistory = async (pageNum = 1, filters = {}) => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams({
        page: pageNum,
        limit: 12
      });

      if (filters.startDate) {
        params.append('startDate', filters.startDate.toISOString().split('T')[0]);
      }

      if (filters.endDate) {
        params.append('endDate', filters.endDate.toISOString().split('T')[0]);
      }

      if (filters.query) {
        params.append('query', filters.query);
      }

      console.log('Fetching analysis history with params:', params.toString());

      // Call the API
      const response = await fetch(`/api/chat/analysis-history?${params.toString()}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analysis history');
      }

      const data = await response.json();
      console.log('Received analysis history data:', data);

      if (data.success) {
        // Get analyses from API
        const analysesArray = data.data.analyses || [];
        setAnalyses(analysesArray);

        // Organize analyses by date
          const byDate = {};

          analysesArray.forEach(analysis => {
            if (!analysis.timestamp) {
              console.log('Analysis missing timestamp:', analysis);
              analysis.timestamp = new Date().toISOString();
            }

            try {
              const date = new Date(analysis.timestamp);
              const dateKey = date.toISOString().split('T')[0]; // YYYY-MM-DD format

              if (!byDate[dateKey]) {
                byDate[dateKey] = [];
              }

              byDate[dateKey].push(analysis);
            } catch (error) {
              console.error('Error processing analysis date:', error);
            }
          });

        // Sort analyses within each date by timestamp
          Object.keys(byDate).forEach(dateKey => {
            byDate[dateKey].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
          });

        console.log('Organized analyses by date:', byDate);
          setAnalysesByDate(byDate);

        setTotalPages(data.data.pagination?.totalPages || 1);
      } else {
        throw new Error(data.message || 'Failed to fetch analysis history');
      }
    } catch (err) {
      console.error('Error fetching analysis history:', err);
      setError(err.message);
      // On error, show empty state
      setAnalyses([]);
      setAnalysesByDate({});
    } finally {
      setIsLoading(false);
    }
  };

  // Effect to translate analysis when selectedAnalysis changes and language is not English
  useEffect(() => {
    if (selectedAnalysis && selectedLanguage !== 'en-IN') {
      // Force refresh translations whenever language or analysis changes
      const event = { target: { value: selectedLanguage } };
      handleLanguageChange(event);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedAnalysis, selectedLanguage]);

  // Update state when initialAnalyses changes from parent component
  useEffect(() => {
    if (!isFirstRender.current) {
      return;
    }

    isFirstRender.current = false;
    console.log('ImageAnalysisHistory: First render, fetching analyses from API');

    // Always fetch analyses from API on first render
    fetchAnalysisHistory();

    // Cleanup function to clear any pending timeouts
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, []); // Only run on first render

  // Handle updates from parent component (new analyses from chat)
  useEffect(() => {
    // Skip first render as it's handled above
    if (isFirstRender.current) {
      return;
    }

    console.log('ImageAnalysisHistory: initialAnalyses updated from parent:', initialAnalyses?.length);

    if (initialAnalyses && Array.isArray(initialAnalyses) && initialAnalyses.length > 0) {
      // These are real analyses from the parent (ChatbotPage)
      setAnalyses(initialAnalyses);

      // Organize by date
      const byDate = {};
      initialAnalyses.forEach(analysis => {
        if (!analysis.timestamp) {
          analysis.timestamp = new Date().toISOString();
        }

        try {
          const date = new Date(analysis.timestamp);
          const dateKey = date.toISOString().split('T')[0];

          if (!byDate[dateKey]) {
            byDate[dateKey] = [];
          }

          byDate[dateKey].push(analysis);
        } catch (error) {
          console.error('Error processing analysis date:', error);
        }
      });

      // Sort analyses within each date by timestamp
      Object.keys(byDate).forEach(dateKey => {
        byDate[dateKey].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      });

      setAnalysesByDate(byDate);
    } else {
      // If no analyses from parent, clear the display
      setAnalyses([]);
      setAnalysesByDate({});
      }
  }, [initialAnalyses]); // React to changes in initialAnalyses

  // Handle page change
  const handlePageChange = (event, value) => {
    setPage(value);
    debouncedFetch(value, { startDate, endDate, query: searchQuery });
  };

  // Handle filter apply
  const handleApplyFilter = () => {
    setPage(1);
    debouncedFetch(1, { startDate, endDate, query: searchQuery });
    setFilterDialogOpen(false);
  };

  // Handle filter reset
  const handleResetFilter = () => {
    setStartDate(null);
    setEndDate(null);
    setSearchQuery('');
    setPage(1);
    debouncedFetch(1, {});
    setFilterDialogOpen(false);
  };

  // Handle analysis card click
  const handleAnalysisClick = (analysis) => {
    setSelectedAnalysis(analysis);
    setDetailDialogOpen(true);
  };

  // Handle export analysis
  const handleExportAnalysis = async () => {
    if (!selectedAnalysis) return;

    try {
      setIsLoading(true);

      // Show a message to the user
      const messageElement = document.createElement('div');
      messageElement.style.position = 'fixed';
      messageElement.style.top = '20px';
      messageElement.style.left = '50%';
      messageElement.style.transform = 'translateX(-50%)';
      messageElement.style.backgroundColor = '#4CAF50';
      messageElement.style.color = 'white';
      messageElement.style.padding = '10px 20px';
      messageElement.style.borderRadius = '4px';
      messageElement.style.zIndex = '9999';
      messageElement.textContent = 'Generating PDF, please wait...';
      document.body.appendChild(messageElement);
      
      // First test if jsPDF and autoTable are working correctly
      try {
        console.log('Testing PDF generation...');
        const testPdfFunction = (await import('../../utils/testPdf')).default;
        const testBlob = await testPdfFunction();
        console.log('Test PDF generated successfully');
      } catch (testError) {
        console.error('Test PDF generation failed:', testError);
        throw new Error(`PDF test failed: ${testError.message}`);
      }
      
      // Import the PDF export service dynamically to avoid issues with SSR
      const PdfExportService = (await import('../../utils/PdfExportService')).default;
      
      // Generate the PDF
      const pdfBlob = await PdfExportService.generateAnalysisPDF(
        selectedAnalysis.analysis, 
        selectedAnalysis.imageUrl
      );
      
      // Update message
      messageElement.textContent = 'PDF generated successfully!';
      
      // Create a download link
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${selectedAnalysis.analysis?.identifiedAs || 'crop'}_analysis_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      
      // Clean up
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      // Remove message after a delay
      setTimeout(() => {
        try {
          document.body.removeChild(messageElement);
        } catch (e) {
          // Element might have been removed already
        }
      }, 3000);
      
    } catch (error) {
      console.error('Error exporting analysis to PDF:', error);
      
      // Show error message
      const errorElement = document.createElement('div');
      errorElement.style.position = 'fixed';
      errorElement.style.top = '20px';
      errorElement.style.left = '50%';
      errorElement.style.transform = 'translateX(-50%)';
      errorElement.style.backgroundColor = '#F44336';
      errorElement.style.color = 'white';
      errorElement.style.padding = '10px 20px';
      errorElement.style.borderRadius = '4px';
      errorElement.style.zIndex = '9999';
      errorElement.textContent = `Failed to export analysis: ${error.message}`;
      document.body.appendChild(errorElement);
      
      // Remove error message after a delay
      setTimeout(() => {
        try {
          document.body.removeChild(errorElement);
        } catch (e) {
          // Element might have been removed already
        }
      }, 5000);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle compare analysis
  const handleCompareAnalysis = () => {
    if (!selectedAnalysis || !selectedAnalysis.analysis || !selectedAnalysis.analysis.identifiedAs) {
      alert('Cannot compare: No crop type identified in the selected analysis.');
      return;
    }

    setComparisonDialogOpen(true);
  };

  // Handle delete analysis
  const handleDeleteAnalysis = async (analysis) => {
    try {
      if (!window.confirm('Are you sure you want to delete this analysis?')) {
        return;
      }

      setIsLoading(true);

      // Check if we have an analysis ID to delete from server
      if (analysis.id) {
        console.log('Deleting analysis from server with ID:', analysis.id);
        
        const response = await fetch(`/api/chat/analysis-history/${encodeURIComponent(analysis.id)}`, {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to delete analysis from server');
        }

        console.log('Successfully deleted analysis from server');
      } else {
        console.log('No analysis ID found, deleting from local state only');
      }

      // Remove from local state
      const uniqueIdentifier = analysis.id || `${new Date(analysis.timestamp).toISOString()}-${analysis.imageUrl}`;
      console.log('Deleting analysis with identifier:', uniqueIdentifier);

      const updatedAnalyses = analyses.filter(a => {
        const aIdentifier = a.id || `${new Date(a.timestamp).toISOString()}-${a.imageUrl}`;
        return aIdentifier !== uniqueIdentifier;
      });

      console.log(`Filtered analyses from ${analyses.length} to ${updatedAnalyses.length}`);
      setAnalyses(updatedAnalyses);

      // Reorganize by date
      const byDate = {};
      updatedAnalyses.forEach(a => {
        if (!a.timestamp) {
          console.log('Analysis missing timestamp during delete reorganization:', a);
          return;
        }

        try {
          const date = new Date(a.timestamp);
          if (isNaN(date.getTime())) {
            console.log('Invalid date during delete reorganization:', a.timestamp);
            return;
          }

          const dateKey = date.toISOString().split('T')[0];

          if (!byDate[dateKey]) {
            byDate[dateKey] = [];
          }

          byDate[dateKey].push(a);
        } catch (error) {
          console.error('Error processing date during delete reorganization:', error);
        }
      });

      // Sort analyses within each date
      Object.keys(byDate).forEach(dateKey => {
        byDate[dateKey].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      });

      console.log('Reorganized analyses by date after deletion:', byDate);
      setAnalysesByDate(byDate);

      // Close the detail dialog if it's open
      if (detailDialogOpen) {
        setDetailDialogOpen(false);
      }

      // Call onRefresh if provided to update parent component
      if (onRefresh) {
        onRefresh();
      }

      alert('Analysis deleted successfully');
    } catch (error) {
      console.error('Error deleting analysis:', error);
      setError('Failed to delete analysis: ' + error.message);
      alert('Failed to delete analysis: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle share analysis
  const handleShareAnalysis = () => {
    if (!selectedAnalysis) return;
    alert('Share functionality will be implemented soon');
  };
  
  // Handle language change and translation
  const handleLanguageChange = async (event) => {
    const newLanguage = event.target.value;
    setSelectedLanguage(newLanguage);
    
    // Only translate if not English and we have selectedAnalysis
    if (newLanguage !== 'en-IN' && selectedAnalysis) {
      try {
        // Set translation loading state to true
        setIsTranslating(true);
        
        // Static headings and labels to translate
        const staticLabels = {
          // Dialog title and sections
          cropAnalysisDetails: 'Crop Analysis Details',
          analysisSummary: 'Analysis Summary',
          healthStatus: 'Health Status:',
          confidenceLevel: 'Confidence Level:',
          highConfidence: 'High confidence',
          mediumConfidence: 'Medium confidence',
          lowConfidence: 'Low confidence',
          issuesDetected: 'Issues Detected',
          noIssuesDetected: 'No issues detected',
          recommendations: 'Recommendations',
          noRecommendations: 'No recommendations available',
          translating: 'Translating...',
          
          // Agricultural Practices section
          agriculturalPractices: 'Agricultural Practices',
          culturalPractices: 'Cultural Practices',
          mechanicalPractices: 'Mechanical Practices',
          biologicalPractices: 'Biological Practices',
          chemicalPractices: 'Chemical Practices',
          
          // AI Insights section
          aiInsights: 'AI Insights',
          cropHealthAnalysis: 'Crop Health Analysis',
          growthStage: 'Growth stage:',
          leafColor: 'Leaf color:',
          canopyDensity: 'Canopy density:',
          stressIndicators: 'Stress indicators:',
          estimatedYieldPotential: 'Estimated Yield Potential',
          currentTrajectory: 'Current trajectory:',
          limitingFactors: 'Limiting factors:',
          improvementPotential: 'Improvement potential:',
          detailedPestDisease: 'Detailed Pest & Disease Assessment',
          managementRecommendations: 'Management Recommendations Timeline',
          immediate: 'Immediate (0-7 days):',
          shortTerm: 'Short-term (1-3 weeks):',
          longTerm: 'Long-term (season):',
          noPestDisease: 'No significant pest or disease pressure detected at this time',
          
          // Soil Analysis section
          soilCompositionAnalysis: 'Soil Composition Analysis',
          texture: 'Texture:',
          structure: 'Structure:',
          porosity: 'Porosity:',
          waterRetention: 'Water retention:',
          estimatedNutrientContent: 'Estimated Nutrient Content',
          organicMatter: 'Organic Matter:',
          nitrogen: 'Nitrogen (N):',
          phosphorus: 'Phosphorus (P):',
          potassium: 'Potassium (K):',
          calcium: 'Calcium (Ca):',
          magnesium: 'Magnesium (Mg):',
          soilHealthIndicators: 'Soil Health Indicators',
          estimatedPH: 'Estimated pH:',
          cationExchange: 'Cation Exchange Capacity (CEC):',
          biologicalActivity: 'Biological activity:',
          compactionRisk: 'Compaction risk:',
          agriculturalPotential: 'Agricultural Potential',
          cropSuitability: 'Crop suitability:',
          irrigationNeeds: 'Irrigation needs:',
          drainage: 'Drainage:',
          fertilityManagement: 'Fertility management:',
          
          // Button labels
          compareGrowth: 'Compare Growth',
          delete: t('chatbot.delete'),
          exportAnalysis: 'Export Analysis',
          share: 'Share'
        };
        
        // First, translate all dynamic content from the analysis
        const translatedContent = {};
        
        // 1. Translate the crop name/type
        if (selectedAnalysis.analysis?.identifiedAs) {
          translatedContent.identifiedAs = await translationService.translateText(
            selectedAnalysis.analysis.identifiedAs, 
            newLanguage
          );
        }
        
        // 2. Translate the main analysis summary text
        if (selectedAnalysis.text) {
          translatedContent.summaryText = await translationService.translateText(
            selectedAnalysis.text,
            newLanguage
          );
        }
        
        // 2b. Translate the date/time
        const date = new Date(selectedAnalysis.timestamp);
        const formattedDate = format(date, 'EEEE, MMMM d, yyyy');
        const formattedTime = format(date, 'h:mm a');
        translatedContent.dateTime = await translationService.translateText(
          `${formattedDate} at ${formattedTime}`,
          newLanguage
        );
        
        // 3. Translate all issues
        const issues = [];
        const pestDescriptions = [];
        
        if (selectedAnalysis.analysis?.issues && selectedAnalysis.analysis.issues.length > 0) {
          // Translate the issues themselves
          for (const issue of selectedAnalysis.analysis.issues) {
            // First, split the issue text to identify technical terms that need separate translation
            const technicalTerms = issue.match(/([\w\s]+scab|[\w\s]+disease|[\w\s]+pest|[\w\s]+deficiency|[\w\s]+rot|[\w\s]+blight)/gi) || [];
            
            let translatedIssue = issue;
            
            // Translate technical terms separately to ensure they're translated correctly
            for (const term of technicalTerms) {
              const translatedTerm = await translationService.translateText(term.trim(), newLanguage);
              translatedIssue = translatedIssue.replace(term, translatedTerm);
            }
            
            // Now translate the full text with replaced technical terms
            translatedIssue = await translationService.translateText(translatedIssue, newLanguage);
            issues.push(translatedIssue);
            
            // Also translate the pest descriptions that appear in the detailed assessment
            if (issue.toLowerCase().includes('disease') || issue.toLowerCase().includes('pest')) {
              // Handle the technical terms separately and explicitly
              // For "Moderate presence, requires prompt treatment" - translate parts separately
              const moderatePresenceText = await translationService.translateText('Moderate presence', newLanguage);
              const requiresPromptText = await translationService.translateText('requires prompt treatment', newLanguage);
              const translatedModerate = `${moderatePresenceText}, ${requiresPromptText}`;
              
              // For "Early stage, treatable with targeted intervention" - translate parts separately
              const earlyStageText = await translationService.translateText('Early stage', newLanguage);
              const treatableText = await translationService.translateText('treatable with targeted intervention', newLanguage);
              const translatedEarly = `${earlyStageText}, ${treatableText}`;
              
              // Add these to the pest descriptions
              pestDescriptions.push(pestDescriptions.length % 2 === 0 ? translatedEarly : translatedModerate);
            }
          }
          
          translatedContent.issues = issues;
          
          if (pestDescriptions.length > 0) {
            translatedContent.pestDescriptions = pestDescriptions;
          }
        }
        
        // 4. Translate all recommendations
        const recommendations = [];
        if (selectedAnalysis.analysis?.recommendations && selectedAnalysis.analysis.recommendations.length > 0) {
          for (const rec of selectedAnalysis.analysis.recommendations) {
            const translatedRec = await translationService.translateText(rec, newLanguage);
            recommendations.push(translatedRec);
          }
          translatedContent.recommendations = recommendations;
        }
        
        // 4b. Translate agricultural practices
        if (selectedAnalysis.analysis?.agriculturalPractices) {
          // Translate cultural practices
          const culturalPractices = [];
          if (selectedAnalysis.analysis.agriculturalPractices.cultural?.length > 0) {
            for (const practice of selectedAnalysis.analysis.agriculturalPractices.cultural) {
              const translatedPractice = await translationService.translateText(practice, newLanguage, { forceNewRequest: true });
              culturalPractices.push(translatedPractice);
            }
            console.log('Translated cultural practices:', culturalPractices);
          }
          
          // Translate mechanical practices
          const mechanicalPractices = [];
          if (selectedAnalysis.analysis.agriculturalPractices.mechanical?.length > 0) {
            for (const practice of selectedAnalysis.analysis.agriculturalPractices.mechanical) {
              const translatedPractice = await translationService.translateText(practice, newLanguage, { forceNewRequest: true });
              mechanicalPractices.push(translatedPractice);
            }
            console.log('Translated mechanical practices:', mechanicalPractices);
          }
          
          // Translate biological practices
          const biologicalPractices = [];
          if (selectedAnalysis.analysis.agriculturalPractices.biological?.length > 0) {
            for (const practice of selectedAnalysis.analysis.agriculturalPractices.biological) {
              const translatedPractice = await translationService.translateText(practice, newLanguage, { forceNewRequest: true });
              biologicalPractices.push(translatedPractice);
            }
            console.log('Translated biological practices:', biologicalPractices);
          }
          
          // Translate chemical practices
          const chemicalPractices = [];
          if (selectedAnalysis.analysis.agriculturalPractices.chemical?.length > 0) {
            for (const practice of selectedAnalysis.analysis.agriculturalPractices.chemical) {
              const translatedPractice = await translationService.translateText(practice, newLanguage, { forceNewRequest: true });
              chemicalPractices.push(translatedPractice);
            }
            console.log('Translated chemical practices:', chemicalPractices);
          }
          
          // If we don't have API-provided practices, generate default practices and translate them
          if (!selectedAnalysis.analysis.agriculturalPractices.cultural?.length && 
              !selectedAnalysis.analysis.agriculturalPractices.mechanical?.length && 
              !selectedAnalysis.analysis.agriculturalPractices.biological?.length && 
              !selectedAnalysis.analysis.agriculturalPractices.chemical?.length) {
                
            // Generate default cultural practices
            const defaultCulturalPractices = [
              `Crop rotation: Rotate ${selectedAnalysis.analysis?.identifiedAs} with legumes or non-host crops to break pest cycles.`,
              `Row spacing: Optimal spacing for ${selectedAnalysis.analysis?.identifiedAs} is typically 75-90 cm between rows.`,
              `Mulching: Apply organic mulch to conserve moisture and suppress weeds.`,
              `Intercropping: Consider companion planting with pest-repelling plants.`
            ];
            
            // Generate default mechanical practices
            const defaultMechanicalPractices = [
              `Tillage: ${selectedAnalysis.analysis?.healthStatus === 'poor' ? 'Deep' : 'Minimum'} tillage recommended based on current soil conditions.`,
              `Hand weeding: Regular weeding helps reduce competition for nutrients.`,
              `Trellising: ${selectedAnalysis.analysis?.identifiedAs === 'tomato' || selectedAnalysis.analysis?.identifiedAs === 'cucumber' ? 'Essential for proper growth and fruit development.' : 'Not typically required for this crop.'}`,
              `Pruning: ${selectedAnalysis.analysis?.identifiedAs === 'tomato' || selectedAnalysis.analysis?.identifiedAs === 'grape' ? 'Regular pruning helps improve air circulation and sunlight exposure.' : 'Limited pruning needed, focus on removing damaged parts.'}`
            ];
            
            // Generate default biological practices
            const defaultBiologicalPractices = [
              `Beneficial insects: Introduce ladybugs and lacewings to control aphids${selectedAnalysis.analysis?.issues?.some(i => i.toLowerCase().includes('aphid')) ? ' (highly recommended based on current issues)' : '.'}`,
              `Microbial inoculants: Apply rhizobacteria to improve nutrient uptake and root health.`,
              `Nematode control: Use nematode-suppressive cover crops in rotation.`,
              `Trap crops: Plant ${selectedAnalysis.analysis?.identifiedAs === 'cotton' ? 'okra as trap crop for bollworms' : 'mustard as trap crop for pest diversion'}.`
            ];
            
            // Generate default chemical practices
            const defaultChemicalPractices = [
              `Fertilization: ${selectedAnalysis.analysis?.issues?.some(i => i.toLowerCase().includes('deficiency')) ? 'Apply balanced NPK with focus on deficient nutrients' : 'Balanced NPK application based on soil test results'}.`,
              `Fungicide: ${selectedAnalysis.analysis?.issues?.some(i => i.toLowerCase().includes('fungal') || i.toLowerCase().includes('disease')) ? 'Apply copper-based fungicide at recommended rates' : 'Preventive application before rainy season'}.`,
              `pH management: Maintain soil pH between 6.0-7.0 for optimal nutrient availability.`,
              `Integrated approach: Combine chemical controls with other management practices for sustainable pest management.`
            ];
            
            // Translate default practices
            for (const practice of defaultCulturalPractices) {
              const translatedPractice = await translationService.translateText(practice, newLanguage, { forceNewRequest: true });
              culturalPractices.push(translatedPractice);
            }
            
            for (const practice of defaultMechanicalPractices) {
              const translatedPractice = await translationService.translateText(practice, newLanguage, { forceNewRequest: true });
              mechanicalPractices.push(translatedPractice);
            }
            
            for (const practice of defaultBiologicalPractices) {
              const translatedPractice = await translationService.translateText(practice, newLanguage, { forceNewRequest: true });
              biologicalPractices.push(translatedPractice);
            }
            
            for (const practice of defaultChemicalPractices) {
              const translatedPractice = await translationService.translateText(practice, newLanguage, { forceNewRequest: true });
              chemicalPractices.push(translatedPractice);
            }
          }
          
          translatedContent.culturalPractices = culturalPractices;
          translatedContent.mechanicalPractices = mechanicalPractices;
          translatedContent.biologicalPractices = biologicalPractices;
          translatedContent.chemicalPractices = chemicalPractices;
        }
        
        // 5. Translate AI Insights sections (if they exist)
        const aiInsightFields = [
          'growthStage',
          'leafColor',
          'canopyDensity',
          'stressIndicators',
          'yieldTrajectory',
          'limitingFactors',
          'improvementPotential',
          'pestAssessment',
          'immediateRec',
          'shortTermRec',
          'longTermRec'
        ];
        
        // 5b. Translate soil analysis content if the crop is identified as soil
        if (selectedAnalysis.analysis?.identifiedAs?.toLowerCase().includes('soil')) {
          // Soil composition section - translate technical terms separately for better accuracy
          const soilTexture = `${selectedAnalysis.analysis?.identifiedAs || 'Loamy'} - balanced mixture of sand, silt, and clay particles`;
          const soilStructure = 'Moderately aggregated with medium crumb structure';
          const soilPorosity = 'Moderate (approximately 45-55%)';
          const soilWaterRetention = 'Medium capacity (holds approximately 1.5-2 inches of water per foot)';
          
          // Break down soil texture translation into components
          const balancedMixture = await translationService.translateText('balanced mixture of', newLanguage);
          const sandSiltClay = await translationService.translateText('sand, silt, and clay particles', newLanguage);
          const soilTypeTranslated = await translationService.translateText(selectedAnalysis.analysis?.identifiedAs || 'Loamy', newLanguage);
          const fullSoilTexture = `${soilTypeTranslated} - ${balancedMixture} ${sandSiltClay}`;
          
          // Nutrient content section
          const organicMatter = 'Low (approximately 1-2%)';
          const nitrogen = 'Low to moderate availability';
          const phosphorus = 'Moderate availability';
          const potassium = 'Moderate to high availability';
          const calcium = 'Adequate levels';
          const magnesium = 'Adequate levels';
          
          // Translate these with specific handling of technical terms
          translatedContent.soilTexture = fullSoilTexture;
          translatedContent.soilStructure = await translationService.translateText(soilStructure, newLanguage);
          translatedContent.soilPorosity = await translationService.translateText(soilPorosity, newLanguage);
          translatedContent.soilWaterRetention = await translationService.translateText(soilWaterRetention, newLanguage);
          
          // Translate nutrient names separately for accuracy
          const lowApprox = await translationService.translateText('Low (approximately 1-2%)', newLanguage);
          const lowToModerate = await translationService.translateText('Low to moderate availability', newLanguage);
          const moderateAvail = await translationService.translateText('Moderate availability', newLanguage);
          const modToHigh = await translationService.translateText('Moderate to high availability', newLanguage);
          const adequateLevels = await translationService.translateText('Adequate levels', newLanguage);
          
          const organicMatterTerm = await translationService.translateText('Organic matter', newLanguage);
          const nitrogenTerm = await translationService.translateText('Nitrogen', newLanguage);
          const phosphorusTerm = await translationService.translateText('Phosphorus', newLanguage);
          const potassiumTerm = await translationService.translateText('Potassium', newLanguage);
          const calciumTerm = await translationService.translateText('Calcium', newLanguage);
          const magnesiumTerm = await translationService.translateText('Magnesium', newLanguage);
          
          translatedContent.organicMatter = `${organicMatterTerm}: ${lowApprox}`;
          translatedContent.nitrogen = `${nitrogenTerm}: ${lowToModerate}`;
          translatedContent.phosphorus = `${phosphorusTerm}: ${moderateAvail}`;
          translatedContent.potassium = `${potassiumTerm}: ${modToHigh}`;
          translatedContent.calcium = `${calciumTerm}: ${adequateLevels}`;
          translatedContent.magnesium = `${magnesiumTerm}: ${adequateLevels}`;
          
          // Continue with rest of translations
          translatedContent.estimatedPH = await translationService.translateText(estimatedPH, newLanguage);
          translatedContent.cationExchange = await translationService.translateText(cationExchange, newLanguage);
          translatedContent.biologicalActivity = await translationService.translateText(biologicalActivity, newLanguage);
          translatedContent.compactionRisk = await translationService.translateText(compactionRisk, newLanguage);
          
          translatedContent.cropSuitability = await translationService.translateText(cropSuitability, newLanguage);
          translatedContent.irrigationNeeds = await translationService.translateText(irrigationNeeds, newLanguage);
          translatedContent.drainage = await translationService.translateText(drainage, newLanguage);
          translatedContent.fertilityManagement = await translationService.translateText(fertilityManagement, newLanguage);
        }
        
        // Default values for AI insight fields if they don't exist in the analysis
        const aiInsightDefaults = {
          growthStage: 'Mid-season vegetative growth',
          leafColor: selectedAnalysis.analysis?.healthStatus === 'good' ? 
              'Deep green, indicating adequate nitrogen' : 
              'Slightly pale, suggesting possible nutrient deficiency',
          canopyDensity: selectedAnalysis.analysis?.healthStatus === 'good' ? 
              'Optimal' : 'Below optimal',
          stressIndicators: selectedAnalysis.analysis?.issues?.length > 0 ? 
              'Present - see Issues Detected section' : 
              'Minimal visible stress',
          yieldTrajectory: selectedAnalysis.analysis?.healthStatus === 'good' ? 
              'On track for optimal yield' : 
              selectedAnalysis.analysis?.healthStatus === 'fair' ? 
                'Moderate yield potential with proper management' : 
                'Below average yield potential without intervention',
          limitingFactors: selectedAnalysis.analysis?.issues?.length > 0 ? 
              selectedAnalysis.analysis.issues[0] : 
              'None significant detected',
          improvementPotential: 'High - implementing recommended actions could significantly improve outcomes',
          pestAssessment: selectedAnalysis.analysis?.issues?.some(issue => 
              issue.toLowerCase().includes('disease') || issue.toLowerCase().includes('pest')) ?
              'Detected issues require treatment' :
              'No significant pest or disease pressure detected at this time',
          immediateRec: selectedAnalysis.analysis?.recommendations?.[0] || 'Monitor for changes in plant health',
          shortTermRec: selectedAnalysis.analysis?.recommendations?.[1] || 'Apply balanced fertilizer if not recently applied',
          longTermRec: 'Implement crop rotation plan for next season to improve soil health and break pest cycles'
        };
        
        // Translate each AI insight field
        for (const field of aiInsightFields) {
          // Use the field from analysis if it exists, otherwise use default
          const fieldValue = selectedAnalysis.analysis?.[field] || aiInsightDefaults[field];
          if (fieldValue) {
            translatedContent[field] = await translationService.translateText(fieldValue, newLanguage);
          }
        }
        
        // 6. Translate any other dynamic fields that might be in the analysis object
        if (selectedAnalysis.analysis) {
          for (const [key, value] of Object.entries(selectedAnalysis.analysis)) {
            // Skip fields we've already translated
            if (
              key === 'issues' || 
              key === 'recommendations' || 
              key === 'identifiedAs' || 
              key === 'healthStatus' ||
              key === 'confidence' ||
              aiInsightFields.includes(key)
            ) {
              continue;
            }
            
            // Only translate string values
            if (typeof value === 'string') {
              translatedContent[key] = await translationService.translateText(value, newLanguage);
            }
          }
        }
        
        // Translate static labels
        const translatedStaticLabels = {};
        for (const [key, value] of Object.entries(staticLabels)) {
          translatedStaticLabels[key] = await translationService.translateText(value, newLanguage);
        }
        
        setTranslatedAnalysis(translatedContent);
        setTranslatedLabels(translatedStaticLabels);
        console.log('Translation complete - Content:', translatedContent);
        console.log('Translation complete - Labels:', translatedStaticLabels);
      } catch (error) {
        console.error('Translation error:', error);
      } finally {
        // Set translation loading state to false when done
        setIsTranslating(false);
      }
    } else {
      // Reset translations when switching back to English
      setTranslatedAnalysis({});
      setTranslatedLabels({});
    }
  };

  // Render health status chip
  const renderHealthStatusChip = (status) => {
    if (!status) return <span><Chip label="Unknown" size="small" /></span>;

    switch (status.toLowerCase()) {
      case 'good':
        return <span><Chip label="Good" size="small" color="success" /></span>;
      case 'moderate':
        return <span><Chip label="Moderate" size="small" color="warning" /></span>;
      case 'poor':
        return <span><Chip label="Poor" size="small" color="error" /></span>;
      default:
        return <span><Chip label={status} size="small" /></span>;
    }
  };

  // Render analysis card
  const renderAnalysisCard = (analysis) => {
    if (!analysis) return null;

    const date = new Date(analysis.timestamp);
    const formattedTime = format(date, 'h:mm a');

    return (
      <Card
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          transition: 'transform 0.2s',
          '&:hover': {
            transform: 'scale(1.02)',
            boxShadow: 3
          }
        }}
      >
        <CardActionArea onClick={() => handleAnalysisClick(analysis)}>
          <CardMedia
            component="img"
            height="140"
            image={analysis.imageUrl || 'https://via.placeholder.com/300x200?text=No+Image'}
            alt={analysis.analysis?.identifiedAs || 'Image analysis'}
            sx={{ objectFit: 'cover' }}
          />
          <CardContent sx={{ flexGrow: 1, pb: 1 }}>
            <Typography variant="h6" component="div" noWrap>
              {analysis.analysis?.identifiedAs || 'Unknown'}
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1, mb: 1 }}>
              {renderHealthStatusChip(analysis.analysis?.healthStatus)}
              <Typography variant="caption" component="span" sx={{ ml: 'auto' }}>
                {formattedTime}
              </Typography>
            </Box>

            <Typography variant="body2" color="text.secondary" sx={{
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              height: '40px'
            }}>
              {analysis.text || 'No analysis text available'}
            </Typography>
          </CardContent>
        </CardActionArea>
      </Card>
    );
  };

  // Render empty state
  const renderEmptyState = () => {
    console.log('Rendering empty state');
    return (
      <Box sx={{ textAlign: 'center', py: 6 }}>
        <Box
          component="img"
          src="https://cdn-icons-png.flaticon.com/512/1829/1829552.png"
          alt="No analyses"
          sx={{ width: 120, height: 120, opacity: 0.6, mb: 2 }}
        />
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No Image Analyses Found
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ maxWidth: 400, mx: 'auto', mb: 3 }}>
          Upload an image in the chat to analyze crops, diseases, or pests. Your analysis history will appear here.
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddPhotoAlternate />}
          onClick={() => {
            // Navigate to chat tab
            window.dispatchEvent(new CustomEvent('navigate-to-chat'));
          }}
        >
          Upload an Image
        </Button>
      </Box>
    );
  };

  // Render date sections helper function
  const renderDateSections = (byDateData) => {
    // Make sure we have valid date keys
    if (!byDateData || typeof byDateData !== 'object' || Object.keys(byDateData).length === 0) {
      return renderEmptyState();
    }

    return Object.keys(byDateData)
      .sort((a, b) => new Date(b) - new Date(a)) // Sort dates in descending order
      .map(dateKey => {
        const dateAnalyses = byDateData[dateKey];
        if (!dateAnalyses || !Array.isArray(dateAnalyses) || dateAnalyses.length === 0) {
          return null;
        }

        const formattedDate = format(new Date(dateKey), 'EEEE, MMMM d, yyyy');

        return (
          <Box key={dateKey} sx={{ mb: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <CalendarMonth sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6">{formattedDate}</Typography>
            </Box>

            <Grid container spacing={3}>
              {dateAnalyses.map((analysis, index) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={`${dateKey}-${index}`}>
                  {renderAnalysisCard(analysis)}
                </Grid>
              ))}
            </Grid>
          </Box>
        );
      }).filter(Boolean); // Filter out any null entries
  };

  // Render analysis by date
  const renderAnalysesByDate = () => {
    // If no analyses at all
    if ((!analyses || analyses.length === 0) && (!analysesByDate || Object.keys(analysesByDate).length === 0)) {
      return renderEmptyState();
    }

    // If we have analyses by date, render them
    if (analysesByDate && Object.keys(analysesByDate).length > 0) {
      return renderDateSections(analysesByDate);
    }

    // If we have analyses but no analysesByDate, show loading
    if (analyses && analyses.length > 0 && (!analysesByDate || Object.keys(analysesByDate).length === 0)) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      );
    }

    // Fallback
    return renderEmptyState();
  };

  // Render analysis detail dialog
  const renderAnalysisDetailDialog = () => {
    if (!selectedAnalysis) return null;

    const date = new Date(selectedAnalysis.timestamp);
    const formattedDate = format(date, 'EEEE, MMMM d, yyyy');
    const formattedTime = format(date, 'h:mm a');

    return (
      <Dialog
        open={detailDialogOpen}
        onClose={() => setDetailDialogOpen(false)}
        maxWidth="md"
        fullWidth
        fullScreen={isMobile}
      >
        <DialogTitle 
          sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center', 
            py: 1.5,
            px: 2,
            position: 'relative'
          }}
        >
          <Typography 
            component="div" 
            sx={{ 
              fontSize: '1.1rem', 
              fontWeight: 500,
              maxWidth: '60%'
            }}
          >
            {translatedLabels.cropAnalysisDetails || 'Crop Analysis Details'}
          </Typography>
          
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            position: 'absolute',
            right: 8,
            top: '50%',
            transform: 'translateY(-50%)'
          }}>
            <Box sx={{ mr: 1.5, position: 'relative' }}>
              <FormControl size="small" sx={{ width: 100 }}>
                <Select
                  value={selectedLanguage}
                  onChange={handleLanguageChange}
                  displayEmpty
                  variant="outlined"
                  disabled={isTranslating}
                  startAdornment={<TranslateIcon fontSize="small" sx={{ ml: 0.5, mr: -0.5, color: 'action.active' }} />}
                  sx={{ 
                    height: 32,
                    '& .MuiSelect-select': {
                      py: 0.5,
                      pr: 2,
                      pl: 2.5
                    }
                  }}
                >
                  <MenuItem value="en-IN">English</MenuItem>
                  <MenuItem value="hi-IN">हिंदी</MenuItem>
                  <MenuItem value="bn-IN">বাংলা</MenuItem>
                  <MenuItem value="te-IN">తెలుగు</MenuItem>
                  <MenuItem value="ta-IN">தமிழ்</MenuItem>
                  <MenuItem value="kn-IN">ಕನ್ನಡ</MenuItem>
                  <MenuItem value="ml-IN">മലയാളം</MenuItem>
                  <MenuItem value="mr-IN">मराठी</MenuItem>
                  <MenuItem value="gu-IN">ગુજરાતી</MenuItem>
                  <MenuItem value="pa-IN">ਪੰਜਾਬੀ</MenuItem>
                </Select>
              </FormControl>
              {isTranslating && (
                <CircularProgress 
                  size={20} 
                  sx={{ 
                    position: 'absolute',
                    top: '50%',
                    right: 8,
                    marginTop: '-10px'
                  }}
                />
              )}
            </Box>
            <IconButton 
              onClick={() => setDetailDialogOpen(false)}
              edge="end"
              size="small"
              sx={{ p: 0.5 }}
            >
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent dividers sx={{ px: { xs: 1, sm: 2 }, py: 2 }}>
          {/* Add loading overlay here */}
          {isTranslating && (
            <Box 
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                zIndex: 10
              }}
            >
              <CircularProgress size={60} />
              <Typography variant="h6" sx={{ mt: 2 }}>
                {selectedLanguage === 'en-IN' ? 'Translating...' : translatedLabels.translating || 'Translating...'}
              </Typography>
            </Box>
          )}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box sx={{ position: 'relative' }}>
                <img
                  src={selectedAnalysis.imageUrl}
                  alt={selectedAnalysis.analysis?.identifiedAs || 'Crop analysis'}
                  style={{
                    width: '100%',
                    borderRadius: theme.shape.borderRadius,
                    maxHeight: '400px',
                    objectFit: 'contain'
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    bgcolor: 'rgba(0,0,0,0.6)',
                    color: 'white',
                    p: 1,
                    borderBottomLeftRadius: theme.shape.borderRadius,
                    borderBottomRightRadius: theme.shape.borderRadius,
                  }}
                >
                  <Typography variant="body2">
                    {selectedLanguage !== 'en-IN' && translatedAnalysis.dateTime ? 
                      translatedAnalysis.dateTime : 
                      `${formattedDate} at ${formattedTime}`}
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ mt: 2 }}>
                <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {translatedLabels.analysisSummary || 'Analysis Summary'}
                </Typography>
                <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                  {selectedLanguage !== 'en-IN' && translatedAnalysis.summaryText ? 
                    translatedAnalysis.summaryText : selectedAnalysis.text}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="h6" gutterBottom>
                  {selectedLanguage !== 'en-IN' && translatedAnalysis.identifiedAs ? 
                    translatedAnalysis.identifiedAs : 
                    selectedAnalysis.analysis?.identifiedAs || 'Unknown Crop'}
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="body2" sx={{ mr: 1 }}>
                    {translatedLabels.healthStatus || 'Health Status:'}
                  </Typography>
                  {renderHealthStatusChip(selectedAnalysis.analysis?.healthStatus)}
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" sx={{ mb: 0.5 }}>
                    {translatedLabels.confidenceLevel || 'Confidence Level:'}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ position: 'relative', display: 'inline-flex', mr: 1 }}>
                      <CircularProgress
                        variant="determinate"
                        value={(selectedAnalysis.analysis?.confidence || 0) * 100}
                        size={40}
                        thickness={4}
                        sx={{
                          color: (selectedAnalysis.analysis?.confidence || 0) > 0.8
                            ? 'success.main'
                            : (selectedAnalysis.analysis?.confidence || 0) > 0.6
                              ? 'warning.main'
                              : 'error.main'
                        }}
                      />
                      <Box
                        sx={{
                          top: 0,
                          left: 0,
                          bottom: 0,
                          right: 0,
                          position: 'absolute',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Typography variant="caption" component="div" color="text.secondary">
                          {Math.round((selectedAnalysis.analysis?.confidence || 0) * 100)}%
                        </Typography>
                      </Box>
                    </Box>
                    <Typography variant="body2">
                      {(selectedAnalysis.analysis?.confidence || 0) > 0.8
                        ? (translatedLabels.highConfidence || 'High confidence')
                        : (selectedAnalysis.analysis?.confidence || 0) > 0.6
                          ? (translatedLabels.mediumConfidence || 'Medium confidence')
                          : (translatedLabels.lowConfidence || 'Low confidence')}
                    </Typography>
                  </Box>
                </Box>
              </Paper>

              <Accordion defaultExpanded sx={{ mb: 2 }}>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography>{translatedLabels.issuesDetected || 'Issues Detected'}</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  {selectedAnalysis.analysis?.issues && selectedAnalysis.analysis.issues.length > 0 ? (
                    <Box component="ul" sx={{ pl: 2, mt: 0 }}>
                      {selectedAnalysis.analysis.issues.map((issue, index) => (
                        <Typography component="li" key={index} variant="body2" sx={{ mb: 1 }}>
                          {selectedLanguage !== 'en-IN' && translatedAnalysis.issues && translatedAnalysis.issues[index] ? 
                            translatedAnalysis.issues[index] : issue}
                        </Typography>
                      ))}
                    </Box>
                  ) : (
                    <Typography variant="body2">{translatedLabels.noIssuesDetected || 'No issues detected'}</Typography>
                  )}
                </AccordionDetails>
              </Accordion>

              <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography>{translatedLabels.recommendations || 'Recommendations'}</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  {selectedAnalysis.analysis?.recommendations && selectedAnalysis.analysis.recommendations.length > 0 ? (
                    <Box component="ul" sx={{ pl: 2, mt: 0 }}>
                      {selectedAnalysis.analysis.recommendations.map((rec, index) => (
                        <Typography component="li" key={index} variant="body2" sx={{ mb: 1 }}>
                          {selectedLanguage !== 'en-IN' && translatedAnalysis.recommendations && translatedAnalysis.recommendations[index] ? 
                            translatedAnalysis.recommendations[index] : rec}
                        </Typography>
                      ))}
                    </Box>
                  ) : (
                    <Typography variant="body2">{translatedLabels.noRecommendations || 'No recommendations available'}</Typography>
                  )}
                </AccordionDetails>
              </Accordion>

              {/* Agricultural Practices Section */}
              <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography>{translatedLabels.agriculturalPractices || 'Agricultural Practices'}</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  {selectedAnalysis.analysis?.identifiedAs && (
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      {/* Cultural Practices */}
                      <Paper variant="outlined" sx={{ p: 1.5 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                          {translatedLabels.culturalPractices || 'Cultural Practices'}
                        </Typography>
                        <Box component="ul" sx={{ pl: 2, mt: 0, mb: 0 }}>
                          {selectedLanguage !== 'en-IN' && translatedAnalysis.culturalPractices && translatedAnalysis.culturalPractices.length > 0 ? (
                            // Use translated practices when available
                            translatedAnalysis.culturalPractices.map((practice, index) => (
                              <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5 }}>
                                {practice}
                              </Typography>
                            ))
                          ) : selectedLanguage !== 'en-IN' ? (
                            // If language is not English but translations aren't ready yet, force translate
                            // and show loading indicators
                            <>
                              {(selectedAnalysis.analysis?.agriculturalPractices?.cultural || []).map((practice, index) => (
                                <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5, display: 'flex', alignItems: 'center' }}>
                                  <Box component="span" sx={{ mr: 1, display: 'inline-flex' }}>
                                    <CircularProgress size={12} thickness={8} />
                                  </Box>
                                  {practice}
                                </Typography>
                              ))}
                              {/* Initialize translation immediately */}
                              <Box sx={{ display: 'none' }}>
                                {(() => {
                                  // Side effect to trigger translation
                                  if (selectedAnalysis.analysis?.agriculturalPractices?.cultural?.length > 0) {
                                    Promise.all(
                                      selectedAnalysis.analysis.agriculturalPractices.cultural.map(
                                        practice => translationService.translateText(practice, selectedLanguage, { forceNewRequest: true })
                                      )
                                    ).then(translations => {
                                      // Update state with translations
                                      setTranslatedAnalysis(prev => ({
                                        ...prev,
                                        culturalPractices: translations
                                      }));
                                    }).catch(err => console.error('Translation error:', err));
                                  } else if (selectedAnalysis.analysis?.identifiedAs) {
                                    // Use default practices if needed
                                    const defaultPractices = [
                                      `Crop rotation: Rotate ${selectedAnalysis.analysis?.identifiedAs} with legumes or non-host crops to break pest cycles.`,
                                      `Row spacing: Optimal spacing for ${selectedAnalysis.analysis?.identifiedAs} is typically 75-90 cm between rows.`,
                                      `Mulching: Apply organic mulch to conserve moisture and suppress weeds.`,
                                      `Intercropping: Consider companion planting with pest-repelling plants.`
                                    ];
                                    
                                    Promise.all(
                                      defaultPractices.map(
                                        practice => translationService.translateText(practice, selectedLanguage, { forceNewRequest: true })
                                      )
                                    ).then(translations => {
                                      // Update state with translations
                                      setTranslatedAnalysis(prev => ({
                                        ...prev,
                                        culturalPractices: translations
                                      }));
                                    }).catch(err => console.error('Translation error:', err));
                                  }
                                  return null;
                                })()}
                              </Box>
                            </>
                          ) : selectedAnalysis.analysis?.agriculturalPractices?.cultural ? (
                            // Use original practices data if available but language is English
                            selectedAnalysis.analysis.agriculturalPractices.cultural.map((practice, index) => (
                              <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5 }}>
                                {practice}
                              </Typography>
                            ))
                          ) : (
                            // Fallback content if no practices data available
                            <>
                              <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                {`Crop rotation: Rotate ${selectedAnalysis.analysis?.identifiedAs} with legumes or non-host crops to break pest cycles.`}
                              </Typography>
                              <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                {`Row spacing: Optimal spacing for ${selectedAnalysis.analysis?.identifiedAs} is typically 75-90 cm between rows.`}
                              </Typography>
                              <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                {`Mulching: Apply organic mulch to conserve moisture and suppress weeds.`}
                              </Typography>
                              <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                {`Intercropping: Consider companion planting with pest-repelling plants.`}
                              </Typography>
                            </>
                          )}
                        </Box>
                      </Paper>

                      {/* Mechanical Practices */}
                      <Paper variant="outlined" sx={{ p: 1.5 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                          {translatedLabels.mechanicalPractices || 'Mechanical Practices'}
                        </Typography>
                        <Box component="ul" sx={{ pl: 2, mt: 0, mb: 0 }}>
                          {selectedLanguage !== 'en-IN' && translatedAnalysis.mechanicalPractices && translatedAnalysis.mechanicalPractices.length > 0 ? (
                            // Use translated practices when available
                            translatedAnalysis.mechanicalPractices.map((practice, index) => (
                              <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5 }}>
                                {practice}
                              </Typography>
                            ))
                          ) : selectedLanguage !== 'en-IN' ? (
                            // If language is not English but translations aren't ready yet, force translate
                            // and show loading indicators
                            <>
                              {(selectedAnalysis.analysis?.agriculturalPractices?.mechanical || []).map((practice, index) => (
                                <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5, display: 'flex', alignItems: 'center' }}>
                                  <Box component="span" sx={{ mr: 1, display: 'inline-flex' }}>
                                    <CircularProgress size={12} thickness={8} />
                                  </Box>
                                  {practice}
                                </Typography>
                              ))}
                              {/* Initialize translation immediately */}
                              <Box sx={{ display: 'none' }}>
                                {(() => {
                                  // Side effect to trigger translation
                                  if (selectedAnalysis.analysis?.agriculturalPractices?.mechanical?.length > 0) {
                                    Promise.all(
                                      selectedAnalysis.analysis.agriculturalPractices.mechanical.map(
                                        practice => translationService.translateText(practice, selectedLanguage, { forceNewRequest: true })
                                      )
                                    ).then(translations => {
                                      // Update state with translations
                                      setTranslatedAnalysis(prev => ({
                                        ...prev,
                                        mechanicalPractices: translations
                                      }));
                                    }).catch(err => console.error('Translation error:', err));
                                  } else if (selectedAnalysis.analysis?.healthStatus) {
                                    // Use default practices if needed
                                    const defaultPractices = [
                                      `Tillage: ${selectedAnalysis.analysis?.healthStatus === 'poor' ? 'Deep' : 'Minimum'} tillage recommended based on current soil conditions.`,
                                      `Hand weeding: Regular weeding helps reduce competition for nutrients.`,
                                      `Trellising: ${selectedAnalysis.analysis?.identifiedAs === 'tomato' || selectedAnalysis.analysis?.identifiedAs === 'cucumber' ? 'Essential for proper growth and fruit development.' : 'Not typically required for this crop.'}`,
                                      `Pruning: ${selectedAnalysis.analysis?.identifiedAs === 'tomato' || selectedAnalysis.analysis?.identifiedAs === 'grape' ? 'Regular pruning helps improve air circulation and sunlight exposure.' : 'Limited pruning needed, focus on removing damaged parts.'}`
                                    ];
                                    
                                    Promise.all(
                                      defaultPractices.map(
                                        practice => translationService.translateText(practice, selectedLanguage, { forceNewRequest: true })
                                      )
                                    ).then(translations => {
                                      // Update state with translations
                                      setTranslatedAnalysis(prev => ({
                                        ...prev,
                                        mechanicalPractices: translations
                                      }));
                                    }).catch(err => console.error('Translation error:', err));
                                  }
                                  return null;
                                })()}
                              </Box>
                            </>
                          ) : selectedAnalysis.analysis?.agriculturalPractices?.mechanical ? (
                            // Use original practices data if available but language is English
                            selectedAnalysis.analysis.agriculturalPractices.mechanical.map((practice, index) => (
                              <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5 }}>
                                {practice}
                              </Typography>
                            ))
                          ) : (
                            // Fallback content if no practices data available
                            <>
                                <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                  {`Tillage: ${selectedAnalysis.analysis?.healthStatus === 'poor' ? 'Deep' : 'Minimum'} tillage recommended based on current soil conditions.`}
                                </Typography>
                                <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                  {`Hand weeding: Regular weeding helps reduce competition for nutrients.`}
                                </Typography>
                                <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                  {`Trellising: ${selectedAnalysis.analysis?.identifiedAs === 'tomato' || selectedAnalysis.analysis?.identifiedAs === 'cucumber' ? 'Essential for proper growth and fruit development.' : 'Not typically required for this crop.'}`}
                                </Typography>
                                <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                  {`Pruning: ${selectedAnalysis.analysis?.identifiedAs === 'tomato' || selectedAnalysis.analysis?.identifiedAs === 'grape' ? 'Regular pruning helps improve air circulation and sunlight exposure.' : 'Limited pruning needed, focus on removing damaged parts.'}`}
                                </Typography>
                            </>
                          )}
                        </Box>
                      </Paper>

                      {/* Biological Practices */}
                      <Paper variant="outlined" sx={{ p: 1.5 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                          {translatedLabels.biologicalPractices || 'Biological Practices'}
                        </Typography>
                        <Box component="ul" sx={{ pl: 2, mt: 0, mb: 0 }}>
                          {selectedLanguage !== 'en-IN' && translatedAnalysis.biologicalPractices && translatedAnalysis.biologicalPractices.length > 0 ? (
                            // Use translated practices when available
                            translatedAnalysis.biologicalPractices.map((practice, index) => (
                              <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5 }}>
                                {practice}
                              </Typography>
                            ))
                          ) : selectedLanguage !== 'en-IN' ? (
                            // If language is not English but translations aren't ready yet, force translate
                            // and show loading indicators
                            <>
                              {(selectedAnalysis.analysis?.agriculturalPractices?.biological || []).map((practice, index) => (
                                <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5, display: 'flex', alignItems: 'center' }}>
                                  <Box component="span" sx={{ mr: 1, display: 'inline-flex' }}>
                                    <CircularProgress size={12} thickness={8} />
                                  </Box>
                                  {practice}
                                </Typography>
                              ))}
                              {/* Initialize translation immediately */}
                              <Box sx={{ display: 'none' }}>
                                {(() => {
                                  // Side effect to trigger translation
                                  if (selectedAnalysis.analysis?.agriculturalPractices?.biological?.length > 0) {
                                    Promise.all(
                                      selectedAnalysis.analysis.agriculturalPractices.biological.map(
                                        practice => translationService.translateText(practice, selectedLanguage, { forceNewRequest: true })
                                      )
                                    ).then(translations => {
                                      // Update state with translations
                                      setTranslatedAnalysis(prev => ({
                                        ...prev,
                                        biologicalPractices: translations
                                      }));
                                    }).catch(err => console.error('Translation error:', err));
                                  } else if (selectedAnalysis.analysis?.identifiedAs) {
                                    // Use default practices if needed
                                    const defaultPractices = [
                                      `Beneficial insects: Introduce ladybugs and lacewings to control aphids${selectedAnalysis.analysis?.issues?.some(i => i.toLowerCase().includes('aphid')) ? ' (highly recommended based on current issues)' : '.'}`,
                                      `Microbial inoculants: Apply rhizobacteria to improve nutrient uptake and root health.`,
                                      `Nematode control: Use nematode-suppressive cover crops in rotation.`,
                                      `Trap crops: Plant ${selectedAnalysis.analysis?.identifiedAs === 'cotton' ? 'okra as trap crop for bollworms' : 'mustard as trap crop for pest diversion'}.`
                                    ];
                                    
                                    Promise.all(
                                      defaultPractices.map(
                                        practice => translationService.translateText(practice, selectedLanguage, { forceNewRequest: true })
                                      )
                                    ).then(translations => {
                                      // Update state with translations
                                      setTranslatedAnalysis(prev => ({
                                        ...prev,
                                        biologicalPractices: translations
                                      }));
                                    }).catch(err => console.error('Translation error:', err));
                                  }
                                  return null;
                                })()}
                              </Box>
                            </>
                          ) : selectedAnalysis.analysis?.agriculturalPractices?.biological ? (
                            // Use original practices data if available but language is English
                            selectedAnalysis.analysis.agriculturalPractices.biological.map((practice, index) => (
                              <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5 }}>
                                {practice}
                              </Typography>
                            ))
                          ) : (
                            // Fallback content if no practices data available
                            <>
                              <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                {`Beneficial insects: Introduce ladybugs and lacewings to control aphids${selectedAnalysis.analysis?.issues?.some(i => i.toLowerCase().includes('aphid')) ? ' (highly recommended based on current issues)' : '.'}`}
                              </Typography>
                              <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                {`Microbial inoculants: Apply rhizobacteria to improve nutrient uptake and root health.`}
                              </Typography>
                              <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                {`Nematode control: Use nematode-suppressive cover crops in rotation.`}
                              </Typography>
                              <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                {`Trap crops: Plant ${selectedAnalysis.analysis?.identifiedAs === 'cotton' ? 'okra as trap crop for bollworms' : 'mustard as trap crop for pest diversion'}.`}
                              </Typography>
                            </>
                          )}
                        </Box>
                      </Paper>

                      {/* Chemical Practices */}
                      <Paper variant="outlined" sx={{ p: 1.5 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                          {translatedLabels.chemicalPractices || 'Chemical Practices'}
                        </Typography>
                        <Box component="ul" sx={{ pl: 2, mt: 0, mb: 0 }}>
                          {selectedLanguage !== 'en-IN' && translatedAnalysis.chemicalPractices && translatedAnalysis.chemicalPractices.length > 0 ? (
                            // Use translated practices when available
                            translatedAnalysis.chemicalPractices.map((practice, index) => (
                              <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5 }}>
                                {practice}
                              </Typography>
                            ))
                          ) : selectedLanguage !== 'en-IN' ? (
                            // If language is not English but translations aren't ready yet, force translate
                            // and show loading indicators
                            <>
                              {(selectedAnalysis.analysis?.agriculturalPractices?.chemical || []).map((practice, index) => (
                                <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5, display: 'flex', alignItems: 'center' }}>
                                  <Box component="span" sx={{ mr: 1, display: 'inline-flex' }}>
                                    <CircularProgress size={12} thickness={8} />
                                  </Box>
                                  {practice}
                                </Typography>
                              ))}
                              {/* Initialize translation immediately */}
                              <Box sx={{ display: 'none' }}>
                                {(() => {
                                  // Side effect to trigger translation
                                  if (selectedAnalysis.analysis?.agriculturalPractices?.chemical?.length > 0) {
                                    Promise.all(
                                      selectedAnalysis.analysis.agriculturalPractices.chemical.map(
                                        practice => translationService.translateText(practice, selectedLanguage, { forceNewRequest: true })
                                      )
                                    ).then(translations => {
                                      // Update state with translations
                                      setTranslatedAnalysis(prev => ({
                                        ...prev,
                                        chemicalPractices: translations
                                      }));
                                    }).catch(err => console.error('Translation error:', err));
                                  } else if (selectedAnalysis.analysis?.issues) {
                                    // Use default practices if needed
                                    const defaultPractices = [
                                      `Fertilization: ${selectedAnalysis.analysis?.issues?.some(i => i.toLowerCase().includes('deficiency')) ? 'Apply balanced NPK with focus on deficient nutrients' : 'Balanced NPK application based on soil test results'}.`,
                                      `Fungicide: ${selectedAnalysis.analysis?.issues?.some(i => i.toLowerCase().includes('fungal') || i.toLowerCase().includes('disease')) ? 'Apply copper-based fungicide at recommended rates' : 'Preventive application before rainy season'}.`,
                                      `pH management: Maintain soil pH between 6.0-7.0 for optimal nutrient availability.`,
                                      `Integrated approach: Combine chemical controls with other management practices for sustainable pest management.`
                                    ];
                                    
                                    Promise.all(
                                      defaultPractices.map(
                                        practice => translationService.translateText(practice, selectedLanguage, { forceNewRequest: true })
                                      )
                                    ).then(translations => {
                                      // Update state with translations
                                      setTranslatedAnalysis(prev => ({
                                        ...prev,
                                        chemicalPractices: translations
                                      }));
                                    }).catch(err => console.error('Translation error:', err));
                                  }
                                  return null;
                                })()}
                              </Box>
                            </>
                          ) : selectedAnalysis.analysis?.agriculturalPractices?.chemical ? (
                            // Use original practices data if available but language is English
                            selectedAnalysis.analysis.agriculturalPractices.chemical.map((practice, index) => (
                              <Typography component="li" key={index} variant="body2" sx={{ mb: 0.5 }}>
                                {practice}
                              </Typography>
                            ))
                          ) : (
                            // Fallback content if no practices data available
                            <>
                              <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                {`Fertilization: ${selectedAnalysis.analysis?.issues?.some(i => i.toLowerCase().includes('deficiency')) ? 'Apply balanced NPK with focus on deficient nutrients' : 'Balanced NPK application based on soil test results'}.`}
                              </Typography>
                              <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                {`Fungicide: ${selectedAnalysis.analysis?.issues?.some(i => i.toLowerCase().includes('fungal') || i.toLowerCase().includes('disease')) ? 'Apply copper-based fungicide at recommended rates' : 'Preventive application before rainy season'}.`}
                              </Typography>
                              <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                {`pH management: Maintain soil pH between 6.0-7.0 for optimal nutrient availability.`}
                              </Typography>
                              <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                                {`Integrated approach: Combine chemical controls with other management practices for sustainable pest management.`}
                              </Typography>
                            </>
                          )}
                        </Box>
                      </Paper>
                    </Box>
                  )}
                </AccordionDetails>
              </Accordion>
              
              {/* Enhanced AI Insights Section */}
              <Accordion defaultExpanded sx={{ mb: 2 }}>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography>{translatedLabels.aiInsights || 'AI Insights'}</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  {selectedAnalysis.analysis?.identifiedAs?.toLowerCase().includes('soil') ? (
                    <>
                      {/* Soil Analysis */}
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mt: 1 }}>
                        {translatedLabels.soilCompositionAnalysis || 'Soil Composition Analysis'}
                      </Typography>
                      <Box component="ul" sx={{ pl: 2, mt: 0.5 }}>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.texture || 'Texture:'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.soilTexture ? 
                            translatedAnalysis.soilTexture : 
                            `${selectedAnalysis.analysis?.identifiedAs || 'Loamy'} - balanced mixture of sand, silt, and clay particles`}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.structure || 'Structure:'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.soilStructure ? 
                            translatedAnalysis.soilStructure : 
                            'Moderately aggregated with medium crumb structure'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.porosity || 'Porosity:'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.soilPorosity ? 
                            translatedAnalysis.soilPorosity : 
                            'Moderate (approximately 45-55%)'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.waterRetention || 'Water retention:'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.soilWaterRetention ? 
                            translatedAnalysis.soilWaterRetention : 
                            'Medium capacity (holds approximately 1.5-2 inches of water per foot)'}
                        </Typography>
                      </Box>
                      
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mt: 2 }}>
                        {translatedLabels.estimatedNutrientContent || 'Estimated Nutrient Content'}
                      </Typography>
                      <Box component="ul" sx={{ pl: 2, mt: 0.5 }}>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.organicMatter || 'Organic Matter:'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.organicMatter ? 
                            translatedAnalysis.organicMatter : 'Low (approximately 1-2%)'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.nitrogen || 'Nitrogen (N):'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.nitrogen ? 
                            translatedAnalysis.nitrogen : 'Low to moderate availability'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.phosphorus || 'Phosphorus (P):'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.phosphorus ? 
                            translatedAnalysis.phosphorus : 'Moderate availability'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.potassium || 'Potassium (K):'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.potassium ? 
                            translatedAnalysis.potassium : 'Moderate to high availability'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.calcium || 'Calcium (Ca):'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.calcium ? 
                            translatedAnalysis.calcium : 'Adequate levels'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.magnesium || 'Magnesium (Mg):'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.magnesium ? 
                            translatedAnalysis.magnesium : 'Adequate levels'}
                        </Typography>
                      </Box>
                      
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mt: 2 }}>
                        {translatedLabels.soilHealthIndicators || 'Soil Health Indicators'}
                      </Typography>
                      <Box component="ul" sx={{ pl: 2, mt: 0.5 }}>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.estimatedPH || 'Estimated pH:'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.estimatedPH ? 
                            translatedAnalysis.estimatedPH : '6.2-6.8 (slightly acidic to neutral)'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.cationExchange || 'Cation Exchange Capacity (CEC):'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.cationExchange ? 
                            translatedAnalysis.cationExchange : 'Medium (10-15 meq/100g)'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.biologicalActivity || 'Biological activity:'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.biologicalActivity ? 
                            translatedAnalysis.biologicalActivity : 'Moderate (some evidence of earthworm activity)'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.compactionRisk || 'Compaction risk:'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.compactionRisk ? 
                            translatedAnalysis.compactionRisk : 'Moderate (visible signs of potential compaction)'}
                        </Typography>
                      </Box>
                      
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mt: 2 }}>
                        {translatedLabels.agriculturalPotential || 'Agricultural Potential'}
                      </Typography>
                      <Box component="ul" sx={{ pl: 2, mt: 0.5 }}>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.cropSuitability || 'Crop suitability:'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.cropSuitability ? 
                            translatedAnalysis.cropSuitability : 'Well-suited for a wide range of crops including corn, wheat, soybeans, vegetables, and many fruit trees'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.irrigationNeeds || 'Irrigation needs:'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.irrigationNeeds ? 
                            translatedAnalysis.irrigationNeeds : 'Moderate - requires regular irrigation during dry periods'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.drainage || 'Drainage:'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.drainage ? 
                            translatedAnalysis.drainage : 'Good natural drainage with moderate water infiltration rate'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.fertilityManagement || 'Fertility management:'}</strong> {selectedLanguage !== 'en-IN' && translatedAnalysis.fertilityManagement ? 
                            translatedAnalysis.fertilityManagement : 'Will respond well to organic matter additions and balanced fertilization'}
                        </Typography>
                      </Box>
                    </>
                  ) : (
                    <>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mt: 1, mb: 2 }}>
                        {translatedLabels.cropHealthAnalysis || 'Crop Health Analysis'}
                      </Typography>
                      <Box component="ul" sx={{ pl: 2, mt: 0.5 }}>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.growthStage || 'Growth stage:'}</strong> {translatedAnalysis.growthStage || 
                            selectedAnalysis.analysis?.cropHealthDetails?.growthStage || 
                            selectedAnalysis.analysis?.growthStage || 'Mid-season vegetative growth'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.leafColor || 'Leaf color:'}</strong> {translatedAnalysis.leafColor || 
                            selectedAnalysis.analysis?.cropHealthDetails?.leafColor ||
                            (selectedAnalysis.analysis?.healthStatus === 'good' ? 
                            'Deep green, indicating adequate nitrogen' : 
                            'Slightly pale, suggesting possible nutrient deficiency')}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.canopyDensity || 'Canopy density:'}</strong> {translatedAnalysis.canopyDensity || 
                            selectedAnalysis.analysis?.cropHealthDetails?.canopyDensity ||
                            (selectedAnalysis.analysis?.healthStatus === 'good' ? 
                            'Optimal' : 'Below optimal') + ' for current growth stage'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.stressIndicators || 'Stress indicators:'}</strong> {translatedAnalysis.stressIndicators || 
                            selectedAnalysis.analysis?.cropHealthDetails?.stressIndicators ||
                            (selectedAnalysis.analysis?.issues?.length > 0 ? 
                            'Present - see Issues Detected section' : 
                            'Minimal visible stress')}
                        </Typography>
                      </Box>
                      
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mt: 2 }}>
                        {translatedLabels.estimatedYieldPotential || 'Estimated Yield Potential'}
                      </Typography>
                      <Box component="ul" sx={{ pl: 2, mt: 0.5 }}>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.currentTrajectory || 'Current trajectory:'}</strong> {translatedAnalysis.yieldTrajectory || 
                            selectedAnalysis.analysis?.cropHealthDetails?.yieldPotential?.currentTrajectory ||
                            (selectedAnalysis.analysis?.healthStatus === 'good' ? 
                            'On track for optimal yield' : 
                            selectedAnalysis.analysis?.healthStatus === 'fair' ? 
                            'Moderate yield potential with proper management' : 
                            'Below average yield potential without intervention')}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.limitingFactors || 'Limiting factors:'}</strong> {translatedAnalysis.limitingFactors || 
                            selectedAnalysis.analysis?.cropHealthDetails?.yieldPotential?.limitingFactors ||
                            (selectedAnalysis.analysis?.issues?.length > 0 ? 
                            selectedAnalysis.analysis.issues[0] : 
                            'None significant detected')}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.improvementPotential || 'Improvement potential:'}</strong> {translatedAnalysis.improvementPotential || 
                            selectedAnalysis.analysis?.cropHealthDetails?.yieldPotential?.improvementPotential ||
                            'High - implementing recommended actions could significantly improve outcomes'}
                        </Typography>
                      </Box>
                      
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mt: 2 }}>
                        {translatedLabels.detailedPestDisease || 'Detailed Pest & Disease Assessment'}
                      </Typography>
                      <Box component="ul" sx={{ pl: 2, mt: 0.5 }}>
                        {selectedAnalysis.analysis?.cropHealthDetails?.pestAndDiseaseAssessment?.detectedIssues?.length > 0 ? (
                          selectedAnalysis.analysis.cropHealthDetails.pestAndDiseaseAssessment.detectedIssues.map((issue, index) => (
                            <Typography key={index} component="li" variant="body2" sx={{ mb: 0.5 }}>
                              <strong>
                                {selectedLanguage !== 'en-IN' && translatedAnalysis.issues && translatedAnalysis.issues[index] ? 
                                  translatedAnalysis.issues[index] : issue}
                              </strong> - Risk Level: {selectedAnalysis.analysis.cropHealthDetails.pestAndDiseaseAssessment.riskLevel}
                            </Typography>
                          ))
                        ) : selectedAnalysis.analysis?.issues?.some(issue => 
                          issue.toLowerCase().includes('disease') || 
                          issue.toLowerCase().includes('pest')) ? (
                          selectedAnalysis.analysis.issues.map((issue, index) => (
                            <Typography key={index} component="li" variant="body2" sx={{ mb: 0.5 }}>
                              <strong>
                                {selectedLanguage !== 'en-IN' && translatedAnalysis.issues && translatedAnalysis.issues[index] ? 
                                  translatedAnalysis.issues[index] : issue}
                              </strong> - {selectedLanguage !== 'en-IN' && translatedAnalysis.pestDescriptions && translatedAnalysis.pestDescriptions[index] ? 
                                translatedAnalysis.pestDescriptions[index] : 
                                (index % 2 === 0 ? 
                                  'Early stage, treatable with targeted intervention' : 
                                  'Moderate presence, requires prompt treatment')}
                            </Typography>
                          ))
                        ) : (
                          <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                            {translatedAnalysis.pestAssessment || translatedLabels.noPestDisease || 
                              'No significant pest or disease pressure detected at this time'}
                          </Typography>
                        )}
                      </Box>
                      
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mt: 2 }}>
                        {translatedLabels.managementRecommendations || 'Management Recommendations Timeline'}
                      </Typography>
                      <Box component="ul" sx={{ pl: 2, mt: 0.5 }}>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.immediate || 'Immediate (0-7 days):'}</strong> {translatedAnalysis.immediateRec || 
                            selectedAnalysis.analysis?.recommendations?.[0] || 
                            'Monitor for changes in plant health'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.shortTerm || 'Short-term (1-3 weeks):'}</strong> {translatedAnalysis.shortTermRec || 
                            selectedAnalysis.analysis?.recommendations?.[1] || 
                            'Apply balanced fertilizer if not recently applied'}
                        </Typography>
                        <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                          <strong>{translatedLabels.longTerm || 'Long-term (season):'}</strong> {translatedAnalysis.longTermRec || 
                            'Implement crop rotation plan for next season to improve soil health and break pest cycles'}
                        </Typography>
                      </Box>
                    </>
                  )}
                </AccordionDetails>
              </Accordion>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            startIcon={<CompareArrows />}
            variant="outlined"
            onClick={handleCompareAnalysis}
            disabled={!selectedAnalysis?.analysis?.identifiedAs}
            sx={{ mr: 'auto' }}
          >
            {translatedLabels.compareGrowth || 'Compare Growth'}
          </Button>
          <Button
            startIcon={<Close />}
            variant="outlined"
            color="error"
            onClick={() => handleDeleteAnalysis(selectedAnalysis)}
          >
            {translatedLabels.delete || 'Delete'}
          </Button>
          <Button
            startIcon={<Download />}
            variant="outlined"
            onClick={handleExportAnalysis}
          >
            {translatedLabels.exportAnalysis || 'Export Analysis'}
          </Button>
          <Button
            startIcon={<Share />}
            variant="contained"
            onClick={handleShareAnalysis}
          >
            {translatedLabels.share || 'Share'}
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Render filter dialog
  const renderFilterDialog = () => (
    <Dialog
      open={filterDialogOpen}
      onClose={() => setFilterDialogOpen(false)}
      maxWidth="xs"
      fullWidth
    >
      <DialogTitle>
        {t('chatbot.filter')} {t('chatbot.imageAnalysisHistory')}
      </DialogTitle>

      <DialogContent dividers>
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Date Range
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexDirection: isMobile ? 'column' : 'row' }}>
              <TextField
                label="Start Date"
                type="date"
                value={startDate ? format(startDate, 'yyyy-MM-dd') : ''}
                onChange={(e) => setStartDate(e.target.value ? new Date(e.target.value) : null)}
                fullWidth
                size="small"
                InputLabelProps={{ shrink: true }}
              />
              <TextField
                label="End Date"
                type="date"
                value={endDate ? format(endDate, 'yyyy-MM-dd') : ''}
                onChange={(e) => setEndDate(e.target.value ? new Date(e.target.value) : null)}
                fullWidth
                size="small"
                InputLabelProps={{ shrink: true }}
              />
            </Box>
          </Box>

        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Search
          </Typography>
          <TextField
            fullWidth
            size="small"
            placeholder="Search by crop type, issues, etc."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: <Search fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />,
            }}
          />
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={handleResetFilter}>
          {t('chatbot.reset')}
        </Button>
        <Button
          variant="contained"
          onClick={handleApplyFilter}
        >
          {t('chatbot.applyFilters')}
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <Box sx={{
      p: { xs: 2, sm: 3 },
      height: '100%',
      maxHeight: 'calc(100vh - 120px)',
      overflow: 'auto',
      '&::-webkit-scrollbar': {
        width: '8px',
        backgroundColor: 'rgba(0,0,0,0.05)'
      },
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: 'rgba(0,0,0,0.2)',
        borderRadius: '4px'
      },
      '&::-webkit-scrollbar-thumb:hover': {
        backgroundColor: 'rgba(0,0,0,0.3)'
      }
    }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h1">
          {t('chatbot.imageAnalysisHistory')}
        </Typography>

        <Button
          variant="outlined"
          startIcon={<FilterList />}
          onClick={() => setFilterDialogOpen(true)}
        >
          {t('chatbot.filter')}
        </Button>
      </Box>

      {/* Loading state */}
      {isLoading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Error state */}
      {error && (
        <Paper
          elevation={1}
          sx={{
            p: 3,
            borderRadius: 2,
            textAlign: 'center',
            bgcolor: 'error.light',
            color: 'error.contrastText',
            mb: 3
          }}
        >
          <Typography variant="body1">
            {error}
          </Typography>
        </Paper>
      )}

      {/* Content */}
      {!isLoading && !error && (
        <>
          {renderAnalysesByDate()}

          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handlePageChange}
                color="primary"
                showFirstButton
                showLastButton
                siblingCount={isMobile ? 0 : 1}
              />
            </Box>
          )}
        </>
      )}

      {/* Dialogs */}
      {renderAnalysisDetailDialog()}
      {renderFilterDialog()}

      {/* Comparison Dialog */}
      {selectedAnalysis && (
        <AnalysisComparisonDialog
          open={comparisonDialogOpen}
          onClose={() => setComparisonDialogOpen(false)}
          currentAnalysis={selectedAnalysis}
          cropType={selectedAnalysis.analysis?.identifiedAs}
        />
      )}
    </Box>
  );
};

export default ImageAnalysisHistory;
