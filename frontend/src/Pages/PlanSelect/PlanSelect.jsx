import React from 'react';
import {
  Box,
  Container,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

const plans = [
  {
    title: 'Basic Plan',
    price: '$9.99',
    period: 'month',
    features: [
      'Basic crop monitoring',
      'Weather updates',
      'Email support',
      'Up to 5 acres',
    ],
  },
  {
    title: 'Pro Plan',
    price: '$19.99',
    period: 'month',
    features: [
      'Advanced crop monitoring',
      'Real-time weather alerts',
      'Priority support',
      'Up to 20 acres',
      'Soil analysis',
      'Crop recommendations',
    ],
    popular: true,
  },
  {
    title: 'Enterprise Plan',
    price: '$49.99',
    period: 'month',
    features: [
      'Full crop monitoring suite',
      'Custom weather alerts',
      '24/7 support',
      'Unlimited acres',
      'Advanced soil analysis',
      'AI-powered recommendations',
      'Market price tracking',
      'Export reports',
    ],
  },
];

const PlanSelect = () => {
  const handleSelectPlan = (plan) => {
    // Add your plan selection logic here
    console.log('Selected plan:', plan);
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 8 }}>
        <Typography
          variant="h3"
          component="h1"
          align="center"
          gutterBottom
          sx={{ mb: 6 }}
        >
          Choose Your Plan
        </Typography>
        <Grid container spacing={4} justifyContent="center">
          {plans.map((plan) => (
            <Grid item xs={12} sm={6} md={4} key={plan.title}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  ...(plan.popular && {
                    border: '2px solid',
                    borderColor: 'primary.main',
                  }),
                }}
              >
                {plan.popular && (
                  <Typography
                    variant="subtitle1"
                    sx={{
                      position: 'absolute',
                      top: -12,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      bgcolor: 'primary.main',
                      color: 'white',
                      px: 2,
                      py: 0.5,
                      borderRadius: 1,
                    }}
                  >
                    Most Popular
                  </Typography>
                )}
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="h4" component="h2" gutterBottom>
                    {plan.title}
                  </Typography>
                  <Typography variant="h3" color="primary" gutterBottom>
                    {plan.price}
                    <Typography component="span" variant="subtitle1" color="text.secondary">
                      /{plan.period}
                    </Typography>
                  </Typography>
                  <List>
                    {plan.features.map((feature) => (
                      <ListItem key={feature}>
                        <ListItemIcon>
                          <CheckCircleIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText primary={feature} />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
                <CardActions sx={{ p: 2 }}>
                  <Button
                    fullWidth
                    variant={plan.popular ? 'contained' : 'outlined'}
                    onClick={() => handleSelectPlan(plan)}
                  >
                    Select Plan
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Container>
  );
};

export default PlanSelect; 