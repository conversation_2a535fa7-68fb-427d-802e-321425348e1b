import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { RecaptchaVerifier, signInWithPhoneNumber } from "firebase/auth";
import { auth } from '../../config/firebase';
import "./Login.css";

const Login = () => {
    const navigate = useNavigate();
    const [countryCode, setCountryCode] = useState("+91");
    const [phoneNumber, setPhoneNumber] = useState("");
    const [otpValues, setOtpValues] = useState(["", "", "", "", "", ""]);
    const [isOtpRequested, setIsOtpRequested] = useState(false);
    const [confirmationResult, setConfirmationResult] = useState(null);
    const [resendAllowed, setResendAllowed] = useState(false);
    const [timer, setTimer] = useState(30);
    const [phoneError, setPhoneError] = useState("");
    const [otpError, setOtpError] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const otpRefs = useRef([...Array(6)].map(() => React.createRef()));
    const recaptchaVerifierRef = useRef(null);

    useEffect(() => {
        if (isOtpRequested && !resendAllowed) {
            const countdown = setInterval(() => {
                setTimer((prev) => {
                    if (prev === 1) {
                        clearInterval(countdown);
                        setResendAllowed(true);
                        return 30;
                    }
                    return prev - 1;
                });
            }, 1000);
            return () => clearInterval(countdown);
        }
    }, [isOtpRequested, resendAllowed]);

    useEffect(() => {
        if (isOtpRequested && otpRefs.current[0]?.current) {
            otpRefs.current[0].current.focus();
        }
    }, [isOtpRequested]);

    useEffect(() => {
        // Initialize RecaptchaVerifier
        if (!recaptchaVerifierRef.current) {
            recaptchaVerifierRef.current = new RecaptchaVerifier(auth, "recaptcha-container", {
                size: "invisible",
                callback: () => {
                    // reCAPTCHA solved
                },
            });
        }

        return () => {
            if (recaptchaVerifierRef.current) {
                recaptchaVerifierRef.current.clear();
                recaptchaVerifierRef.current = null;
            }
        };
    }, []);

    const handlePhoneNumberChange = (e) => {
        const { value } = e.target;
        if (!/^[0-9]{0,10}$/.test(value)) return;
        setPhoneNumber(value);
        if (phoneError) setPhoneError("");
    };

    const handleGetOtp = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);
        if (phoneNumber.length !== 10) {
            setPhoneError("Please enter a valid 10-digit phone number.");
            setIsSubmitting(false);
            return;
        }
        try {
            const fullPhoneNumber = `${countryCode}${phoneNumber}`;
            
            // Reset reCAPTCHA if it exists
            if (recaptchaVerifierRef.current) {
                recaptchaVerifierRef.current.clear();
                recaptchaVerifierRef.current = new RecaptchaVerifier(auth, "recaptcha-container", {
                    size: "invisible",
                    callback: () => {
                        // reCAPTCHA solved
                    },
                });
            }

            const confirmation = await signInWithPhoneNumber(auth, fullPhoneNumber, recaptchaVerifierRef.current);
            setConfirmationResult(confirmation);
            setIsOtpRequested(true);
            setResendAllowed(false);
            setTimer(30);
        } catch (error) {
            console.error("Error sending OTP:", error);
            setPhoneError(error.message || "Failed to send OTP. Please try again.");
        }
        setIsSubmitting(false);
    };

    const handleVerifyOtp = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);
        const typedOtp = otpValues.join("");
        if (typedOtp.length < 6) {
            setOtpError("OTP must be 6 digits.");
            setIsSubmitting(false);
            return;
        }
        try {
            const result = await confirmationResult.confirm(typedOtp);
            console.log("OTP verified successfully:", result.user);
            navigate("/tm-dashboard");
        } catch (error) {
            console.error("Error verifying OTP:", error);
            setOtpError("Invalid OTP. Please try again.");
        }
        setIsSubmitting(false);
    };

    const handleResendOtp = async () => {
        setIsSubmitting(true);
        try {
            await handleGetOtp(new Event("submit"));
        } catch (error) {
            console.error("Error resending OTP:", error);
        }
        setIsSubmitting(false);
    };

    const handleOtpChange = (e, index) => {
        const { value } = e.target;
        if (!/^[0-9]*$/.test(value)) return;
        const newOtp = [...otpValues];
        newOtp[index] = value ? value.slice(-1) : "";
        setOtpValues(newOtp);
        if (value && index < 5) {
            otpRefs.current[index + 1].current.focus();
        }
        if (otpError) setOtpError("");
    };

    return (
        <div className="loginpage">
            <div className="loginpage-area">
                <h1 className="loginpage-title">
                    {isOtpRequested ? 'Enter OTP' : 'Login with Phone'}
                </h1>

                {phoneError && (
                    <div className="error-message">{phoneError}</div>
                )}

                {otpError && (
                    <div className="error-message">{otpError}</div>
                )}

                <form onSubmit={isOtpRequested ? handleVerifyOtp : handleGetOtp}>
                    {!isOtpRequested ? (
                        <div className="loginpage-formgroup">
                            <label>Phone Number</label>
                            <div className="phone-input-wrapper">
                                <select 
                                    className="loginpage-input country-select"
                                    value={countryCode}
                                    onChange={(e) => setCountryCode(e.target.value)}
                                >
                                    <option value="+91">+91</option>
                                    <option value="+1">+1</option>
                                    <option value="+44">+44</option>
                                </select>
                                <input
                                    type="tel"
                                    className={`loginpage-input phone-input ${phoneError ? 'input-error' : ''}`}
                                    placeholder="Enter phone number"
                                    value={phoneNumber}
                                    onChange={handlePhoneNumberChange}
                                    maxLength="10"
                                    required
                                />
                            </div>
                        </div>
                    ) : (
                        <div className="loginpage-formgroup">
                            <label>Enter OTP</label>
                            <div className="otp-wrapper">
                                {otpValues.map((digit, index) => (
                                    <input
                                        key={index}
                                        type="text"
                                        maxLength={1}
                                        value={digit}
                                        ref={otpRefs.current[index]}
                                        onChange={(e) => handleOtpChange(e, index)}
                                        className={`otp-input ${otpError ? 'input-error' : ''}`}
                                        required
                                    />
                                ))}
                            </div>
                            <div className="loginpage-switchtext">
                                <button
                                    type="button"
                                    onClick={handleResendOtp}
                                    disabled={!resendAllowed}
                                    className="loginpage-click"
                                >
                                    {resendAllowed ? 'Resend OTP' : `Resend OTP in ${timer}s`}
                                </button>
                            </div>
                        </div>
                    )}

                    <div id="recaptcha-container"></div>

                    <button
                        type="submit"
                        disabled={isSubmitting || (!isOtpRequested && phoneNumber.length !== 10) || (isOtpRequested && otpValues.join('').length !== 6)}
                        className="loginpage-btn"
                    >
                        {isSubmitting ? 'Processing...' : isOtpRequested ? 'Verify OTP' : 'Send OTP'}
                    </button>

                    {isOtpRequested && (
                        <div className="loginpage-switchtext">
                            <button
                                type="button"
                                onClick={() => {
                                    setIsOtpRequested(false);
                                    setOtpValues(["", "", "", "", "", ""]);
                                    setConfirmationResult(null);
                                    setOtpError("");
                                }}
                                className="loginpage-click"
                            >
                                Try different number
                            </button>
                        </div>
                    )}
                </form>
            </div>
        </div>
    );
};

export default Login;

