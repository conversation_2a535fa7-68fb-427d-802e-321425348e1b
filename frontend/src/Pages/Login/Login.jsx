import React, { useState, useEffect } from "react";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { API_BASE_URL, ENDPOINTS, makeApiCall } from "../../config/api";
import HomeIcon from "@mui/icons-material/Home";
import {
  sendFirebaseOTP,
  verifyFirebaseOTP,
} from "../../services/firebaseAuthService";
import "./Login.css";
import axios from "axios";
import { useTranslation } from "react-i18next";

// Helper function to create a direct login for test users
const createDirectLogin = (phoneNumber, role) => {
  // Create a mock user object
  const mockUser = {
    id: "test-user-id",
    name: "Test User",
    mobile: phoneNumber,
    phoneNumber: phoneNumber,
    role: role,
    preferredLanguage: "en",
  };

  // Generate a client-side token
  const timestamp = Date.now();
  const token = btoa(`${phoneNumber}:${role}:${timestamp}`);

  // Create user data with token
  const userData = {
    ...mockUser,
    token,
    tokenTimestamp: timestamp,
  };

  // Store in localStorage
  localStorage.setItem("token", token);
  localStorage.setItem("user", JSON.stringify(userData));

  return userData;
};

const Login = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { verifyOTP, verifyFirebaseAuth, sendOTP, currentUser, loading } =
    useAuth();
  const [countryCode, setCountryCode] = useState("+91");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [otp, setOtp] = useState("");
  const [selectedRole, setSelectedRole] = useState("");
  const [isOtpRequested, setIsOtpRequested] = useState(false);
  const [phoneError, setPhoneError] = useState("");
  const [otpError, setOtpError] = useState("");
  const [roleError, setRoleError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(null);
  const [useFirebase, setUseFirebase] = useState(true); // Flag to toggle Firebase auth

  useEffect(() => {
    if (currentUser && !loading) {
      const dashboardPath = getDashboardPath(currentUser.role);
      navigate(dashboardPath, { replace: true });
    }

    // Retrieve stored role from localStorage when component mounts
    const storedRole = localStorage.getItem("tempSelectedRole");
    if (storedRole) {
      setSelectedRole(storedRole);
      console.log("Retrieved stored role:", storedRole);
    }
  }, [currentUser, loading, navigate]);

  useEffect(() => {
    console.log("Selected role in component:", selectedRole);
  }, [selectedRole]);

  const handlePhoneNumberChange = (e) => {
    const value = e.target.value.replace(/\D/g, "");
    if (value.length <= 10) {
      setPhoneNumber(value);
      setPhoneError("");
    }
  };

  const validatePhoneNumber = (phone) => {
    if (!phone) return t("phone_no_req");
    if (phone.length !== 10) return t("phone_no_digits");
    return null;
  };

  const handleGetOtp = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError("");
    setPhoneError("");
    setRoleError("");

    const phoneError = validatePhoneNumber(phoneNumber);
    if (phoneError) {
      setPhoneError(phoneError);
      setIsSubmitting(false);
      return;
    }

    if (!selectedRole) {
      setRoleError(t("pls_sel_role"));
      setIsSubmitting(false);
      return;
    }

    // Store selected role in localStorage
    localStorage.setItem("tempSelectedRole", selectedRole);
    console.log("Stored role in localStorage:", selectedRole);

    try {
      const fullPhoneNumber = `${countryCode}${phoneNumber}`;
      console.log("Attempting to send OTP to:", fullPhoneNumber);

      // Verify user exists with selected role first
      try {
        const verifyEndpoint =
          selectedRole === "Farmer" ? "/farmers/verify" : "/tm/verify";
        console.log(
          "Verifying user at endpoint:",
          API_BASE_URL + verifyEndpoint
        );

        // Use axios directly for more control
        const verifyResponse = await axios.post(API_BASE_URL + verifyEndpoint, {
          phoneNumber,
        });

        console.log("User verification successful:", verifyResponse.data);
      } catch (verifyError) {
        console.error("User verification failed:", verifyError);
        throw new Error(
          verifyError.response?.data?.message ||
            "No user found with this phone number and role"
        );
      }

      // Clear any previous Firebase recaptcha
      try {
        if (window.recaptchaVerifier) {
          window.recaptchaVerifier.clear();
          window.recaptchaVerifier = null;
        }
      } catch (err) {
        console.error("Error clearing recaptcha:", err);
      }

      // Send OTP
      if (useFirebase) {
        // Use Firebase auth
        try {
          console.log("Using Firebase auth for OTP");
          await sendFirebaseOTP(fullPhoneNumber);
          setIsOtpRequested(true);
          setSuccess(t("login_otp_success"));
        } catch (firebaseError) {
          console.error("Firebase OTP error:", firebaseError);

          // Fallback to legacy OTP system for development
          if (phoneNumber === "9611966747") {
            console.log("Using test phone number, bypassing OTP sending");
            setIsOtpRequested(true);
            setSuccess(t("test_user_success"));
          } else {
            throw firebaseError;
          }
        }
      } else {
        // Use legacy backend OTP
        await sendOTP(fullPhoneNumber, selectedRole);
        setIsOtpRequested(true);
        setSuccess(t("login_otp_success"));
      }
    } catch (error) {
      console.error("Error sending OTP:", error);
      // Clean specific error message
      let errorMessage = "Failed to send OTP. Please try again.";
      if (error.message) {
        errorMessage = error.message;
        // Handle Firebase-specific errors
        if (error.code) {
          switch (error.code) {
            case "auth/invalid-phone-number":
              errorMessage =
                t("invalid_format");
              break;
            case "auth/too-many-requests":
              errorMessage = t("too_many_req");
              break;
            case "auth/captcha-check-failed":
              errorMessage = t("recap_error");
              // Try to reset reCAPTCHA
              if (window.recaptchaVerifier) {
                try {
                  window.recaptchaVerifier.clear();
                  window.recaptchaVerifier = null;
                } catch (err) {}
              }
              break;
            default:
              errorMessage = error.message;
          }
        }
      }
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleVerifyOtp = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError("");
    setOtpError("");
    setSuccess("");

    if (!selectedRole) {
      setOtpError(t("role_req"));
      setIsSubmitting(false);
      return;
    }

    console.log("Verifying OTP with role:", selectedRole);

    if (otp.length !== 6) {
      setOtpError(t("otp_six_req"));
      setIsSubmitting(false);
      return;
    }

    try {
      console.log("Starting OTP verification...");
      const fullPhoneNumber = `${countryCode}${phoneNumber}`;

      if (useFirebase) {
        // Using Firebase authentication
        try {
          // Special case for test user
          if (phoneNumber === "9611966747" && otp === "123456") {
            console.log(
              "Using test phone number with fixed OTP, role:",
              selectedRole
            );
            const userData = createDirectLogin(phoneNumber, selectedRole);
            setSuccess(t("verify_redirect"));

            setTimeout(() => {
              const dashboardPath = getDashboardPath(selectedRole);
              window.location.href = dashboardPath;
            }, 1000);
            return;
          }

          // Verify with Firebase
          const firebaseResult = await verifyFirebaseOTP(otp);
          console.log("Firebase verification successful:", firebaseResult);

          // Get Firebase UID
          const firebaseUid = firebaseResult?.user?.uid || null;

          // Now verify with backend using the new verifyFirebaseAuth method
          console.log(
            "Sending role to backend for Firebase verification:",
            selectedRole
          );
          const userData = await verifyFirebaseAuth(
            fullPhoneNumber,
            selectedRole,
            firebaseUid
          );

          if (userData) {
            setSuccess(t("verify_redirect"));
            const dashboardPath = getDashboardPath(userData.role);
            navigate(dashboardPath, { replace: true });
          } else {
            throw new Error("Verification failed - no user data received");
          }
        } catch (firebaseError) {
          console.error("Firebase verification error:", firebaseError);

          // Special case for development: if Firebase verification fails but using test OTP
          if (otp === "123456") {
            console.log(
              "Firebase verification failed but using test OTP, trying with backend"
            );
            try {
              const userData = await verifyOTP(
                fullPhoneNumber,
                otp,
                selectedRole
              );
              if (userData) {
                setSuccess(t("verify_redirect"));
                const dashboardPath = getDashboardPath(userData.role);
                navigate(dashboardPath, { replace: true });
                return;
              }
            } catch (backendError) {
              console.error("Backend verification also failed:", backendError);
              // Fall through to throw the original Firebase error
            }
          }

          throw firebaseError;
        }
      } else {
        // Legacy authentication
        const userData = await verifyOTP(fullPhoneNumber, otp, selectedRole);

        if (userData) {
          setSuccess(t("verify_redirect"));
          const dashboardPath = getDashboardPath(userData.role);
          navigate(dashboardPath, { replace: true });
        } else {
          throw new Error("Verification failed - no user data received");
        }
      }
    } catch (error) {
      console.error("Error in verification flow:", error);

      // Handle specific error messages with more user-friendly text
      if (error.message.includes("already registered as a")) {
        const roleMatch = error.message.match(/registered as a (\w+)/);
        const role = roleMatch ? roleMatch[1] : "different role";
        setOtpError(
          `This phone number is registered as a ${role}. Please select ${role} as your role to login.`
        );

        document
          .getElementById("role-select")
          ?.classList.add("highlight-animation");
        setTimeout(() => {
          document
            .getElementById("role-select")
            ?.classList.remove("highlight-animation");
        }, 2000);
      } else if (error.message.includes("already registered")) {
        setOtpError(t("no_reg_already"));
      } else if (error.message.includes("Network connection")) {
        setOtpError(t("net_issue"));
      } else {
        setOtpError(error.message || t("ver_fail"));
      }

      // Clear OTP on error
      setOtp("");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getDashboardPath = (role) => {
    console.log("Getting dashboard path for role:", role);
    let path;
    switch (role) {
      case "Farmer":
        path = "/dashboard";
        break;
      case "TM":
        path = "/tm";
        break;
      case "TMManager":
        path = "/tm";
        break;
      case "HR":
        path = "/hrdash";
        break;
      default:
        path = "/dashboard";
        break;
    }
    console.log("Dashboard path:", path);
    return path;
  };

  return (
    <div className="loginpage">
      <div className="loginpage-area">
        <h1 className="loginpage-title">{t("login_welcome_msg")}</h1>

        {error && <div className="error-message">{error}</div>}
        {success && <div className="success-message">{success}</div>}

        {!isOtpRequested ? (
          <form onSubmit={handleGetOtp}>
            <div className="loginpage-formgroup">
              <label>{t("login_select_role")}</label>
              <div id="role-select" className="role-selector">
                <button
                  type="button"
                  className={`role-button ${
                    selectedRole === "Farmer" ? "active" : ""
                  }`}
                  onClick={() => {
                    setSelectedRole("Farmer");
                    setRoleError("");
                  }}
                >
                  {t("login_farmer_role")}
                </button>
                <button
                  type="button"
                  className={`role-button ${
                    selectedRole === "TM" ? "active" : ""
                  }`}
                  onClick={() => {
                    setSelectedRole("TM");
                    setRoleError("");
                  }}
                >
                  {t("login_tm_role")}
                </button>
              </div>
              {roleError && <div className="error-message">{roleError}</div>}
            </div>

            <div className="loginpage-formgroup">
              <label>{t("login_phone")}</label>
              <div className="phone-input-wrapper">
                <select
                  className="country-select"
                  value={countryCode}
                  onChange={(e) => setCountryCode(e.target.value)}
                >
                  <option value="+91">+91</option>
                  <option value="+1">+1</option>
                  <option value="+44">+44</option>
                </select>
                <div className="phone-input-container">
                  <input
                    type="tel"
                    className="phone-input"
                    placeholder="Enter 10-digit phone number"
                    value={phoneNumber}
                    onChange={handlePhoneNumberChange}
                    pattern="[0-9]{10}"
                    maxLength="10"
                    required
                  />
                  <div className="phone-input-length">
                    {phoneNumber.length}/10
                  </div>
                </div>
              </div>
              {phoneError && <div className="error-message">{phoneError}</div>}
            </div>

            {/* Properly positioned reCAPTCHA container */}
            <div
              id="recaptcha-container"
              style={{ margin: "20px auto", minHeight: "65px" }}
            ></div>

            <button
              type="submit"
              className={`loginpage-button ${isSubmitting ? "submitting" : ""}`}
              disabled={isSubmitting}
            >
              {isSubmitting ? t("login_send_otp") : t("login_get_otp")}
            </button>
          </form>
        ) : (
          <form onSubmit={handleVerifyOtp}>
            <div className="loginpage-formgroup">
              <div
                className="role-info"
                style={{ marginBottom: "15px", textAlign: "center" }}
              >
                <span style={{ fontWeight: "bold" }}>
                  {t("login_selected_role")}
                </span>{" "}
                {(selectedRole === "Farmer"
                  ? t("farmer_role")
                  : t("tm_role")) || t("none_msg")}
                {!selectedRole && (
                  <div className="error-message">{t("login_no_select")}</div>
                )}
              </div>
            </div>

            <div className="loginpage-formgroup">
              <label>{t("login_enter_otp")}</label>
              <input
                type="text"
                className="loginpage-input otp-input"
                placeholder={t("login_six_digit")}
                value={otp}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, "");
                  if (value.length <= 6) {
                    setOtp(value);
                    setOtpError("");
                  }
                }}
                maxLength="6"
                required
              />
              {otpError && <div className="error-message">{otpError}</div>}
            </div>

            <button
              type="submit"
              className={`loginpage-button ${isSubmitting ? "submitting" : ""}`}
              disabled={isSubmitting || !selectedRole}
            >
              {isSubmitting ? t("login_verify") : t("login_verify_otp")}
            </button>

            <button
              type="button"
              className="loginpage-button resend-button"
              onClick={() => {
                // Keep selected role when going back
                setIsOtpRequested(false);
                // Make sure role is still in localStorage
                localStorage.setItem("tempSelectedRole", selectedRole);
              }}
            >
              {t("login_change_phone")}
            </button>
          </form>
        )}

        <div className="loginpage-switchtext">
          <Link to="/hrlogin" className="loginpage-click hr-login">
            {t("hr_login")}
          </Link>
        </div>

        <div className="loginpage-home-button-container">
          <button
            type="button"
            className="loginpage-home-button"
            onClick={() => navigate("/")}
          >
            <HomeIcon />
            <p>{t("back_home")}</p>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Login;
