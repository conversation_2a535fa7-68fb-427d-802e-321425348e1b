import React, { useState } from 'react';
import {
  Container,
  Paper,
  Typography,
  Grid,
  Box,
  Card,
  CardContent,
  CircularProgress,
  Button,
} from '@mui/material';
import { ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const mockData = {
  totalYield: 2500,
  averageGrowthRate: 15.5,
  healthScore: 85,
  yieldOverTime: {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    data: [1800, 2000, 2200, 2400, 2300, 2500],
  },
  cropPerformance: [
    { name: 'Wheat', yield: 1200, growth: 12 },
    { name: 'Rice', yield: 800, growth: 18 },
    { name: 'Corn', yield: 500, growth: 15 },
  ],
  soilHealth: {
    ph: 6.5,
    nitrogen: 45,
    phosphorus: 30,
    potassium: 40,
  },
};

const Analysis = () => {
  const navigate = useNavigate();
  const [data, setData] = useState(mockData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleBack = () => {
    navigate('/dashboard');
  };

  const chartData = {
    labels: data.yieldOverTime.labels,
    datasets: [
      {
        label: 'Yield (kg)',
        data: data.yieldOverTime.data,
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Yield Over Time',
      },
    },
  };

  if (loading) {
    return (
      <Container sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{
            mb: 3,
            alignSelf: 'flex-start',
            bgcolor: '#2e7d32',
            '&:hover': {
              bgcolor: '#1b5e20'
            }
          }}
        >
          Back to Dashboard
        </Button>
        <CircularProgress />
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{
            mt: 2,
            mb: 2,
            bgcolor: '#2e7d32',
            '&:hover': {
              bgcolor: '#1b5e20'
            }
          }}
        >
          Back to Dashboard
        </Button>
        <Typography color="error" variant="h6">
          Error: {error}
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{
            mr: 2,
            bgcolor: '#2e7d32',
            '&:hover': {
              bgcolor: '#1b5e20'
            }
          }}
        >
          Back to Dashboard
        </Button>
        <Typography variant="h4" component="h1">
          Farm Analysis
        </Typography>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Yield
              </Typography>
              <Typography variant="h4">
                {data.totalYield} kg
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Average Growth Rate
              </Typography>
              <Typography variant="h4">
                {data.averageGrowthRate}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Health Score
              </Typography>
              <Typography variant="h4">
                {data.healthScore}/100
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Yield Chart */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Line data={chartData} options={chartOptions} />
      </Paper>

      {/* Crop Performance */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Crop Performance
            </Typography>
            {data.cropPerformance.map((crop) => (
              <Box key={crop.name} sx={{ mb: 2 }}>
                <Typography variant="subtitle1">{crop.name}</Typography>
                <Typography>Yield: {crop.yield} kg</Typography>
                <Typography>Growth Rate: {crop.growth}%</Typography>
              </Box>
            ))}
          </Paper>
        </Grid>

        {/* Soil Health */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Soil Health
            </Typography>
            <Box>
              <Typography>pH Level: {data.soilHealth.ph}</Typography>
              <Typography>Nitrogen: {data.soilHealth.nitrogen} mg/kg</Typography>
              <Typography>Phosphorus: {data.soilHealth.phosphorus} mg/kg</Typography>
              <Typography>Potassium: {data.soilHealth.potassium} mg/kg</Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Analysis;