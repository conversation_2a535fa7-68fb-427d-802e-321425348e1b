.signuppage {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1e4d2b 0%, #006400 100%);
    padding: 20px;
}

.signuppage-area {
    background: rgba(255, 255, 255, 0.95);
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 480px;
    position: relative;
    backdrop-filter: blur(10px);
}

.signuppage-title {
    color: #1e4d2b;
    font-size: 2rem;
    text-align: center;
    margin-bottom: 2rem;
    font-weight: 600;
}

.signuppage-formgroup {
    margin-bottom: 1.5rem;
}

.signuppage-formgroup label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.role-selector {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.role-button {
    padding: 1rem;
    border: 2px solid #1e4d2b;
    border-radius: 10px;
    background: transparent;
    color: #1e4d2b;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.role-button:hover {
    background: rgba(30, 77, 43, 0.1);
}

.role-button.active {
    background: #1e4d2b;
    color: white;
}

.phone-input-wrapper {
    display: flex;
    gap: 8px;
    align-items: center;
    width: 100%;
}

.country-select {
    width: 70px;
    min-width: 70px;
    padding: 0.75rem 0.25rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    background: white;
    color: #333;
    font-size: 1rem;
}

.phone-input-container {
    flex: 1;
    position: relative;
    min-width: 200px;
}

.phone-input {
    width: 100%;
    padding: 0.75rem 1rem;
    padding-right: 3.5rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.phone-input-length {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 0.875rem;
    pointer-events: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.05);
}

.signuppage-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.signuppage-input:focus {
    border-color: #1e4d2b;
    outline: none;
}

.signuppage-input.input-error {
    border-color: #dc2626;
}

.error-message {
    color: #dc2626;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: rgba(220, 38, 38, 0.1);
    border-radius: 8px;
    text-align: center;
}

.success-message {
    color: #059669;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: rgba(5, 150, 105, 0.1);
    border-radius: 8px;
    text-align: center;
}

.signuppage-button {
    width: 100%;
    padding: 1rem;
    background: #1e4d2b;
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.signuppage-button:hover:not(:disabled) {
    background: #2c6d3f;
    transform: translateY(-2px);
}

.signuppage-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.signuppage-button.submitting {
    background: #ccc;
    cursor: wait;
}

.signuppage-switchtext {
    text-align: center;
    margin-top: 1rem;
}

.signuppage-click {
    color: #1e4d2b;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.signuppage-click:hover {
    color: #2c6d3f;
}

@media (max-width: 480px) {
    .signuppage-area {
        padding: 1.5rem;
    }

    .signuppage-title {
        font-size: 1.5rem;
    }

    .role-selector {
        grid-template-columns: 1fr;
    }

    .phone-input-wrapper {
        flex-direction: row;
    }

    .country-select {
        width: 60px;
        min-width: 60px;
        padding: 0.75rem 0.25rem;
    }

    .phone-input-container {
        min-width: 150px;
    }

    .phone-input {
        padding-right: 4rem;
    }
} 