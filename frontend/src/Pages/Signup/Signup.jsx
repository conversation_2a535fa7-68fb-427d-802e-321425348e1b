import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { API_BASE_URL, ENDPOINTS, makeApiCall } from '../../config/api';
import './Signup.css';

const Signup = () => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        phoneNumber: '',
        password: '',
        confirmPassword: '',
        role: '',
        countryCode: '+91'
    });
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [loading, setLoading] = useState(false);
    const [roleError, setRoleError] = useState('');
    const navigate = useNavigate();
    const { signup } = useAuth();

    const handleChange = (e) => {
        const { name, value } = e.target;
        if (name === 'phoneNumber') {
            const phoneValue = value.replace(/\D/g, '');
            if (phoneValue.length <= 10) {
                setFormData(prev => ({
                    ...prev,
                    [name]: phoneValue
                }));
            }
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }
    };

    const validateForm = () => {
        if (!formData.role) {
            setRoleError('Please select your role');
            return false;
        }
        if (!formData.name) {
            setError('Name is required');
            return false;
        }
        if (!formData.email) {
            setError('Email is required');
            return false;
        }
        if (!formData.phoneNumber) {
            setError('Phone number is required');
            return false;
        }
        if (formData.phoneNumber.length !== 10) {
            setError('Phone number must be 10 digits');
            return false;
        }
        if (!formData.password) {
            setError('Password is required');
            return false;
        }
        if (formData.password.length < 6) {
            setError('Password must be at least 6 characters');
            return false;
        }
        if (formData.password !== formData.confirmPassword) {
            setError('Passwords do not match');
            return false;
        }
        return true;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setSuccess('');
        setRoleError('');

        if (!validateForm()) {
            return;
        }

        setLoading(true);

        try {
            // First, verify if the phone number is already registered
            const formattedNumber = formData.phoneNumber.replace(/\D/g, '');
            const verifyResponse = await makeApiCall(ENDPOINTS.FARMERS.VERIFY, {
                method: 'POST',
                body: JSON.stringify({ phoneNumber: formattedNumber })
            });

            if (verifyResponse.success) {
                setError('This phone number is already registered');
                setLoading(false);
                return;
            }

            // Create user account
            const userData = {
                name: formData.name,
                email: formData.email,
                phoneNumber: `${formData.countryCode}${formData.phoneNumber}`,
                role: formData.role,
                password: formData.password
            };

            await signup(userData);
            setSuccess('Account created successfully! Redirecting to login...');
            
            // Redirect to login after 2 seconds
            setTimeout(() => {
                navigate('/login');
            }, 2000);
        } catch (err) {
            setError(err.message || 'Failed to create an account');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="signuppage">
            <div className="signuppage-area">
                <h1 className="signuppage-title">Create Account</h1>

                {error && <div className="error-message">{error}</div>}
                {success && <div className="success-message">{success}</div>}
                {roleError && <div className="error-message">{roleError}</div>}

                <form onSubmit={handleSubmit}>
                    <div className="signuppage-formgroup">
                        <label>Select Role</label>
                        <div className="role-selector">
                            <button
                                type="button"
                                className={`role-button ${formData.role === 'Farmer' ? 'active' : ''}`}
                                onClick={() => {
                                    setFormData(prev => ({ ...prev, role: 'Farmer' }));
                                    setRoleError('');
                                }}
                            >
                                Farmer
                            </button>
                            <button
                                type="button"
                                className={`role-button ${formData.role === 'TM' ? 'active' : ''}`}
                                onClick={() => {
                                    setFormData(prev => ({ ...prev, role: 'TM' }));
                                    setRoleError('');
                                }}
                            >
                                Territory Manager
                            </button>
                        </div>
                    </div>

                    <div className="signuppage-formgroup">
                        <label>Full Name</label>
                        <input
                            type="text"
                            className="signuppage-input"
                            name="name"
                            value={formData.name}
                            onChange={handleChange}
                            required
                        />
                    </div>

                    <div className="signuppage-formgroup">
                        <label>Email Address</label>
                        <input
                            type="email"
                            className="signuppage-input"
                            name="email"
                            value={formData.email}
                            onChange={handleChange}
                            required
                        />
                    </div>

                    <div className="signuppage-formgroup">
                        <label>Phone Number</label>
                        <div className="phone-input-wrapper">
                            <select 
                                className="country-select"
                                value={formData.countryCode}
                                onChange={(e) => setFormData(prev => ({ ...prev, countryCode: e.target.value }))}
                            >
                                <option value="+91">+91</option>
                                <option value="+1">+1</option>
                                <option value="+44">+44</option>
                            </select>
                            <div className="phone-input-container">
                                <input
                                    type="tel"
                                    className="phone-input"
                                    name="phoneNumber"
                                    placeholder="Enter 10-digit phone number"
                                    value={formData.phoneNumber}
                                    onChange={handleChange}
                                    pattern="[0-9]{10}"
                                    maxLength="10"
                                    required
                                />
                                <div className="phone-input-length">{formData.phoneNumber.length}/10</div>
                            </div>
                        </div>
                    </div>

                    <div className="signuppage-formgroup">
                        <label>Password</label>
                        <input
                            type="password"
                            className="signuppage-input"
                            name="password"
                            value={formData.password}
                            onChange={handleChange}
                            required
                        />
                    </div>

                    <div className="signuppage-formgroup">
                        <label>Confirm Password</label>
                        <input
                            type="password"
                            className="signuppage-input"
                            name="confirmPassword"
                            value={formData.confirmPassword}
                            onChange={handleChange}
                            required
                        />
                    </div>

                    <button
                        type="submit"
                        className={`signuppage-button ${loading ? "submitting" : ""}`}
                        disabled={loading}
                    >
                        {loading ? "Creating Account..." : "Sign Up"}
                    </button>
                </form>

                <div className="signuppage-switchtext">
                    Already have an account?{' '}
                    <Link to="/login" className="signuppage-click">
                        Sign in
                    </Link>
                </div>
            </div>
        </div>
    );
};

export default Signup; 