.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f5f5f5;
    padding: 20px;
}

.login-box {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.login-box h2 {
    text-align: center;
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.form-group input:focus {
    outline: none;
    border-color: #4f46e5;
}

button {
    width: 100%;
    padding: 0.75rem;
    border: none;
    border-radius: 4px;
    background-color: #4f46e5;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

button:hover {
    background-color: #4338ca;
}

button:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
}

.home-button {
    margin-top: 1rem;
    background-color: #6b7280;
}

.home-button:hover {
    background-color: #4b5563;
}

.error {
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    text-align: center;
}

#recaptcha-container {
    display: none;
}

.loginpage {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1e4d2b 0%, #006400 100%);
    padding: 20px;
}

.loginpage-area {
    background: rgba(255, 255, 255, 0.95);
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 480px;
    position: relative;
    backdrop-filter: blur(10px);
}

.loginpage-title {
    color: #1e4d2b;
    font-size: 2rem;
    text-align: center;
    margin-bottom: 2rem;
    font-weight: 600;
}

.loginpage-formgroup {
    margin-bottom: 1.5rem;
}

.loginpage-formgroup label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.role-selector {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.role-button {
    padding: 1rem;
    border: 2px solid #1e4d2b;
    border-radius: 10px;
    background: transparent;
    color: #1e4d2b;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.role-button:hover {
    background: rgba(30, 77, 43, 0.1);
}

.role-button.active {
    background: #1e4d2b;
    color: white;
}

.phone-input-wrapper {
    display: flex;
    gap: 8px;
    align-items: center;
    width: 100%;
}

.country-select {
    width: 70px;
    min-width: 70px;
    padding: 0.75rem 0.25rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    background: white;
    color: #333;
    font-size: 1rem;
}

.phone-input-container {
    flex: 1;
    position: relative;
    min-width: 200px;
}

.phone-input {
    width: 100%;
    padding: 0.75rem 1rem;
    padding-right: 3.5rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.phone-input-length {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 0.875rem;
    pointer-events: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.05);
}

.loginpage-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.loginpage-input:focus {
    border-color: #1e4d2b;
    outline: none;
}

.loginpage-input.input-error {
    border-color: #dc2626;
}

.error-message {
    color: #dc2626;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: rgba(220, 38, 38, 0.1);
    border-radius: 8px;
    text-align: center;
}

.loginpage-button {
    width: 100%;
    padding: 1rem;
    background: #1e4d2b;
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.loginpage-button:hover:not(:disabled) {
    background: #2c6d3f;
    transform: translateY(-2px);
}

.loginpage-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.loginpage-button.submitting {
    background: #ccc;
    cursor: wait;
}

.emulator-hint {
    text-align: center;
    color: #666;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: #f5f5f5;
    border-radius: 8px;
}

.otp-input {
    text-align: center;
    letter-spacing: 0.5rem;
    font-size: 1.5rem !important;
}

#recaptcha-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
}

@media (max-width: 480px) {
    .loginpage-area {
        padding: 1.5rem;
    }

    .loginpage-title {
        font-size: 1.5rem;
    }

    .role-selector {
        grid-template-columns: 1fr;
    }

    .phone-input-wrapper {
        flex-direction: row;
    }

    .country-select {
        width: 60px;
        min-width: 60px;
        padding: 0.75rem 0.25rem;
    }

    .phone-input-container {
        min-width: 150px;
    }

    .phone-input {
        padding-right: 4rem;
    }
} 