import React, { useState, useEffect } from "react";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { RecaptchaVerifier, signInWithPhoneNumber } from "firebase/auth";
import { auth } from "../../config/firebase";
import { useAuth } from "../../contexts/AuthContext";
import { useLanguage } from "../../contexts/LanguageContext";
import { API_BASE_URL, ENDPOINTS, makeApiCall } from "../../config/api";
import { getTranslation } from "../../translations";
import "./Login.css";

const Login = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { sendOTP, verifyOTP, currentUser, loading } = useAuth();
    const { selectedLanguage } = useLanguage();
    const [countryCode, setCountryCode] = useState("+91");
    const [phoneNumber, setPhoneNumber] = useState("");
    const [otp, setOtp] = useState("");
    const [selectedRole, setSelectedRole] = useState("");
    const [isOtpRequested, setIsOtpRequested] = useState(false);
    const [confirmationResult, setConfirmationResult] = useState(null);
    const [phoneError, setPhoneError] = useState("");
    const [otpError, setOtpError] = useState("");
    const [roleError, setRoleError] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        if (currentUser && !loading) {
            const dashboardPath = getDashboardPath(currentUser.role);
            navigate(dashboardPath, { replace: true });
        }
    }, [currentUser, loading, navigate]);

    const handlePhoneNumberChange = (e) => {
        const value = e.target.value.replace(/\D/g, '');
        setPhoneNumber(value);
        setPhoneError("");
    };

    const validatePhoneNumber = (phone) => {
        if (!phone) return getTranslation('phoneNumberRequired', selectedLanguage);
        if (phone.length !== 10) return getTranslation('phoneNumberLength', selectedLanguage);
        return null;
    };

    const handleGetOtp = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);

        if (!selectedRole) {
            setRoleError(getTranslation('selectRole', selectedLanguage));
            setIsSubmitting(false);
            return;
        }

        const phoneValidationError = validatePhoneNumber(phoneNumber);
        if (phoneValidationError) {
            setPhoneError(phoneValidationError);
            setIsSubmitting(false);
            return;
        }

        try {
            const fullPhoneNumber = `${countryCode}${phoneNumber}`;
            
            // If role is TM, verify their status first
            if (selectedRole === 'TM') {
                try {
                    // Format phone number to match backend expectation (10 digits only)
                    const formattedNumber = phoneNumber.replace(/\D/g, '');
                    const data = await makeApiCall(ENDPOINTS.TM.VERIFY, {
                        method: 'POST',
                        body: JSON.stringify({ phoneNumber: formattedNumber })
                    });

                    if (!data.success) {
                        setPhoneError(data.message || getTranslation('notAuthorizedTM', selectedLanguage));
                        setIsSubmitting(false);
                        return;
                    }
                } catch (error) {
                    setPhoneError(error.message || getTranslation('tmVerificationFailed', selectedLanguage));
                    setIsSubmitting(false);
                    return;
                }
            }

            // For development/testing in emulator
            if (window.location.hostname === "localhost" || window.location.hostname === "127.0.0.1") {
                await sendOTP(fullPhoneNumber, selectedRole);
                setIsOtpRequested(true);
                setIsSubmitting(false);
                return;
            }

            // Set up reCAPTCHA
            if (!window.recaptchaVerifier) {
                window.recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
                    size: 'invisible',
                });
            }

            const confirmation = await signInWithPhoneNumber(auth, fullPhoneNumber, window.recaptchaVerifier);
            setConfirmationResult(confirmation);
            setIsOtpRequested(true);
        } catch (error) {
            console.error("Error sending OTP:", error);
            setPhoneError(error.message || getTranslation('otpSendFailed', selectedLanguage));
        }

        setIsSubmitting(false);
    };

    const handleVerifyOtp = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);

        if (otp.length !== 6) {
            setOtpError(getTranslation('otpLength', selectedLanguage));
            setIsSubmitting(false);
            return;
        }

        try {
            const fullPhoneNumber = `${countryCode}${phoneNumber}`;

            // For development/testing in emulator
            if (window.location.hostname === "localhost" || window.location.hostname === "127.0.0.1") {
                await verifyOTP(fullPhoneNumber, otp);
            } else {
                if (!confirmationResult) {
                    throw new Error(getTranslation('noOtpRequest', selectedLanguage));
                }

                const result = await confirmationResult.confirm(otp);
                await verifyOTP(fullPhoneNumber, otp);
            }

            const dashboardPath = getDashboardPath(selectedRole);
            navigate(dashboardPath, { replace: true });
        } catch (error) {
            console.error("Error in verification flow:", error);
            setOtpError(error.message || getTranslation('invalidOtp', selectedLanguage));
        }

        setIsSubmitting(false);
    };

    const getDashboardPath = (role) => {
        switch (role) {
            case 'TM':
                return '/tm';
            case 'HR':
                return '/hrdash';
            case 'Farmer':
                return '/dashboard';
            default:
                return '/dashboard';
        }
    };

    return (
        <div className="loginpage">
            <div className="loginpage-area">
                <h1 className="loginpage-title">
                    {isOtpRequested 
                        ? getTranslation('enterOTP', selectedLanguage)
                        : getTranslation('welcomeToAgriCare', selectedLanguage)}
                </h1>

                {phoneError && <div className="error-message">{phoneError}</div>}
                {otpError && <div className="error-message">{otpError}</div>}
                {roleError && <div className="error-message">{roleError}</div>}

                <form onSubmit={isOtpRequested ? handleVerifyOtp : handleGetOtp}>
                    {!isOtpRequested ? (
                        <>
                            <div className="loginpage-formgroup">
                                <label>{getTranslation('selectRole', selectedLanguage)}</label>
                                <div className="role-selector">
                                    <button
                                        type="button"
                                        className={`role-button ${selectedRole === 'Farmer' ? 'active' : ''}`}
                                        onClick={() => {
                                            setSelectedRole('Farmer');
                                            setRoleError('');
                                        }}
                                    >
                                        {getTranslation('farmer', selectedLanguage)}
                                    </button>
                                    <button
                                        type="button"
                                        className={`role-button ${selectedRole === 'TM' ? 'active' : ''}`}
                                        onClick={() => {
                                            setSelectedRole('TM');
                                            setRoleError('');
                                        }}
                                    >
                                        {getTranslation('territoryManager', selectedLanguage)}
                                    </button>
                                </div>
                            </div>
                            <div className="loginpage-formgroup">
                                <label>{getTranslation('phoneNumber', selectedLanguage)}</label>
                                <div className="phone-input-wrapper">
                                    <select 
                                        className="loginpage-input country-select"
                                        value={countryCode}
                                        onChange={(e) => setCountryCode(e.target.value)}
                                    >
                                        <option value="+91">+91</option>
                                        <option value="+1">+1</option>
                                        <option value="+44">+44</option>
                                    </select>
                                    <div className="phone-input-container">
                                        <input
                                            type="tel"
                                            className={`loginpage-input phone-input ${phoneError ? "input-error" : ""}`}
                                            placeholder={getTranslation('enterPhoneNumber', selectedLanguage)}
                                            value={phoneNumber}
                                            onChange={handlePhoneNumberChange}
                                            pattern="[0-9]{10}"
                                            maxLength="10"
                                            required
                                        />
                                        <div className="phone-input-length">{phoneNumber.length}/10</div>
                                    </div>
                                </div>
                            </div>
                        </>
                    ) : (
                        <div className="loginpage-formgroup">
                            <label>{getTranslation('enterOTP', selectedLanguage)}</label>
                            <input
                                type="text"
                                className={`loginpage-input otp-input ${otpError ? "input-error" : ""}`}
                                placeholder={getTranslation('enter6DigitOTP', selectedLanguage)}
                                value={otp}
                                onChange={(e) => {
                                    setOtp(e.target.value.replace(/\D/g, '').slice(0, 6));
                                    setOtpError('');
                                }}
                                pattern="[0-9]{6}"
                                maxLength="6"
                                required
                            />
                        </div>
                    )}

                    <button
                        type="submit"
                        className={`loginpage-button ${isSubmitting ? "submitting" : ""}`}
                        disabled={isSubmitting}
                    >
                        {isSubmitting
                            ? getTranslation('pleaseWait', selectedLanguage)
                            : isOtpRequested
                            ? getTranslation('verifyOTP', selectedLanguage)
                            : getTranslation('getOTP', selectedLanguage)}
                    </button>

                    {isOtpRequested && (
                        <div className="loginpage-switchtext">
                            <button
                                type="button"
                                onClick={() => {
                                    setIsOtpRequested(false);
                                    setOtp("");
                                    setConfirmationResult(null);
                                    setOtpError("");
                                }}
                                className="loginpage-click"
                            >
                                {getTranslation('tryDifferentNumber', selectedLanguage)}
                            </button>
                        </div>
                    )}
                </form>

                <div className="loginpage-switchtext">
                    <Link to="/hrlogin" className="loginpage-click">
                        {getTranslation('hrLogin', selectedLanguage)}
                    </Link>
                </div>

                <div id="recaptcha-container"></div>
            </div>
        </div>
    );
};

export default Login; 