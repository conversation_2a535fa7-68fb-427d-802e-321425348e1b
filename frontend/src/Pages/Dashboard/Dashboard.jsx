import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  useTheme,
  keyframes,
  Typography
} from '@mui/material';
import leafIcon from '../../assets/leafIcon.webp';
import DashboardNav from "../../components/DashboardNav/DashboardNav";
import DashData from "./DashComponents/DashData/DashData";
import ChatBubble from "../../components/ChatBubble/ChatBubble";
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import DynamicTranslation from '../../components/DynamicTranslation/DynamicTranslation';
import { useTranslation } from '../../hooks/useTranslation';

// Define animations
const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const gradientAnimation = keyframes`
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
`;

const Dashboard = () => {
  const theme = useTheme();
  const { currentUser } = useAuth();
  const { selectedLanguage } = useLanguage();
  const { t, i18n } = useTranslation();
  const isFarmer = currentUser?.role === 'Farmer';
  const [farmerData, setFarmerData] = useState(null);
  
  // Format current date according to the selected language
  const formatDate = () => {
    const options = { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' };
    try {
      // Try to format in the current language
      if (selectedLanguage) {
        const language = selectedLanguage.replace('_', '-');
        return new Date().toLocaleDateString(language, options);
      }
      // Fallback to English
      return new Date().toLocaleDateString('en-IN', options);
    } catch (error) {
      // If language not supported, fall back to English
      return new Date().toLocaleDateString('en-IN', options);
    }
  };

  useEffect(() => {
    // Fetch farmer data if user is logged in
    const fetchFarmerData = async () => {
      if (currentUser && currentUser.token) {
        try {
          // Use the existing user data from auth context
          // This assumes the currentUser already has the necessary information
          // If not, you would make an API call here to fetch more details
          setFarmerData({
            name: currentUser.name || currentUser.displayName || '',
            mobile: currentUser.mobile || currentUser.phoneNumber || '',
            id: currentUser.id || currentUser._id || currentUser.uid || ''
          });
        } catch (error) {
          console.error('Error fetching farmer data:', error);
        }
      }
    };

    fetchFarmerData();
  }, [currentUser]);

  return (
    <Box sx={{
      backgroundColor: '#ffffff',
      minHeight: '100vh',
      color: '#1b5e20',
      position: 'relative',
      overflow: 'hidden',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '100%',
        background: 'linear-gradient(135deg, #f1f8e9 0%, #dcedc8 50%, #c5e1a5 100%)',
        backgroundSize: '200% 200%',
        animation: `${gradientAnimation} 15s ease infinite`,
        zIndex: 0
      },
      '&::after': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '100%',
        background: `
          radial-gradient(circle at 50% 50%, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%),
          url(${leafIcon}) repeat
        `,
        backgroundSize: '800px 800px',
        opacity: 0.03,
        zIndex: 1
      }
    }}>
      <DashboardNav />
      <Box sx={{
        pt: '64px',
        position: 'relative',
        zIndex: 2,
        minHeight: 'calc(100vh - 64px)',
        background: 'transparent',
        pb: { xs: 2, sm: 3 },
        animation: `${fadeIn} 0.5s ease-out`
      }}>
        <Container maxWidth="xl">
          <Box sx={{
            mt: 3,
            mb: 4,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            animation: `${fadeIn} 0.5s ease-out 0.2s both`
          }}>
            <Box sx={{
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(12px)',
              p: { xs: 3, sm: 4 },
              borderRadius: '16px',
              border: '1px solid rgba(46, 125, 50, 0.2)',
              boxShadow: '0 8px 32px rgba(27, 94, 32, 0.15)',
              transition: 'all 0.3s ease',
              width: '100%',
              position: 'relative',
              overflow: 'hidden',
              minHeight: { xs: '120px', sm: '140px', md: '160px' },
              display: 'flex',
              alignItems: 'center',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 10px 40px rgba(27, 94, 32, 0.2)'
              },
              '&::after': {
                content: '""',
                position: 'absolute',
                right: -20,
                bottom: -20,
                width: 120,
                height: 120,
                backgroundImage: `url(${leafIcon})`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center center',
                backgroundSize: 'contain',
                opacity: 0.1,
                zIndex: 0
              }
            }}>
              <Box sx={{
                position: 'relative',
                zIndex: 1,
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                justifyContent: 'space-between',
                alignItems: 'center',
                width: '100%',
                minHeight: 'inherit'
              }}>
                                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    width: '100%',
                    minHeight: 'inherit'
                  }}>
                  <Box
                    component="img"
                    src={leafIcon}
                    alt="Leaf"
                    sx={{
                      width: { xs: 32, md: 40 },
                      height: { xs: 32, md: 40 },
                      filter: 'invert(27%) sepia(51%) saturate(2878%) hue-rotate(93deg) brightness(95%) contrast(101%)',
                      flexShrink: 0
                    }}
                  />

                  <Box sx={{ flex: 1 }}>
                    <Box sx={{
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' },
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      width: '100%',
                      gap: { xs: 1, sm: 2 },
                      minHeight: 'inherit'
                    }}>
                      <Typography variant="h5" sx={{
                        fontWeight: 600,
                        color: '#1b5e20',
                        display: 'flex',
                        alignItems: 'center',
                        flexWrap: 'wrap',
                        gap: 1,
                        lineHeight: 1.2,
                        textAlign: { xs: 'center', sm: 'left' }
                      }}>
                        {t('dashboard.welcome')}
                        <Box component="span" sx={{
                          fontWeight: 700,
                          ml: 0.5
                        }}>
                          <DynamicTranslation text={farmerData?.name || currentUser?.name || 'Varun'} />
                        </Box>
                      </Typography>

                      <Typography variant="body2" sx={{
                        color: '#558b2f',
                        fontWeight: 500,
                        fontSize: '0.9rem',
                        whiteSpace: { sm: 'nowrap' },
                        lineHeight: 1.2,
                        textAlign: { xs: 'center', sm: 'right' }
                      }}>
                        <DynamicTranslation text={formatDate()} />
                      </Typography>
                    </Box>

                    {isFarmer && (
                      <Typography variant="body1" sx={{
                        color: '#689f38',
                        mt: 1,
                        fontStyle: 'italic',
                        lineHeight: 1.3,
                        textAlign: { xs: 'center', sm: 'left' }
                      }}>
                        <DynamicTranslation text={t('dashboard.cropStatus', 'How are your crops doing today?')} />
                      </Typography>
                    )}
                  </Box>
                </Box>
              </Box>
            </Box>
          </Box>
          <DashData />
        </Container>

        {/* Chat Bubble */}
        <ChatBubble />
      </Box>
    </Box>
  );
};

export default Dashboard;
