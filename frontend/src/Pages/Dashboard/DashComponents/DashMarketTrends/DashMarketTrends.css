.section-market-trends {
  margin-top: 64px;
}

.section-market-trends h1 {
  font-size: 64px;
  text-align: center;
  color: #d9480f;
}

.market-trends-top-grid {
  display: grid;
  grid-template-columns: 2.04fr 1fr;
  gap: 16px;
  margin-top: 32px;
  height: 50vh;
}

/* Market trend styling */

.market-trends-top-grid-left {
  background-color: #e9fac8;
  border-radius: 16px;
}

.market-trends-top-grid-left h2 {
  text-align: center;
  padding: 16px;
  font-size: 24px;
  font-weight: 600;
  color: #fafafa;
  border-radius: 16px 16px 0px 0px;
  background: linear-gradient(45deg, #006400, #00b500, #00b500, #006400);
  background-size: 400% 400%; /* Increase the size for smooth animation */
  animation: gradientAnimation 5s ease infinite; /* Apply the animation */
}

.market-trends-top-grid-left p {
  font-size: 20px;
  font-weight: 600;
  padding: 16px;
}

.market-trends-top-grid-left-container {
  height: 200px;
  overflow-y: scroll;
  padding: 8px 32px;
  direction: rtl;
}

.market-trends-top-grid-left-container * {
  direction: ltr;
}

.market-trends-top-grid-left-container::-webkit-scrollbar {
  width: 8px;
  background-color: #e9fac8;
}

.market-trends-top-grid-left-container::-webkit-scrollbar-thumb {
  background-color: #5c940d;
}

.market-trends-top-grid-left-container::-webkit-scrollbar-thumb:hover {
  background-color: #446e0a;
}

.market-trends-top-grid-left-container::-webkit-scrollbar-button {
  display: none;
}

.market-trends-top-grid-left-container ul {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.market-trends-top-grid-left-container li {
  font-size: 18px;
}

/* Crop calendar styling */

.market-trends-top-grid-right {
  background-color: #e3fafc;
  border-radius: 16px;
}

.market-trends-top-grid-right h2 {
  text-align: center;
  padding: 16px;
  font-size: 24px;
  font-weight: 600;
  color: #fafafa;
  border-radius: 16px 16px 0px 0px;
  background: linear-gradient(45deg, #006400, #00b500, #00b500, #006400);
  background-size: 400% 400%; /* Increase the size for smooth animation */
  animation: gradientAnimation 5s ease infinite; /* Apply the animation */
}

.crop-calendar {
  display: flex;
  align-items: center;
  justify-content: center;
}

.crop-calendar-styling {
  border-radius: 0px 0px 16px 16px;
  width: 100% !important;
  height: 100% !important;
  border: none;
  background-color: #e7f5ff !important;
}

/*  */

.market-trends-bottom-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
}

.market-trends-bottom-grid-container {
  background-color: #d0ebff;
  margin-top: 64px;
  height: 50vh;
  border-radius: 16px;
}

.market-trends-bottom-grid-container h2 {
  text-align: center;
  padding: 16px;
  font-size: 24px;
  font-weight: 600;
  color: #fafafa;
  border-radius: 16px 16px 0px 0px;
  background: linear-gradient(45deg, #006400, #00b500, #00b500, #006400);
  background-size: 400% 400%; /* Increase the size for smooth animation */
  animation: gradientAnimation 5s ease infinite; /* Apply the animation */
}

.market-trends-bottom-grid-container p {
  padding: 16px;
  font-size: 24px;
  font-weight: 600;
}

.textarea-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
}

.textarea-container textarea {
  width: 100%;
  height: 150px;
  border: none;
  border-radius: 16px;
  padding: 8px 16px;
  background-color: #a2d4fb;
  font-size: 20px;
}

/* sesonal task styling */

.task-grid {
  overflow-y: scroll;
  height: 175px;
  direction: rtl;
  padding: 16px;
}

.task-grid * {
  direction: ltr;
}

.task-grid::-webkit-scrollbar {
  width: 8px;
  background-color: #e9fac8;
}

.task-grid::-webkit-scrollbar-thumb {
  background-color: #5c940d;
}

.task-grid::-webkit-scrollbar-thumb:hover {
  background-color: #446e0a;
}

.task-grid::-webkit-scrollbar-button {
  display: none;
}

.task-item {
  padding: 8px;
}

/*  financial report styling */

.financial-item {
  padding: 8px;
  font-size: 20px;
}

.financial-btn-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.download-report-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 16px;
  background-color: royalblue;
  font-size: 20px;
  color: #fafafa;
  font-weight: 600;
  margin-top: 16px;
}

.market-trends-redirect-btn {
  display: block;
  margin: 16px auto;
  padding: 12px 24px;
  font-size: 18px;
  background-color: #006400;
  color: #fff;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

/*  */

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 50% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  75% {
    background-position: 50% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
