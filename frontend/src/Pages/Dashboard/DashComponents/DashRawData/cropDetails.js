const cropDetails = [
  {
    heading: "Moisture",
    sensor_id: "MST123",
    value: "45%",
    desc: "The soil moisture level is optimal, ensuring proper hydration for your crops without risk of waterlogging.",
  },
  {
    heading: "Nutrient Content",
    sensor_id: "NUT456",
    value: "High",
    desc: "The soil is rich in essential nutrients like nitrogen, phosphorus, and potassium, promoting vigorous crop growth and high yields.",
  },
  {
    heading: "pH Level",
    sensor_id: "PHL789",
    value: "6.5",
    desc: "The soil pH is slightly acidic, which is ideal for most crops as it enhances nutrient availability and root development.",
  },
  {
    heading: "Conductivity",
    sensor_id: "CDT321",
    value: "2.3 mS/cm",
    desc: "The electrical conductivity indicates a balanced level of salts in the soil, ensuring healthy crop growth without salinity stress.",
  },
  {
    heading: "Temperature",
    sensor_id: "TMP654",
    value: "22°C",
    desc: "The soil temperature is within the optimal range, promoting seed germination and root activity for most crops.",
  },
  {
    heading: "Extra field",
    sensor_id: "EXT987",
    value: "N/A",
    desc: "This field can be used to monitor additional parameters such as sunlight exposure, wind speed, or custom metrics specific to your farming needs.",
  },
];

export default cropDetails;
