import React, { useState } from 'react';
import { useTranslation } from '../../../../hooks/useTranslation';
import DynamicTranslation from '../../../../components/DynamicTranslation/DynamicTranslation';
import { formatDate } from '../../../../utils/translationUtils';
import {
  Box,
  Paper,
  Typography,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Tooltip,
  IconButton,
  CircularProgress,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Fade,
  Divider
} from '@mui/material';
import {
  CheckCircle as CompletedIcon,
  Schedule as PendingIcon,
  Warning as OverdueIcon,
  Agriculture as FarmingIcon,
  WaterDrop as IrrigationIcon,
  BugReport as PestControlIcon,
  LocalShipping as HarvestIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Grass,
  WbSunny
} from '@mui/icons-material';

const WeeklySchedule = ({ data, weatherData, soilHealth }) => {
  const { t, translateDynamic } = useTranslation();
  const [selectedTask, setSelectedTask] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [error, setError] = useState(null);

  // Generate schedule based on weather and soil data
  const generateSchedule = () => {
    try {
      const schedule = [];
      const today = new Date();
      
      // Get next 7 days
      for (let i = 0; i < 7; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        const dayNameEn = date.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
        const dayName = t(`days.${dayNameEn}`, date.toLocaleDateString('en-US', { weekday: 'long' }));
        
        const tasks = [];
        
        // Add regular farming tasks based on day
        switch (dayNameEn) {
          case 'monday':
            tasks.push({
              title: t('weeklySchedule.fieldInspection', 'Field Inspection'),
              description: t('weeklySchedule.weeklyFieldHealthCheck', 'Weekly field health check'),
              icon: <FarmingIcon />,
              priority: 'medium'
            });
            break;
          case 'wednesday':
            tasks.push({
              title: t('weeklySchedule.pestMonitoring', 'Pest Monitoring'),
              description: t('weeklySchedule.checkPestInfestation', 'Check for pest infestation'),
              icon: <PestControlIcon color="warning" />,
              priority: 'medium'
            });
            break;
          case 'friday':
            tasks.push({
              title: t('weeklySchedule.equipmentMaintenance', 'Equipment Maintenance'),
              description: t('weeklySchedule.maintainEquipment', 'Check and maintain farming equipment'),
              icon: <HarvestIcon />,
              priority: 'low'
            });
            break;
        }
        
        // Check weather forecast if available
        if (weatherData?.forecast?.forecastday?.[i]) {
          const forecast = weatherData.forecast.forecastday[i];
          const condition = forecast.day.condition.text.toLowerCase();
          
          if (condition.includes('rain')) {
            tasks.push({
              title: t('weeklySchedule.weatherAlert', 'Weather Alert'),
              description: t('weeklySchedule.expectedRainfall', 'Expected rainfall. Plan indoor activities.'),
              icon: <IrrigationIcon color="primary" />,
              priority: 'high'
            });
          } else if (condition.includes('sun') || condition.includes('clear')) {
            tasks.push({
              title: t('weeklySchedule.optimalWeather', 'Optimal Weather'),
              description: t('weeklySchedule.goodDayForFieldwork', 'Good day for field work'),
              icon: <WbSunny color="success" />,
              priority: 'medium'
            });
          }
        }
        
        // Add soil health based tasks
        if (soilHealth?.currentReadings) {
          const { moisture, nitrogen } = soilHealth.currentReadings;
          
          if (moisture < 30) {
            tasks.push({
              title: t('weeklySchedule.irrigation', 'Irrigation Needed'),
              description: t('weeklySchedule.lowSoilMoisture', 'Low soil moisture detected'),
              icon: <IrrigationIcon color="warning" />,
              priority: 'high'
            });
          }
          
          if (nitrogen < 300) {
            tasks.push({
              title: t('weeklySchedule.fertilizer', 'Fertilizer Application'),
              description: t('weeklySchedule.applyNitrogenFertilizer', 'Apply nitrogen-rich fertilizer'),
              icon: <Grass color="success" />,
              priority: 'medium'
            });
          }
        }
        
        schedule.push({
          date,
          dayName,
          tasks
        });
      }
      
      return schedule;
    } catch (error) {
      console.error('Error generating schedule:', error);
      return [];
    }
  };

  // Use provided tasks or generate new schedule
  const schedule = Array.isArray(data?.tasks) ? 
    data.tasks.map(task => ({
      date: new Date(task.date),
      dayName: t(`days.${new Date(task.date).toLocaleDateString('en-US', { weekday: 'lowercase' })}`, 
                new Date(task.date).toLocaleDateString('en-US', { weekday: 'long' })),
      tasks: [{
        title: task.title,
        description: task.description,
        icon: getTaskIcon(task.title),
        priority: task.priority
      }]
    })) : 
    generateSchedule();

  // Helper function to get appropriate icon for task
  function getTaskIcon(title) {
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('water') || lowerTitle.includes('irrigation')) {
      return <IrrigationIcon color="primary" />;
    } else if (lowerTitle.includes('pest')) {
      return <PestControlIcon color="warning" />;
    } else if (lowerTitle.includes('fertilizer')) {
      return <Grass color="success" />;
    } else {
      return <FarmingIcon />;
    }
  }

  if (!Array.isArray(schedule) || schedule.length === 0) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="info"><DynamicTranslation text={t('weeklySchedule.noTasks', 'No scheduled tasks available.')} /></Alert>
      </Box>
    );
  }

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      // Implement refresh logic here
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleTaskClick = (task) => {
    setSelectedTask(task);
    setIsEditing(true);
    setIsDialogOpen(true);
  };

  const handleAddTask = () => {
    setSelectedTask(null);
    setIsEditing(false);
    setIsDialogOpen(true);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setSelectedTask(null);
    setError(null);
  };

  const handleSaveTask = async () => {
    try {
      // Implement save task logic here
    } catch (err) {
      setError(err.message || 'Failed to update task');
    }
  };

  return (
    <Paper
      sx={{
        p: 3,
        bgcolor: 'rgba(255, 255, 255, 0.1)',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'all 0.3s ease-in-out',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: '0 4px 20px rgba(0,0,0,0.2)'
        }
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" sx={{ color: 'white', flexGrow: 1 }}>
          Weekly Schedule
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            Week of {new Date(data?.weekStart).toLocaleDateString()}
          </Typography>
          <Tooltip title="Refresh schedule">
            <IconButton
              onClick={handleRefresh}
              disabled={isRefreshing}
              sx={{ color: 'white' }}
            >
              {isRefreshing ? (
                <CircularProgress size={20} sx={{ color: 'white' }} />
              ) : (
                <RefreshIcon />
              )}
            </IconButton>
          </Tooltip>
          <Tooltip title="Add new task">
            <IconButton onClick={handleAddTask} sx={{ color: 'white' }}>
              <AddIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Fade in={!error}>
        <Box>
          <Box sx={{ mb: 2 }}>
            <Grid container spacing={1}>
              <Grid item>
                <Chip
                  icon={<CompletedIcon />}
                  label={<DynamicTranslation text={t('weeklySchedule.status.completed', 'Completed')} />}
                  size="small"
                  sx={{ bgcolor: 'rgba(76, 175, 80, 0.2)', color: '#4caf50' }}
                />
              </Grid>
              <Grid item>
                <Chip
                  icon={<PendingIcon />}
                  label={<DynamicTranslation text={t('weeklySchedule.status.pending', 'Pending')} />}
                  size="small"
                  sx={{ bgcolor: 'rgba(255, 152, 0, 0.2)', color: '#ff9800' }}
                />
              </Grid>
              <Grid item>
                <Chip
                  icon={<OverdueIcon />}
                  label={<DynamicTranslation text={t('weeklySchedule.status.overdue', 'Overdue')} />}
                  size="small"
                  sx={{ bgcolor: 'rgba(244, 67, 54, 0.2)', color: '#f44336' }}
                />
              </Grid>
            </Grid>
          </Box>

          <List sx={{ flexGrow: 1 }}>
            {schedule.map((day, index) => (
              <React.Fragment key={index}>
                <ListItem
                  sx={{
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                    bgcolor: 'rgba(46, 125, 50, 0.04)',
                    borderRadius: 1,
                    mb: 1
                  }}
                >
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, color: '#2e7d32', mb: 1 }}>
                    <DynamicTranslation text={day.dayName} /> ({formatDate(day.date, t)})
                  </Typography>
                  {Array.isArray(day.tasks) && day.tasks.length > 0 ? (
                    day.tasks.map((task, taskIndex) => (
                      <Box
                        key={taskIndex}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          width: '100%',
                          py: 0.5
                        }}
                      >
                        <ListItemIcon sx={{ minWidth: 40 }}>
                          {task.icon}
                        </ListItemIcon>
                        <ListItemText
                          primary={<DynamicTranslation text={task.title} />}
                          secondary={<DynamicTranslation text={task.description} />}
                          primaryTypographyProps={{
                            variant: 'body1',
                            fontWeight: task.priority === 'high' ? 600 : 400,
                            color: task.priority === 'high' ? 'error.main' : 'text.primary'
                          }}
                        />
                      </Box>
                    ))
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      <DynamicTranslation text={t('weeklySchedule.noScheduledTasks', 'No scheduled tasks')} />
                    </Typography>
                  )}
                </ListItem>
                {index < schedule.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </Box>
      </Fade>

      <Dialog
        open={isDialogOpen}
        onClose={handleDialogClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            bgcolor: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)'
          }
        }}
      >
        <DialogTitle sx={{ color: 'white' }}>
          <DynamicTranslation text={isEditing ? t('weeklySchedule.dialog.editTask', 'Edit Task') : t('weeklySchedule.dialog.addNewTask', 'Add New Task')} />
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              label={<DynamicTranslation text={t('weeklySchedule.dialog.taskTitle', 'Task Title')} />}
              fullWidth
              value={selectedTask?.title || ''}
              onChange={(e) => setSelectedTask({ ...selectedTask, title: e.target.value })}
              sx={{
                '& .MuiOutlinedInput-root': {
                  color: 'white',
                  '& fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.3)'
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.5)'
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#2196f3'
                  }
                },
                '& .MuiInputLabel-root': {
                  color: 'rgba(255, 255, 255, 0.7)'
                }
              }}
            />
            <TextField
              select
              label={<DynamicTranslation text={t('weeklySchedule.dialog.taskType', 'Task Type')} />}
              fullWidth
              value={selectedTask?.type || ''}
              onChange={(e) => setSelectedTask({ ...selectedTask, type: e.target.value })}
              sx={{
                '& .MuiOutlinedInput-root': {
                  color: 'white',
                  '& fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.3)'
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.5)'
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#2196f3'
                  }
                },
                '& .MuiInputLabel-root': {
                  color: 'rgba(255, 255, 255, 0.7)'
                }
              }}
            >
              <MenuItem value="farming"><DynamicTranslation text={t('weeklySchedule.taskType.farming', 'Farming')} /></MenuItem>
              <MenuItem value="irrigation"><DynamicTranslation text={t('weeklySchedule.taskType.irrigation', 'Irrigation')} /></MenuItem>
              <MenuItem value="pest control"><DynamicTranslation text={t('weeklySchedule.taskType.pestControl', 'Pest Control')} /></MenuItem>
              <MenuItem value="harvest"><DynamicTranslation text={t('weeklySchedule.taskType.harvest', 'Harvest')} /></MenuItem>
            </TextField>
            <TextField
              select
              label={<DynamicTranslation text={t('weeklySchedule.dialog.status', 'Status')} />}
              fullWidth
              value={selectedTask?.status || ''}
              onChange={(e) => setSelectedTask({ ...selectedTask, status: e.target.value })}
              sx={{
                '& .MuiOutlinedInput-root': {
                  color: 'white',
                  '& fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.3)'
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(255, 255, 255, 0.5)'
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#2196f3'
                  }
                },
                '& .MuiInputLabel-root': {
                  color: 'rgba(255, 255, 255, 0.7)'
                }
              }}
            >
              <MenuItem value="completed"><DynamicTranslation text={t('weeklySchedule.status.completed', 'Completed')} /></MenuItem>
              <MenuItem value="pending"><DynamicTranslation text={t('weeklySchedule.status.pending', 'Pending')} /></MenuItem>
              <MenuItem value="overdue"><DynamicTranslation text={t('weeklySchedule.status.overdue', 'Overdue')} /></MenuItem>
            </TextField>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose} sx={{ color: 'white' }}>
            <DynamicTranslation text={t('cancel', 'Cancel')} />
          </Button>
          <Button onClick={handleSaveTask} variant="contained" color="primary">
            <DynamicTranslation text={t('save', 'Save')} />
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default WeeklySchedule; 