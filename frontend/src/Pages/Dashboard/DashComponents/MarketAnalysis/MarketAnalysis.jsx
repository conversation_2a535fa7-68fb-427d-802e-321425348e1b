import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Tooltip,
  keyframes
} from '@mui/material';
import {
  TrendingUp as UpIcon,
  TrendingDown as DownIcon,
  TrendingFlat as FlatIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useLanguage } from '../../../../contexts/LanguageContext';
import { useTranslation } from '../../../../hooks/useTranslation';
import DynamicTranslation from '../../../../components/DynamicTranslation/DynamicTranslation';
import { translateNumber, formatValueWithUnit } from '../../../../utils/translationUtils';

const shimmerAnimation = keyframes`
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
`;

const MarketAnalysis = ({ data, isLoading }) => {
  const { selectedLanguage } = useLanguage();
  const { t } = useTranslation();

  if (isLoading) {
    return (
      <Box sx={{ height: '400px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Typography><DynamicTranslation text={t('loading', 'Loading market data...')} /></Typography>
      </Box>
    );
  }

  if (!data) {
    return (
      <Box sx={{ height: '400px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Typography><DynamicTranslation text={t('marketAnalysis.noData', 'No market data available')} /></Typography>
      </Box>
    );
  }

  const { crops = [], marketTrends = [], lastUpdated } = data;

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up':
        return <UpIcon color="success" />;
      case 'down':
        return <DownIcon color="error" />;
      default:
        return <FlatIcon color="warning" />;
    }
  };

  const getTrendColor = (trend) => {
    if (trend === 'up') return '#2e7d32';
    if (trend === 'down') return '#d32f2f';
    return '#757575';
  };

  const getTrendLabel = (trend) => {
    if (trend === 'up') return t('marketAnalysis.up', 'Up');
    if (trend === 'down') return t('marketAnalysis.down', 'Down');
    return t('marketAnalysis.stable', 'Stable');
  };

  const marketStyles = {
    container: {
      position: 'relative',
      zIndex: 1
    },
    tableContainer: {
      maxHeight: 400,
      overflow: 'auto',
      '&::-webkit-scrollbar': {
        width: '6px'
      },
      '&::-webkit-scrollbar-track': {
        background: 'rgba(46, 125, 50, 0.1)',
        borderRadius: '3px'
      },
      '&::-webkit-scrollbar-thumb': {
        background: 'rgba(46, 125, 50, 0.3)',
        borderRadius: '3px',
        '&:hover': {
          background: 'rgba(46, 125, 50, 0.5)'
        }
      }
    },
    table: {
      minWidth: 650,
      '& .MuiTableCell-root': {
        borderColor: 'rgba(46, 125, 50, 0.2)',
        color: '#1b5e20',
        padding: '12px 16px'
      },
      '& .MuiTableHead-root .MuiTableCell-root': {
        backgroundColor: 'rgba(46, 125, 50, 0.1)',
        color: '#1b5e20',
        fontWeight: 700,
        fontSize: '0.95rem',
        textShadow: '0 1px 1px rgba(0,0,0,0.1)'
      },
      '& .MuiTableRow-root:hover': {
        backgroundColor: 'rgba(46, 125, 50, 0.05)'
      }
    },
    cropName: {
      fontWeight: 700,
      fontSize: '1rem',
      color: '#1b5e20',
      textShadow: '0 1px 1px rgba(0,0,0,0.1)'
    },
    price: {
      fontWeight: 700,
      fontSize: '1.1rem',
      color: '#2e7d32'
    },
    trendChip: {
      fontWeight: 600,
      fontSize: '0.8rem',
      height: '24px',
      '&.up': {
        backgroundColor: 'rgba(46, 125, 50, 0.2)',
        color: '#1b5e20'
      },
      '&.down': {
        backgroundColor: 'rgba(211, 47, 47, 0.2)',
        color: '#d32f2f'
      },
      '&.stable': {
        backgroundColor: 'rgba(117, 117, 117, 0.2)',
        color: '#757575'
      }
    },
    volume: {
      fontWeight: 600,
      color: '#2e7d32'
    },
    marketName: {
      fontWeight: 600,
      color: '#1b5e20',
      fontSize: '0.9rem'
    }
  };

  return (
    <Box sx={marketStyles.container}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          <DynamicTranslation text={t('marketAnalysis', 'Market Analysis')} />
        </Typography>
        <Tooltip title={t('marketDataUpdated', 'Market data is updated every 24 hours')}>
          <InfoIcon fontSize="small" sx={{ color: 'text.secondary' }} />
        </Tooltip>
      </Box>

      <Grid container spacing={2} sx={{ mb: 3 }}>
        {marketTrends.map((trend, index) => (
          <Grid item xs={4} key={index}>
            <Paper
              sx={{
                p: 1,
                textAlign: 'center',
                bgcolor: `${getTrendColor(trend.direction)}.lighter`,
                color: `${getTrendColor(trend.direction)}.main`
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                {getTrendIcon(trend.direction)}
                <Typography variant="body2"><DynamicTranslation text={trend.label} /></Typography>
              </Box>
              <Typography variant="h6">{translateNumber(trend.value, t)}</Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>

      <TableContainer component={Paper} sx={marketStyles.tableContainer}>
        <Table sx={marketStyles.table} aria-label="market analysis table">
          <TableHead>
            <TableRow>
              <TableCell><DynamicTranslation text={t('marketAnalysis.crop', 'Crop')} /></TableCell>
              <TableCell align="right"><DynamicTranslation text={t('marketAnalysis.price', 'Price')} /></TableCell>
              <TableCell align="right"><DynamicTranslation text={t('marketAnalysis.trend', 'Trend')} /></TableCell>
              <TableCell align="right"><DynamicTranslation text={t('marketAnalysis.volume', 'Volume')} /></TableCell>
              <TableCell><DynamicTranslation text={t('marketAnalysis.market', 'Market')} /></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {crops.map((crop) => (
              <TableRow key={crop.name}>
                <TableCell component="th" scope="row">
                  <Typography sx={marketStyles.cropName}>
                    <DynamicTranslation text={crop.name} />
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Typography sx={marketStyles.price}>
                    ₹{translateNumber(crop.currentPrice, t)}
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Chip
                    label={<DynamicTranslation text={getTrendLabel(crop.trend)} />}
                    className={crop.trend}
                    sx={marketStyles.trendChip}
                    size="small"
                  />
                </TableCell>
                <TableCell align="right">
                  <Typography sx={marketStyles.volume}>
                    {formatValueWithUnit(crop.volume, 'tons', t)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography sx={marketStyles.marketName}>
                    <DynamicTranslation text={crop.market} />
                  </Typography>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Typography variant="caption" sx={{ display: 'block', mt: 2, textAlign: 'right', color: 'text.secondary' }}>
        <DynamicTranslation text={t('lastUpdated', 'Last updated')}/> {lastUpdated}
      </Typography>
    </Box>
  );
};

export default MarketAnalysis; 