import React, { useState } from 'react';
import { makeApiCall, ENDPOINTS } from '../../../config/apiConfig';
import './AddFarm.css';

const AddFarm = ({ onAddFarm, onClose }) => {
    const [formData, setFormData] = useState({
        name: '',
        location: '',
        crops: '',
        size: '',
        description: ''
    });
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        setError(''); // Clear error when user types
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setLoading(true);

        try {
            // Validate form data
            if (!formData.name.trim()) {
                throw new Error('Farm name is required');
            }
            if (!formData.location.trim()) {
                throw new Error('Location is required');
            }

            // Make API call to add farm
            const response = await makeApiCall(ENDPOINTS.TM.ADD, {
                method: 'POST',
                body: JSON.stringify(formData)
            });

            // Add the new farm to the list
            onAddFarm({
                id: response.id || Date.now(),
                name: formData.name,
                location: formData.location,
                crops: formData.crops.split(',').map(crop => crop.trim()),
                size: formData.size,
                description: formData.description
            });

            // Close the form
            onClose();
        } catch (err) {
            setError(err.message || 'Failed to add farm. Please try again.');
            console.error('Error adding farm:', err);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="add-farm-modal">
            <div className="add-farm-content">
                <h2>Add New Farm</h2>
                {error && <div className="error-message">{error}</div>}
                <form onSubmit={handleSubmit}>
                    <div className="form-group">
                        <label htmlFor="name">Farm Name *</label>
                        <input
                            type="text"
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={handleChange}
                            required
                        />
                    </div>
                    <div className="form-group">
                        <label htmlFor="location">Location *</label>
                        <input
                            type="text"
                            id="location"
                            name="location"
                            value={formData.location}
                            onChange={handleChange}
                            required
                        />
                    </div>
                    <div className="form-group">
                        <label htmlFor="crops">Crops (comma-separated)</label>
                        <input
                            type="text"
                            id="crops"
                            name="crops"
                            value={formData.crops}
                            onChange={handleChange}
                            placeholder="e.g., Wheat, Corn, Soybeans"
                        />
                    </div>
                    <div className="form-group">
                        <label htmlFor="size">Farm Size (acres)</label>
                        <input
                            type="number"
                            id="size"
                            name="size"
                            value={formData.size}
                            onChange={handleChange}
                            min="0"
                        />
                    </div>
                    <div className="form-group">
                        <label htmlFor="description">Description</label>
                        <textarea
                            id="description"
                            name="description"
                            value={formData.description}
                            onChange={handleChange}
                            rows="4"
                        />
                    </div>
                    <div className="form-actions">
                        <button type="button" onClick={onClose} className="cancel-btn">
                            Cancel
                        </button>
                        <button type="submit" className="submit-btn" disabled={loading}>
                            {loading ? 'Adding...' : 'Add Farm'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default AddFarm; 