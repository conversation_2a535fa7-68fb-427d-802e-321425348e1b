.farm-list {
  display: flex;
  gap: 4px;
  align-items: center;
}

.farm-list-item {
  background-color: #007b02;
  padding: 8px 16px;
  border-radius: 16px;
  color: #fafafa;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.3s;
}

.farm-list-item:hover {
  background-color: #004a01;
}

/* Styling for the remove (X) button */
.remove-farm {
  margin-right: 8px;
  font-weight: bold;
  color: #ff6b6b;
  cursor: pointer;
}

/* Styling for the add button */
.add-farm-button {
  background-color: #b4b4b4;
  padding: 8px 16px;
  border-radius: 16px;
  color: #fafafa;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-farm-button:hover {
  background-color: #5f5f5f;
}

.farm-list-container hr {
  border: solid 1px rgba(215, 215, 215, 0.51);
  margin-top: 4px;
}

.farm-list-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.farm-item {
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

.farm-item-content {
    background: white;
    border-radius: 0.75rem;
    padding: 1.25rem;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.farm-item:hover .farm-item-content {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);
}

.farm-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.farm-item-header h3 {
    color: #1e293b;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
}

.location {
    color: #64748b;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.location i {
    color: #3b82f6;
    font-size: 0.875rem;
}

.farm-crops {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.crop-tag {
    background-color: #f1f5f9;
    color: #475569;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.farm-item-footer {
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #e2e8f0;
    padding-top: 1rem;
    margin-top: 0.5rem;
}

.view-details {
    background: none;
    border: none;
    color: #3b82f6;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0;
    transition: all 0.2s ease;
}

.view-details:hover {
    color: #2563eb;
}

.view-details i {
    font-size: 0.75rem;
    transition: transform 0.2s ease;
}

.farm-item:hover .view-details i {
    transform: translateX(4px);
}

@media (max-width: 768px) {
    .farm-item-content {
        padding: 1rem;
    }

    .farm-item-header {
        flex-direction: column;
        gap: 0.5rem;
    }

    .location {
        font-size: 0.75rem;
    }
}
