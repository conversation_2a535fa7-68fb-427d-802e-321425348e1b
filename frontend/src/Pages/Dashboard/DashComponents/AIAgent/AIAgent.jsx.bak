import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  TextField,
  IconButton,
  Typography,
  CircularProgress,
  Paper,
  Tooltip,
  Button,
  InputAdornment
} from '@mui/material';
import {
  Send as SendIcon,
  Mic as MicIcon,
  VolumeUp as VolumeUpIcon,
  SmartToy as SmartToyIcon,
  Refresh as RefreshIcon,
  Clear as ClearIcon,
  VolumeOff as VolumeOffIcon,
  Pause as PauseIcon,
  PlayArrow as PlayArrowIcon
} from '@mui/icons-material';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import * as SpeechSDK from 'microsoft-cognitiveservices-speech-sdk';
import LanguageSelector from './LanguageSelector';
import { useAuth } from '../../../../contexts/AuthContext';
import { API_BASE_URL } from '../../../../config/api';

const getVoiceName = (lang) => {
  console.log('Getting voice for language:', lang);
  const voiceMapping = {
    "en-IN": "en-US-JennyNeural",
    "hi-IN": "hi-IN-SwaraNeural",
    "bn-IN": "bn-IN-SumonaNeural",
    "gu-IN": "gu-IN-NehaNeural",
    "kn-IN": "kn-IN-ChitraNeural",
    "ml-IN": "ml-IN-SobhaNeural",
    "mr-IN": "mr-IN-AarohiNeural",
    "pa-IN": "pa-IN-ManeetNeural",
    "ta-IN": "ta-IN-VaniNeural",
    "te-IN": "te-IN-ShrutiNeural",
    "ur-IN": "ur-IN-ZaraNeural",
    "bho-IN": "hi-IN-SwaraNeural" // Fallback to Hindi for Bhojpuri
  };
  const voice = voiceMapping[lang] || 'en-US-JennyNeural';
  console.log('Selected voice:', voice);
  return voice;
};

// Helper function to clean text for speech synthesis
const cleanTextForSpeech = (text) => {
  if (!text) return '';
  return text
    .replace(/[#*_`~>\-]+/g, '')
    .replace(/\[(.*?)\]\((.*?)\)/g, '$1')
    .replace(/\!\[(.*?)\]\((.*?)\)/g, '')
    .replace(/\n{2,}/g, '. ')
    .replace(/\n/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
};

const AIAgent = ({ weatherData, soilData, marketData, alerts, schedule, onRefresh }) => {
  const { currentUser, getToken } = useAuth();
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  // Add a ref to track if we're already falling back to browser TTS
  const fallingBackToBrowserTTS = useRef(false);
  const [selectedLanguage, setSelectedLanguage] = useState('en-IN');
  const recognitionRef = useRef(null);
  const messagesEndRef = useRef(null);
  const [localWeatherData, setLocalWeatherData] = useState(null);

  // Add handler for weather data updates
  const handleWeatherDataUpdate = (data) => {
    console.log('Weather data updated in AIAgent:', data);
    setLocalWeatherData(data);
  };

  // Effect to update local weather data when props change
  useEffect(() => {
    if (weatherData) {
      console.log('Weather data props updated in AIAgent:', weatherData);
      setLocalWeatherData(weatherData);
    }
  }, [weatherData]);

  useEffect(() => {
    setupSpeechRecognition();
    return () => {
      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
        } catch (err) {
          console.error('Error stopping recognition:', err);
        }
      }
    };
  }, [selectedLanguage]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    if (messages.length === 0) {
      let welcomeMessage = "Hello! I'm your AI farming assistant. ";

      // Use local weather data if available, otherwise use props
      const weather = localWeatherData || weatherData;
      console.log('Generating welcome message with weather data:', weather);

      // Check if we have weather data in any format
      const current = weather?.current || weather;

      if (current && (current.temperature || current.humidity || current.conditions)) {
        welcomeMessage += `Here's your current weather:\n\n`;
        welcomeMessage += `Temperature: ${current.temperature || 'N/A'}°C\n`;
        welcomeMessage += `Humidity: ${current.humidity || 'N/A'}%\n`;
        welcomeMessage += `Conditions: ${current.conditions || 'N/A'}\n`;

        // Only add wind speed if it exists
        if (current.windSpeed) {
          welcomeMessage += `Wind Speed: ${(current.windSpeed * 3.6).toFixed(1)} km/h\n`;
        }
        welcomeMessage += '\n';
      } else {
        welcomeMessage += "I don't have current weather data available at the moment.\n\n";
      }

      // Check for bulletin in different possible locations
      const bulletin = weather?.bulletin || weather?.data?.bulletin;

      if (bulletin) {
        // Handle both string and object bulletin formats
        const bulletinText = typeof bulletin === 'string'
          ? bulletin
          : (bulletin.data?.bulletin || bulletin.summary || bulletin.forecast || 'Weather bulletin available');
        welcomeMessage += `Weather Bulletin:\n${bulletinText}\n\n`;
      }

      welcomeMessage += "How can I help you with your farming today?";

      setMessages([{
        role: 'assistant',
        content: welcomeMessage,
        timestamp: new Date()
      }]);

      // Delay the initial speech to ensure everything is initialized
      setTimeout(() => {
        speak(cleanTextForSpeech(welcomeMessage));
      }, 1000);
    }
  }, [localWeatherData, weatherData, messages.length]);

  // Track user interaction to enable speech synthesis
  const [userInteracted, setUserInteracted] = useState(false);
  // Track if we've shown the not-allowed alert
  const [shownNotAllowedAlert, setShownNotAllowedAlert] = useState(false);

  // Add a function to initialize speech synthesis
  const initializeSpeechSynthesis = useCallback(() => {
    if (window.speechSynthesis) {
      try {
        // First cancel any ongoing speech
        window.speechSynthesis.cancel();

        // Create and speak a silent utterance
        const silentUtterance = new SpeechSynthesisUtterance(' ');
        silentUtterance.volume = 0;
        silentUtterance.onend = () => console.log('Silent utterance completed');
        silentUtterance.onerror = (e) => console.warn('Silent utterance error:', e);

        // Use a timeout to ensure the browser is ready
        setTimeout(() => {
          window.speechSynthesis.speak(silentUtterance);
          // Cancel after a short delay
          setTimeout(() => window.speechSynthesis.cancel(), 100);
        }, 100);
      } catch (error) {
        console.warn('Could not initialize speech synthesis:', error);
      }
    }
  }, []);

  // Add event listener for user interaction
  useEffect(() => {
    const handleUserInteraction = () => {
      if (!userInteracted) {
        console.log('User interaction detected, enabling speech synthesis');
        setUserInteracted(true);
        setShownNotAllowedAlert(false); // Reset alert state on new interaction

        // Initialize speech synthesis
        initializeSpeechSynthesis();
      }
    };

    // Call immediately on mount to check if user has already interacted
    handleUserInteraction();

    // Add event listeners for common user interactions
    document.addEventListener('click', handleUserInteraction);
    document.addEventListener('keydown', handleUserInteraction);
    document.addEventListener('touchstart', handleUserInteraction);
    document.addEventListener('mousemove', handleUserInteraction);
    document.addEventListener('scroll', handleUserInteraction);

    // Also try to initialize on window focus
    window.addEventListener('focus', handleUserInteraction);

    return () => {
      // Clean up
      document.removeEventListener('click', handleUserInteraction);
      document.removeEventListener('keydown', handleUserInteraction);
      document.removeEventListener('touchstart', handleUserInteraction);
      document.removeEventListener('mousemove', handleUserInteraction);
      document.removeEventListener('scroll', handleUserInteraction);
      window.removeEventListener('focus', handleUserInteraction);
    };
  }, [userInteracted, initializeSpeechSynthesis]);

  // Add a separate effect to periodically try to initialize speech synthesis
  useEffect(() => {
    // Only run this if the user has interacted but we're still getting not-allowed errors
    if (userInteracted && shownNotAllowedAlert) {
      console.log('Setting up periodic speech synthesis initialization');

      // Try to initialize speech synthesis every 2 seconds
      const intervalId = setInterval(() => {
        console.log('Periodic speech synthesis initialization attempt');
        initializeSpeechSynthesis();
      }, 2000);

      return () => {
        clearInterval(intervalId);
      };
    }
  }, [userInteracted, shownNotAllowedAlert, initializeSpeechSynthesis]);

  // Initialize speech synthesis voices
  useEffect(() => {
    if (window.speechSynthesis) {
      // Load voices
      const loadVoices = () => {
        const voices = window.speechSynthesis.getVoices();
        if (voices.length > 0) {
          console.log('Speech synthesis voices loaded:', voices.length);
        }
      };

      // Try to load voices immediately
      loadVoices();

      // Also set up an event listener for when voices are loaded
      window.speechSynthesis.onvoiceschanged = loadVoices;

      return () => {
        // Clean up
        window.speechSynthesis.onvoiceschanged = null;
      };
    }
  }, []);

  const setupSpeechRecognition = () => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (SpeechRecognition) {
      const recognition = new SpeechRecognition();
      recognition.lang = selectedLanguage;
      recognition.interimResults = false;
      recognition.continuous = false;

      recognition.onstart = () => setIsRecording(true);
      recognition.onend = () => setIsRecording(false);
      recognition.onerror = () => setIsRecording(false);
      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setInput(transcript);
      };

      recognitionRef.current = recognition;
    }
  };

  // Store the synthesizer in a ref so we can control it
  const synthesizerRef = useRef(null);
  // Add a ref to track the last time we tried to use Azure TTS
  const lastAzureTTSAttempt = useRef(0);
  // Add a ref to track if Azure TTS is disabled
  const azureTTSDisabled = useRef(false);

  // Fallback speech function using browser's built-in speech synthesis
  const speakWithBrowserTTS = (text) => {
    // Check if we're already falling back to browser TTS to prevent double speech
    if (fallingBackToBrowserTTS.current) {
      console.log('Already falling back to browser TTS, skipping duplicate call');
      return;
    }

    fallingBackToBrowserTTS.current = true;

    if (!text || !window.speechSynthesis) {
      console.warn('Browser speech synthesis not available');
      fallingBackToBrowserTTS.current = false;
      return;
    }

    // Check if user has interacted with the page
    if (!userInteracted) {
      console.warn('User has not interacted with the page yet, speech synthesis may be blocked');
      // We'll still try, but it might fail with 'not-allowed'
    }

    try {
      // Cancel any ongoing speech
      window.speechSynthesis.cancel();

      // Initialize voices - this is needed in some browsers
      let voices = window.speechSynthesis.getVoices();

      // If voices array is empty, we need to wait for the voiceschanged event
      if (voices.length === 0) {
        console.log('No voices available yet, using default voice');
        // We'll use a simple English voice as fallback
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = 'en-US';
        utterance.rate = 1.0;
        utterance.pitch = 1.0;
        utterance.volume = 1.0;

        utterance.onstart = () => {
          setIsSpeaking(true);
        };

        utterance.onend = () => {
          setIsSpeaking(false);
          // Reset the fallback flag when speech ends
          fallingBackToBrowserTTS.current = false;
        };

        utterance.onerror = (event) => {
          console.error('Browser speech synthesis error:', event);
          if (event.error === 'not-allowed') {
            console.warn('Speech synthesis not allowed. User interaction may be required first.');
            // Force user interaction state to false to trigger re-initialization
            setUserInteracted(false);

            // Don't show alert if we've already shown it
            if (!shownNotAllowedAlert) {
              // Display a message to the user
              alert('Please click anywhere on the page to enable text-to-speech functionality.');
              setShownNotAllowedAlert(true);

              // Add a one-time click handler to try again after user interaction
              const clickHandler = () => {
                console.log('User clicked after not-allowed error, trying again');
                setUserInteracted(true);
                // Try speaking a silent utterance again
                try {
                  const silentUtterance = new SpeechSynthesisUtterance(' ');
                  silentUtterance.volume = 0;
                  window.speechSynthesis.speak(silentUtterance);
                  window.speechSynthesis.cancel();
                } catch (e) {
                  console.warn('Error in click handler:', e);
                }
                document.removeEventListener('click', clickHandler);
              };
              document.addEventListener('click', clickHandler);
            }
          }
          setIsSpeaking(false);
          // Reset the fallback flag when there's an error
          fallingBackToBrowserTTS.current = false;
        };

        window.speechSynthesis.speak(utterance);
        return;
      }

      // Map our language codes to browser language codes
      const langMap = {
        'en-IN': 'en',
        'hi-IN': 'hi',
        'ta-IN': 'ta',
        'te-IN': 'te',
        'kn-IN': 'kn',
        'bho-IN': 'hi', // Fallback to Hindi for Bhojpuri
        'mr-IN': 'mr',
        'bn-IN': 'bn',
        'gu-IN': 'gu',
        'ml-IN': 'ml',
        'pa-IN': 'pa',
        'ur-IN': 'ur'
      };

      const langCode = langMap[selectedLanguage] || 'en';

      // Try to find a voice that matches the selected language
      let voice = voices.find(v => v.lang.startsWith(langCode));

      // If no matching voice found, try to find any voice in the same language family
      if (!voice && langCode.length >= 2) {
        const langFamily = langCode.substring(0, 2);
        voice = voices.find(v => v.lang.startsWith(langFamily));
      }

      // Create the utterance
      const utterance = new SpeechSynthesisUtterance(text);

      if (voice) {
        console.log(`Using browser voice: ${voice.name} (${voice.lang})`);
        utterance.voice = voice;
        utterance.lang = voice.lang;
      } else {
        console.log(`No matching browser voice found for ${langCode}, using default`);
        utterance.lang = 'en-US';
      }

      // Set speech parameters
      utterance.rate = 1.0;
      utterance.pitch = 1.0;
      utterance.volume = 1.0;

      utterance.onstart = () => {
        setIsSpeaking(true);
      };

      utterance.onend = () => {
        setIsSpeaking(false);
        // Reset the fallback flag when speech ends
        fallingBackToBrowserTTS.current = false;
      };

      utterance.onerror = (event) => {
        console.error('Browser speech synthesis error:', event);
        if (event.error === 'not-allowed') {
          console.warn('Speech synthesis not allowed. User interaction may be required first.');
          // Don't show alert if we've already shown it
          if (!shownNotAllowedAlert) {
            // Display a message to the user
            alert('Please click anywhere on the page to enable text-to-speech functionality.');
            setShownNotAllowedAlert(true);
          }

          // Try to initialize speech synthesis again
          initializeSpeechSynthesis();
        }
        setIsSpeaking(false);
        // Reset the fallback flag when there's an error
        fallingBackToBrowserTTS.current = false;
      };

      window.speechSynthesis.speak(utterance);
    } catch (error) {
      console.error('Error in browser speech synthesis:', error);
      setIsSpeaking(false);
    } finally {
      // Reset the flag after a short delay to prevent immediate re-triggering
      setTimeout(() => {
        fallingBackToBrowserTTS.current = false;
      }, 500);
    }
  };

  // Audio control functions
  const pauseSpeech = () => {
    let azurePaused = false;

    // Try to pause Azure TTS if active
    if (synthesizerRef.current) {
      try {
        // Check if the synthesizer is already disposed before trying to pause it
        if (!synthesizerRef.current.isDisposed) {
          synthesizerRef.current.pauseAsync(
            () => {
              console.log('Azure speech paused successfully');
              azurePaused = true;
              setIsSpeaking(false);
            },
            (error) => {
              console.error('Error pausing Azure speech:', error);
            }
          );
        } else {
          console.warn('Cannot pause: synthesizer is already disposed');
          synthesizerRef.current = null;
        }
      } catch (error) {
        console.error('Error in pauseSpeech:', error);
      }
    }

    // Also try to pause browser's speech synthesis if active
    try {
      if (window.speechSynthesis && window.speechSynthesis.speaking) {
        window.speechSynthesis.pause();
        console.log('Browser speech paused successfully');
        setIsSpeaking(false);
      }
    } catch (error) {
      console.error('Error pausing browser speech:', error);
    }

    // If Azure wasn't paused, make sure speaking state is updated
    if (!azurePaused) {
      setIsSpeaking(false);
    }
  };

  const resumeSpeech = () => {
    let resumed = false;

    // Try to resume Azure TTS if active
    if (synthesizerRef.current) {
      try {
        // Check if the synthesizer is already disposed before trying to resume it
        if (!synthesizerRef.current.isDisposed) {
          synthesizerRef.current.resumeAsync(
            () => {
              console.log('Azure speech resumed successfully');
              setIsSpeaking(true);
              resumed = true;
            },
            (error) => {
              console.error('Error resuming Azure speech:', error);
            }
          );
        } else {
          console.warn('Cannot resume: synthesizer is already disposed');
          synthesizerRef.current = null;
        }
      } catch (error) {
        console.error('Error in resumeSpeech:', error);
      }
    }

    // Also try to resume browser's speech synthesis if active
    try {
      if (window.speechSynthesis && window.speechSynthesis.paused) {
        window.speechSynthesis.resume();
        console.log('Browser speech resumed successfully');
        setIsSpeaking(true);
        resumed = true;
      }
    } catch (error) {
      console.error('Error resuming browser speech:', error);
    }

    // If neither Azure nor browser speech was resumed, try to speak the response again
    if (!resumed && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === 'assistant') {
        const cleanedText = cleanTextForSpeech(lastMessage.content);
        console.log('No active speech to resume, starting new speech');
        speak(cleanedText);
      }
    }
  };

  const stopSpeech = () => {
    // Stop Azure TTS if active
    if (synthesizerRef.current) {
      try {
        // Check if the synthesizer is already disposed before trying to close it
        if (!synthesizerRef.current.isDisposed) {
          synthesizerRef.current.close();
          console.log('Azure speech stopped successfully');
        }
      } catch (error) {
        console.error('Error stopping Azure speech:', error);
      } finally {
        synthesizerRef.current = null;
      }
    }

    // Also stop browser's speech synthesis if active
    try {
      if (window.speechSynthesis) {
        window.speechSynthesis.cancel();
        console.log('Browser speech stopped successfully');
      }
    } catch (error) {
      console.error('Error stopping browser speech:', error);
    }

    // Always set speaking to false and reset fallback flag
    setIsSpeaking(false);
    fallingBackToBrowserTTS.current = false;
  };

  const speak = async (text) => {
    if (!text) return;

    // Check if Azure TTS is disabled
    if (azureTTSDisabled.current) {
      console.log('Azure TTS is disabled due to previous failures, using browser TTS instead');
      if (!fallingBackToBrowserTTS.current) {
        speakWithBrowserTTS(text);
      }
      return;
    }

    // Check if we've tried Azure TTS recently (within the last 10 seconds)
    // This helps avoid hitting rate limits
    const now = Date.now();
    const timeSinceLastAttempt = now - lastAzureTTSAttempt.current;
    const shouldUseAzureTTS = timeSinceLastAttempt > 10000; // 10 seconds cooldown

    if (!shouldUseAzureTTS) {
      console.log(`Azure TTS was used ${timeSinceLastAttempt}ms ago, using browser TTS instead to avoid rate limits`);
      if (!fallingBackToBrowserTTS.current) {
        speakWithBrowserTTS(text);
      }
      return;
    }

    // Update the last attempt timestamp
    lastAzureTTSAttempt.current = now;

    // Check if we've had multiple failures with Azure TTS
    // If so, just use browser TTS directly and disable Azure TTS
    if (window.azureTTSFailureCount && window.azureTTSFailureCount > 2) {
      console.log(`Azure TTS has failed ${window.azureTTSFailureCount} times, disabling Azure TTS and using browser TTS instead`);
      azureTTSDisabled.current = true;
      if (!fallingBackToBrowserTTS.current) {
        speakWithBrowserTTS(text);
      }
      return;
    }

    // If already speaking, stop it first
    if (synthesizerRef.current) {
      try {
        // Check if the synthesizer is already disposed before trying to close it
        if (!synthesizerRef.current.isDisposed) {
          synthesizerRef.current.close();
        }
      } catch (err) {
        console.error('Error closing previous synthesizer:', err);
      } finally {
        synthesizerRef.current = null;
      }
    }

    const key = import.meta.env.VITE_AZURE_SPEECH_KEY;
    const region = import.meta.env.VITE_AZURE_SPEECH_REGION;

    if (!key || !region) {
      console.error('Azure Speech key or region is missing');
      // Fall back to browser TTS if not already falling back
      if (!fallingBackToBrowserTTS.current) {
        speakWithBrowserTTS(text);
      } else {
        console.log("Already falling back to browser TTS, skipping duplicate fallback");
      }
      return;
    }

    // Check if SpeechSDK is properly loaded
    if (!SpeechSDK || !SpeechSDK.SpeechConfig) {
      console.error('Azure Speech SDK not properly loaded');
      // Fall back to browser TTS if not already falling back
      if (!fallingBackToBrowserTTS.current) {
        speakWithBrowserTTS(text);
      } else {
        console.log("Already falling back to browser TTS, skipping duplicate fallback");
      }
      return;
    }

    try {
      console.log('Starting speech synthesis with language:', selectedLanguage);
      console.log('Using voice:', getVoiceName(selectedLanguage));

      // Create speech config with the region already included
      let speechConfig;
      try {
        speechConfig = SpeechSDK.SpeechConfig.fromSubscription(key, region);
        speechConfig.speechSynthesisVoiceName = getVoiceName(selectedLanguage);

        // Set the speech synthesis output format
        try {
          speechConfig.speechSynthesisOutputFormat = SpeechSDK.SpeechSynthesisOutputFormat.Audio16Khz32KBitRateMonoMp3;
        } catch (formatError) {
          console.warn('Could not set speech synthesis output format:', formatError);
          // Continue anyway, as this is not critical
        }
      } catch (configError) {
        console.error('Error creating speech config:', configError);
        // Fall back to browser TTS if not already falling back
        if (!fallingBackToBrowserTTS.current) {
          speakWithBrowserTTS(text);
        } else {
          console.log("Already falling back to browser TTS, skipping duplicate fallback");
        }
        return;
      }

      // Create an audio config using the default speaker
      const audioConfig = SpeechSDK.AudioConfig.fromDefaultSpeakerOutput();

      // Create the synthesizer with both speech config and audio config
      let synthesizer;
      try {
        synthesizer = new SpeechSDK.SpeechSynthesizer(speechConfig, audioConfig);
        synthesizerRef.current = synthesizer;
        setIsSpeaking(true);
      } catch (synthesizerError) {
        console.error('Error creating speech synthesizer:', synthesizerError);
        // Fall back to browser TTS if not already falling back
        if (!fallingBackToBrowserTTS.current) {
          speakWithBrowserTTS(text);
        } else {
          console.log("Already falling back to browser TTS, skipping duplicate fallback");
        }
        return;
      }

      // Use a try-catch block inside the Promise to handle errors properly
      await new Promise((resolve, reject) => {
        try {
          // Double-check that synthesizer is valid before using it
          if (!synthesizer || typeof synthesizer.speakTextAsync !== 'function') {
            throw new Error('Synthesizer is not properly initialized');
          }

          synthesizer.speakTextAsync(
            text,
            result => {
              try {
                if (result.reason === SpeechSDK.ResultReason.SynthesizingAudioCompleted) {
                  console.log("✅ Azure TTS completed");
                  resolve();
                } else {
                  console.error("Azure TTS failed:", result.errorDetails);

                  // Increment the failure count
                  window.azureTTSFailureCount = (window.azureTTSFailureCount || 0) + 1;
                  console.log(`Azure TTS failure count: ${window.azureTTSFailureCount}`);

                  // If we've had multiple failures, disable Azure TTS
                  if (window.azureTTSFailureCount > 2) {
                    console.log('Disabling Azure TTS due to multiple failures');
                    azureTTSDisabled.current = true;
                  }

                  reject(new Error(result.errorDetails || 'Unknown error'));

                  // Try fallback to browser's built-in speech synthesis if not already falling back
                  if (!fallingBackToBrowserTTS.current) {
                    console.log("Falling back to browser's built-in speech synthesis after Azure TTS failure");
                    speakWithBrowserTTS(text);
                  } else {
                    console.log("Already falling back to browser TTS, skipping duplicate fallback");
                  }
                }
              } catch (innerError) {
                console.error("Error in result callback:", innerError);
                reject(innerError);
              } finally {
                setIsSpeaking(false);
                // Only close if not already disposed
                if (synthesizer && !synthesizer.isDisposed) {
                  try {
                    synthesizer.close();
                  } catch (closeError) {
                    console.warn("Warning when closing synthesizer:", closeError);
                  }
                }
                synthesizerRef.current = null;
              }
            },
            error => {
              console.error("Speech synthesis error:", error);

              // Increment the failure count
              window.azureTTSFailureCount = (window.azureTTSFailureCount || 0) + 1;
              console.log(`Azure TTS failure count: ${window.azureTTSFailureCount}`);

              // If we've had multiple failures, disable Azure TTS
              if (window.azureTTSFailureCount > 2) {
                console.log('Disabling Azure TTS due to multiple failures');
                azureTTSDisabled.current = true;
              }

              reject(error);
              setIsSpeaking(false);
              // Only close if not already disposed
              if (synthesizer && !synthesizer.isDisposed) {
                try {
                  synthesizer.close();
                } catch (closeError) {
                  console.warn("Warning when closing synthesizer:", closeError);
                }
              }
              synthesizerRef.current = null;

              // Try fallback to browser's built-in speech synthesis if not already falling back
              if (!fallingBackToBrowserTTS.current) {
                console.log("Falling back to browser's built-in speech synthesis after error");
                speakWithBrowserTTS(text);
              } else {
                console.log("Already falling back to browser TTS, skipping duplicate fallback");
              }
            }
          );
        } catch (asyncError) {
          console.error("Error in speakTextAsync:", asyncError);
          reject(asyncError);
          setIsSpeaking(false);
          synthesizerRef.current = null;
        }
      });
    } catch (error) {
      console.error("Error in speech synthesis:", error);
      setIsSpeaking(false);
      synthesizerRef.current = null;

      // Try fallback to browser's built-in speech synthesis if not already falling back
      if (!fallingBackToBrowserTTS.current) {
        console.log("Falling back to browser's built-in speech synthesis");
        speakWithBrowserTTS(text);
      } else {
        console.log("Already falling back to browser TTS, skipping duplicate fallback");
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userInput = input.trim();
    setMessages(prev => [...prev, {
      role: 'user',
      content: userInput,
      timestamp: new Date()
    }]);
    setInput('');
    setIsLoading(true);

    try {
      if (!currentUser) {
        throw new Error('Please log in to use the AI assistant');
      }

      const token = getToken();
      const authStatus = {
        hasUser: !!currentUser,
        hasToken: !!token,
        userRole: currentUser?.role,
        userId: currentUser?.id,
        tokenTimestamp: currentUser?.tokenTimestamp
      };
      console.log('Auth status:', authStatus);

      if (!token) {
        console.error('Authentication failed:', authStatus);
        throw new Error('Authentication token not available. Please log in again.');
      }

      // Check if token is expired
      if (currentUser.tokenTimestamp && Date.now() - currentUser.tokenTimestamp >= 24 * 60 * 60 * 1000) {
        console.error('Token expired:', {
          tokenAge: Math.floor((Date.now() - currentUser.tokenTimestamp) / (60 * 60 * 1000)) + ' hours'
        });
        throw new Error('Your session has expired. Please log in again.');
      }

      // Use local weather data if available, otherwise use props
      const weather = localWeatherData || weatherData;

      // Ensure weather data is properly formatted for the API
      const formattedWeather = { ...weather };

      // Format current weather if needed
      if (!formattedWeather.current && (formattedWeather.temperature || formattedWeather.humidity)) {
        formattedWeather.current = {
          temperature: formattedWeather.temperature,
          humidity: formattedWeather.humidity,
          conditions: formattedWeather.conditions,
          windSpeed: formattedWeather.windSpeed
        };
      }

      // Format bulletin if needed
      if (formattedWeather?.bulletin && typeof formattedWeather.bulletin !== 'string') {
        formattedWeather.bulletin = typeof formattedWeather.bulletin === 'string'
          ? formattedWeather.bulletin
          : (formattedWeather.bulletin.data?.bulletin ||
             formattedWeather.bulletin.summary ||
             JSON.stringify(formattedWeather.bulletin));
      }

      // Check for bulletin in data property
      if (formattedWeather?.data?.bulletin) {
        formattedWeather.bulletin = typeof formattedWeather.data.bulletin === 'string'
          ? formattedWeather.data.bulletin
          : JSON.stringify(formattedWeather.data.bulletin);
      }

      console.log('Sending chat request with data:', {
        weather: formattedWeather,
        soilData,
        marketData,
        alerts,
        schedule
      });

      const response = await fetch(`${API_BASE_URL}/ai/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          message: userInput,
          farmData: {
            weather: formattedWeather,
            soilHealth: soilData,
            marketData: marketData,
            alerts: alerts,
            schedule: schedule
          },
          language: selectedLanguage,
          userId: currentUser.id
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('AI response error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorData
        });
        if (response.status === 401) {
          console.error('Authentication failed:', errorData);
          throw new Error('Session expired. Please log in again.');
        }
        throw new Error(errorData.message || 'Failed to get response from AI');
      }

      const data = await response.json();
      console.log('AI response received');

      if (!data.success) {
        throw new Error(data.message || 'Failed to get AI response');
      }

      const reply = data.response?.trim() || "Sorry, I couldn't understand that.";
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: reply,
        timestamp: new Date()
      }]);
      speak(cleanTextForSpeech(reply));
    } catch (err) {
      console.error('Chat error:', err);

      // Handle authentication errors
      if (err.message.includes('log in') || err.message.includes('token') || err.message.includes('session expired')) {
        setMessages([{
          role: 'assistant',
          content: 'Your session has expired. Please log in again to continue.',
          timestamp: new Date()
        }]);
        // Clear auth data on token expiry
        localStorage.removeItem('user');
        localStorage.removeItem('token');
        window.location.href = '/login';
      } else {
        setMessages(prev => [...prev, {
          role: 'assistant',
          content: `Error: ${err.message || 'Something went wrong. Please try again.'}`,
          timestamp: new Date()
        }]);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const clearChat = () => {
    setMessages([]);
    setInput('');
    if (isSpeaking) {
      setIsSpeaking(false);
    }
  };

  return (
    <Box sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      bgcolor: 'background.paper',
      borderRadius: 2,
      overflow: 'hidden',
      width: '100%',
      maxHeight: 'calc(100vh - 200px)',
      mt: 2
    }}>
      <Box sx={{
        p: { xs: 0.5, sm: 1 },
        borderBottom: 1,
        borderColor: 'divider',
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        alignItems: { xs: 'stretch', sm: 'center' },
        justifyContent: 'space-between',
        gap: { xs: 0.5, sm: 0 },
        bgcolor: 'rgba(46, 125, 50, 0.04)'
      }}>
        <Typography variant="h6" sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          fontSize: { xs: '0.9rem', sm: '1rem' },
          color: '#2e7d32',
          fontWeight: 600,
          p: 1
        }}>
          <SmartToyIcon sx={{
            fontSize: { xs: '1rem', sm: '1.2rem' },
            color: '#2e7d32'
          }} /> Quamin AI Farmer Assistant
        </Typography>
        <Box sx={{
          display: 'flex',
          gap: 1,
          flexWrap: 'wrap',
          justifyContent: { xs: 'center', sm: 'flex-end' },
          alignItems: 'center',
          p: 1
        }}>
          <LanguageSelector
            value={selectedLanguage}
            onChange={(newLang) => {
              if (isRecording && recognitionRef.current) {
                try {
                  recognitionRef.current.stop();
                } catch (err) {
                  console.error('Error stopping recognition:', err);
                }
              }
              setSelectedLanguage(newLang);
              if (recognitionRef.current) {
                recognitionRef.current.lang = newLang;
              }
            }}
          />
          <Box sx={{
            display: 'flex',
            gap: 0.5,
            bgcolor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: 1,
            p: 0.5
          }}>
            <Tooltip title={isRecording ? "Stop Recording" : "Start Recording"}>
              <span>
                <IconButton
                  onClick={() => {
                    if (isRecording) {
                      try {
                        recognitionRef.current?.stop();
                      } catch (err) {
                        console.error('Error stopping recognition:', err);
                      }
                    } else {
                      try {
                        if (recognitionRef.current) {
                          recognitionRef.current.lang = selectedLanguage;
                          recognitionRef.current.start();
                        } else {
                          setupSpeechRecognition();
                          recognitionRef.current?.start();
                        }
                      } catch (err) {
                        console.error('Error starting recognition:', err);
                      }
                    }
                  }}
                  color={isRecording ? "error" : "primary"}
                  disabled={isLoading}
                  size="small"
                  sx={{
                    '&:hover': {
                      bgcolor: isRecording ? 'error.light' : 'primary.light'
                    }
                  }}
                >
                  <MicIcon />
                </IconButton>
              </span>
            </Tooltip>
            <Tooltip title={isSpeaking ? "Stop Speaking" : "Start Speaking"}>
              <span>
                <IconButton
                  onClick={() => {
                    if (isSpeaking) {
                      stopSpeech();
                    } else if (messages.length > 0) {
                      const lastMessage = messages[messages.length - 1];
                      if (lastMessage.role === 'assistant') {
                        speak(cleanTextForSpeech(lastMessage.content));
                      }
                    }
                  }}
                  color={isSpeaking ? "error" : "primary"}
                  disabled={isLoading || messages.length === 0}
                  size="small"
                  sx={{
                    '&:hover': {
                      bgcolor: isSpeaking ? 'error.light' : 'primary.light'
                    }
                  }}
                >
                  {isSpeaking ? <VolumeOffIcon /> : <VolumeUpIcon />}
                </IconButton>
              </span>
            </Tooltip>
            {isSpeaking && (
              <>
                <Tooltip title="Pause Speaking">
                  <span>
                    <IconButton
                      onClick={pauseSpeech}
                      color="primary"
                      size="small"
                      sx={{
                        '&:hover': {
                          bgcolor: 'primary.light'
                        }
                      }}
                    >
                      <PauseIcon />
                    </IconButton>
                  </span>
                </Tooltip>
                <Tooltip title="Resume Speaking">
                  <span>
                    <IconButton
                      onClick={resumeSpeech}
                      color="primary"
                      size="small"
                      sx={{
                        '&:hover': {
                          bgcolor: 'primary.light'
                        }
                      }}
                    >
                      <PlayArrowIcon />
                    </IconButton>
                  </span>
                </Tooltip>
              </>
            )}
            <Tooltip title="Clear Chat">
              <span>
                <IconButton
                  onClick={clearChat}
                  color="primary"
                  disabled={isLoading || messages.length === 0}
                  size="small"
                  sx={{
                    '&:hover': {
                      bgcolor: 'primary.light'
                    }
                  }}
                >
                  <ClearIcon />
                </IconButton>
              </span>
            </Tooltip>
            <Tooltip title="Refresh Data">
              <span>
                <IconButton
                  onClick={onRefresh}
                  color="primary"
                  disabled={isLoading}
                  size="small"
                  sx={{
                    '&:hover': {
                      bgcolor: 'primary.light'
                    }
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </span>
            </Tooltip>
          </Box>
        </Box>
      </Box>

      <Box sx={{
        flex: 1,
        overflow: 'auto',
        p: { xs: 0.5, sm: 1 },
        display: 'flex',
        flexDirection: 'column',
        gap: 1,
        minHeight: { xs: '200px', sm: '300px' }
      }}>
        {messages.length === 0 ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            gap: 2,
            p: { xs: 1, sm: 2 }
          }}>
            <CircularProgress />
            <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center' }}>
              Loading farm data...
            </Typography>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={onRefresh}
              size="small"
            >
              Refresh Data
            </Button>
          </Box>
        ) : (
          <>
            {messages.map((message, index) => (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
                  mb: 2,
                  width: '100%'
                }}
              >
                <Paper
                  elevation={1}
                  sx={{
                    p: { xs: 1.5, sm: 2 },
                    maxWidth: { xs: '90%', sm: '80%' },
                    bgcolor: message.role === 'user' ? 'primary.light' : 'background.default',
                    color: message.role === 'user' ? 'primary.contrastText' : 'text.primary',
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  }}
                >
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>{message.content}</ReactMarkdown>
                </Paper>
              </Box>
            ))}
            {isLoading && (
              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
                <Paper elevation={1} sx={{
                  p: { xs: 1.5, sm: 2 },
                  maxWidth: { xs: '90%', sm: '80%' }
                }}>
                  <Typography>Thinking...</Typography>
                </Paper>
              </Box>
            )}
            <div ref={messagesEndRef} />
          </>
        )}
      </Box>

      <Box sx={{
        p: { xs: 0.5, sm: 1 },
        borderTop: 1,
        borderColor: 'divider',
        width: '100%'
      }}>
        <form onSubmit={handleSubmit}>
          <TextField
            fullWidth
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask me anything about your farm..."
            disabled={isLoading}
            size="small"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    type="submit"
                    disabled={!input.trim() || isLoading}
                    size="small"
                  >
                    <SendIcon />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </form>
      </Box>
    </Box>
  );
};

export default AIAgent;