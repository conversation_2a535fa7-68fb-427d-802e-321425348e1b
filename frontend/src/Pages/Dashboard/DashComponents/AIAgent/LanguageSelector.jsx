import React from 'react';
import { FormControl, Select, MenuItem, InputLabel } from '@mui/material';
import { useTranslation } from '../../../../hooks/useTranslation';
import { useLanguage } from '../../../../contexts/LanguageContext';

const languages = [
  { code: 'en-IN', name: 'English' },
  { code: 'hi-IN', name: 'हिंदी' },
  { code: 'bn-IN', name: 'বাংলা' },
  { code: 'gu-IN', name: 'ગુજરાતી' },
  { code: 'kn-IN', name: 'ಕನ್ನಡ' },
  { code: 'ml-IN', name: 'മലയാളം' },
  { code: 'mr-IN', name: 'मराठी' },
  { code: 'pa-IN', name: 'ਪੰਜਾਬੀ' },
  { code: 'ta-IN', name: 'தமிழ்' },
  { code: 'te-IN', name: 'తెలుగు' },
  { code: 'ur-IN', name: 'اردو' },
  { code: 'or-IN', name: 'ଓଡ଼ିଆ' },
  { code: 'as-IN', name: 'অসমীয়া' },
  { code: 'bho-IN', name: 'भोजपुरी' },
  { code: 'ma-IN', name: 'मैथिली' },
  { code: 'sa-IN', name: 'संस्कृत' },
  { code: 'ks-IN', name: 'کٲشُر' },
  { code: 'mni-IN', name: 'মৈতৈলোন্' },
  { code: 'ne-IN', name: 'नेपाली' },
  { code: 'sd-IN', name: 'سنڌي' }
];

const LanguageSelector = () => {
  const { t } = useTranslation();
  const { selectedLanguage, setSelectedLanguage } = useLanguage();
  
  return (
    <FormControl size="small" sx={{ minWidth: 120 }}>
      <InputLabel>{t('languageSelector.label', 'Language')}</InputLabel>
      <Select
        value={selectedLanguage}
        onChange={(e) => setSelectedLanguage(e.target.value)}
        label={t('languageSelector.label', 'Language')}
        sx={{ height: 40 }}
      >
        {languages.map((lang) => (
          <MenuItem key={lang.code} value={lang.code}>
            {lang.name}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default LanguageSelector; 