import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useLanguage } from '../../../../contexts/LanguageContext';
import {
  Box,
  TextField,
  IconButton,
  Typography,
  CircularProgress,
  Paper,
  Tooltip,
  Button,
  InputAdornment
} from '@mui/material';
import {
  Send as SendIcon,
  Mic as MicIcon,
  VolumeUp as VolumeUpIcon,
  SmartToy as SmartToyIcon,
  Clear as ClearIcon,
  VolumeOff as VolumeOffIcon,
  Pause as PauseIcon,
  PlayArrow as PlayArrowIcon
} from '@mui/icons-material';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import * as SpeechSDK from 'microsoft-cognitiveservices-speech-sdk';
import LanguageSelector from './LanguageSelector';
import { useAuth } from '../../../../contexts/AuthContext';
import { API_BASE_URL } from '../../../../config/api';
import translationService from '../../../../utils/TranslationService';
import { useTranslation } from '../../../../hooks/useTranslation';
import DynamicTranslation from '../../../../components/DynamicTranslation/DynamicTranslation';

// Helper function to get voice name based on language
const getVoiceName = (lang) => {
  const voiceMapping = {
    // English voices
    "en-IN": "en-IN-NeerjaNeural",
    "en-US": "en-US-JennyNeural",

    // Indian language voices with native support
    "hi-IN": "hi-IN-SwaraNeural",    // Hindi
    "bn-IN": "bn-IN-TanishaaNeural", // Bengali
    "gu-IN": "gu-IN-DhwaniNeural",   // Gujarati
    "kn-IN": "kn-IN-SapnaNeural",    // Kannada
    "ml-IN": "ml-IN-SobhanaNeural",  // Malayalam
    "mr-IN": "mr-IN-AarohiNeural",   // Marathi
    "pa-IN": "pa-IN-GurleenNeural",  // Punjabi
    "ta-IN": "ta-IN-PallaviNeural",  // Tamil
    "te-IN": "te-IN-ShrutiNeural",   // Telugu
    "ur-IN": "ur-PK-UzmaNeural",     // Urdu (using Pakistan voice as fallback)

    // Indian languages without direct neural voice support
    // Fallback to English-India voice
    "or-IN": "en-IN-NeerjaNeural",    // Odia
    "as-IN": "en-IN-NeerjaNeural",    // Assamese
    "bho-IN": "hi-IN-SwaraNeural",    // Bhojpuri (fallback to Hindi)
    "ma-IN": "en-IN-NeerjaNeural",    // Manipuri
    "sa-IN": "en-IN-NeerjaNeural",    // Sanskrit
    "ks-IN": "en-IN-NeerjaNeural",    // Kashmiri
    "mni-IN": "en-IN-NeerjaNeural",   // Manipuri
    "ne-IN": "en-IN-NeerjaNeural",    // Nepali
    "sd-IN": "en-IN-NeerjaNeural"     // Sindhi
  };
  return voiceMapping[lang] || "en-IN-NeerjaNeural";
};

// Helper function to clean text for speech synthesis
const cleanTextForSpeech = (text) => {
  if (!text) return '';
  return text
    .replace(/[#*_`~>\\-]+/g, '')
    .replace(/\\[(.*?)\\]\\((.*?)\\)/g, '$1')
    .replace(/\\!\\[(.*?)\\]\\((.*?)\\)/g, '')
    .replace(/\\n{2,}/g, '. ')
    .replace(/\\n/g, ' ')
    .replace(/\\s+/g, ' ')
    .trim();
};

const AIAgent = ({ weatherData, localWeatherData, cropData, bulletinData, soilData, alerts, schedule, marketData, initialMessages = [], onMessagesUpdate }) => {
  const { currentUser, getToken } = useAuth();
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState(initialMessages);
  const [isLoading, setIsLoading] = useState(false);
  // Use global language context instead of local state
  const { selectedLanguage, setSelectedLanguage } = useLanguage();
  const messagesEndRef = useRef(null);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const recognitionRef = useRef(null);
  const { t } = useTranslation();

  // Add a ref to track if we're already falling back to browser TTS
  const fallingBackToBrowserTTS = useRef(false);

  // Track user interaction to enable speech synthesis
  const [userInteracted, setUserInteracted] = useState(false);

  // Track if we've shown the not-allowed alert
  const [shownNotAllowedAlert, setShownNotAllowedAlert] = useState(false);

  // Scroll to bottom when messages change
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();

    // Notify parent component about message updates if callback exists
    if (onMessagesUpdate && typeof onMessagesUpdate === 'function') {
      onMessagesUpdate(messages);
    }
  }, [messages, onMessagesUpdate]);

  // Add a function to initialize audio context for browser audio playback
  const initializeAudioContext = useCallback(() => {
    try {
      // Create a silent audio context to enable audio on user interaction
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      if (AudioContext) {
        const audioCtx = new AudioContext();
        // Create a silent oscillator
        const oscillator = audioCtx.createOscillator();
        const gainNode = audioCtx.createGain();
        // Set the volume to 0
        gainNode.gain.value = 0;
        oscillator.connect(gainNode);
        gainNode.connect(audioCtx.destination);
        // Start and stop quickly
        oscillator.start();
        setTimeout(() => {
          oscillator.stop();
          console.log('Audio context initialized successfully');
        }, 100);
      }
    } catch (error) {
      console.warn('Could not initialize audio context:', error);
    }
  }, []);

  // Add event listener for user interaction
  useEffect(() => {
    const handleUserInteraction = () => {
      if (!userInteracted) {
        console.log('User interaction detected, enabling audio playback');
        setUserInteracted(true);
        setShownNotAllowedAlert(false); // Reset alert state on new interaction

        // Initialize audio context
        initializeAudioContext();
      }
    };

    // Call immediately on mount to check if user has already interacted
    handleUserInteraction();

    // Add event listeners for common user interactions
    document.addEventListener('click', handleUserInteraction);
    document.addEventListener('keydown', handleUserInteraction);
    document.addEventListener('touchstart', handleUserInteraction);
    document.addEventListener('mousemove', handleUserInteraction);
    document.addEventListener('scroll', handleUserInteraction);

    // Also try to initialize on window focus
    window.addEventListener('focus', handleUserInteraction);

    return () => {
      // Clean up
      document.removeEventListener('click', handleUserInteraction);
      document.removeEventListener('keydown', handleUserInteraction);
      document.removeEventListener('touchstart', handleUserInteraction);
      document.removeEventListener('mousemove', handleUserInteraction);
      document.removeEventListener('scroll', handleUserInteraction);
      window.removeEventListener('focus', handleUserInteraction);
    };
  }, [userInteracted, initializeAudioContext]);

  // Add a separate effect to periodically try to initialize audio context
  useEffect(() => {
    // Only run this if the user has interacted but we're still getting audio errors
    if (userInteracted && shownNotAllowedAlert) {
      console.log('Setting up periodic audio context initialization');

      // Try to initialize audio context every 5 seconds
      const intervalId = setInterval(() => {
        console.log('Periodic audio context initialization attempt');
        initializeAudioContext();
      }, 5000);

      return () => {
        clearInterval(intervalId);
      };
    }
  }, [userInteracted, shownNotAllowedAlert, initializeAudioContext]);

  // Fallback speech function using browser's built-in speech synthesis
  const speakWithBrowserTTS = (text) => {
    // Check if we're already falling back to browser TTS to prevent double speech
    if (fallingBackToBrowserTTS.current) {
      console.log('Already falling back to browser TTS, skipping duplicate call');
      return;
    }

    fallingBackToBrowserTTS.current = true;

    if (!text || !window.speechSynthesis) {
      console.warn('Browser speech synthesis not available');
      fallingBackToBrowserTTS.current = false;
      return;
    }

    // Check if user has interacted with the page
    if (!userInteracted) {
      console.warn('User has not interacted with the page yet, speech synthesis may be blocked');
      // We'll still try, but it might fail with 'not-allowed'
    }

    try {
      // Cancel any ongoing speech
      window.speechSynthesis.cancel();

      // Initialize voices - this is needed in some browsers
      let voices = window.speechSynthesis.getVoices();

      // If voices array is empty, we need to wait for the voiceschanged event
      if (voices.length === 0) {
        console.log('No voices available yet, using default voice');
        // We'll use a simple English voice as fallback
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = 'en-IN'; // Use Indian English as default
        utterance.rate = 1.0;
        utterance.pitch = 1.0;
        utterance.volume = 1.0;

        utterance.onstart = () => {
          setIsSpeaking(true);
          setIsPaused(false);
        };

        utterance.onend = () => {
          setIsSpeaking(false);
          setIsPaused(false);
          // Reset the fallback flag when speech ends
          fallingBackToBrowserTTS.current = false;
        };

        utterance.onerror = (event) => {
          console.error('Browser speech synthesis error:', event);
          if (event.error === 'not-allowed') {
            console.warn('Speech synthesis not allowed. User interaction may be required first.');

            // Don't show alert if we've already shown it
            if (!shownNotAllowedAlert) {
              // Display a message to the user
              alert('Please click anywhere on the page to enable text-to-speech functionality.');
              setShownNotAllowedAlert(true);
            }

            // Try to initialize audio context again
            initializeAudioContext();
          }
          setIsSpeaking(false);
          setIsPaused(false);
          // Reset the fallback flag when there's an error
          fallingBackToBrowserTTS.current = false;
        };

        window.speechSynthesis.speak(utterance);
        return;
      }

      // Map our language codes to browser language codes
      const langMap = {
        // English
        'en-US': 'en-US',
        'en-IN': 'en-IN',

        // Indian languages with browser support
        'hi-IN': 'hi-IN', // Hindi
        'ta-IN': 'ta-IN', // Tamil
        'te-IN': 'te-IN', // Telugu
        'kn-IN': 'kn-IN', // Kannada
        'mr-IN': 'mr-IN', // Marathi
        'bn-IN': 'bn-IN', // Bengali
        'gu-IN': 'gu-IN', // Gujarati
        'ml-IN': 'ml-IN', // Malayalam
        'pa-IN': 'pa-IN', // Punjabi
        'ur-IN': 'ur',    // Urdu

        // Fallbacks for languages with limited browser support
        'bho-IN': 'hi-IN', // Bhojpuri -> Hindi
        'or-IN': 'en-IN',  // Odia -> English (India)
        'as-IN': 'en-IN',  // Assamese -> English (India)
        'ma-IN': 'en-IN',  // Manipuri -> English (India)
        'sa-IN': 'en-IN',  // Sanskrit -> English (India)
        'ks-IN': 'en-IN',  // Kashmiri -> English (India)
        'mni-IN': 'en-IN', // Manipuri -> English (India)
        'ne-IN': 'en-IN',  // Nepali -> English (India)
        'sd-IN': 'en-IN'   // Sindhi -> English (India)
      };

      const langCode = langMap[selectedLanguage] || 'en-IN';

      // Try to find a voice that matches the selected language exactly
      let voice = voices.find(v => v.lang === langCode);

      // If no exact match, try to find a voice that starts with the language code
      if (!voice) {
        voice = voices.find(v => v.lang.startsWith(langCode));
      }

      // If still no match, try to find any voice in the same language family
      if (!voice && langCode.length >= 2) {
        const langFamily = langCode.substring(0, 2);
        voice = voices.find(v => v.lang.startsWith(langFamily));
      }

      // If still no match, fall back to English
      if (!voice) {
        voice = voices.find(v => v.lang.startsWith('en-US'));
      }

      // If still no match, try any English voice
      if (!voice) {
        voice = voices.find(v => v.lang.startsWith('en'));
      }

      // Create the utterance
      const utterance = new SpeechSynthesisUtterance(text);

      if (voice) {
        console.log(`Using browser voice: ${voice.name} (${voice.lang})`);
        utterance.voice = voice;
        utterance.lang = voice.lang;
      } else {
        console.log(`No matching browser voice found for ${langCode}, using default`);
        utterance.lang = 'en-IN'; // Use Indian English as default
      }

      // Set speech parameters
      utterance.rate = 1.0;
      utterance.pitch = 1.0;
      utterance.volume = 1.0;

      utterance.onstart = () => {
        setIsSpeaking(true);
        setIsPaused(false);
      };

      utterance.onend = () => {
        setIsSpeaking(false);
        setIsPaused(false);
        // Reset the fallback flag when speech ends
        fallingBackToBrowserTTS.current = false;
      };

      utterance.onerror = (event) => {
        console.error('Browser speech synthesis error:', event);
        if (event.error === 'not-allowed') {
          console.warn('Speech synthesis not allowed. User interaction may be required first.');

          // Don't show alert if we've already shown it
          if (!shownNotAllowedAlert) {
            // Display a message to the user
            alert('Please click anywhere on the page to enable text-to-speech functionality.');
            setShownNotAllowedAlert(true);
          }

          // Try to initialize audio context again
          initializeAudioContext();
        }
        setIsSpeaking(false);
        setIsPaused(false);
        // Reset the fallback flag when there's an error
        fallingBackToBrowserTTS.current = false;
      };

      window.speechSynthesis.speak(utterance);
    } catch (error) {
      console.error('Error in browser speech synthesis:', error);
      setIsSpeaking(false);
      setIsPaused(false);
    } finally {
      // Reset the flag after a short delay to prevent immediate re-triggering
      setTimeout(() => {
        fallingBackToBrowserTTS.current = false;
      }, 500);
    }
  };

  // Audio control functions
  const pauseSpeech = () => {
    let audioPaused = false;

    // Try to pause audio element if active
    if (window.currentSpeechAudio) {
      try {
        window.currentSpeechAudio.pause();
        console.log('Audio playback paused successfully');
        audioPaused = true;
        setIsSpeaking(false);
        setIsPaused(true);
      } catch (error) {
        console.error('Error pausing audio playback:', error);
      }
    }

    // Also try to pause browser's speech synthesis if active
    try {
      if (window.speechSynthesis && window.speechSynthesis.speaking) {
        window.speechSynthesis.pause();
        console.log('Browser speech paused successfully');
        setIsSpeaking(false);
        setIsPaused(true);
        audioPaused = true;
      }
    } catch (error) {
      console.error('Error pausing browser speech:', error);
    }

    // If nothing was paused, make sure speaking state is updated
    if (!audioPaused) {
      setIsSpeaking(false);
      setIsPaused(false); // Reset paused state if nothing was actually paused
    }
  };

  const resumeSpeech = () => {
    let resumed = false;

    // Only attempt to resume if we're actually in a paused state
    if (!isPaused) {
      console.log('Not in paused state, nothing to resume');
      return;
    }

    // Try to resume audio element if active
    if (window.currentSpeechAudio) {
      try {
        const playPromise = window.currentSpeechAudio.play();

        if (playPromise !== undefined) {
          playPromise.then(() => {
            console.log('Audio playback resumed successfully');
            setIsSpeaking(true);
            setIsPaused(false);
            resumed = true;
          }).catch(error => {
            console.error('Error resuming audio playback:', error);
            // If we can't resume, try to restart from the beginning
            if (error.name === 'NotAllowedError') {
              window.currentSpeechAudio.currentTime = 0;
              window.currentSpeechAudio.play().catch(e => {
                console.error('Failed to restart audio:', e);
              });
            }
          });
        } else {
          // For browsers that don't return a promise
          console.log('Audio playback resumed (legacy)');
          setIsSpeaking(true);
          setIsPaused(false);
          resumed = true;
        }
      } catch (error) {
        console.error('Error resuming audio playback:', error);
      }
    }

    // Also try to resume browser's speech synthesis if active
    try {
      if (window.speechSynthesis) {
        // Check if speech synthesis is paused
        const wasPaused = window.speechSynthesis.paused;

        // Try to resume
        window.speechSynthesis.resume();

        // In some browsers, we can't detect if it was paused, so we'll just try to resume anyway
        console.log('Browser speech resume attempted');
        setIsSpeaking(true);
        setIsPaused(false);

        if (wasPaused) {
          console.log('Browser speech resumed successfully');
          resumed = true;
        }
      }
    } catch (error) {
      console.error('Error resuming browser speech:', error);
    }

    // If neither audio nor browser speech was resumed, try to speak the response again
    if (!resumed && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === 'assistant') {
        const cleanedText = cleanTextForSpeech(lastMessage.content);
        console.log('No active speech to resume, starting new speech');
        speak(cleanedText);
      }
    }

    // Reset paused state
    setIsPaused(false);
  };

  const stopSpeech = () => {
    // Stop audio element if it's active
    if (window.currentSpeechAudio) {
      try {
        window.currentSpeechAudio.pause();
        window.currentSpeechAudio.currentTime = 0;
        console.log('Audio playback stopped successfully');
        // Release the object URL if it exists
        if (window.currentSpeechAudio.src && window.currentSpeechAudio.src.startsWith('blob:')) {
          URL.revokeObjectURL(window.currentSpeechAudio.src);
        }
        window.currentSpeechAudio = null;
      } catch (error) {
        console.error('Error stopping audio playback:', error);
      }
    }

    // Also stop browser's speech synthesis if active
    try {
      if (window.speechSynthesis) {
        window.speechSynthesis.cancel();
        console.log('Browser speech stopped successfully');
      }
    } catch (error) {
      console.error('Error stopping browser speech:', error);
    }

    // Always set speaking to false, reset paused state, and reset fallback flag
    setIsSpeaking(false);
    setIsPaused(false);
    fallingBackToBrowserTTS.current = false;
  };

  const speak = async (text) => {
    if (!text) return;

    // Clean the text for speech
    const cleanedText = cleanTextForSpeech(text);
    if (!cleanedText) return;

    // Stop any ongoing speech
    stopSpeech();

    // Set speaking state and reset paused state
    setIsSpeaking(true);
    setIsPaused(false);

    try {
      const voiceName = getVoiceName(selectedLanguage);
      const key = import.meta.env.VITE_AZURE_SPEECH_KEY;
      const region = import.meta.env.VITE_AZURE_SPEECH_REGION;

      if (!key || !region) {
        console.error('Azure Speech key or region is missing.');
        speakWithBrowserTTS(cleanedText);
        return;
      }

      console.log(`Using Azure TTS with voice: ${voiceName} for language: ${selectedLanguage}`);

      // Create speech configuration
      const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(key, region);
      speechConfig.speechSynthesisVoiceName = voiceName;

      // Create audio configuration
      const audioConfig = SpeechSDK.AudioConfig.fromDefaultSpeakerOutput();

      // Create synthesizer
      const synthesizer = new SpeechSDK.SpeechSynthesizer(speechConfig, audioConfig);

      // Speak the text
      synthesizer.speakTextAsync(
        cleanedText,
        result => {
          if (result.reason === SpeechSDK.ResultReason.SynthesizingAudioCompleted) {
            console.log("✅ Azure TTS completed successfully");
          } else {
            console.error("Azure TTS failed:", result.errorDetails);
            // Fall back to browser TTS
            speakWithBrowserTTS(cleanedText);
          }
          setIsSpeaking(false);
          synthesizer.close();
        },
        error => {
          console.error("Azure TTS error:", error);
          // Fall back to browser TTS
          speakWithBrowserTTS(cleanedText);
          setIsSpeaking(false);
          synthesizer.close();
        }
      );
    } catch (error) {
      console.error("Error initializing Azure TTS:", error);
      // Fall back to browser TTS
      speakWithBrowserTTS(cleanedText);
    }
  };

  const setupSpeechRecognition = () => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      console.error('Speech recognition not supported in this browser');
      return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = selectedLanguage;

    recognition.onstart = () => {
      setIsRecording(true);
      console.log('Speech recognition started');
    };

    recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript;
      console.log('Speech recognition result:', transcript);
      setInput(transcript);
    };

    recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      setIsRecording(false);
    };

    recognition.onend = () => {
      setIsRecording(false);
      console.log('Speech recognition ended');
    };

    recognitionRef.current = recognition;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userInput = input.trim();
    setMessages(prev => [...prev, {
      role: 'user',
      content: userInput,
      timestamp: new Date()
    }]);
    setInput('');
    setIsLoading(true);

    try {
      // Prepare context data with proper transformation
      const contextData = {
        weather: {
          location: weatherData?.location || 'Unknown',
          current: {
            temperature: weatherData?.current?.temperature || weatherData?.current?.temp_c || weatherData?.current?.temp || 35,
            humidity: weatherData?.current?.humidity || 65,
            windSpeed: weatherData?.current?.windSpeed || weatherData?.current?.wind_kph / 3.6 || 12,
            conditions: weatherData?.current?.conditions || weatherData?.current?.condition?.text || weatherData?.current?.description || 'broken clouds',
            lastUpdated: weatherData?.current?.lastUpdated || weatherData?.current?.last_updated || '20/04/2025, 02:40 pm'
          },
          forecast: Array.isArray(weatherData?.forecast?.forecastday) ? weatherData.forecast.forecastday.map(day => ({
            date: day.date,
            temperature: day.day?.avgtemp_c || 29,
            maxTemp: day.day?.maxtemp_c || 32,
            minTemp: day.day?.mintemp_c || 26,
            condition: day.day?.condition?.text || 'Partly Cloudy',
            precipitation: day.day?.totalprecip_mm || 0
          })) : Array.isArray(weatherData?.forecast) ? weatherData.forecast.map(day => ({
            date: day.date,
            temperature: day.temperature || day.temp || 29,
            maxTemp: day.maxTemp || day.max || 32,
            minTemp: day.minTemp || day.min || 26,
            condition: day.condition || day.description || 'Partly Cloudy',
            precipitation: day.precipitation || day.rain || 0
          })) : [
            { date: '20/04/2025', temperature: 35, maxTemp: 38, minTemp: 32, condition: 'broken clouds', precipitation: 0 },
            { date: '21/04/2025', temperature: 36, maxTemp: 39, minTemp: 33, condition: 'Sunny', precipitation: 0 },
            { date: '22/04/2025', temperature: 34, maxTemp: 37, minTemp: 31, condition: 'Rain', precipitation: 15 },
            { date: '23/04/2025', temperature: 33, maxTemp: 36, minTemp: 30, condition: 'Rain', precipitation: 20 },
            { date: '24/04/2025', temperature: 34, maxTemp: 37, minTemp: 31, condition: 'Partly Cloudy', precipitation: 5 }
          ],
          bulletin: weatherData?.bulletin || bulletinData?.bulletin || (bulletinData && bulletinData.title ? `${bulletinData.title}: ${bulletinData.description || ''}` : 'Weather Bulletin: High temperatures expected to continue. Ensure adequate irrigation for crops.')
        },
        soilHealth: soilData ? {
          ph: soilData.ph || 6.5,
          nitrogen: soilData.nitrogen || 'Medium (45 ppm)',
          phosphorus: soilData.phosphorus || 'High (65 ppm)',
          potassium: soilData.potassium || 'Medium (180 ppm)',
          organicMatter: soilData.organicMatter || '3.2%',
          moisture: soilData.moisture || '22%',
          temperature: soilData.temperature || '18°C',
          lastTested: soilData.lastTested || '01/04/2025'
        } : {
          ph: 6.5,
          nitrogen: 'Medium (45 ppm)',
          phosphorus: 'High (65 ppm)',
          potassium: 'Medium (180 ppm)',
          organicMatter: '3.2%',
          moisture: '22%',
          temperature: '18°C',
          lastTested: '01/04/2025'
        },
        alerts: alerts && alerts.length > 0 ? alerts : [
          {
            id: 1,
            title: 'Low Soil Moisture',
            message: 'Soil moisture is below optimal levels in Field A',
            timestamp: '04/04/2025, 21:26:10',
            type: 'INFO'
          },
          {
            id: 2,
            title: 'Weather Update',
            message: 'Rain expected in the next 24 hours',
            timestamp: '04/04/2025, 21:26:10',
            type: 'INFO'
          }
        ],
        crop: cropData ? {
          name: cropData.name || 'Rice',
          variety: cropData.variety || 'IR-36',
          plantingDate: cropData.plantingDate || '15/03/2025',
          expectedHarvest: cropData.expectedHarvest || '15/07/2025',
          growthStage: cropData.growthStage || 'Vegetative',
          healthStatus: cropData.healthStatus || 'Good',
          fieldSize: cropData.fieldSize || '5 acres'
        } : {
          name: 'Rice',
          variety: 'IR-36',
          plantingDate: '15/03/2025',
          expectedHarvest: '15/07/2025',
          growthStage: 'Vegetative',
          healthStatus: 'Good',
          fieldSize: '5 acres'
        },
        schedule: schedule || {},
        market: marketData || {}
      };

      // We've already handled all the data in the contextData object above

      // Log the actual data for debugging with more details
      console.log('Context data details (AIAgent.jsx):', {
        weather: JSON.stringify(weatherData),
        soilHealth: JSON.stringify(soilData),
        bulletin: JSON.stringify(bulletinData),
        alerts: JSON.stringify(alerts),
        crop: JSON.stringify(cropData)
      });

      // Log the full context data being sent to the API
      console.log('Full context data being sent to API (AIAgent.jsx):', JSON.stringify(contextData));

      console.log('Context data availability:', {
        hasWeather: !!weatherData,
        hasLocalWeather: !!localWeatherData,
        hasCrop: !!cropData,
        hasBulletin: !!bulletinData,
        hasSoilHealth: !!soilData,
        hasAlerts: !!(alerts && alerts.length),
        hasSchedule: !!schedule,
        hasMarket: !!marketData
      });

      // Call the AI API
      const apiUrl = import.meta.env.VITE_API_BASE_URL_CHATBOT || API_BASE_URL;
      console.log('Using API URL:', apiUrl);

      const response = await fetch(`${apiUrl}/api/ai/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(currentUser && getToken ? { 'Authorization': `Bearer ${getToken()}` } : {})
        },
        body: JSON.stringify({
          message: userInput,
          language: 'en-IN', // Always request English from backend for consistent AI responses
          farmData: contextData  // Use the correct key name expected by the API
        })
      });

      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status}`);
      }

      const data = await response.json();
      let reply = data.response?.trim() || "Sorry, I couldn't understand that.";
      
      // Translate the reply if language is not English
      if (selectedLanguage !== 'en-IN' && selectedLanguage !== 'en-US' && selectedLanguage !== 'en') {
        try {
          console.log(`Translating response to ${selectedLanguage}`);
          const translatedReply = await translationService.translateText(
            reply,
            selectedLanguage
          );
          
          if (translatedReply && translatedReply !== reply) {
            console.log('Translation successful');
            reply = translatedReply;
          }
        } catch (error) {
          console.error('Translation error:', error);
          // Continue with original reply if translation fails
        }
      }

      // Add the AI response to messages
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: reply,
        originalContent: data.response?.trim(), // Store original English content
        timestamp: new Date()
      }]);

      // Speak the response
      speak(reply);
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: `Error: ${error.message || 'Something went wrong. Please try again.'}`,
        timestamp: new Date()
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearChat = () => {
    stopSpeech();
    setIsPaused(false);
    setMessages([]);
  };

  // Language change is now handled by global language context
  // Update recognition language when global language changes
  useEffect(() => {
    if (recognitionRef.current) {
      recognitionRef.current.lang = selectedLanguage;
    }
  }, [selectedLanguage]);

  return (
    <Box sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      bgcolor: 'background.paper',
      borderRadius: 2,
      overflow: 'hidden',
      width: '100%',
      minHeight: '550px',
      maxHeight: '650px',
      mt: 2,
      mx: { xs: 0, sm: 1, md: 2 }
    }}>
      <Box sx={{
        p: { xs: 0.5, sm: 1 },
        borderBottom: 1,
        borderColor: 'divider',
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        alignItems: { xs: 'stretch', sm: 'center' },
        justifyContent: 'space-between',
        gap: { xs: 0.5, sm: 0 },
        bgcolor: 'rgba(46, 125, 50, 0.04)'
      }}>
        <Typography variant="h6" sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          fontSize: { xs: '0.9rem', sm: '1rem' },
          color: '#2e7d32',
          fontWeight: 600,
          p: 1
        }}>
          <Box
            component="img"
            src="/images/ai-chatbot.jpg"
            alt="AI Chatbot"
            sx={{
              width: { xs: '24px', sm: '28px' },
              height: { xs: '24px', sm: '28px' },
              borderRadius: '50%',
              objectFit: 'cover',
              filter: 'hue-rotate(270deg) saturate(1.5)',
              border: '2px solid #2e7d32'
            }}
          /> <DynamicTranslation text={t('aiAgent.title', 'Quamin AI Farmer Assistant')} />
        </Typography>
        <Box sx={{
          display: 'flex',
          gap: 1,
          flexWrap: 'wrap',
          justifyContent: { xs: 'center', sm: 'flex-end' },
          alignItems: 'center',
          p: 1
        }}>
          <LanguageSelector />
          <Box sx={{
            display: 'flex',
            gap: 0.5,
            bgcolor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: 1,
            p: 0.5
          }}>
            <Tooltip title={isRecording ? t('aiAgent.stopRecording', "Stop Recording") : t('aiAgent.startRecording', "Start Recording")}>
              <span>
                <IconButton
                  onClick={() => {
                    if (isRecording) {
                      try {
                        recognitionRef.current?.stop();
                      } catch (err) {
                        console.error('Error stopping recognition:', err);
                      }
                    } else {
                      try {
                        if (recognitionRef.current) {
                          recognitionRef.current.lang = selectedLanguage;
                          recognitionRef.current.start();
                        } else {
                          setupSpeechRecognition();
                          recognitionRef.current?.start();
                        }
                      } catch (err) {
                        console.error('Error starting recognition:', err);
                      }
                    }
                  }}
                  color={isRecording ? "error" : "primary"}
                  disabled={isLoading}
                  size="small"
                  sx={{
                    '&:hover': {
                      bgcolor: isRecording ? 'error.light' : 'primary.light'
                    }
                  }}
                >
                  <MicIcon />
                </IconButton>
              </span>
            </Tooltip>
            <Tooltip title={isSpeaking ? t('aiAgent.stopSpeaking', "Stop Speaking") : t('aiAgent.startSpeaking', "Start Speaking")}>
              <span>
                <IconButton
                  onClick={() => {
                    if (isSpeaking) {
                      stopSpeech();
                    } else if (messages.length > 0) {
                      const lastMessage = messages[messages.length - 1];
                      if (lastMessage.role === 'assistant') {
                        speak(lastMessage.content);
                      }
                    }
                  }}
                  color={isSpeaking ? "error" : "primary"}
                  disabled={isLoading || messages.length === 0}
                  size="small"
                  sx={{
                    '&:hover': {
                      bgcolor: isSpeaking ? 'error.light' : 'primary.light'
                    }
                  }}
                >
                  {isSpeaking ? <VolumeOffIcon /> : <VolumeUpIcon />}
                </IconButton>
              </span>
            </Tooltip>
            {isSpeaking && (
              <>
                <Tooltip title={t('aiAgent.pauseSpeaking', "Pause Speaking")}>
                  <span>
                    <IconButton
                      onClick={pauseSpeech}
                      color="primary"
                      size="small"
                      sx={{
                        '&:hover': {
                          bgcolor: 'primary.light'
                        }
                      }}
                    >
                      <PauseIcon />
                    </IconButton>
                  </span>
                </Tooltip>
                <Tooltip title={t('aiAgent.resumeSpeaking', "Resume Speaking")}>
                  <span>
                    <IconButton
                      onClick={resumeSpeech}
                      color="primary"
                      size="small"
                      sx={{
                        '&:hover': {
                          bgcolor: 'primary.light'
                        }
                      }}
                    >
                      <PlayArrowIcon />
                    </IconButton>
                  </span>
                </Tooltip>
              </>
            )}
            <Tooltip title={t('aiAgent.clearChat', "Clear Chat")}>
              <span>
                <IconButton
                  onClick={handleClearChat}
                  color="primary"
                  size="small"
                  sx={{
                    '&:hover': {
                      bgcolor: 'primary.light'
                    }
                  }}
                >
                  <ClearIcon />
                </IconButton>
              </span>
            </Tooltip>
          </Box>
        </Box>
      </Box>

      <Box sx={{
        flex: 1,
        overflow: 'auto',
        p: { xs: 1, sm: 2 },
        display: 'flex',
        flexDirection: 'column',
        gap: 1,
        minHeight: { xs: '250px', sm: '350px' },
        maxHeight: '400px'
      }}>
        {messages.length === 0 ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            gap: 2,
            p: { xs: 1, sm: 2 }
          }}>
            <Box
              component="img"
              src="/images/ai-chatbot.jpg"
              alt="AI Chatbot"
              sx={{
                width: '80px',
                height: '80px',
                borderRadius: '50%',
                objectFit: 'cover',
                mb: 2,
                boxShadow: '0 4px 12px rgba(46, 125, 50, 0.4)',
                filter: 'hue-rotate(270deg) saturate(1.5)',
                border: '3px solid #2e7d32',
                p: 0.5,
                bgcolor: 'rgba(46, 125, 50, 0.1)'
              }}
            />
            <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center' }}>
              <DynamicTranslation text={t('aiAgent.askAnything', 'Ask me anything about your farm, crops, weather, or agricultural practices.')} />
            </Typography>
            <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2, width: '100%' }}>
              {bulletinData && bulletinData.title && (
                <Box sx={{ p: 2, bgcolor: 'rgba(46, 125, 50, 0.1)', borderRadius: 2, width: '100%', maxWidth: '90%' }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
                    <DynamicTranslation text={t('aiAgent.latestWeatherBulletin', 'Latest Weather Bulletin:')} /> {bulletinData.title}
                  </Typography>
                  {bulletinData.description && (
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      {bulletinData.description}
                    </Typography>
                  )}
                </Box>
              )}

              {soilData ? (
                <Box sx={{ p: 2, bgcolor: 'rgba(46, 125, 50, 0.1)', borderRadius: 2, width: '100%', maxWidth: '90%' }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
                    <DynamicTranslation text={t('aiAgent.soilHealthAvailable', 'Soil Health Data Available')} />
                  </Typography>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    <DynamicTranslation text={t('aiAgent.askAboutSoil', 'You can ask me about your soil health, including pH levels, nutrient content, and recommendations.')} />
                  </Typography>
                  {soilData.ph && (
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      <DynamicTranslation text={t('aiAgent.currentPh', 'Current pH:')} /> {soilData.ph}
                    </Typography>
                  )}
                  {soilData.nitrogen && (
                    <Typography variant="body2">
                      <DynamicTranslation text={t('soilHealth.nitrogen', 'Nitrogen:')} /> {soilData.nitrogen}
                    </Typography>
                  )}
                  {soilData.phosphorus && (
                    <Typography variant="body2">
                      <DynamicTranslation text={t('soilHealth.phosphorus', 'Phosphorus:')} /> {soilData.phosphorus}
                    </Typography>
                  )}
                  {soilData.potassium && (
                    <Typography variant="body2">
                      <DynamicTranslation text={t('soilHealth.potassium', 'Potassium:')} /> {soilData.potassium}
                    </Typography>
                  )}
                </Box>
              ) : (
                <Box sx={{ p: 2, bgcolor: 'rgba(46, 125, 50, 0.05)', borderRadius: 2, width: '100%', maxWidth: '90%' }}>
                  <Typography variant="body2" color="text.secondary">
                    <DynamicTranslation text={t('soilHealth.noData', 'Soil health data not available')} />
                  </Typography>
                </Box>
              )}

              {alerts && alerts.length > 0 ? (
                <Box sx={{ p: 2, bgcolor: 'rgba(46, 125, 50, 0.1)', borderRadius: 2, width: '100%', maxWidth: '90%' }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
                    {alerts.length} <DynamicTranslation text={t('aiAgent.recentAlert', 'Recent Alert')} />{alerts.length !== 1 ? <DynamicTranslation text={t('aiAgent.pluralS', 's')} /> : ''}
                  </Typography>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    <DynamicTranslation text={t('aiAgent.askAboutAlerts', 'You can ask me about your recent alerts and recommendations.')} />
                  </Typography>
                  <Box sx={{ mt: 1, maxHeight: '100px', overflow: 'auto' }}>
                    {alerts.slice(0, 3).map((alert, index) => (
                      <Typography key={index} variant="body2" sx={{ mt: 0.5, fontSize: '0.85rem' }}>
                        • {alert.title || alert.message || t('aiAgent.alertDefault', 'Alert') + ' ' + (index + 1)}
                      </Typography>
                    ))}
                    {alerts.length > 3 && (
                      <Typography variant="body2" sx={{ mt: 0.5, fontSize: '0.85rem', fontStyle: 'italic' }}>
                        + {alerts.length - 3} <DynamicTranslation text={t('aiAgent.moreAlerts', 'more alerts...')} />
                      </Typography>
                    )}
                  </Box>
                </Box>
              ) : null}
            </Box>
          </Box>
        ) : (
          <>
            {messages.map((message, index) => (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
                  mb: 2,
                  width: '100%'
                }}
              >
                <Paper
                  elevation={1}
                  sx={{
                    p: 1.5,
                    maxWidth: '85%',
                    bgcolor: message.role === 'user' ? 'primary.light' : 'background.default',
                    borderRadius: 2
                  }}
                >
                  {message.role === 'assistant' ? (
                    <ReactMarkdown
                      children={message.content}
                      remarkPlugins={[remarkGfm]}
                      components={{
                        p: ({node, ...props}) => <Typography variant="body1" {...props} sx={{ mb: 1 }} />,
                        h1: ({node, ...props}) => <Typography variant="h5" {...props} sx={{ mb: 1, mt: 1 }} />,
                        h2: ({node, ...props}) => <Typography variant="h6" {...props} sx={{ mb: 1, mt: 1 }} />,
                        h3: ({node, ...props}) => <Typography variant="subtitle1" {...props} sx={{ mb: 1, mt: 1 }} />,
                        ul: ({node, ...props}) => <Box component="ul" sx={{ pl: 2, mb: 1 }} {...props} />,
                        ol: ({node, ...props}) => <Box component="ol" sx={{ pl: 2, mb: 1 }} {...props} />,
                        li: ({node, ...props}) => <Typography component="li" variant="body1" {...props} sx={{ mb: 0.5 }} />,
                        a: ({node, ...props}) => <Typography component="a" variant="body1" {...props} sx={{ color: 'primary.main' }} />
                      }}
                    />
                  ) : (
                    <Typography variant="body1">{message.content}</Typography>
                  )}
                </Paper>
              </Box>
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </Box>

      <Box sx={{
        p: { xs: 2, sm: 3, md: 4 },
        borderTop: 1,
        borderColor: 'divider',
        width: '100%',
        position: 'sticky',
        bottom: 0,
        bgcolor: 'background.paper',
        zIndex: 10,
        minHeight: '80px',
        display: 'flex',
        alignItems: 'center'
      }}>
        <form onSubmit={handleSubmit}>
          <TextField
            fullWidth
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={t('aiAgent.inputPlaceholder', 'Ask me anything about your farm...')}
            disabled={isLoading}
            size="medium"
            multiline
            maxRows={3}
            sx={{
              width: '100%',
              '& .MuiOutlinedInput-root': {
                minHeight: '50px',
                fontSize: '1rem',
                padding: '12px 16px',
                borderRadius: '8px'
              },
              '& .MuiOutlinedInput-input': {
                padding: '8px 0'
              }
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    type="submit"
                    disabled={!input.trim() || isLoading}
                    size="medium"
                    sx={{
                      height: '45px',
                      width: '45px',
                      marginRight: '8px'
                    }}
                  >
                    {isLoading ? <CircularProgress size={24} /> : <SendIcon />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </form>
      </Box>
    </Box>
  );
};

export default AIAgent;
