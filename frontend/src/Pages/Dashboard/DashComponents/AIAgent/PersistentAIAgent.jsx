import React from 'react';
import { Box, Paper } from '@mui/material';
import AIAgent from './AIAgent';

const PersistentAIAgent = ({ weatherData, soilData, marketData, alerts, schedule, bulletinData, cropData }) => {
  return (
    <Paper
      elevation={3}
      sx={{
        p: 0,
        borderRadius: 2,
        height: '100%',
        minHeight: '550px',
        maxHeight: '650px',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}
    >
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        <AIAgent
          weatherData={weatherData}
          soilData={soilData}
          marketData={marketData}
          alerts={alerts}
          schedule={schedule}
          bulletinData={bulletinData}
          cropData={cropData}
          onRefresh={() => console.log('Refresh requested from AIAgent')}
        />
      </Box>
    </Paper>
  );
};

export default PersistentAIAgent;
