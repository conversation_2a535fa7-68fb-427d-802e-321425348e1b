import React from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  LinearProgress,
  Divider
} from '@mui/material';
import {
  TrendingUp,
  WaterDrop,
  Thermostat,
  Agriculture,
  Assessment,
  Timeline
} from '@mui/icons-material';

const FarmPerformance = () => {
  // Mock data - will be replaced with real data from API
  const farmData = {
    overallPerformance: {
      score: 85,
      trend: 'up',
      change: 5
    },
    metrics: [
      { label: 'Crop Yield', value: 92, target: 100, unit: '%' },
      { label: 'Water Efficiency', value: 78, target: 85, unit: '%' },
      { label: 'Soil Health', value: 88, target: 90, unit: '%' },
      { label: 'Resource Utilization', value: 82, target: 85, unit: '%' }
    ],
    recentAchievements: [
      { title: 'Optimal Water Usage', date: '2024-03-20', impact: 'high' },
      { title: 'Improved Soil Quality', date: '2024-03-18', impact: 'medium' },
      { title: 'Record Crop Yield', date: '2024-03-15', impact: 'high' }
    ]
  };

  const getImpactColor = (impact) => {
    switch (impact) {
      case 'high': return 'success.main';
      case 'medium': return 'warning.main';
      case 'low': return 'info.main';
      default: return 'text.secondary';
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Overall Performance */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Overall Performance
            </Typography>
            <Box display="flex" alignItems="center" gap={2} mb={3}>
              <Assessment sx={{ fontSize: 48, color: 'primary.main' }} />
              <Box>
                <Typography variant="h3">
                  {farmData.overallPerformance.score}
                </Typography>
                <Box display="flex" alignItems="center" gap={1}>
                  <TrendingUp sx={{ color: 'success.main' }} />
                  <Typography variant="body2" color="success.main">
                    +{farmData.overallPerformance.change}% from last month
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Key Metrics */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Key Performance Metrics
            </Typography>
            <Grid container spacing={2}>
              {farmData.metrics.map((metric) => (
                <Grid item xs={12} sm={6} key={metric.label}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box display="flex" justifyContent="space-between" mb={1}>
                        <Typography variant="subtitle2">
                          {metric.label}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {metric.value}{metric.unit}
                        </Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={(metric.value / metric.target) * 100} 
                        sx={{ 
                          height: 8, 
                          borderRadius: 4,
                          backgroundColor: 'rgba(0, 0, 0, 0.1)',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: metric.value >= metric.target ? '#2e7d32' : '#ff9800'
                          }
                        }}
                      />
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>

        {/* Recent Achievements */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Achievements
            </Typography>
            <Grid container spacing={2}>
              {farmData.recentAchievements.map((achievement) => (
                <Grid item xs={12} sm={4} key={achievement.title}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <Timeline sx={{ color: getImpactColor(achievement.impact) }} />
                        <Typography variant="subtitle1">
                          {achievement.title}
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {new Date(achievement.date).toLocaleDateString()}
                      </Typography>
                      <Box 
                        sx={{ 
                          mt: 1,
                          display: 'inline-block',
                          px: 1,
                          py: 0.5,
                          borderRadius: 1,
                          bgcolor: `${getImpactColor(achievement.impact)}15`,
                          color: getImpactColor(achievement.impact)
                        }}
                      >
                        <Typography variant="caption" sx={{ textTransform: 'capitalize' }}>
                          {achievement.impact} impact
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default FarmPerformance; 