import React from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Divider,
  LinearProgress
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  CurrencyRupee,
  Assessment,
  Timeline
} from '@mui/icons-material';

const MarketAnalysis = () => {
  // Mock data - will be replaced with real data from API
  const marketData = {
    currentPrices: {
      wheat: { price: 2500, change: 5, trend: 'up' },
      corn: { price: 1800, change: -2, trend: 'down' },
      soybeans: { price: 4500, change: 3, trend: 'up' }
    },
    marketTrends: {
      weekly: {
        wheat: 75,
        corn: 60,
        soybeans: 85
      },
      monthly: {
        wheat: 65,
        corn: 70,
        soybeans: 80
      }
    },
    predictions: [
      { crop: 'Wheat', prediction: 'Bullish', confidence: 75 },
      { crop: 'Corn', prediction: 'Bearish', confidence: 60 },
      { crop: 'Soybeans', prediction: 'Bullish', confidence: 85 }
    ]
  };

  const getTrendIcon = (trend) => {
    return trend === 'up' ? (
      <TrendingUp sx={{ color: 'success.main' }} />
    ) : (
      <TrendingDown sx={{ color: 'error.main' }} />
    );
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(price);
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Current Market Prices */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Current Market Prices
            </Typography>
            <Grid container spacing={2}>
              {Object.entries(marketData.currentPrices).map(([crop, data]) => (
                <Grid item xs={12} sm={4} key={crop}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <CurrencyRupee sx={{ color: 'primary.main' }} />
                        <Typography variant="subtitle1" sx={{ textTransform: 'capitalize' }}>
                          {crop}
                        </Typography>
                      </Box>
                      <Typography variant="h4" gutterBottom>
                        {formatPrice(data.price)}
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1}>
                        {getTrendIcon(data.trend)}
                        <Typography 
                          variant="body2" 
                          color={data.trend === 'up' ? 'success.main' : 'error.main'}
                        >
                          {data.change}% from last week
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>

        {/* Market Trends */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Weekly Market Trends
            </Typography>
            {Object.entries(marketData.marketTrends.weekly).map(([crop, value]) => (
              <Box key={crop} sx={{ mb: 2 }}>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                    {crop}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {value}%
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={value} 
                  sx={{ 
                    height: 8, 
                    borderRadius: 4,
                    backgroundColor: 'rgba(0, 0, 0, 0.1)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: '#2e7d32'
                    }
                  }}
                />
              </Box>
            ))}
          </Paper>
        </Grid>

        {/* Market Predictions */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Market Predictions
            </Typography>
            {marketData.predictions.map((prediction) => (
              <Box key={prediction.crop} sx={{ mb: 2 }}>
                <Card variant="outlined">
                  <CardContent>
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <Assessment sx={{ 
                        color: prediction.prediction === 'Bullish' ? 'success.main' : 'error.main' 
                      }} />
                      <Typography variant="subtitle1">
                        {prediction.crop}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {prediction.prediction} Outlook
                    </Typography>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Timeline sx={{ fontSize: 16 }} />
                      <Typography variant="body2">
                        {prediction.confidence}% Confidence
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Box>
            ))}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MarketAnalysis; 