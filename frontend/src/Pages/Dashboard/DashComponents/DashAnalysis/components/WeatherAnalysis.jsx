import React from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import {
  Thermostat,
  WaterDrop,
  Air,
  WbSunny,
  WbCloudy,
  Grain
} from '@mui/icons-material';

const WeatherAnalysis = () => {
  // Mock data - will be replaced with real data from API
  const weatherData = {
    current: {
      temperature: 25,
      humidity: 65,
      windSpeed: 12,
      description: 'Partly Cloudy',
      feelsLike: 27
    },
    forecast: [
      { date: '2024-03-21', temp: 26, humidity: 68, windSpeed: 10, description: 'Sunny' },
      { date: '2024-03-22', temp: 24, humidity: 72, windSpeed: 15, description: 'Cloudy' },
      { date: '2024-03-23', temp: 22, humidity: 75, windSpeed: 8, description: 'Rainy' }
    ],
    patterns: {
      rainfall: {
        weekly: 45,
        monthly: 180,
        trend: 'increasing'
      },
      temperature: {
        average: 24,
        max: 32,
        min: 18,
        trend: 'stable'
      }
    }
  };

  const getWeatherIcon = (description) => {
    const desc = description.toLowerCase();
    if (desc.includes('sunny')) return <WbSunny sx={{ fontSize: 32, color: '#ffa000' }} />;
    if (desc.includes('cloud')) return <WbCloudy sx={{ fontSize: 32, color: '#757575' }} />;
    if (desc.includes('rain')) return <Grain sx={{ fontSize: 32, color: '#2196f3' }} />;
    return <WbSunny sx={{ fontSize: 32, color: '#ffa000' }} />;
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Current Weather */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Current Weather
            </Typography>
            <Box display="flex" alignItems="center" gap={2} mb={3}>
              {getWeatherIcon(weatherData.current.description)}
              <Box>
                <Typography variant="h3">
                  {weatherData.current.temperature}°C
                </Typography>
                <Typography variant="subtitle1" color="text.secondary">
                  {weatherData.current.description}
                </Typography>
              </Box>
            </Box>
            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Box textAlign="center">
                  <WaterDrop sx={{ fontSize: 24, color: '#2196f3', mb: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    Humidity
                  </Typography>
                  <Typography variant="h6">
                    {weatherData.current.humidity}%
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box textAlign="center">
                  <Air sx={{ fontSize: 24, color: '#757575', mb: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    Wind
                  </Typography>
                  <Typography variant="h6">
                    {weatherData.current.windSpeed} m/s
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box textAlign="center">
                  <Thermostat sx={{ fontSize: 24, color: '#f44336', mb: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    Feels Like
                  </Typography>
                  <Typography variant="h6">
                    {weatherData.current.feelsLike}°C
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Weather Patterns
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Rainfall
                    </Typography>
                    <Typography variant="h4" gutterBottom>
                      {weatherData.patterns.rainfall.weekly}mm
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      This Week
                    </Typography>
                    <Typography variant="body2" color="success.main">
                      {weatherData.patterns.rainfall.trend} trend
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Temperature
                    </Typography>
                    <Typography variant="h4" gutterBottom>
                      {weatherData.patterns.temperature.average}°C
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Average
                    </Typography>
                    <Typography variant="body2" color="info.main">
                      {weatherData.patterns.temperature.trend} trend
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>

      {/* Forecast */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              3-Day Forecast
            </Typography>
            <Grid container spacing={2}>
              {weatherData.forecast.map((day, index) => (
                <Grid item xs={12} sm={4} key={index}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
                        <Typography variant="subtitle2" color="text.secondary">
                          {new Date(day.date).toLocaleDateString('en-US', {
                            weekday: 'short',
                            month: 'short',
                            day: 'numeric'
                          })}
                        </Typography>
                        {getWeatherIcon(day.description)}
                        <Typography variant="h5">
                          {day.temp}°C
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {day.description}
                        </Typography>
                        <Divider sx={{ width: '100%', my: 1 }} />
                        <Grid container spacing={1}>
                          <Grid item xs={4}>
                            <Typography variant="caption" display="block" color="text.secondary">
                              Humidity
                            </Typography>
                            <Typography variant="body2">{day.humidity}%</Typography>
                          </Grid>
                          <Grid item xs={4}>
                            <Typography variant="caption" display="block" color="text.secondary">
                              Wind
                            </Typography>
                            <Typography variant="body2">{day.windSpeed} m/s</Typography>
                          </Grid>
                        </Grid>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default WeatherAnalysis; 