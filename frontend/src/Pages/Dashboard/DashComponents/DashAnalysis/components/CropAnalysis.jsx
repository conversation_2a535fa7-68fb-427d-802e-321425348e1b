import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../../../../contexts/LanguageContext';
import {
  Box,
  Grid,
  Paper,
  Typography,
  LinearProgress,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Divider,
  FormControl,
  Select,
  MenuItem
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot
} from '@mui/lab';
import {
  LocalOffer,
  TrendingUp,
  Warning,
  CheckCircle,
  Info,
  Translate as TranslateIcon
} from '@mui/icons-material';
import translationService from '../../../../../utils/TranslationService';

const CropAnalysis = () => {
  // State for language translation
  // Use global language context instead of local state
  const { selectedLanguage, setSelectedLanguage } = useLanguage();
  const [translatedLabels, setTranslatedLabels] = useState({});
  const [translatedContent, setTranslatedContent] = useState({});
  
  // Mock data - will be replaced with real data from API
  const cropData = {
    name: 'Wheat',
    variety: 'Winter Wheat',
    plantedDate: '2024-02-15',
    expectedHarvest: '2024-06-15',
    health: 85,
    growth: 65,
    issues: [
      { type: 'warning', message: 'Slight nutrient deficiency detected', date: '2024-03-20' },
      { type: 'success', message: 'Pest control measures effective', date: '2024-03-18' }
    ],
    metrics: [
      { label: 'Soil pH', value: '6.5', status: 'good' },
      { label: 'Nitrogen Level', value: 'Medium', status: 'warning' },
      { label: 'Moisture', value: 'Optimal', status: 'good' }
    ]
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'good': return '#4caf50';
      case 'warning': return '#ff9800';
      case 'critical': return '#f44336';
      default: return '#757575';
    }
  };

  const getStatusIcon = (type) => {
    switch (type) {
      case 'warning': return <Warning color="warning" />;
      case 'success': return <CheckCircle color="success" />;
      case 'info': return <Info color="info" />;
      default: return <Info />;
    }
  };
  
  // Handle language change
  const handleLanguageChange = async (event) => {
    const newLanguage = event.target.value;
    setSelectedLanguage(newLanguage);
    
    if (newLanguage === 'en-IN') {
      // Reset translations when switching back to English
      setTranslatedLabels({});
      setTranslatedContent({});
      return;
    }
    
    try {
      // Static labels to translate
      const staticLabels = {
        // Section titles
        cropOverview: "Crop Overview",
        plantedDate: "Planted Date",
        expectedHarvest: "Expected Harvest",
        cropHealth: "Crop Health",
        overallHealth: "Overall Health",
        growthStage: "Growth Stage",
        currentProgress: "Current Progress",
        soilMetrics: "Soil Metrics",
        recentUpdates: "Recent Updates",
        
        // Metric labels
        soilpH: "Soil pH",
        nitrogenLevel: "Nitrogen Level",
        moisture: "Moisture",
        medium: "Medium",
        optimal: "Optimal"
      };
      
      // Content to translate
      const content = {
        variety: cropData.variety,
        slightDeficiency: "Slight nutrient deficiency detected",
        pestControl: "Pest control measures effective"
      };
      
      // Translate static labels
      const translatedLabelsResult = {};
      for (const [key, value] of Object.entries(staticLabels)) {
        translatedLabelsResult[key] = await translationService.translateText(value, newLanguage);
      }
      
      // Translate content
      const translatedContentResult = {};
      for (const [key, value] of Object.entries(content)) {
        translatedContentResult[key] = await translationService.translateText(value, newLanguage);
      }
      
      // Translate issue messages
      translatedContentResult.issues = [];
      for (const issue of cropData.issues) {
        const translatedMessage = await translationService.translateText(issue.message, newLanguage);
        translatedContentResult.issues.push({
          ...issue,
          translatedMessage
        });
      }
      
      // Translate metric labels
      translatedContentResult.metrics = [];
      for (const metric of cropData.metrics) {
        const translatedLabel = await translationService.translateText(metric.label, newLanguage);
        const translatedValue = await translationService.translateText(metric.value, newLanguage);
        translatedContentResult.metrics.push({
          ...metric,
          translatedLabel,
          translatedValue
        });
      }
      
      setTranslatedLabels(translatedLabelsResult);
      setTranslatedContent(translatedContentResult);
      console.log('Translation complete:', { translatedLabelsResult, translatedContentResult });
    } catch (error) {
      console.error('Translation error:', error);
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Language Selector */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" component="h1" sx={{ fontWeight: 'medium' }}>
          {translatedLabels.cropOverview || 'Crop Analysis'}
        </Typography>
        <FormControl size="small" sx={{ minWidth: 150 }}>
          <Select
            value={selectedLanguage}
            onChange={handleLanguageChange}
            displayEmpty
            startAdornment={<TranslateIcon fontSize="small" sx={{ mr: 1, color: 'action.active' }} />}
            sx={{ height: 40 }}
          >
            <MenuItem value="en-IN">English</MenuItem>
            <MenuItem value="hi-IN">हिंदी (Hindi)</MenuItem>
            <MenuItem value="bn-IN">বাংলা (Bengali)</MenuItem>
            <MenuItem value="te-IN">తెలుగు (Telugu)</MenuItem>
            <MenuItem value="ta-IN">தமிழ் (Tamil)</MenuItem>
            <MenuItem value="kn-IN">ಕನ್ನಡ (Kannada)</MenuItem>
            <MenuItem value="ml-IN">മലയാളം (Malayalam)</MenuItem>
            <MenuItem value="mr-IN">मराठी (Marathi)</MenuItem>
            <MenuItem value="gu-IN">ગુજરાતી (Gujarati)</MenuItem>
            <MenuItem value="pa-IN">ਪੰਜਾਬੀ (Punjabi)</MenuItem>
          </Select>
        </FormControl>
      </Box>
      
      {/* Crop Overview */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Box display="flex" alignItems="center" gap={2} mb={3}>
              <LocalOffer sx={{ fontSize: 32, color: 'primary.main' }} />
              <Box>
                <Typography variant="h6">{cropData.name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {translatedContent.variety || cropData.variety}
                </Typography>
              </Box>
            </Box>
            
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">
                  {translatedLabels.plantedDate || 'Planted Date'}
                </Typography>
                <Typography variant="body1">
                  {new Date(cropData.plantedDate).toLocaleDateString(selectedLanguage !== 'en-IN' ? selectedLanguage.split('-')[0] : undefined)}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">
                  {translatedLabels.expectedHarvest || 'Expected Harvest'}
                </Typography>
                <Typography variant="body1">
                  {new Date(cropData.expectedHarvest).toLocaleDateString(selectedLanguage !== 'en-IN' ? selectedLanguage.split('-')[0] : undefined)}
                </Typography>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              {translatedLabels.cropHealth || 'Crop Health'}
            </Typography>
            <Box sx={{ mb: 3 }}>
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">{translatedLabels.overallHealth || 'Overall Health'}</Typography>
                <Typography variant="body2">{cropData.health}%</Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={cropData.health} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: '#4caf50'
                  }
                }}
              />
            </Box>

            <Typography variant="h6" gutterBottom>
              {translatedLabels.growthStage || 'Growth Stage'}
            </Typography>
            <Box>
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">{translatedLabels.currentProgress || 'Current Progress'}</Typography>
                <Typography variant="body2">{cropData.growth}%</Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={cropData.growth} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: '#2196f3'
                  }
                }}
              />
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Soil Metrics */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {translatedLabels.soilMetrics || 'Soil Metrics'}
            </Typography>
            <Grid container spacing={2}>
              {cropData.metrics.map((metric, index) => {
                // Get translated metric data if available
                const translatedMetric = translatedContent.metrics && translatedContent.metrics[index];
                
                return (
                  <Grid item xs={12} sm={4} key={index}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box display="flex" alignItems="center" gap={1}>
                          <Box
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              backgroundColor: getStatusColor(metric.status)
                            }}
                          />
                          <Typography variant="subtitle2">
                            {translatedMetric ? translatedMetric.translatedLabel : metric.label}
                          </Typography>
                        </Box>
                        <Typography variant="h6" sx={{ mt: 1 }}>
                          {translatedMetric ? translatedMetric.translatedValue : metric.value}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                );
              })}
            </Grid>
          </Paper>
        </Grid>
      </Grid>

      {/* Timeline */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {translatedLabels.recentUpdates || 'Recent Updates'}
            </Typography>
            <Timeline>
              {cropData.issues.map((issue, index) => {
                // Get translated issue data if available
                const translatedIssue = translatedContent.issues && translatedContent.issues[index];
                
                return (
                  <TimelineItem key={index}>
                    <TimelineSeparator>
                      <TimelineDot color={issue.type === 'warning' ? 'warning' : 'success'}>
                        {getStatusIcon(issue.type)}
                      </TimelineDot>
                      {index < cropData.issues.length - 1 && <TimelineConnector />}
                    </TimelineSeparator>
                    <TimelineContent>
                      <Typography variant="body2" color="text.secondary">
                        {new Date(issue.date).toLocaleDateString(selectedLanguage !== 'en-IN' ? selectedLanguage.split('-')[0] : undefined)}
                      </Typography>
                      <Typography variant="body1">
                        {translatedIssue ? translatedIssue.translatedMessage : issue.message}
                      </Typography>
                    </TimelineContent>
                  </TimelineItem>
                );
              })}
            </Timeline>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CropAnalysis; 