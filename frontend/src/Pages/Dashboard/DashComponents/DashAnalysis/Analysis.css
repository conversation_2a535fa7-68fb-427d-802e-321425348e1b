.analysis-container {
  height: 100%;
  overflow: auto;
  padding: 24px;
}

.analysis-header {
  background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
  color: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.analysis-tabs {
  background: white;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.analysis-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  min-height: 400px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Custom scrollbar styles */
.analysis-container::-webkit-scrollbar {
  width: 8px;
}

.analysis-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.analysis-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.analysis-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Tab styles */
.MuiTab-root {
  min-height: 64px !important;
  text-transform: none !important;
  font-size: 1rem !important;
  font-weight: 500 !important;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .analysis-container {
    padding: 16px;
  }
  
  .analysis-header {
    padding: 16px;
  }
  
  .analysis-content {
    padding: 16px;
  }
} 