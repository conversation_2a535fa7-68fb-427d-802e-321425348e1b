import React, { useState } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Tabs,
  Tab,
  CircularProgress,
  useTheme
} from '@mui/material';
import {
  TrendingUp,
  WbSunny,
  Agriculture,
  Assessment
} from '@mui/icons-material';
import CropAnalysis from './components/CropAnalysis';
import WeatherAnalysis from './components/WeatherAnalysis';
import MarketAnalysis from './components/MarketAnalysis';
import FarmPerformance from './components/FarmPerformance';

const Analysis = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const theme = useTheme();

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const tabs = [
    { label: 'Crop Analysis', icon: <Agriculture />, component: <CropAnalysis /> },
    { label: 'Weather Analysis', icon: <WbSunny />, component: <WeatherAnalysis /> },
    { label: 'Market Analysis', icon: <TrendingUp />, component: <MarketAnalysis /> },
    { label: 'Farm Performance', icon: <Assessment />, component: <FarmPerformance /> }
  ];

  return (
    <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
      <Paper 
        elevation={0} 
        sx={{ 
          p: 3, 
          mb: 3,
          background: 'linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%)',
          color: 'white'
        }}
      >
        <Typography variant="h4" gutterBottom>
          Farm Analysis Dashboard
        </Typography>
        <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
          Comprehensive insights and analytics for your farm
        </Typography>
      </Paper>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={selectedTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              minHeight: 64,
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 500
            }
          }}
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              icon={tab.icon}
              iconPosition="start"
              value={index}
            />
          ))}
        </Tabs>
      </Paper>

      <Paper sx={{ p: 2, minHeight: 400 }}>
        {tabs[selectedTab].component}
      </Paper>
    </Box>
  );
};

export default Analysis; 