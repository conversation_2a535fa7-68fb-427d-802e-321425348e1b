.section-crop-details {
  margin-top: 64px;
}

.section-crop-details h1 {
  font-size: 64px;
  text-align: center;
  color: #087f5b;
}

.section-crop-details h1 span {
  display: inline-block;
  color: #1864ab;
  animation: jumping 1s cubic-bezier(0.5, 0.05, 0.5, 1) infinite;
}

@keyframes jumping {
  0% {
    transform: translateY(0);
  }
  20% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-20px);
  }
  50% {
    transform: translateY(-30px);
  }
  70% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0);
  }
}

.crop-detail-card-container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  margin-top: 32px;
  row-gap: 64px;
  column-gap: 32px;
}

.crop-detail-card {
  background-color: #dbe4ff;
  border-radius: 16px;
  height: 50vh;
}

.crop-detail-card h2 {
  text-align: center;
  padding: 16px;
  font-size: 24px;
  font-weight: 600;
  color: #fafafa;
  border-radius: 16px 16px 0px 0px;
  background: linear-gradient(45deg, #006400, #00b500, #00b500, #006400);
  background-size: 400% 400%; /* Increase the size for smooth animation */
  animation: gradientAnimation 5s ease infinite; /* Apply the animation */
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 50% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  75% {
    background-position: 50% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.crop-detail-value,
.crop-detail-sensor {
  font-size: 32px;
  font-weight: 600;
  padding: 8px 32px;
}

.crop-detail-desc {
  font-size: 24px;
  margin-top: 16px;
  padding: 8px 32px;
}
