import React from 'react';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Tooltip,
  Divider
} from '@mui/material';
import {
  Warning as WarningIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon,
  Cloud as CloudIcon,
  AccessTime as AccessTimeIcon
} from '@mui/icons-material';
import { useTranslation } from '../../../../hooks/useTranslation';
import DynamicTranslation from '../../../../components/DynamicTranslation/DynamicTranslation';
import { formatDate } from '../../../../utils/translationUtils';

const alertStyles = {
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: 2,
    height: '100%'
  },
  alertList: {
    display: 'flex',
    flexDirection: 'column',
    gap: 1.5,
    overflowY: 'auto',
    maxHeight: '400px',
    '&::-webkit-scrollbar': {
      width: '6px'
    },
    '&::-webkit-scrollbar-track': {
      background: 'rgba(46, 125, 50, 0.1)',
      borderRadius: '3px'
    },
    '&::-webkit-scrollbar-thumb': {
      background: 'rgba(46, 125, 50, 0.3)',
      borderRadius: '3px',
      '&:hover': {
        background: 'rgba(46, 125, 50, 0.5)'
      }
    }
  },
  alertItem: {
    display: 'flex',
    alignItems: 'flex-start',
    gap: 2,
    p: 2,
    background: 'rgba(255, 255, 255, 0.8)',
    borderRadius: '12px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
    transition: 'all 0.3s ease',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
    }
  },
  alertIcon: {
    width: '40px',
    height: '40px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    background: 'rgba(46, 125, 50, 0.1)',
    '& svg': {
      fontSize: '24px',
      color: '#2e7d32'
    }
  },
  alertContent: {
    flex: 1
  },
  alertTitle: {
    fontSize: '1rem',
    fontWeight: 600,
    color: '#1b5e20',
    mb: 0.5
  },
  alertMessage: {
    fontSize: '0.875rem',
    color: '#666',
    mb: 1
  },
  alertMeta: {
    display: 'flex',
    alignItems: 'center',
    gap: 1,
    fontSize: '0.75rem',
    color: '#999'
  },
  severityChip: {
    height: '24px',
    '& .MuiChip-label': {
      px: 1,
      fontSize: '0.75rem'
    }
  }
};

const RecentAlerts = ({ alerts }) => {
  console.log('RecentAlerts received data:', alerts);
  const { t } = useTranslation();

  // Handle different possible data structures
  const alertsArray = Array.isArray(alerts) ? alerts : alerts?.alerts || [];
  const lastUpdated = alerts?.lastUpdated || new Date().toISOString();

  if (!alertsArray.length) {
    return (
      <Paper sx={{ p: 2, height: '100%' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6"><DynamicTranslation text={t('recentAlerts', 'Recent Alerts')} /></Typography>
          <Tooltip title="System alerts and notifications">
            <InfoIcon fontSize="small" sx={{ color: 'text.secondary' }} />
          </Tooltip>
        </Box>
        <Typography variant="body2" color="text.secondary" align="center">
          <DynamicTranslation text={t('recentAlerts.noAlerts', 'No alerts to display')} />
        </Typography>
      </Paper>
    );
  }

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'error':
        return '#d32f2f';
      case 'warning':
        return '#ed6c02';
      case 'info':
        return '#1976d2';
      default:
        return '#2e7d32';
    }
  };

  const getAlertIcon = (severity) => {
    switch (severity) {
      case 'error':
        return <ErrorIcon />;
      case 'warning':
        return <WarningIcon />;
      case 'info':
        return <InfoIcon />;
      default:
        return <SuccessIcon />;
    }
  };

  // Sort alerts by timestamp, most recent first
  const sortedAlerts = [...alertsArray].sort((a, b) => 
    new Date(b.timestamp) - new Date(a.timestamp)
  );

  return (
    <Box sx={alertStyles.container}>
      <Box sx={alertStyles.alertList}>
        {sortedAlerts.map((alert, index) => (
          <Box key={alert.id || alert.timestamp} sx={alertStyles.alertItem}>
            {index > 0 && <Divider sx={{ my: 1 }} />}
            <Box sx={{ ...alertStyles.alertIcon, background: `rgba(${getSeverityColor(alert.severity)}, 0.1)` }}>
              {getAlertIcon(alert.severity)}
            </Box>
            <Box sx={alertStyles.alertContent}>
              <Typography sx={alertStyles.alertTitle}>
                <DynamicTranslation text={alert.title || t('recentAlerts.title', 'Alert')} />
              </Typography>
              <Typography sx={alertStyles.alertMessage}>
                <DynamicTranslation text={alert.message || 'No message available'} />
              </Typography>
              <Box sx={alertStyles.alertMeta}>
                <AccessTimeIcon sx={{ fontSize: '1rem' }} />
                <Typography variant="caption">
                  {formatDate(alert.timestamp, t, { includeTime: true })}
                </Typography>
                <Chip
                  label={<DynamicTranslation text={t(`recentAlerts.severity.${alert.severity?.toLowerCase()}`, alert.severity?.toUpperCase() || 'INFO')} />}
                  size="small"
                  sx={{
                    ...alertStyles.severityChip,
                    background: `rgba(${getSeverityColor(alert.severity)}, 0.1)`,
                    color: getSeverityColor(alert.severity)
                  }}
                />
              </Box>
            </Box>
          </Box>
        ))}
      </Box>

      <Typography variant="caption" sx={{ display: 'block', mt: 2, textAlign: 'right', color: 'text.secondary' }}>
        <DynamicTranslation text={t('recentAlerts.lastUpdated', 'Last updated')} />: {formatDate(lastUpdated, t, { includeTime: true })}
      </Typography>
    </Box>
  );
};

export default RecentAlerts; 