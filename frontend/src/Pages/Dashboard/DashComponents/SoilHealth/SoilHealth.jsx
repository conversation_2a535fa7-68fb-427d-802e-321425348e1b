import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  LinearProgress,
  Tooltip
} from '@mui/material';
import { Info as InfoIcon } from '@mui/icons-material';
import { WaterDrop as WaterDropIcon, Science as ScienceIcon, LocalFlorist as LocalFloristIcon, Thermostat as ThermostatIcon } from '@mui/icons-material';
import { CheckCircle as CheckCircleIcon } from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, ResponsiveContainer } from 'recharts';
import { useTranslation } from '../../../../hooks/useTranslation';
import DynamicTranslation from '../../../../components/DynamicTranslation/DynamicTranslation';
import { translateNumber, formatValueWithUnit } from '../../../../utils/translationUtils';

const soilHealthStyles = {
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: 2,
    height: '100%'
  },
  metricsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(2, 1fr)',
    gap: 2,
    mb: 2
  },
  metricCard: {
    background: 'rgba(255, 255, 255, 0.8)',
    borderRadius: '12px',
    p: 2,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 1,
    boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
    transition: 'all 0.3s ease',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
    }
  },
  metricIcon: {
    width: '40px',
    height: '40px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    background: 'rgba(46, 125, 50, 0.1)',
    '& svg': {
      fontSize: '24px',
      color: '#2e7d32'
    }
  },
  metricValue: {
    fontSize: '1.2rem',
    fontWeight: 600,
    color: '#1b5e20',
    textAlign: 'center'
  },
  metricLabel: {
    fontSize: '0.875rem',
    color: '#666',
    textAlign: 'center'
  },
  chartContainer: {
    background: 'rgba(255, 255, 255, 0.8)',
    borderRadius: '12px',
    p: 2,
    mt: 2,
    mb: 2,
    height: '280px',  // Increased height to use more vertical space
    boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
  },
  chartTitle: {
    fontSize: '1rem',
    fontWeight: 600,
    color: '#1b5e20',
    mb: 1
  },
  recommendations: {
    background: 'rgba(46, 125, 50, 0.1)',
    borderRadius: '12px',
    p: 2,
    mt: 2
  },
  recommendationTitle: {
    fontSize: '1rem',
    fontWeight: 600,
    color: '#1b5e20',
    mb: 1
  },
  recommendationList: {
    listStyle: 'none',
    padding: 0,
    margin: 0
  },
  recommendationItem: {
    display: 'flex',
    alignItems: 'flex-start',
    gap: 1,
    mb: 1,
    '&:last-child': {
      mb: 0
    }
  },
  recommendationIcon: {
    color: '#2e7d32',
    fontSize: '1rem',
    mt: 0.25
  },
  recommendationText: {
    fontSize: '0.875rem',
    color: '#666',
    flex: 1
  }
};

// SoilHealthChart component for displaying weekly trends
const SoilHealthChart = ({ historyData }) => {
  const { t } = useTranslation();
  // If no history data is provided, generate sample data
  const chartData = historyData && historyData.length > 0 ? historyData : [
    { date: '2024-03-29', ph: 6.7, moisture: 32, nitrogen: 275, phosphorus: 43, potassium: 185 },
    { date: '2024-03-30', ph: 6.8, moisture: 34, nitrogen: 278, phosphorus: 44, potassium: 187 },
    { date: '2024-03-31', ph: 6.9, moisture: 36, nitrogen: 280, phosphorus: 45, potassium: 190 },
    { date: '2024-04-01', ph: 6.8, moisture: 35, nitrogen: 282, phosphorus: 46, potassium: 192 },
    { date: '2024-04-02', ph: 6.7, moisture: 33, nitrogen: 279, phosphorus: 45, potassium: 189 },
    { date: '2024-04-03', ph: 6.8, moisture: 34, nitrogen: 281, phosphorus: 46, potassium: 191 },
    { date: '2024-04-04', ph: 6.9, moisture: 35, nitrogen: 283, phosphorus: 47, potassium: 193 }
  ];

  // Format the date for display
  const formattedData = chartData.map(item => ({
    ...item,
    date: new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  }));

  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart
        data={formattedData}
        margin={{ top: 10, right: 30, left: 0, bottom: 10 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.1)" />
        <XAxis dataKey="date" tick={{ fontSize: 10 }} />
        <YAxis yAxisId="left" orientation="left" tick={{ fontSize: 10 }} />
        <YAxis yAxisId="right" orientation="right" tick={{ fontSize: 10 }} />
        <RechartsTooltip
          contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', borderRadius: '8px', border: 'none', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}
          labelStyle={{ fontWeight: 'bold', marginBottom: '5px' }}
        />
        <Legend
          wrapperStyle={{ fontSize: '10px', paddingTop: '10px' }}
          layout="horizontal"
          verticalAlign="bottom"
          align="center"
          iconSize={10}
          iconType="circle"
        />
        <Line yAxisId="left" type="monotone" dataKey="ph" stroke="#8884d8" activeDot={{ r: 6 }} name={t('soilHealth.phLevel', 'pH')} strokeWidth={2} />
        <Line yAxisId="left" type="monotone" dataKey="moisture" stroke="#2e7d32" activeDot={{ r: 6 }} name={t('soilHealth.moisture', 'Moisture %')} strokeWidth={2} />
        <Line yAxisId="right" type="monotone" dataKey="nitrogen" stroke="#82ca9d" activeDot={{ r: 6 }} name={t('soilHealth.nitrogen', 'Nitrogen')} strokeWidth={1.5} dot={false} />
        <Line yAxisId="right" type="monotone" dataKey="phosphorus" stroke="#ffc658" activeDot={{ r: 6 }} name={t('soilHealth.phosphorus', 'Phosphorus')} strokeWidth={1.5} dot={false} />
        <Line yAxisId="right" type="monotone" dataKey="potassium" stroke="#ff7300" activeDot={{ r: 6 }} name={t('soilHealth.potassium', 'Potassium')} strokeWidth={1.5} dot={false} />
      </LineChart>
    </ResponsiveContainer>
  );
};

const SoilHealth = ({ data, isLoading }) => {
  const { t, translateDynamic } = useTranslation();
  
  if (isLoading) {
    return (
      <Box sx={{ height: '400px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Typography><DynamicTranslation text={t('loading', 'Loading soil health data...')} /></Typography>
      </Box>
    );
  }
  
  if (!data) {
    return (
      <Box sx={{ height: '400px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Typography><DynamicTranslation text={t('soilHealth.noData', 'No soil data available')} /></Typography>
      </Box>
    );
  }

  // Extract values from the data structure
  const moisture = data.currentReadings?.moisture;
  const ph = data.currentReadings?.pH;
  const nitrogen = data.currentReadings?.nitrogen;
  const phosphorus = data.currentReadings?.phosphorus;
  const potassium = data.currentReadings?.potassium;
  const organicMatter = data.currentReadings?.organic_matter;
  const temperature = 28; // Assuming a default value
  const history = data.history;
  const lastUpdated = new Date().toLocaleDateString();

  const getStatusColor = (value, min, max) => {
    if (value < min) return 'error';
    if (value > max) return 'warning';
    return 'success';
  };

  const getStatusText = (value, min, max) => {
    if (value < min) return t('soilHealth.status.low', 'Low');
    if (value > max) return t('soilHealth.status.high', 'High');
    return t('soilHealth.status.optimal', 'Optimal');
  };

  const metrics = [
    {
      label: t('soilHealth.moisture', 'Moisture'),
      value: moisture,
      icon: <WaterDropIcon />,
      unit: '%'
    },
    {
      label: t('soilHealth.phLevel', 'pH Level'),
      value: ph,
      icon: <ScienceIcon />,
      unit: ''
    },
    {
      label: t('soilHealth.organicMatter', 'Organic Matter'),
      value: organicMatter || '--',
      icon: <LocalFloristIcon />,
      unit: '%'
    },
    {
      label: t('soilHealth.temperature', 'Temperature'),
      value: temperature,
      icon: <ThermostatIcon />,
      unit: '°C'
    },
    {
      label: t('soilHealth.nitrogen', 'Nitrogen (N)'),
      value: nitrogen,
      icon: <ScienceIcon />,
      unit: 'mg/kg'
    },
    {
      label: t('soilHealth.phosphorus', 'Phosphorus (P)'),
      value: phosphorus,
      icon: <ScienceIcon />,
      unit: 'mg/kg'
    },
    {
      label: t('soilHealth.potassium', 'Potassium (K)'),
      value: potassium,
      icon: <ScienceIcon />,
      unit: 'mg/kg'
    },
    {
      label: t('soilHealth.ec', 'EC'),
      value: data?.ec || '--',
      icon: <ScienceIcon />,
      unit: 'mS/cm'
    }
  ];

  const recommendations = [
    t('soilHealth.recommendations.ph', 'Soil pH is slightly acidic. Consider adding lime to raise pH.'),
    t('soilHealth.recommendations.organic', 'Organic matter content is low. Add compost or organic fertilizers.'),
    t('soilHealth.recommendations.nitrogen', 'Nitrogen levels are below optimal. Apply nitrogen-rich fertilizer.'),
    t('soilHealth.recommendations.moisture', 'Maintain consistent moisture levels between 40-60%.')
  ];

  return (
    <Box sx={soilHealthStyles.container}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" sx={{ color: 'white', flexGrow: 1 }}>
          <DynamicTranslation text={t('soilHealth', 'Soil Health')} />
        </Typography>
        <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
          <DynamicTranslation text={t('lastUpdated', 'Last updated')}/> {new Date(lastUpdated).toLocaleString()}
        </Typography>
      </Box>

      <Grid container spacing={2}>
        {metrics.map((metric, index) => (
          <Grid item xs={6} key={index}>
            <Box sx={soilHealthStyles.metricCard}>
              <Box sx={soilHealthStyles.metricIcon}>
                {metric.icon}
              </Box>
              <Typography sx={soilHealthStyles.metricValue}>
                {metric.value !== '--' 
                  ? formatValueWithUnit(metric.value, metric.unit, t)
                  : '--'}
              </Typography>
              <Typography sx={soilHealthStyles.metricLabel}>
                <DynamicTranslation text={metric.label} />
              </Typography>
            </Box>
          </Grid>
        ))}
      </Grid>

      {/* Weekly Soil Health Trend Chart */}
      <Box sx={soilHealthStyles.chartContainer}>
        <Typography sx={soilHealthStyles.chartTitle}>
          <DynamicTranslation text={t('soilHealth.weeklyTrends', 'Weekly Soil Health Trends')} />
        </Typography>
        <SoilHealthChart historyData={history} />
      </Box>

      <Box sx={soilHealthStyles.recommendations}>
        <Typography sx={soilHealthStyles.recommendationTitle}>
          <DynamicTranslation text={t('soilHealth.recommendations', 'Recommendations')} />
        </Typography>
        <Box component="ul" sx={soilHealthStyles.recommendationList}>
          {recommendations.map((rec, index) => (
            <Box component="li" key={index} sx={soilHealthStyles.recommendationItem}>
              <CheckCircleIcon sx={soilHealthStyles.recommendationIcon} />
              <Typography sx={soilHealthStyles.recommendationText}>
                <DynamicTranslation text={rec} />
              </Typography>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default SoilHealth;