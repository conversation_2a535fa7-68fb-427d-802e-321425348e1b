// Weather.jsx — Updated to use current user's ID

import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../../../../contexts/AuthContext';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  CircularProgress,
  Button,
  Paper,
  Divider,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Thermostat,
  WaterDrop,
  Air,
  WbSunny,
  Cloud,
  WbCloudy,
  Grain
} from '@mui/icons-material';

const Weather = () => {
  const { currentUser } = useAuth();
  const [currentWeather, setCurrentWeather] = useState(null);
  const [forecastData, setForecastData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const fetchWeatherData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch current weather
      const response = await axios.get(`${import.meta.env.VITE_API_URL}/v1/weather/current?farmerId=${currentUser.id}`);
      if (!response.data.success) {
        throw new Error(response.data.message);
      }
      setCurrentWeather(response.data.data);

      // Fetch forecast
      const forecastResponse = await axios.get(`${import.meta.env.VITE_API_URL}/v1/weather/forecast?farmerId=${currentUser.id}`);
      if (!forecastResponse.data.success) {
        throw new Error(forecastResponse.data.message);
      }
      setForecastData(forecastResponse.data.data);
    } catch (err) {
      console.error('Error fetching weather data:', err);
      setError(err.message || 'Failed to fetch weather data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentUser?.id) {
      fetchWeatherData();
    }
  }, [currentUser?.id]);

  const getWeatherIcon = (description) => {
    const desc = description.toLowerCase();
    if (desc.includes('clear')) return <WbSunny />;
    if (desc.includes('cloud')) return <WbCloudy />;
    if (desc.includes('rain')) return <Grain />;
    return <Cloud />;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box display="flex" flexDirection="column" alignItems="center" gap={2} p={3}>
        <Typography color="error">{error}</Typography>
        <Button variant="contained" onClick={fetchWeatherData}>
          Retry
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ 
      p: 3, 
      height: '100%', 
      width: '100%',
      overflow: 'auto',
      '&::-webkit-scrollbar': {
        width: '8px',
      },
      '&::-webkit-scrollbar-track': {
        background: 'rgba(0, 0, 0, 0.1)',
        borderRadius: '4px',
      },
      '&::-webkit-scrollbar-thumb': {
        background: 'rgba(0, 0, 0, 0.2)',
        borderRadius: '4px',
        '&:hover': {
          background: 'rgba(0, 0, 0, 0.3)',
        },
      },
    }}>
      <Grid container spacing={4} sx={{ width: '100%', maxWidth: '100%', margin: 0 }}>
        {/* Current Weather */}
        <Grid item xs={12}>
          <Card sx={{ 
            background: 'linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%)',
            color: 'white',
            borderRadius: 2,
            mb: 3,
            width: '100%'
          }}>
            <CardContent sx={{ p: 3 }}>
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Current Weather
                  </Typography>
                  {currentWeather && (
                    <>
                      <Box display="flex" alignItems="center" gap={2}>
                        <Thermostat sx={{ fontSize: 32 }} />
                        <Typography variant="h3" sx={{ fontSize: { xs: '2rem', md: '2.5rem' } }}>
                          {Math.round(currentWeather.temperature)}°C
                        </Typography>
                      </Box>
                      <Typography variant="h6" sx={{ mt: 1 }}>
                        {currentWeather.description}
                      </Typography>
                    </>
                  )}
                </Grid>
                <Grid item xs={12} md={6}>
                  {currentWeather && (
                    <Grid container spacing={2}>
                      <Grid item xs={4}>
                        <Box textAlign="center" sx={{ p: 1.5, bgcolor: 'rgba(255, 255, 255, 0.1)', borderRadius: 1 }}>
                          <WaterDrop sx={{ fontSize: 24, mb: 0.5 }} />
                          <Typography variant="body2">Humidity</Typography>
                          <Typography variant="h6">{currentWeather.humidity}%</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={4}>
                        <Box textAlign="center" sx={{ p: 1.5, bgcolor: 'rgba(255, 255, 255, 0.1)', borderRadius: 1 }}>
                          <Air sx={{ fontSize: 24, mb: 0.5 }} />
                          <Typography variant="body2">Wind</Typography>
                          <Typography variant="h6">{currentWeather.windSpeed} m/s</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={4}>
                        <Box textAlign="center" sx={{ p: 1.5, bgcolor: 'rgba(255, 255, 255, 0.1)', borderRadius: 1 }}>
                          <Thermostat sx={{ fontSize: 24, mb: 0.5 }} />
                          <Typography variant="body2">Feels</Typography>
                          <Typography variant="h6">{Math.round(currentWeather.feelsLike)}°C</Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  )}
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Forecast */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
            14-Day Forecast
          </Typography>
          <Box 
            sx={{ 
              width: '100%',
              overflowX: 'auto',
              pb: 1,
              '&::-webkit-scrollbar': {
                height: '6px',
              },
              '&::-webkit-scrollbar-track': {
                background: 'rgba(0, 0, 0, 0.1)',
                borderRadius: '3px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: 'rgba(0, 0, 0, 0.2)',
                borderRadius: '3px',
                '&:hover': {
                  background: 'rgba(0, 0, 0, 0.3)',
                },
              },
            }}
          >
            <Box sx={{ display: 'flex', gap: 2, minWidth: 'min-content', px: 1 }}>
              {forecastData.map((day, index) => (
                <Paper
                  key={index}
                  elevation={1}
                  sx={{
                    width: '200px',
                    flexShrink: 0,
                    p: 2,
                    background: 'rgba(46, 125, 50, 0.05)',
                    border: '1px solid rgba(46, 125, 50, 0.1)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      background: 'rgba(46, 125, 50, 0.1)',
                      boxShadow: 2
                    }
                  }}
                >
                  <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
                    <Typography variant="subtitle2" color="primary">
                      {new Date(day.date).toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </Typography>
                    <Box sx={{ fontSize: 32, color: 'primary.main' }}>
                      {getWeatherIcon(day.description)}
                    </Box>
                    <Typography variant="h5" sx={{ mb: 0.5 }}>
                      {Math.round(day.temp)}°C
                    </Typography>
                    <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 1 }}>
                      {day.description}
                    </Typography>
                    <Grid container spacing={1} sx={{ width: '100%' }}>
                      <Grid item xs={4}>
                        <Box textAlign="center">
                          <WaterDrop sx={{ fontSize: 16, mb: 0.5, color: 'primary.main' }} />
                          <Typography variant="caption" display="block" color="text.secondary">
                            Humidity
                          </Typography>
                          <Typography variant="body2">{day.humidity}%</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={4}>
                        <Box textAlign="center">
                          <Air sx={{ fontSize: 16, mb: 0.5, color: 'primary.main' }} />
                          <Typography variant="caption" display="block" color="text.secondary">
                            Wind
                          </Typography>
                          <Typography variant="body2">{day.windSpeed} m/s</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={4}>
                        <Box textAlign="center">
                          <Thermostat sx={{ fontSize: 16, mb: 0.5, color: 'primary.main' }} />
                          <Typography variant="caption" display="block" color="text.secondary">
                            Feels
                          </Typography>
                          <Typography variant="body2">{Math.round(day.feelsLike)}°C</Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </Paper>
              ))}
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Weather;

