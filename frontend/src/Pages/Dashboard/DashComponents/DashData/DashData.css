.weather-container {
  width: 100%;
  height: 100%;
  padding: 1.5rem;
  background: linear-gradient(135deg, #0d47a1, #1976d2);
  border-radius: 1rem;
  color: white;
  overflow-y: auto;
}

/* Loading and Error States */
.weather-loading,
.weather-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
}

.weather-error button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.15);
  border: none;
  color: white;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: 0.3s;
}

.weather-error button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Current Weather Section */
.current-weather-card {
  background: linear-gradient(135deg, #2e7d32, #1b5e20);
  border-radius: 1rem;
  color: white;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  max-width: 600px;
  margin: 0 auto;
}

/* Temperature Display */
.weather-main {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 2.5rem;
  font-weight: 600;
}

/* Description */
.weather-description {
  font-size: 1.1rem;
  margin-top: 0.5rem;
  color: #e3f2fd;
  text-transform: capitalize;
}

/* Detail Grid */
.weather-details {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
}

.detail-box {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.75rem;
  border-radius: 0.75rem;
}

.detail-box:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Forecast Section */
.forecast-wrapper {
  margin-top: 2rem;
}

.forecast-title {
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

.forecast-scroll {
  display: flex;
  overflow-x: auto;
  gap: 1rem;
  padding-bottom: 0.5rem;
}

.forecast-scroll::-webkit-scrollbar {
  height: 6px;
}

.forecast-scroll::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

/* Forecast Card */
.forecast-card {
  background: rgba(255, 255, 255, 0.05);
  padding: 1rem;
  border-radius: 0.75rem;
  min-width: 160px;
  transition: all 0.3s ease;
}

.forecast-card:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.forecast-date {
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  color: #bbdefb;
}

.forecast-icon {
  font-size: 1.5rem;
  text-align: center;
}

.forecast-temp {
  font-size: 1.4rem;
  font-weight: 600;
  color: #fff;
  text-align: center;
}

.forecast-description {
  font-size: 0.85rem;
  text-align: center;
  color: #e3f2fd;
  margin-bottom: 0.5rem;
}

/* Mini-details */
.forecast-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #e3f2fd;
}

