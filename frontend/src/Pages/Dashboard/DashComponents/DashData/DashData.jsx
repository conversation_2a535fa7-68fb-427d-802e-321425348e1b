import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Typography,
  CircularProgress,
  IconButton,
  Tooltip,
  Alert,
  Snackbar,
  Container,
  Paper,
  useTheme,
  Divider,
  Button
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Dashboard as DashboardIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CalendarToday as CalendarIcon,
  SmartToy as SmartToyIcon,
  WbSunny as WbSunnyIcon,
  Grass as GrassIcon,
  CalendarToday as CalendarTodayIcon,
  AccountBalance as AccountBalanceIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';
import { fetchWithRetry, clearCache } from '../../../../utils/api';
import FallbackComponent from '../../../../components/FallbackComponent';
import Weather from '../Weather/Weather';
import SoilHealth from '../SoilHealth/SoilHealth';
import WeeklySchedule from '../WeeklySchedule/WeeklySchedule';
import MarketAnalysis from '../MarketAnalysis/MarketAnalysis';
import RecentAlerts from '../RecentAlerts/RecentAlerts';
import PersistentAIAgent from '../AIAgent/PersistentAIAgent';
import WeatherBulletin from '../Weather/WeatherBulletin';
import { useAuth } from '../../../../contexts/AuthContext';
import { useLanguage } from '../../../../contexts/LanguageContext';
import DynamicTranslation from '../../../../components/DynamicTranslation/DynamicTranslation';
import { useTranslation } from '../../../../hooks/useTranslation';
import { keyframes } from '@mui/system';
import { Link } from 'react-router-dom';
import { getWeatherData } from '../../../../services/weatherService';

const mockSchedule = {
  tasks: [
    {
      id: 1,
      title: "Water the crops",
      description: "Irrigate the wheat field in the morning",
      date: new Date().toISOString(),
      status: "pending",
      priority: "high"
    },
    {
      id: 2,
      title: "Fertilizer application",
      description: "Apply NPK fertilizer to the rice field",
      date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      status: "scheduled",
      priority: "medium"
    },
    {
      id: 3,
      title: "Pest control",
      description: "Spray organic pesticide on cotton plants",
      date: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString(),
      status: "scheduled",
      priority: "high"
    }
  ]
};

const mockAlerts = [
  {
    id: 1,
    message: "High temperature alert: Temperature expected to reach 35°C tomorrow",
    type: "warning",
    timestamp: new Date().toISOString(),
    severity: "high"
  },
  {
    id: 2,
    message: "Soil moisture level is below optimal range",
    type: "alert",
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    severity: "medium"
  },
  {
    id: 3,
    message: "Market price for wheat has increased by 5%",
    type: "info",
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
    severity: "low"
  }
];

// Add animations
const shimmerAnimation = keyframes`
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
`;

const floatAnimation = keyframes`
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
`;

const glowAnimation = keyframes`
  0% {
    box-shadow: 0 0 5px rgba(129, 199, 132, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(129, 199, 132, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(129, 199, 132, 0.5);
  }
`;

// Sample weather data for user6747
const sampleWeatherData = {
  current: {
    temp_c: 32,
    humidity: 65,
    wind_kph: 15,
    condition: { text: 'Partly Cloudy' },
    last_updated: new Date().toISOString(),
    feelslike_c: 34,
    uv: 6
  },
  location: {
    name: 'Bangalore Rural',
    region: 'Karnataka'
  },
  forecast: {
    forecastday: [
      {
        date: new Date().toISOString(),
        day: {
          mintemp_c: 24,
          maxtemp_c: 32,
          condition: { text: 'Partly Cloudy' }
        }
      },
      {
        date: new Date(Date.now() + 86400000).toISOString(),
        day: {
          mintemp_c: 23,
          maxtemp_c: 31,
          condition: { text: 'Chance of Rain' }
        }
      },
      {
        date: new Date(Date.now() + 172800000).toISOString(),
        day: {
          mintemp_c: 22,
          maxtemp_c: 30,
          condition: { text: 'Light Rain' }
        }
      },
      {
        date: new Date(Date.now() + 259200000).toISOString(),
        day: {
          mintemp_c: 23,
          maxtemp_c: 29,
          condition: { text: 'Rain' }
        }
      },
      {
        date: new Date(Date.now() + 345600000).toISOString(),
        day: {
          mintemp_c: 24,
          maxtemp_c: 31,
          condition: { text: 'Partly Cloudy' }
        }
      }
    ]
  },
  bulletin: `Current Weather Summary:
Temperature is above average at 32°C with moderate humidity levels. Light winds from the southwest.

Weather Prediction for Next 5 Days:
- Expect increasing cloud cover with chances of rain starting tomorrow
- Temperatures will gradually decrease to 29-30°C
- Good rainfall probability (60-70%) in the next 3-4 days
- Wind speeds may increase to 20-25 km/h during rain

Agricultural Advisory:
- Consider delaying any planned pesticide applications
- Prepare fields for upcoming rainfall
- Monitor soil moisture levels
- Plan harvesting activities before heavy rain sets in`
};

// Sample soil health data for user6747
const sampleSoilData = {
  currentReadings: {
    pH: 6.8,
    nitrogen: 280,
    phosphorus: 45,
    potassium: 190,
    organic_matter: 2.8,
    moisture: 35
  },
  recommendations: [
    {
      type: "pH Balance",
      status: "Good",
      message: "Soil pH is within optimal range for most crops"
    },
    {
      type: "Nitrogen",
      status: "Moderate",
      message: "Consider applying nitrogen-rich fertilizer in the next 2 weeks"
    },
    {
      type: "Phosphorus",
      status: "Good",
      message: "Phosphorus levels are adequate"
    },
    {
      type: "Potassium",
      status: "Good",
      message: "Potassium levels are sufficient for current growth stage"
    }
  ],
  history: [
    {
      date: "2024-03-15",
      pH: 6.7,
      moisture: 32
    },
    {
      date: "2024-03-10",
      pH: 6.9,
      moisture: 38
    },
    {
      date: "2024-03-05",
      pH: 6.8,
      moisture: 36
    }
  ],
  alerts: [
    {
      type: "info",
      message: "Optimal conditions for crop growth",
      timestamp: new Date().toISOString()
    }
  ]
};

// Add sample financial data
const sampleFinancialData = {
  investments: [
    { category: 'Seeds', amount: 15000 },
    { category: 'Field Preparation', amount: 25000 },
    { category: 'Labor', amount: 35000 },
    { category: 'Equipment', amount: 50000 },
    { category: 'Fertilizers', amount: 20000 },
    { category: 'Irrigation', amount: 30000 }
  ],
  totalInvestment: 175000,
  lastUpdated: new Date().toISOString()
};

// Add sample market data after the other sample data
const sampleMarketData = {
  crops: [
    { name: "Rice", currentPrice: 2400, trend: "up", volume: 450, market: "Bengaluru APMC" },
    { name: "Wheat", currentPrice: 2150, trend: "down", volume: 320, market: "Chennai Wholesale" },
    { name: "Maize", currentPrice: 1850, trend: "stable", volume: 280, market: "Hyderabad Grain Market" },
    { name: "Cotton", currentPrice: 6500, trend: "up", volume: 180, market: "Nagpur Cotton Exchange" },
    { name: "Soybean", currentPrice: 4250, trend: "down", volume: 210, market: "Indore Market" }
  ],
  marketTrends: [
    { label: "MSP Change", value: "+5.2%", direction: "up" },
    { label: "Weekly Volume", value: "-2.1%", direction: "down" },
    { label: "Demand Index", value: "Stable", direction: "stable" }
  ],
  lastUpdated: new Date().toLocaleDateString()
};

const DashData = () => {
  const theme = useTheme();
  const { currentUser } = useAuth();
  const { t, translateDynamic, translateBatch } = useTranslation();
  const [dataLoading, setDataLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showError, setShowError] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [weatherData, setWeatherData] = useState(null);
  const [soilData, setSoilData] = useState(null);
  const [schedule, setSchedule] = useState(null);
  const [alerts, setAlerts] = useState(null);
  const [marketData, setMarketData] = useState(null);
  const [translatedWeather, setTranslatedWeather] = useState(null);
  const [translatedSoil, setTranslatedSoil] = useState(null);
  const [translatedSchedule, setTranslatedSchedule] = useState(null);
  const [translatedAlerts, setTranslatedAlerts] = useState(null);
  const [translatedMarketData, setTranslatedMarketData] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const { selectedLanguage } = useLanguage();

  // Function to translate weather data
  const translateWeatherData = useCallback(async (data) => {
    if (!data || selectedLanguage === 'en-IN') {
      return data;
    }

    try {
      // Deep clone the data to avoid modifying the original
      const translated = JSON.parse(JSON.stringify(data));
      
      // Translate current weather condition
      if (translated.current && translated.current.condition) {
        translated.current.condition.text = await translateDynamic(data.current.condition.text);
      }
      
      // Translate forecast data
      if (translated.forecast && translated.forecast.forecastday) {
        for (let i = 0; i < translated.forecast.forecastday.length; i++) {
          const day = translated.forecast.forecastday[i];
          if (day.day && day.day.condition) {
            day.day.condition.text = await translateDynamic(data.forecast.forecastday[i].day.condition.text);
          }
        }
      }
      
      // Translate location names
      if (translated.location) {
        translated.location.name = await translateDynamic(data.location.name);
        if (data.location.region) {
          translated.location.region = await translateDynamic(data.location.region);
        }
      }
      
      // Translate bulletin text
      if (translated.bulletin) {
        translated.bulletin = await translateDynamic(data.bulletin);
      }
      
      return translated;
    } catch (error) {
      console.error('Error translating weather data:', error);
      return data; // Return original on error
    }
  }, [selectedLanguage, translateDynamic]);

  // Function to translate soil data
  const translateSoilData = useCallback(async (data) => {
    if (!data || selectedLanguage === 'en-IN') {
      return data;
    }

    try {
      // Deep clone the data to avoid modifying the original
      const translated = JSON.parse(JSON.stringify(data));
      
      // Translate recommendations
      if (translated.recommendations && translated.recommendations.length > 0) {
        for (let i = 0; i < translated.recommendations.length; i++) {
          const recommendation = translated.recommendations[i];
          recommendation.type = await translateDynamic(data.recommendations[i].type);
          recommendation.status = await translateDynamic(data.recommendations[i].status);
          recommendation.message = await translateDynamic(data.recommendations[i].message);
        }
      }
      
      return translated;
    } catch (error) {
      console.error('Error translating soil data:', error);
      return data; // Return original on error
    }
  }, [selectedLanguage, translateDynamic]);

  // Function to translate schedule data
  const translateScheduleData = useCallback(async (data) => {
    if (!data || selectedLanguage === 'en-IN') {
      return data;
    }

    try {
      // Deep clone the data to avoid modifying the original
      const translated = JSON.parse(JSON.stringify(data));
      
      // Translate tasks
      if (translated.tasks && translated.tasks.length > 0) {
        for (let i = 0; i < translated.tasks.length; i++) {
          const task = translated.tasks[i];
          task.title = await translateDynamic(data.tasks[i].title);
          task.description = await translateDynamic(data.tasks[i].description);
          task.status = await translateDynamic(data.tasks[i].status);
          task.priority = await translateDynamic(data.tasks[i].priority);
        }
      }
      
      return translated;
    } catch (error) {
      console.error('Error translating schedule data:', error);
      return data; // Return original on error
    }
  }, [selectedLanguage, translateDynamic]);

  // Function to translate alert data
  const translateAlertData = useCallback(async (data) => {
    if (!data || !Array.isArray(data) || selectedLanguage === 'en-IN') {
      return data;
    }

    try {
      // Deep clone the data to avoid modifying the original
      const translated = JSON.parse(JSON.stringify(data));
      
      // Translate each alert
      for (let i = 0; i < translated.length; i++) {
        translated[i].message = await translateDynamic(data[i].message);
        translated[i].type = await translateDynamic(data[i].type);
        translated[i].severity = await translateDynamic(data[i].severity);
      }
      
      return translated;
    } catch (error) {
      console.error('Error translating alert data:', error);
      return data; // Return original on error
    }
  }, [selectedLanguage, translateDynamic]);

  // Function to translate market data
  const translateMarketData = useCallback(async (data) => {
    if (!data || selectedLanguage === 'en-IN' || selectedLanguage === 'en') {
      return data;
    }

    try {
      // Deep clone the data to avoid modifying the original
      const translated = JSON.parse(JSON.stringify(data));
      
      // Translate crop names and market names
      if (translated.crops && translated.crops.length > 0) {
        for (let i = 0; i < translated.crops.length; i++) {
          const crop = translated.crops[i];
          crop.name = await translateDynamic(data.crops[i].name);
          crop.market = await translateDynamic(data.crops[i].market);
        }
      }
      
      // Translate market trend labels
      if (translated.marketTrends && translated.marketTrends.length > 0) {
        for (let i = 0; i < translated.marketTrends.length; i++) {
          translated.marketTrends[i].label = await translateDynamic(data.marketTrends[i].label);
          if (typeof data.marketTrends[i].value === 'string' && 
              !data.marketTrends[i].value.includes('%') && 
              !data.marketTrends[i].value.includes('+') && 
              !data.marketTrends[i].value.includes('-')) {
            translated.marketTrends[i].value = await translateDynamic(data.marketTrends[i].value);
          }
        }
      }
      
      return translated;
    } catch (error) {
      console.error('Error translating market data:', error);
      return data; // Return original on error
    }
  }, [selectedLanguage, translateDynamic]);

  // Main data fetching function
  const fetchData = async () => {
    setRefreshing(true);
    setDataLoading(true);

    try {
      // Simulate API calls with mock data
      // In a real implementation, you'd make actual API calls here
      const weatherResult = await getWeatherData(currentUser?.id || 'user6747');
      const soilResult = sampleSoilData;
      const scheduleResult = mockSchedule;
      const alertsResult = mockAlerts;
      const marketResult = sampleMarketData;

      // Set the original data
      setWeatherData(weatherResult);
      setSoilData(soilResult);
      setSchedule(scheduleResult);
      setAlerts(alertsResult);
      setMarketData(marketResult);

      // Translate the data if needed
      if (selectedLanguage !== 'en-IN' && selectedLanguage !== 'en') {
        const translatedWeatherResult = await translateWeatherData(weatherResult);
        const translatedSoilResult = await translateSoilData(soilResult);
        const translatedScheduleResult = await translateScheduleData(scheduleResult);
        const translatedAlertsResult = await translateAlertData(alertsResult);
        const translatedMarketResult = await translateMarketData(marketResult);

        setTranslatedWeather(translatedWeatherResult);
        setTranslatedSoil(translatedSoilResult);
        setTranslatedSchedule(translatedScheduleResult);
        setTranslatedAlerts(translatedAlertsResult);
        setTranslatedMarketData(translatedMarketResult);
      } else {
        // Use original data for English
        setTranslatedWeather(weatherResult);
        setTranslatedSoil(soilResult);
        setTranslatedSchedule(scheduleResult);
        setTranslatedAlerts(alertsResult);
        setTranslatedMarketData(marketResult);
      }

      setError(null);
      setShowSuccess(true);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError(t('dashboard.fetchError', 'Failed to fetch dashboard data. Please try again.'));
      setShowError(true);
    } finally {
      setDataLoading(false);
      setRefreshing(false);
    }
  };

  // Fetch data initially and when language changes
  useEffect(() => {
    fetchData();
  }, [selectedLanguage]);

  // Handle refresh action
  const handleRefresh = () => {
    fetchData();
  };

  const sectionStyles = {
    container: {
      p: 2,
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      gap: 2,
      background: 'linear-gradient(135deg, rgba(46, 125, 50, 0.1) 0%, rgba(129, 199, 132, 0.1) 100%)'
    },
    section: {
      background: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(12px)',
      borderRadius: '16px',
      p: 3,
      height: '100%',
      position: 'relative',
      overflow: 'hidden',
      boxShadow: '0 8px 32px rgba(27, 94, 32, 0.15)',
      border: '1px solid rgba(46, 125, 50, 0.2)'
    },
    sectionTitle: {
      display: 'flex',
      alignItems: 'center',
      gap: 1,
      mb: 2,
      color: '#1b5e20',
      fontWeight: 600,
      fontSize: '1.2rem',
      '& svg': {
        color: '#2e7d32'
      }
    },
    loadingContainer: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      minHeight: '300px'
    }
  };

  if (dataLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <DynamicTranslation text={error} />
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={sectionStyles.container}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2
          }}>
            <Typography variant="h4" sx={{ fontWeight: 600, color: '#2e7d32', display: 'flex', alignItems: 'center' }}>
              <DashboardIcon sx={{ mr: 1 }} />
              <DynamicTranslation text={t('dashboard.title', 'Dashboard')} />
            </Typography>
            <Tooltip title={t('refresh')}>
              <IconButton onClick={handleRefresh} disabled={refreshing}>
                {refreshing ? <CircularProgress size={24} /> : <RefreshIcon />}
              </IconButton>
            </Tooltip>
          </Box>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={0} sx={sectionStyles.section}>
            <Box sx={sectionStyles.sectionTitle}>
              <WbSunnyIcon />
              <Typography variant="h6"><DynamicTranslation text={t('weather', 'Weather')} /></Typography>
            </Box>
            <Weather weatherData={translatedWeather || weatherData} isLoading={dataLoading} />
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={0} sx={sectionStyles.section}>
            <Box sx={sectionStyles.sectionTitle}>
              <GrassIcon />
              <Typography variant="h6"><DynamicTranslation text={t('soilHealth', 'Soil Health')} /></Typography>
            </Box>
            <SoilHealth data={translatedSoil || soilData} isLoading={dataLoading} />
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={0} sx={sectionStyles.section}>
            <Box sx={sectionStyles.sectionTitle}>
              <CalendarTodayIcon />
              <Typography variant="h6"><DynamicTranslation text={t('weeklySchedule', 'Weekly Schedule')} /></Typography>
            </Box>
            <WeeklySchedule schedule={translatedSchedule || schedule} isLoading={dataLoading} />
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={0} sx={sectionStyles.section}>
            <Box sx={sectionStyles.sectionTitle}>
              <TrendingUpIcon />
              <Typography variant="h6"><DynamicTranslation text={t('marketAnalysis', 'Market Analysis')} /></Typography>
            </Box>
            <MarketAnalysis data={translatedMarketData || marketData} isLoading={dataLoading} />
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={0} sx={sectionStyles.section}>
            <Box sx={sectionStyles.sectionTitle}>
              <WarningIcon />
              <Typography variant="h6"><DynamicTranslation text={t('recentAlerts', 'Recent Alerts')} /></Typography>
            </Box>
            <RecentAlerts alerts={translatedAlerts || alerts} isLoading={dataLoading} />
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={0} sx={sectionStyles.section}>
            <Box sx={sectionStyles.sectionTitle}>
              <AccountBalanceIcon />
              <Typography variant="h6"><DynamicTranslation text={t('farmFinancial', 'Farm Financial')} /></Typography>
            </Box>
            <Box sx={{ p: 2 }}>
              <Typography variant="subtitle1" sx={{ mb: 2, color: '#1b5e20' }}>
                <DynamicTranslation text={t('totalInvestment', 'Total Investment')} />: ₹100,000
              </Typography>
              <Grid container spacing={2}>
                {[
                  { category: 'Seeds', amount: 15000 },
                  { category: 'Fertilizer', amount: 25000 },
                  { category: 'Equipment', amount: 40000 },
                  { category: 'Labor', amount: 20000 }
                ].map((item, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Box
                      sx={{
                        p: 2,
                        bgcolor: 'rgba(46, 125, 50, 0.04)',
                        borderRadius: 1,
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                      }}
                    >
                      <Typography variant="body2" color="text.secondary">
                        <DynamicTranslation text={item.category} />
                      </Typography>
                      <Typography variant="body1" color="text.primary" fontWeight={500}>
                        ₹{item.amount.toLocaleString()}
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="caption" color="text.secondary">
                  <DynamicTranslation text={t('lastUpdated', 'Last updated')} />: {new Date().toLocaleDateString()}
                </Typography>
                <Button
                  variant="text"
                  color="primary"
                  size="small"
                  component={Link}
                  to="/credit-score"
                  endIcon={<ArrowForwardIcon fontSize="small" />}
                >
                  <DynamicTranslation text={t('viewCreditScore', 'View Credit Score')} />
                </Button>
              </Box>
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12}>
          <Paper elevation={0} sx={{
            ...sectionStyles.section,
            minHeight: '650px',
            maxHeight: '750px',
            mx: { xs: 0, sm: 1, md: 2 },
            px: { xs: 1, sm: 2, md: 3 }
          }}>
            <Box sx={sectionStyles.sectionTitle}>
              <SmartToyIcon />
              <Typography variant="h6"><DynamicTranslation text={t('aiAgent', 'AI Assistant')} /></Typography>
            </Box>
            <Box sx={{ height: 'calc(100% - 60px)', overflow: 'hidden' }}>
              <PersistentAIAgent />
            </Box>
          </Paper>
        </Grid>
      </Grid>
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={() => setShowError(false)}
      >
        <Alert onClose={() => setShowError(false)} severity="error">
          <DynamicTranslation text={error} />
        </Alert>
      </Snackbar>
      <Snackbar
        open={showSuccess}
        autoHideDuration={3000}
        onClose={() => setShowSuccess(false)}
      >
        <Alert onClose={() => setShowSuccess(false)} severity="success">
          <DynamicTranslation text={t('dashboard.updateSuccess', 'Dashboard updated successfully')} />
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DashData;