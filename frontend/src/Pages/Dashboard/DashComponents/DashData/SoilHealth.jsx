import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Grid,
} from '@mui/material';
import {
  Science as NPKIcon,
  ElectricBolt as ECIcon,
} from '@mui/icons-material';
import axios from 'axios';
import './DashData.css';

const SoilHealth = () => {
  const [soilData, setSoilData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchSoilData = async () => {
      try {
        // TODO: Replace with actual API endpoint when available
        // For now, using mock data
        const mockData = {
          nitrogen: 45, // mg/kg
          phosphorus: 30, // mg/kg
          potassium: 250, // mg/kg
          ec: 1.2, // mS/cm
        };
        setSoilData(mockData);
      } catch (err) {
        setError('Failed to fetch soil health data');
        console.error('Soil Health API Error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchSoilData();
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={2}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Box className="soil-health-data">
      <Box className="soil-health-top-row">
        <Typography variant="h4" color="white">Soil Health Metrics</Typography>
      </Box>
      
      <Box className="soil-health-bottom-row">
        <Box className="soil-health-bottom-row-left">
          <Box display="flex" flexDirection="column" gap={3}>
            <Box display="flex" alignItems="center" gap={2}>
              <NPKIcon sx={{ fontSize: 40, color: '#fafafa' }} />
              <Box>
                <Typography variant="h6" color="white" gutterBottom>
                  NPK Values
                </Typography>
                <Box display="flex" flexDirection="column" gap={2}>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Typography className="soil-health-label">Nitrogen:</Typography>
                    <Typography className="soil-health-value">{soilData?.nitrogen} mg/kg</Typography>
                  </Box>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Typography className="soil-health-label">Phosphorus:</Typography>
                    <Typography className="soil-health-value">{soilData?.phosphorus} mg/kg</Typography>
                  </Box>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Typography className="soil-health-label">Potassium:</Typography>
                    <Typography className="soil-health-value">{soilData?.potassium} mg/kg</Typography>
                  </Box>
                </Box>
              </Box>
            </Box>
            
            <Box display="flex" alignItems="center" gap={2}>
              <ECIcon sx={{ fontSize: 40, color: '#fafafa' }} />
              <Box>
                <Typography variant="h6" color="white" gutterBottom>
                  Electrical Conductivity
                </Typography>
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography className="soil-health-label">EC Value:</Typography>
                  <Typography className="soil-health-value">{soilData?.ec} mS/cm</Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default SoilHealth; 