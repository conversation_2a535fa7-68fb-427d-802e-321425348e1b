/* Dashboard Layout */
.dashboard-container {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 24px;
  padding: 24px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* Left Section */
.dashboard-left {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dashboard-left-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-left-bottom {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.farm-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.farm-stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.alerts-list {
  margin-top: 16px;
  max-height: 200px;
  overflow-y: auto;
}

/* Right Section */
.dashboard-right {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Weather Section */
.weather-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* AI Agent Section */
.ai-agent-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.ai-agent-header {
  padding: 16px 24px;
  background: linear-gradient(45deg, #1976d2, #2196f3);
  color: white;
}

.ai-agent-content {
  height: 400px;
  padding: 24px;
  overflow-y: auto;
}

/* Farming Schedule Section */
.farming-schedule-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.schedule-header {
  padding: 16px 24px;
  background: linear-gradient(45deg, #2e7d32, #4caf50);
  color: white;
}

.schedule-content {
  padding: 24px;
}

.schedule-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 16px;
}

.schedule-day {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.day-name {
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 12px;
}

.day-tasks {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-item {
  font-size: 0.875rem;
  color: #666;
  padding: 4px 8px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-container {
    grid-template-columns: 1fr;
  }
  
  .schedule-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-left-bottom {
    grid-template-columns: 1fr;
  }
  
  .schedule-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .farm-stats {
    grid-template-columns: 1fr;
  }
}

/* To do list styling */

.todo-data {
  background-color: #e9fde3;
  border-radius: 16px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.todo-data p {
  text-align: center;
  padding: 16px;
  font-size: 24px;
  font-weight: 600;
  color: #fafafa;
  border-radius: 16px 16px 0px 0px;
  background: linear-gradient(45deg, #006400, #00b500, #00b500, #006400);
  background-size: 400% 400%; /* Increase the size for smooth animation */
  animation: gradientAnimation 5s ease infinite; /* Apply the animation */
}

/* Weather data styling */

.weather-data {
  border-radius: 16px;
  height: 100%;
  width: 100%;
  background: linear-gradient(
    45deg,
    #191970,
    #7cc4ff,
    #003153
  );
  background-size: 400% 400%;
  animation: gradientAnimation 10s ease infinite;
  display: flex;
  flex-direction: column;
}

.weather-top-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px 0px 32px;
  flex-shrink: 0;
}

.weather-top-row p {
  color: #fafafa;
  font-size: 32px;
  font-weight: 600;
}

.weather-bottom-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32px;
  flex: 1;
  min-height: 0;
}

.weather-bottom-row-left {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 48px;
  flex: 1;
}

.weather-bottom-row-left h1 {
  font-size: 96px;
  color: #fafafa;
  margin: 0;
}

.weather-bottom-row-right {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
  padding-left: 32px;
}

.weather-bottom-row-right p {
  font-size: 20px;
  font-weight: 500;
  color: #fafafa;
  margin: 0;
}

/* Weather forecast content area */
.weather-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px 32px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  margin: 16px 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.weather-content::-webkit-scrollbar {
  width: 6px;
}

.weather-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.weather-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.weather-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .weather-top-row {
    padding: 16px 24px 0px 24px;
  }

  .weather-bottom-row {
    padding: 24px;
    flex-direction: column;
    gap: 24px;
  }

  .weather-bottom-row-left h1 {
    font-size: 72px;
  }

  .weather-bottom-row-right {
    padding-left: 0;
    align-items: center;
  }

  .weather-content {
    margin: 12px 24px;
    padding: 16px 24px;
  }
}

/* Keyframes for the gradient animation */
@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  25% {
    background-position: 50% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  75% {
    background-position: 50% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Soil Data Styling */

.dashdata-bottom-right {
  display: grid;
  grid-template-columns: 1fr 1fr;
  height: 100%;
  gap: 16px;
  margin-top: 16px;
}

.soil-data {
  background-color: #ffec99;
  border-radius: 16px;
}

.soil-data h2 {
  text-align: center;
  padding: 16px;
  font-size: 24px;
  font-weight: 600;
  color: #fafafa;
  border-radius: 16px 16px 0px 0px;
  background: linear-gradient(45deg, #006400, #00b500, #00b500, #006400);
  background-size: 400% 400%; /* Increase the size for smooth animation */
  animation: gradientAnimation 5s ease infinite; /* Apply the animation */
}

.soil-data p {
  font-size: 20px;
  font-weight: 600;
  text-align: center;
}

.soil-data-nutrients-container {
  overflow-y: scroll;
  padding: 16px;
  direction: rtl;
  height: 200px;
}

.soil-data-nutrients-container * {
  direction: ltr;
}

.soil-data-nutrients-container ul {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px 32px;
  line-height: 32px;
}

.soil-data-nutrients-container li {
  list-style: bullets;
  font-size: 20px;
  font-weight: 500;
}

.soil-data-nutrients-container::-webkit-scrollbar {
  width: 8px;
  background-color: #ffec99;
}

.soil-data-nutrients-container::-webkit-scrollbar-thumb {
  background-color: #9b6c00;
}

.soil-data-nutrients-container::-webkit-scrollbar-thumb:hover {
  background-color: #725000;
}

.soil-data-nutrients-container::-webkit-scrollbar-button {
  display: none;
}

/*notification area styling  */

.notification-data {
  background-color: #b1f3ff;
  border-radius: 16px;
}

.notification-data h2 {
  text-align: center;
  padding: 16px;
  font-size: 24px;
  font-weight: 600;
  color: #fafafa;
  border-radius: 16px 16px 0px 0px;
  background: linear-gradient(45deg, #006400, #00b500, #00b500, #006400);
  background-size: 400% 400%; /* Increase the size for smooth animation */
  animation: gradientAnimation 5s ease infinite; /* Apply the animation */
}

.notification-data p {
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  color: #0a0a0a;
}

.notification-container {
  overflow-y: scroll;
  padding: 16px;
  direction: rtl;
  height: 200px;
  color: #0a0a0a;
}

.notification-container * {
  direction: ltr;
}

.notification-container ul {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px 32px;
  line-height: 32px;
}

.notification-container li {
  list-style: bullets;
  font-size: 20px;
  font-weight: 500;
  /* background-color: #22b8cf; */
  padding: 8px;
  border-radius: 8px;
}

.notification-container::-webkit-scrollbar {
  width: 8px;
  background-color: #0b7285;
}

.notification-container::-webkit-scrollbar-thumb {
  background-color: #22b8cf;
}

.notification-container::-webkit-scrollbar-thumb:hover {
  background-color: #074955;
}

.notification-container::-webkit-scrollbar-button {
  display: none;
}

.dash-data {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-areas: 
      "soil soil"
      "tasks alerts";
  gap: 2rem;
  padding: 1.5rem;
  height: auto;
  min-height: 100%;
}

.soil-card {
  grid-area: soil;
  height: auto;
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

.tasks-card {
  grid-area: tasks;
  height: auto;
  min-height: 250px;
  display: flex;
  flex-direction: column;
}

.alerts-card {
  grid-area: alerts;
  height: auto;
  min-height: 250px;
  display: flex;
  flex-direction: column;
}

.data-card {
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.8));
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 100, 0, 0.1);
  transition: all 0.3s ease;
  height: auto;
  min-height: 250px;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  border: 1px solid rgba(0, 181, 0, 0.1);
  backdrop-filter: blur(10px);
}

.data-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 100, 0, 0.15);
  border-color: rgba(0, 181, 0, 0.2);
}

.data-card h3 {
  color: #006400;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  font-weight: 600;
  flex-shrink: 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid rgba(0, 181, 0, 0.1);
}

/* Soil Card specific styling */
.soil-metrics {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1rem;
  align-content: start;
}

.metric {
  text-align: center;
  padding: 1rem;
  background: rgba(0, 181, 0, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 181, 0, 0.1);
  transition: all 0.2s ease;
}

.metric:hover {
  background: rgba(0, 181, 0, 0.1);
  border-color: rgba(0, 181, 0, 0.2);
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #006400;
}

.metric-label {
  font-size: 0.875rem;
  color: #1a2f23;
  margin: 0.5rem 0;
}

.metric-status {
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  display: inline-block;
}

.metric-status.good {
  background: rgba(0, 181, 0, 0.1);
  color: #006400;
}

.metric-status.warning {
  background: rgba(255, 166, 0, 0.1);
  color: #b45309;
}

/* Tasks Card specific styling */
.task-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.5rem 0;
  overflow-y: auto;
  margin-top: 0.5rem;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.task-item:hover {
  background-color: rgba(0, 181, 0, 0.05);
  border-color: rgba(0, 181, 0, 0.1);
}

/* Alert list adjustments */
.alert-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 0.5rem 0;
  overflow-y: auto;
  margin-top: 1rem;
  max-height: calc(100% - 3rem);
}

/* Ensure consistent card heights in the bottom row */
.tasks-card,
.alerts-card {
  height: 100%;
  min-height: 300px;
}

/* Add some spacing between cards */
.data-card + .data-card {
  margin-top: 0;
}

/* Weather Card */
.weather-card .data-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.date-time {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 0.875rem;
  color: #718096;
}

.weather-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.weather-main img {
  width: 64px;
  height: 64px;
}

.temperature {
  display: flex;
  align-items: baseline;
}

.temp-value {
  font-size: 3rem;
  font-weight: 600;
  color: #2d3748;
}

.temp-unit {
  font-size: 1.5rem;
  color: #718096;
  margin-left: 0.25rem;
}

.weather-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.weather-detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4a5568;
  font-size: 0.875rem;
}

.weather-detail-item i {
  color: #4299e1;
}

@media (max-width: 1280px) {
  .dash-data {
    gap: 1.5rem;
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .dash-data {
    grid-template-columns: 1fr;
    grid-template-areas: 
        "soil"
        "tasks"
        "alerts";
    gap: 1rem;
    padding: 0.75rem;
  }

  .data-card {
    min-height: 250px;
    padding: 1rem;
  }

  .soil-metrics {
    grid-template-columns: 1fr;
  }

  .tasks-card,
  .alerts-card {
    min-height: 250px;
  }
}

/* Alert styles with theme colors */
.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.alert-item.warning {
  background: rgba(255, 166, 0, 0.1);
  border-color: rgba(255, 166, 0, 0.2);
}

.alert-item.info {
  background: rgba(0, 181, 0, 0.05);
  border-color: rgba(0, 181, 0, 0.1);
}

.alert-item i {
  font-size: 1.25rem;
}

.alert-item.warning i {
  color: #b45309;
}

.alert-item.info i {
  color: #006400;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  color: #1a2f23;
  margin-bottom: 0.25rem;
}

.alert-description {
  font-size: 0.875rem;
  color: #2d3748;
}

/* Smaller Checkbox styles with theme colors */
.task-item input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  border: 2px solid #00b500;
  border-radius: 3px;
  cursor: pointer;
  margin: 0;
  padding: 0;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-color: white;
  position: relative;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.task-item input[type="checkbox"]:checked {
  background-color: #006400;
  border-color: #006400;
}

.task-item input[type="checkbox"]:checked::after {
  content: '';
  position: absolute;
  left: 3px;
  top: 0px;
  width: 5px;
  height: 9px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.task-item input[type="checkbox"]:hover {
  border-color: #006400;
  box-shadow: 0 0 0 2px rgba(0, 100, 0, 0.1);
}

.task-item label {
  color: #1a2f23;
  font-size: 0.875rem;
  cursor: pointer;
  flex: 1;
  margin: 0;
  padding: 0;
  line-height: 1.25rem;
  transition: color 0.2s ease;
}

.task-item input[type="checkbox"]:checked + label {
  color: #006400;
  text-decoration: line-through;
  opacity: 0.7;
}

/* Scrollbar styling */
.task-list::-webkit-scrollbar,
.alert-list::-webkit-scrollbar {
  width: 6px;
}

.task-list::-webkit-scrollbar-track,
.alert-list::-webkit-scrollbar-track {
  background: rgba(0, 181, 0, 0.05);
  border-radius: 3px;
}

.task-list::-webkit-scrollbar-thumb,
.alert-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 100, 0, 0.2);
  border-radius: 3px;
}

.task-list::-webkit-scrollbar-thumb:hover,
.alert-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 100, 0, 0.3);
}

.soil-health-data {
  border-radius: 16px;
  height: 100%;
  width: 100%;
  background: linear-gradient(
    45deg,
    #191970,
    #7cc4ff,
    #003153
  );
  background-size: 400% 400%;
  animation: gradientAnimation 10s ease infinite;
}

.soil-health-top-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32px 64px 0px 64px;
}

.soil-health-top-row p {
  color: #fafafa;
  font-size: 36px;
  font-weight: 600;
}

.soil-health-bottom-row {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 64px;
}

.soil-health-bottom-row-left {
  display: flex;
  flex-direction: column;
  gap: 32px;
  width: 100%;
}

.soil-health-bottom-row-left > div {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.soil-health-bottom-row-left h6 {
  color: #fafafa;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.soil-health-value {
  color: #fafafa;
  font-weight: 600;
  font-size: 1.1rem;
}

.soil-health-label {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  font-size: 1.1rem;
}

/* AI Agent Styles */
.ai-agent-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  margin-bottom: 16px;
}

.message {
  margin-bottom: 16px;
  padding: 12px 16px;
  border-radius: 8px;
  max-width: 85%;
  color: #ffffff;
  font-size: 0.875rem;
  line-height: 1.5;
}

.user-message {
  background: rgba(255, 255, 255, 0.2);
  margin-left: auto;
  color: #ffffff;
}

.assistant-message {
  background: rgba(255, 255, 255, 0.15);
  margin-right: auto;
  color: #ffffff;
}

.input-container {
  display: flex;
  gap: 8px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.input-container input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  font-size: 0.875rem;
}

.input-container input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.language-selector {
  min-width: 200px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
}

.status-indicator {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: #ffffff;
}

.status-indicator.recording {
  background: rgba(244, 67, 54, 0.2);
  color: #ffffff;
}

.status-indicator.speaking {
  background: rgba(33, 150, 243, 0.2);
  color: #ffffff;
}

/* Scrollbar styling for AI agent */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
