.weather-container {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem;
  position: relative;
  min-height: 200px;
}

.weather-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.weather-content h6 {
  margin: 0 0 0.5rem 0;
  color: #fff;
}

.weather-content p {
  margin: 0;
  color: #e0e0e0;
}

.refresh-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  color: #fff;
}

.refresh-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.weather-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #ff6b6b;
}

.weather-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
} 