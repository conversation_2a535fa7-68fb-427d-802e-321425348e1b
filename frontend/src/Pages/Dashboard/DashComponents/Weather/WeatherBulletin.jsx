import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Stack,
  Divider
} from '@mui/material';
import {
  Thermostat as ThermostatIcon,
  WaterDrop as WaterDropIcon,
  Air as AirIcon,
  WbSunny as WbSunnyIcon,
  Warning as WarningIcon
} from '@mui/icons-material';

const WeatherBulletin = ({ bulletin }) => {
  if (!bulletin) return null;

  // Parse the bulletin text into sections
  const sections = bulletin.split('\n\n').filter(Boolean);
  
  const getCurrentConditions = (section) => {
    const conditions = section.split('\n').slice(1);
    return conditions.map(condition => {
      const [label, value] = condition.split(':').map(s => s.trim());
      return { label, value };
    });
  };

  const getRecommendations = (section) => {
    return section.split('\n').slice(1).map(rec => rec.trim());
  };

  const getWeeklyForecast = (section) => {
    return section.split('\n').slice(1).map(day => day.trim());
  };

  return (
    <Card sx={{ 
      bgcolor: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      borderRadius: '12px',
      color: 'white'
    }}>
      <CardContent>
        <Typography variant="h5" gutterBottom sx={{ color: 'white' }}>
          Weather Bulletin
        </Typography>
        
        {/* Current Conditions */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{ color: 'white', mb: 1 }}>
            Current Conditions
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap" gap={1}>
            {getCurrentConditions(sections[0]).map((condition, index) => (
              <Chip
                key={index}
                icon={
                  condition.label.includes('Temperature') ? <ThermostatIcon /> :
                  condition.label.includes('Humidity') ? <WaterDropIcon /> :
                  condition.label.includes('Wind') ? <AirIcon /> :
                  <WbSunnyIcon />
                }
                label={`${condition.label}: ${condition.value}`}
                sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  color: 'white',
                  '& .MuiChip-icon': { color: 'white' }
                }}
              />
            ))}
          </Stack>
        </Box>

        <Divider sx={{ my: 2, bgcolor: 'rgba(255, 255, 255, 0.2)' }} />

        {/* Agricultural Recommendations */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{ color: 'white', mb: 1 }}>
            Agricultural Recommendations
          </Typography>
          <Stack spacing={1}>
            {sections.slice(1, -1).map((section, index) => {
              if (section.includes('Agricultural Recommendations')) {
                return getRecommendations(section).map((rec, recIndex) => (
                  <Box key={recIndex} sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    <WarningIcon sx={{ color: '#ffd700', mt: 0.5 }} />
                    <Typography variant="body2">{rec}</Typography>
                  </Box>
                ));
              }
              return null;
            })}
          </Stack>
        </Box>

        <Divider sx={{ my: 2, bgcolor: 'rgba(255, 255, 255, 0.2)' }} />

        {/* Weekly Forecast */}
        <Box>
          <Typography variant="h6" sx={{ color: 'white', mb: 1 }}>
            Weekly Forecast
          </Typography>
          <Stack spacing={1}>
            {getWeeklyForecast(sections[sections.length - 1]).map((day, index) => (
              <Typography key={index} variant="body2">
                {day}
              </Typography>
            ))}
          </Stack>
        </Box>
      </CardContent>
    </Card>
  );
};

export default WeatherBulletin; 