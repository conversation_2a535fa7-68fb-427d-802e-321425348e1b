import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../../../../contexts/AuthContext';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  CircularProgress,
  IconButton,
  Tooltip,
  Paper,
  Divider
} from '@mui/material';
import {
  WbSunny,
  Cloud,
  WbCloudy,
  Grain,
  Refresh,
  WaterDrop,
  Air,
  Thermostat,
  Info as InfoIcon
} from '@mui/icons-material';
import { keyframes } from '@mui/system';
import { useTranslation } from '../../../../hooks/useTranslation';
import DynamicTranslation from '../../../../components/DynamicTranslation/DynamicTranslation';
import { translateNumber, formatValueWithUnit, formatDate } from '../../../../utils/translationUtils';

// Fallback data for testing
const fallbackWeatherData = {
  current: {
    temperature: 23.3,
    feels_like: 23.6,
    humidity: 75,
    wind_speed: 2.17,
    conditions: "overcast clouds",
    pressure: 1013,
    visibility: 10000,
    cloudiness: 100,
    last_update: new Date().toISOString(),
    sunrise: "06:30",
    sunset: "18:45"
  },
  forecast: [
    {
      date: "03/04/2025",
      temperature: {
        max: 23.5,
        min: 20.1,
        avg: 21.8
      },
      humidity: 78,
      wind_speed: 2.5,
      conditions: "moderate rain",
      precipitation: 4.8
    },
    {
      date: "04/04/2025",
      temperature: {
        max: 24.2,
        min: 19.8,
        avg: 22.0
      },
      humidity: 75,
      wind_speed: 2.8,
      conditions: "light rain",
      precipitation: 2.1
    },
    {
      date: "05/04/2025",
      temperature: {
        max: 25.0,
        min: 20.5,
        avg: 22.8
      },
      humidity: 72,
      wind_speed: 3.0,
      conditions: "overcast clouds",
      precipitation: 0.5
    },
    {
      date: "06/04/2025",
      temperature: {
        max: 25.5,
        min: 21.0,
        avg: 23.3
      },
      humidity: 70,
      wind_speed: 3.2,
      conditions: "scattered clouds",
      precipitation: 0.2
    },
    {
      date: "07/04/2025",
      temperature: {
        max: 26.0,
        min: 21.5,
        avg: 23.8
      },
      humidity: 68,
      wind_speed: 3.5,
      conditions: "clear sky",
      precipitation: 0.0
    }
  ],
  bulletin: `Weather Bulletin for 03/04/2025

Current Conditions:
- Temperature: 23.3°C (Feels like: 23.6°C)
- Humidity: 75%
- Wind Speed: 2.17 m/s
- Conditions: Overcast clouds

Tomorrow's Forecast:
- Temperature: 24.2°C (Min: 19.8°C)
- Conditions: Light rain expected
- Precipitation: 2.1 mm

Agricultural Recommendations:
1. Ensure proper field drainage due to expected rainfall
2. Monitor soil moisture levels
3. Consider delaying outdoor farming activities during heavy rain
4. Protect sensitive crops from potential waterlogging

Weekly Outlook (03/04/2025 - 07/04/2025):
- Moderate to light rain expected in the first half of the week
- Gradual clearing and warming trend towards the weekend
- Ideal conditions for crop growth and development
- Good opportunity for soil moisture replenishment`
};

// Helper function to format IST time
const formatISTTime = (utcTime) => {
  if (!utcTime) return '--:--';
  const date = new Date(utcTime);
  return date.toLocaleTimeString('en-IN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
    timeZone: 'Asia/Kolkata'
  });
};

// Rain animation keyframes
const rainAnimation = keyframes`
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
`;

const Weather = ({ onDataUpdate }) => {
  const { currentUser } = useAuth();
  const { t, translateDynamic } = useTranslation();
  const [currentWeather, setCurrentWeather] = useState(fallbackWeatherData.current);
  const [forecastData, setForecastData] = useState(fallbackWeatherData.forecast);
  const [weatherBulletin, setWeatherBulletin] = useState(fallbackWeatherData.bulletin);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch weather data on component mount
  useEffect(() => {
    fetchWeatherData();
  }, []);

  // Update parent component when weather data changes
  useEffect(() => {
    if (currentWeather) {
      const weatherData = {
        current: currentWeather,
        forecast: forecastData || fallbackWeatherData.forecast,
        bulletin: weatherBulletin || fallbackWeatherData.bulletin,
        location: {
          name: 'Bangalore Rural',
          region: 'Karnataka'
        }
      };
      console.log('Updating parent with weather data:', weatherData);
      onDataUpdate?.(weatherData);
    }
  }, [currentWeather, forecastData, weatherBulletin, onDataUpdate]);

  const getWeatherIcon = (conditions) => {
    if (!conditions) return <Cloud sx={{ color: '#757575' }} />;
    const desc = conditions?.toLowerCase() || '';
    const size = 32;

    if (desc.includes('clear')) return <WbSunny sx={{ color: '#ffa000', fontSize: size }} />;
    if (desc.includes('cloud')) return <WbCloudy sx={{ color: '#757575', fontSize: size }} />;
    if (desc.includes('rain')) {
      return (
        <Box sx={{ position: 'relative', width: size, height: size }}>
          <WbCloudy sx={{ color: '#757575', fontSize: size }} />
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              overflow: 'hidden'
            }}
          >
            {[...Array(6)].map((_, i) => (
              <Box
                key={i}
                sx={{
                  position: 'absolute',
                  top: -5,
                  left: 3 + i * 5,
                  width: 2,
                  height: 10,
                  backgroundColor: '#64b5f6',
                  animation: `${rainAnimation} ${0.6 + i * 0.1}s linear infinite`,
                  animationDelay: `${i * 0.1}s`
                }}
              />
            ))}
          </Box>
        </Box>
      );
    }
    return <Cloud sx={{ color: '#757575', fontSize: size }} />;
  };

  // Five Day Forecast Section
  const renderForecast = () => {
    // Ensure forecastData is an array and has items
    const forecast = Array.isArray(forecastData) ? forecastData : fallbackWeatherData.forecast;

    if (!forecast || forecast.length === 0) {
      return (
        <Box sx={{ textAlign: 'center', py: 2 }}>
          <Typography color="text.secondary"><DynamicTranslation text={t('weather.forecast.unavailable', 'Forecast data not available')} /></Typography>
        </Box>
      );
    }

    return (
      <Grid container spacing={2}>
        {forecast.slice(0, 5).map((day, index) => {
          // Get the day name and translate it
          const dayDate = new Date(day.date);
          const dayNameEn = dayDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
          const translatedDayName = t(`days.${dayNameEn}`, dayDate.toLocaleDateString('en-US', { weekday: 'long' }));
          
          return (
            <Grid item xs={12} sm={6} md={2.4} key={index}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 3
                  },
                  transition: 'all 0.3s ease-in-out'
                }}
              >
                <CardContent sx={{ flexGrow: 1, position: 'relative', zIndex: 1, p: 1.5 }}>
                  <Box textAlign="center" mb={1}>
                    <Typography variant="subtitle1" color="primary.main" fontWeight="bold">
                      <DynamicTranslation text={translatedDayName} />
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {formatDate(day.date || dayDate, t)}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
                    <Box sx={{ textAlign: 'center' }}>
                      {getWeatherIcon(day.conditions)}
                      <Typography variant="h6" sx={{ mt: 0.5 }}>
                        {day.temperature?.max ? translateNumber(Math.round(day.temperature.max), t) : '--'}°C
                      </Typography>
                    </Box>
                  </Box>

                  <Typography variant="body2" color="text.secondary" align="center" sx={{ fontSize: '0.8rem', height: '2.4em', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                    <DynamicTranslation text={day.conditions || 'Unknown'} />
                  </Typography>

                  <Box sx={{ mt: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <WaterDrop sx={{ fontSize: 14, color: 'primary.main', mr: 0.5 }} />
                    <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                      {formatValueWithUnit(day.precipitation || 0, 'mm', t)}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>
    );
  };

  const fetchWeatherData = async () => {
    try {
      setLoading(true);
      setError(null);
      setIsRefreshing(true);

      // Use a valid test ID if currentUser is not available
      const farmerId = currentUser?.id || '67dbeb95072698d242a3f415';

      // Log the farmer ID being used
      console.log('Using farmer ID for weather data:', farmerId, 'Current user:', currentUser?.name);

      if (!farmerId) {
        console.warn('No farmer ID available, using fallback data');
        setCurrentWeather(fallbackWeatherData.current);
        setForecastData(fallbackWeatherData.forecast);
        setWeatherBulletin(fallbackWeatherData.bulletin);
        return;
      }

      console.log('Fetching weather data for farmer:', farmerId);

      try {
        // Fetch current weather
        const currentResponse = await axios.get(`${import.meta.env.VITE_API_URL}/weather/current/${farmerId}`);
        if (currentResponse.data.success) {
          const currentData = currentResponse.data.data.current || currentResponse.data.data;
          setCurrentWeather(currentData);
        } else {
          console.warn('Current weather API returned success: false');
          setCurrentWeather(fallbackWeatherData.current);
        }
      } catch (currentError) {
        console.error('Error fetching current weather:', currentError);
        setCurrentWeather(fallbackWeatherData.current);
      }

      try {
        // Fetch forecast
        const forecastResponse = await axios.get(`${import.meta.env.VITE_API_URL}/weather/forecast/${farmerId}`);
        if (forecastResponse.data.success) {
          const forecastData = forecastResponse.data.data.forecast || forecastResponse.data.data;
          setForecastData(Array.isArray(forecastData) ? forecastData : fallbackWeatherData.forecast);
        } else {
          console.warn('Forecast API returned success: false');
          setForecastData(fallbackWeatherData.forecast);
        }
      } catch (forecastError) {
        console.error('Error fetching forecast:', forecastError);
        setForecastData(fallbackWeatherData.forecast);
      }

      try {
        // Fetch bulletin
        const bulletinResponse = await axios.get(`${import.meta.env.VITE_API_URL}/weather/bulletin/${farmerId}`);
        if (bulletinResponse.data.success) {
          const bulletinData = bulletinResponse.data.data.bulletin;
          setWeatherBulletin(bulletinData || fallbackWeatherData.bulletin);
        } else {
          console.warn('Bulletin API returned success: false');
          setWeatherBulletin(fallbackWeatherData.bulletin);
        }
      } catch (bulletinError) {
        console.error('Error fetching bulletin:', bulletinError);
        setWeatherBulletin(fallbackWeatherData.bulletin);
      }
    } catch (err) {
      console.error('Error fetching weather data:', err);
      if (err.response) {
        console.error('Error response:', {
          status: err.response.status,
          data: err.response.data,
          headers: err.response.headers
        });
      } else if (err.request) {
        console.error('Error request:', err.request);
      } else {
        console.error('Error message:', err.message);
      }
      setError('Failed to fetch weather data. Using fallback data.');
      setCurrentWeather(fallbackWeatherData.current);
      setForecastData(fallbackWeatherData.forecast);
      setWeatherBulletin(fallbackWeatherData.bulletin);
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    let mounted = true;

    const fetchData = async () => {
      if (mounted) {
        console.log('Component mounted, fetching data...');
        await fetchWeatherData();
      }
    };

    fetchData();

    // Auto-refresh every 5 minutes
    const interval = setInterval(() => {
      if (mounted) {
        console.log('Auto-refresh triggered');
        fetchData();
      }
    }, 5 * 60 * 1000);

    return () => {
      console.log('Component unmounting, cleaning up...');
      mounted = false;
      clearInterval(interval);
    };
  }, []);

  // Debug useEffect to monitor state changes
  useEffect(() => {
    console.log('State update detected:', {
      currentWeather,
      forecastData,
      weatherBulletin,
      loading,
      error
    });
  }, [currentWeather, forecastData, weatherBulletin, loading, error]);

  if (loading && !isRefreshing) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" p={3}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, height: '100%', width: '100%', overflow: 'auto' }}>
      <Grid container spacing={3}>
        {/* Current Weather Card */}
        <Grid item xs={12}>
          <Card sx={{
            background: 'linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%)',
            color: 'white',
            height: '100%'
          }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6"><DynamicTranslation text={t('weather.current', 'Current Weather')} /></Typography>
                <span>
                  <Tooltip title={isRefreshing ? "Refreshing..." : "Refresh"}>
                    <span>
                      <IconButton
                        onClick={fetchWeatherData}
                        disabled={isRefreshing}
                        sx={{
                          color: 'white',
                          '&.Mui-disabled': {
                            color: 'rgba(255, 255, 255, 0.3)'
                          }
                        }}
                      >
                        {isRefreshing ? <CircularProgress color="inherit" size={18} /> : <Refresh />}
                      </IconButton>
                    </span>
                  </Tooltip>
                </span>
              </Box>

              {error ? (
                <Box display="flex" flexDirection="column" alignItems="center" gap={2} p={3}>
                  <Typography color="error">{error}</Typography>
                  <span>
                    <Tooltip title="Try again">
                      <span>
                        <IconButton
                          onClick={fetchWeatherData}
                          disabled={isRefreshing}
                          sx={{ color: 'white' }}
                        >
                          <Refresh />
                        </IconButton>
                      </span>
                    </Tooltip>
                  </span>
                </Box>
              ) : currentWeather ? (
                <>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                      mb: 2
                    }}
                  >
                    <Box>
                      {getWeatherIcon(currentWeather.conditions)}
                    </Box>
                    <Box>
                      <Typography variant="h3" sx={{ fontWeight: 600 }}>
                        {currentWeather.temperature ? translateNumber(Math.round(currentWeather.temperature), t) : '--'}°C
                      </Typography>
                      <Typography variant="body2">
                        <DynamicTranslation text={currentWeather.conditions || 'No data'} />
                      </Typography>
                      <Typography variant="caption" sx={{ opacity: 0.8 }}>
                        <DynamicTranslation text={t('weather.lastUpdated', 'Last updated')}/> {currentWeather.lastUpdated ? formatDate(new Date(currentWeather.lastUpdated), t) : '--:--'}
                      </Typography>
                    </Box>
                  </Box>

                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Box
                        sx={{
                          bgcolor: 'rgba(255,255,255,0.1)',
                          borderRadius: '8px',
                          p: 2,
                          textAlign: 'center'
                        }}
                      >
                        <WaterDrop fontSize="small" />
                        <Typography variant="caption" display="block">
                          <DynamicTranslation text={t('weather.humidity', 'Humidity')} />
                        </Typography>
                        <Typography variant="body2">
                          {currentWeather.humidity ? formatValueWithUnit(currentWeather.humidity, '%', t) : '--'}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={4}>
                      <Box
                        sx={{
                          bgcolor: 'rgba(255,255,255,0.1)',
                          borderRadius: '8px',
                          p: 2,
                          textAlign: 'center'
                        }}
                      >
                        <Air fontSize="small" />
                        <Typography variant="caption" display="block">
                          <DynamicTranslation text={t('weather.wind', 'Wind')} />
                        </Typography>
                        <Typography variant="body2">
                          {currentWeather.wind_speed ? formatValueWithUnit(currentWeather.wind_speed, 'km/h', t) : '--'}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={4}>
                      <Box
                        sx={{
                          bgcolor: 'rgba(255,255,255,0.1)',
                          borderRadius: '8px',
                          p: 2,
                          textAlign: 'center'
                        }}
                      >
                        <Thermostat fontSize="small" />
                        <Typography variant="caption" display="block">
                          <DynamicTranslation text={t('weather.feelsLike', 'Feels Like')} />
                        </Typography>
                        <Typography variant="body2">
                          {currentWeather.feels_like ? translateNumber(Math.round(currentWeather.feels_like), t) : '--'}°C
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>

                  {weatherBulletin && (
                    <>
                      <Divider sx={{ my: 2, bgcolor: 'rgba(255, 255, 255, 0.2)' }} />
                      <Box sx={{
                        bgcolor: 'rgba(255, 255, 255, 0.1)',
                        p: 2,
                        borderRadius: 2,
                        maxHeight: '200px',
                        overflowY: 'auto',
                        '&::-webkit-scrollbar': {
                          width: '4px',
                        },
                        '&::-webkit-scrollbar-track': {
                          background: 'rgba(255, 255, 255, 0.1)',
                          borderRadius: '2px',
                        },
                        '&::-webkit-scrollbar-thumb': {
                          background: 'rgba(255, 255, 255, 0.3)',
                          borderRadius: '2px',
                        },
                      }}>
                        <Box display="flex" alignItems="center" gap={1} mb={1}>
                          <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}><DynamicTranslation text={t('weather.bulletin', 'Weather Bulletin')} /></Typography>
                          <Tooltip title="Agricultural weather advisory">
                            <span>
                              <IconButton size="small" sx={{ color: 'white', p: 0.5 }}>
                                <InfoIcon sx={{ fontSize: 16 }} />
                              </IconButton>
                            </span>
                          </Tooltip>
                        </Box>
                        <Typography variant="body2" sx={{
                          fontSize: '0.8rem',
                          lineHeight: 1.5,
                          whiteSpace: 'pre-line' // This will preserve line breaks in the bulletin
                        }}>
                          <DynamicTranslation text={weatherBulletin} />
                        </Typography>
                      </Box>
                    </>
                  )}
                </>
              ) : (
                <Typography><DynamicTranslation text={t('weather.noData', 'No weather data available')} /></Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Forecast Section */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom sx={{ mt: 2, color: 'primary.main' }}>
            <DynamicTranslation text={t('weather.forecast', 'Weather Forecast')} />
          </Typography>
          <Box sx={{
            overflowX: 'auto',
            pb: 2,
            '&::-webkit-scrollbar': {
              height: '8px',
            },
            '&::-webkit-scrollbar-track': {
              background: 'rgba(0, 0, 0, 0.1)',
              borderRadius: '4px',
            },
            '&::-webkit-scrollbar-thumb': {
              background: 'rgba(0, 0, 0, 0.2)',
              borderRadius: '4px',
              '&:hover': {
                background: 'rgba(0, 0, 0, 0.3)',
              },
            },
          }}>
            {forecastData.length > 0 ? (
              <Box display="flex" gap={2}>
                {forecastData.map((day, index) => (
                  <Paper
                    key={index}
                    elevation={1}
                    sx={{
                      minWidth: '180px',
                      p: 1.5,
                      bgcolor: 'rgba(46, 125, 50, 0.05)',
                      border: '1px solid rgba(46, 125, 50, 0.1)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: 3
                      }
                    }}
                  >
                    <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
                      <Box textAlign="center">
                        <Typography variant="subtitle1" color="primary.main" fontWeight="bold">
                          <DynamicTranslation text={(() => {
                            const dayDate = new Date(day.monthlyDate || day.date);
                            const dayNameEn = dayDate.toLocaleDateString('en-US', { weekday: 'long', timeZone: 'Asia/Kolkata' }).toLowerCase();
                            return t(`days.${dayNameEn}`, day.monthlyDayOfWeek || dayDate.toLocaleDateString('en-IN', {
                              weekday: 'long',
                              timeZone: 'Asia/Kolkata'
                            }));
                          })()} />
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatDate(day.monthlyDate || day.date, t)}
                        </Typography>
                      </Box>
                      <Box sx={{ p: 0.5, bgcolor: 'rgba(46, 125, 50, 0.1)', borderRadius: '50%', display: 'flex', justifyContent: 'center', alignItems: 'center', width: 40, height: 40 }}>
                        {getWeatherIcon(day.conditions)}
                      </Box>
                      <Typography variant="h6" color="primary.main">
                        {day.temperature?.max ? translateNumber(Math.round(day.temperature.max), t) : '--'}°C
                      </Typography>
                      <Typography variant="body2" color="text.secondary" align="center" sx={{ fontSize: '0.8rem', height: '2.4em', overflow: 'hidden', textOverflow: 'ellipsis', width: '100%' }}>
                        <DynamicTranslation text={day.conditions || 'Unknown'} />
                      </Typography>
                      <Grid container spacing={1} sx={{ mt: 0.5 }}>
                        <Grid item xs={4}>
                          <Box textAlign="center">
                            <WaterDrop sx={{ color: 'primary.main', fontSize: '1rem' }} />
                            <Typography variant="caption" display="block" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                              <DynamicTranslation text={t('weather.humidity', 'Humidity')} />
                            </Typography>
                            <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>{day.humidity}%</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={4}>
                          <Box textAlign="center">
                            <Air sx={{ color: 'primary.main', fontSize: '1rem' }} />
                            <Typography variant="caption" display="block" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                              <DynamicTranslation text={t('weather.wind', 'Wind')} />
                            </Typography>
                            <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>{formatValueWithUnit(day.wind_speed, 'km/h', t)}</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={4}>
                          <Box textAlign="center">
                            <WaterDrop sx={{ color: 'primary.main', fontSize: '1rem' }} />
                            <Typography variant="caption" display="block" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                              <DynamicTranslation text={t('weather.rain', 'Rain')} />
                            </Typography>
                            <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>{formatValueWithUnit(day.precipitation, 'mm', t)}</Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </Box>
                  </Paper>
                ))}
              </Box>
            ) : (
              <Typography color="text.secondary"><DynamicTranslation text={t('weather.forecast.unavailable', 'No forecast data available')} /></Typography>
            )}
          </Box>
        </Grid>

        {forecastData.length > 0 && (
          <Grid item xs={12}>
            <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)' }}>
              <CardContent>
                <Typography variant="h6" mb={2}><DynamicTranslation text={t('weather.fiveDayForecast', '5-Day Forecast')} /></Typography>
                {renderForecast()}
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default Weather;