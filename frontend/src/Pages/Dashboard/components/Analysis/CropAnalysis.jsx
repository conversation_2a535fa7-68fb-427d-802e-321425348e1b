import React from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  LinearProgress,
  Card,
  CardContent
} from '@mui/material';
import {
  LocalOffer,
  WaterDrop,
  Thermostat,
  Agriculture,
  Warning
} from '@mui/icons-material';

const CropAnalysis = () => {
  // Mock data - will be replaced with real data from API
  const cropData = {
    name: 'Wheat',
    variety: 'Winter Wheat',
    plantedDate: '2024-01-15',
    expectedHarvest: '2024-06-15',
    health: 85,
    growth: 65,
    issues: ['Water stress', 'Pest activity']
  };

  const getStatusColor = (health) => {
    if (health >= 80) return 'success.main';
    if (health >= 60) return 'warning.main';
    return 'error.main';
  };

  const getStatusIcon = (health) => {
    if (health >= 80) return <Agriculture sx={{ color: 'success.main' }} />;
    if (health >= 60) return <Warning sx={{ color: 'warning.main' }} />;
    return <Warning sx={{ color: 'error.main' }} />;
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Crop Overview */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Crop Overview
            </Typography>
            <Box display="flex" alignItems="center" gap={2} mb={3}>
              <Agriculture sx={{ fontSize: 48, color: 'primary.main' }} />
              <Box>
                <Typography variant="h4">
                  {cropData.name}
                </Typography>
                <Typography variant="subtitle1" color="text.secondary">
                  {cropData.variety}
                </Typography>
              </Box>
            </Box>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">
                  Planted Date
                </Typography>
                <Typography variant="body1">
                  {new Date(cropData.plantedDate).toLocaleDateString()}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">
                  Expected Harvest
                </Typography>
                <Typography variant="body1">
                  {new Date(cropData.expectedHarvest).toLocaleDateString()}
                </Typography>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Health Metrics */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Health Metrics
            </Typography>
            <Box display="flex" alignItems="center" gap={2} mb={3}>
              {getStatusIcon(cropData.health)}
              <Box>
                <Typography variant="h4">
                  {cropData.health}%
                </Typography>
                <Typography variant="subtitle1" color="text.secondary">
                  Overall Health
                </Typography>
              </Box>
            </Box>
            <Box sx={{ mb: 3 }}>
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">Growth Stage</Typography>
                <Typography variant="body2">{cropData.growth}%</Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={cropData.growth} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: getStatusColor(cropData.health)
                  }
                }}
              />
            </Box>
          </Paper>
        </Grid>

        {/* Soil Metrics */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Soil Metrics
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <WaterDrop sx={{ color: 'info.main' }} />
                      <Typography variant="subtitle2">
                        Moisture
                      </Typography>
                    </Box>
                    <Typography variant="h4">
                      65%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Optimal range: 60-70%
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <Thermostat sx={{ color: 'warning.main' }} />
                      <Typography variant="subtitle2">
                        Temperature
                      </Typography>
                    </Box>
                    <Typography variant="h4">
                      22°C
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Optimal range: 20-25°C
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Issues */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Current Issues
            </Typography>
            {cropData.issues.map((issue, index) => (
              <Card key={index} variant="outlined" sx={{ mb: 2 }}>
                <CardContent>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Warning sx={{ color: 'warning.main' }} />
                    <Typography variant="subtitle1">
                      {issue}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            ))}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CropAnalysis; 