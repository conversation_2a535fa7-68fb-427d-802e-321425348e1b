import React from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  LinearProgress
} from '@mui/material';
import {
  WbSunny,
  WaterDrop,
  Air,
  Thermostat
} from '@mui/icons-material';

const WeatherAnalysis = () => {
  // Mock data - will be replaced with real data from API
  const weatherData = {
    current: {
      temperature: 25,
      humidity: 65,
      windSpeed: 12,
      description: 'Partly cloudy'
    },
    patterns: {
      rainfall: {
        weekly: 45,
        monthly: 180
      },
      temperature: {
        weekly: 75,
        monthly: 70
      }
    },
    forecast: [
      {
        date: '2024-03-24',
        temperature: 28,
        condition: 'Sunny',
        humidity: 60,
        windSpeed: 10
      },
      {
        date: '2024-03-25',
        temperature: 26,
        condition: 'Cloudy',
        humidity: 70,
        windSpeed: 15
      },
      {
        date: '2024-03-26',
        temperature: 24,
        condition: 'Rainy',
        humidity: 80,
        windSpeed: 20
      }
    ]
  };

  const getWeatherIcon = (description) => {
    switch (description.toLowerCase()) {
      case 'sunny':
        return <WbSunny sx={{ fontSize: 40, color: '#ffa000' }} />;
      case 'cloudy':
        return <WbSunny sx={{ fontSize: 40, color: '#757575' }} />;
      case 'rainy':
        return <WaterDrop sx={{ fontSize: 40, color: '#1976d2' }} />;
      default:
        return <WbSunny sx={{ fontSize: 40, color: '#757575' }} />;
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Current Weather */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Current Weather
            </Typography>
            <Box display="flex" alignItems="center" gap={2} mb={3}>
              {getWeatherIcon(weatherData.current.description)}
              <Box>
                <Typography variant="h3">
                  {weatherData.current.temperature}°C
                </Typography>
                <Typography variant="subtitle1" color="text.secondary">
                  {weatherData.current.description}
                </Typography>
              </Box>
            </Box>
            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Box textAlign="center">
                  <WaterDrop sx={{ color: 'info.main', mb: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    Humidity
                  </Typography>
                  <Typography variant="h6">
                    {weatherData.current.humidity}%
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box textAlign="center">
                  <Air sx={{ color: 'info.main', mb: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    Wind
                  </Typography>
                  <Typography variant="h6">
                    {weatherData.current.windSpeed} km/h
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box textAlign="center">
                  <Thermostat sx={{ color: 'info.main', mb: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    Feels Like
                  </Typography>
                  <Typography variant="h6">
                    {weatherData.current.temperature + 2}°C
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Weather Patterns */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Weather Patterns
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle2" gutterBottom>
                      Weekly Rainfall
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      <Box display="flex" justifyContent="space-between" mb={1}>
                        <Typography variant="body2">Progress</Typography>
                        <Typography variant="body2">{weatherData.patterns.rainfall.weekly}%</Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={weatherData.patterns.rainfall.weekly} 
                        sx={{ 
                          height: 8, 
                          borderRadius: 4,
                          backgroundColor: 'rgba(0, 0, 0, 0.1)',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: '#1976d2'
                          }
                        }}
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Monthly Target: {weatherData.patterns.rainfall.monthly}mm
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle2" gutterBottom>
                      Temperature Trend
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      <Box display="flex" justifyContent="space-between" mb={1}>
                        <Typography variant="body2">Weekly Average</Typography>
                        <Typography variant="body2">{weatherData.patterns.temperature.weekly}%</Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={weatherData.patterns.temperature.weekly} 
                        sx={{ 
                          height: 8, 
                          borderRadius: 4,
                          backgroundColor: 'rgba(0, 0, 0, 0.1)',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: '#f44336'
                          }
                        }}
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Monthly Average: {weatherData.patterns.temperature.monthly}°C
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* 3-Day Forecast */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              3-Day Forecast
            </Typography>
            <Grid container spacing={2}>
              {weatherData.forecast.map((day, index) => (
                <Grid item xs={12} sm={4} key={index}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom>
                        {new Date(day.date).toLocaleDateString('en-US', { weekday: 'long' })}
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1} mb={2}>
                        {getWeatherIcon(day.condition)}
                        <Typography variant="h4">
                          {day.temperature}°C
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {day.condition}
                      </Typography>
                      <Box display="flex" justifyContent="space-between" mt={2}>
                        <Typography variant="body2">
                          Humidity: {day.humidity}%
                        </Typography>
                        <Typography variant="body2">
                          Wind: {day.windSpeed} km/h
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default WeatherAnalysis; 