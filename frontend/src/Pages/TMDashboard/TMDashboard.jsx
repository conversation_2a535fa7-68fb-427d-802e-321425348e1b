import React from 'react';
import { Outlet } from 'react-router-dom';
import { Box, Container, AppBar, Toolbar, Typography } from '@mui/material';
import TMSidebar from '../../Components/TMDashboard/TMNavigation';

const TMDashboard = () => {
  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', overflow: 'hidden' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${250}px)` },
          ml: { sm: `${250}px` },
          zIndex: (theme) => theme.zIndex.drawer + 1,
          background: 'linear-gradient(90deg, #2e7d32 0%, #388e3c 100%)',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)'
        }}
      >
        <Toolbar>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            TM Dashboard
          </Typography>
        </Toolbar>
      </AppBar>
      <TMSidebar />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - 250px)` },
          ml: { sm: '0' },  // Removed margin to eliminate space
          backgroundColor: '#f5f5f5',
        }}
      >
        <Toolbar /> {/* This creates space below the AppBar */}
        <Container maxWidth="lg" sx={{ mt: 2, mb: 4 }}>
          <Outlet />
        </Container>
      </Box>
    </Box>
  );
};

export default TMDashboard;