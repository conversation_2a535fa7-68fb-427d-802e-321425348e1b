import React, { useState } from "react";
import "./AssignedFarmers.css";

const AssignedFarmers = ({ farmers, onSelectFarmer, farms, selectedFarmer }) => {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredFarmers = farmers.filter((farmer) => {
    const searchLower = searchQuery.toLowerCase();
    return (
      farmer.name?.toLowerCase().includes(searchLower) ||
      farmer.phone?.toLowerCase().includes(searchLower) ||
      farmer.location?.toLowerCase().includes(searchLower)
    );
  });

  // Get farm count for a farmer
  const getFarmCount = (farmer) => {
    if (!farms || !farmer) return 0;
    // Try different possible ID fields
    return farms.filter(farm => 
      farm.farmerId === farmer.id || 
      farm.farmerId === farmer._id || 
      farm.farmer === farmer.id ||
      farm.farmer === farmer._id
    ).length;
  };

  return (
    <div className="assigned-farmers-container">
      <div className="assigned-farmers-sub-container">
        <p>Assigned Farmers</p>
        
        {/* Search Box */}
        <div className="search-box">
          <input
            type="text"
            placeholder="Search farmers by name, phone, or location"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="assigned-farmers-list">
          {filteredFarmers.length > 0 ? (
            filteredFarmers.map((farmer) => (
              <div
                key={farmer.id || farmer._id}
                className={`farmer-card ${selectedFarmer?.id === farmer.id || selectedFarmer?._id === farmer._id ? 'selected' : ''}`}
                onClick={() => onSelectFarmer(farmer.id || farmer._id)}
              >
                <div className="farmer-info">
                  <span className="farmer-name">{farmer.name}</span>
                  {farmer.location && (
                    <span className="farmer-location">📍 {farmer.location}</span>
                  )}
                  <span className="farmer-mobile">📱 {farmer.phone}</span>
                  <span className="farm-count">
                    🌾 Farms: {getFarmCount(farmer)}
                  </span>
                </div>
              </div>
            ))
          ) : searchQuery ? (
            <div className="no-farmers-message">No farmers found matching "{searchQuery}"</div>
          ) : (
            <div className="no-farmers-message">No farmers assigned yet!</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AssignedFarmers;
