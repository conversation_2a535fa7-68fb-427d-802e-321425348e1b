.assigned-farmers-card {
  grid-row: span 9;
  background-color: #94ec79;
  border-radius: 16px;
  overflow: hidden;
}

.assigned-farmers-card p {
  text-align: center;
  padding: 16px;
  font-size: 24px;
  font-weight: 600;
  color: #fafafa;
  border-radius: 16px 16px 0px 0px;
  background: linear-gradient(45deg, #006400, #00b500, #00b500, #006400);
  background-size: 400% 400%;
  animation: gradientAnimation 5s ease infinite;
}

.assigned-farmers-card ul {
  display: flex;
  flex-direction: column;
  gap: 50px;
  padding: 16px 32px;
  line-height: 32px;
  list-style: decimal;
}

.assigned-farmers-entries {
  flex: 1;
  height: 100vh;
  overflow-y: auto;
  padding: 16px;
  direction: rtl;
}

.assigned-farmers-entries * {
  direction: ltr;
}

.assigned-farmers-entries::-webkit-scrollbar {
  width: 8px;
}

.assigned-farmers-entries::-webkit-scrollbar-thumb {
  background-color: #228b22;
}

.assigned-farmers-entries::-webkit-scrollbar-thumb:hover {
  background-color: #1e7b1e;
}

.assigned-farmers-entries::-webkit-scrollbar-button {
  display: none;
}

.assigned-farmers-entries li {
  background-color: white;
  text-align: center;
  border-radius: 16px;
  font-size: 20px;
  font-weight: 500;
}

.button-container {
  display: flex;
  gap: 30px;
  justify-content: center;
  padding: 20px;
}

.button-container button {
  font-size: 20px;
  font-weight: 600;
  padding: 5px;
  border-radius: 10px;
  background-color: #058721;
  border: none;
  color: white;
  transition: transform 0.3s ease;
}

.button-container button:hover {
  transform: scale(1.1);
}

.task-info .disabled {
  background-color: red;
}

.assigned-farmers-container {
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.assigned-farmers-sub-container {
  width: 100%;
}

.assigned-farmers-sub-container p {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

.assigned-farmers-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.farmer-card {
  padding: 15px;
  border-radius: 8px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  margin-bottom: 10px;
}

.farmer-card:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  border-color: #4CAF50;
}

.farmer-card.selected {
  background: #E8F5E9;
  border-color: #4CAF50;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.farmer-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.farmer-name {
  font-weight: 600;
  color: #2c3e50;
}

.farmer-id {
  font-size: 0.9rem;
  color: #666;
}

.farmer-location {
  font-size: 0.9rem;
  color: #666;
  display: flex;
  align-items: center;
  gap: 5px;
}

.farmer-mobile {
  font-size: 0.9rem;
  color: #666;
  display: flex;
  align-items: center;
  gap: 5px;
}

.farm-count {
  font-size: 0.9rem;
  color: #4CAF50;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-top: 5px;
}

.no-farmers-message {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.search-box {
  margin: 15px 0;
  padding: 0 10px;
  position: relative;
}

.search-box::before {
  content: '🔍';
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  color: #666;
  pointer-events: none;
}

.search-box input {
  width: 100%;
  padding: 12px 15px 12px 45px;
  border: 2px solid #e3e3e3;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
}

.search-box input:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
  background-color: #fff;
}

.search-box input::placeholder {
  color: #999;
  font-style: italic;
}