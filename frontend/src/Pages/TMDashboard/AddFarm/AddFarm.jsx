import React, { useState } from "react";
import "./AddFarm.css";
import locationData from "../../Profile/data.json";
import axios from 'axios';
import { useAuth } from '../../../contexts/AuthContext';

const AddFarm = ({ farmer, onFarmAdded }) => {
  const { currentUser } = useAuth();
  const [stateShowDropDown, setShowStateDropDown] = useState(false);
  const [districtShowDropDown, setShowDistrictDropDown] = useState(false);
  const [metricDropDown, setMetricDropDown] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const [selectedState, setSelectedState] = useState("");
  const [selectedDistrict, setSelectedDistrict] = useState("");
  const [selectedMetric, setSelectedMetric] = useState("");
  const [farmSize, setFarmSize] = useState("");

  //* Getting districts based on selected state:

  const selectedStateData = locationData.states.find(
    (s) => s.state.trim() === selectedState.trim()
  );
  const districts = selectedStateData ? selectedStateData.districts : [];

  //* For checking if all form fields are valid and filled:

  const isFormValid =
    selectedState && selectedDistrict && selectedMetric && farmSize.trim();

  const handleAddFarm = async (e) => {
    e.preventDefault();
    if (!isFormValid) return;

    const farmData = {
      farmerId: farmer.id,
      farmerName: farmer.name,
      state: selectedState,
      district: selectedDistrict,
      farmSize: `${farmSize} ${selectedMetric}`,
      cropType: 'Not specified',
      irrigationStatus: 'Not specified'
    };

    console.log('Submitting farm data:', farmData);

    if (window.confirm("Are you sure you want to add this farm?")) {
      setLoading(true);
      setError("");
      
      try {
        const idToken = await currentUser.firebaseUser.getIdToken();
        const response = await axios.post(
          `${import.meta.env.VITE_API_URL}/api/farms`,
          farmData,
          {
            headers: { Authorization: `Bearer ${idToken}` }
          }
        );

        console.log('Farm creation response:', response.data);

        const newFarm = response.data.data || response.data;
        
        // Reset form
        setSelectedState("");
        setSelectedDistrict("");
        setSelectedMetric("");
        setFarmSize("");
        
        // Notify parent component with the complete farm data
        if (onFarmAdded) {
          onFarmAdded(newFarm);
        }
      } catch (error) {
        console.error('Error adding farm:', error);
        setError(error.response?.data?.message || 'Failed to add farm. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div className="add-farm-container">
      <div className="add-farm-sub-container">
        <p>Add Farm</p>
        <h2 className="add-farm-farmer-identity">
          {farmer.name} (ID: {farmer.id})
        </h2>
        {error && <div className="error-message">{error}</div>}
        <form className="add-farm-form">
          <div className="add-farm-state-name-container">
            <label>
              State
              <span className="required-asterisk"> *</span>
            </label>
            <div
              className="add-farm-select-btn"
              onClick={() => {
                setShowStateDropDown(!stateShowDropDown);
                setShowDistrictDropDown(false);
                setMetricDropDown(false);
              }}
            >
              <span>{selectedState || "Select State"}</span>
              <i className="fa-solid fa-angle-down"></i>
            </div>
            {stateShowDropDown && (
              <div className="add-farm-content">
                <ul className="add-farm-options">
                  {locationData.states.map((state, index) => (
                    <li
                      key={index}
                      onClick={() => {
                        setSelectedState(state.state);
                        setShowStateDropDown(false);
                        setSelectedDistrict(""); // Reset district when state changes
                      }}
                    >
                      {state.state}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
          <div className="add-farm-district-name-container">
            <label>
              District
              <span className="required-asterisk"> *</span>
            </label>
            <div
              className={`add-farm-select-district-btn ${
                selectedState ? "enabled" : "disabled"
              }`}
              onClick={() => {
                if (selectedState) {
                  setShowDistrictDropDown(!districtShowDropDown);
                  setShowStateDropDown(false);
                  setMetricDropDown(false);
                }
              }}
            >
              {selectedState ? (
                <span>{selectedDistrict || "Select District"}</span>
              ) : (
                <span>Select State First</span>
              )}
              {selectedState && <i className="fa-solid fa-angle-down"></i>}
            </div>
            {districtShowDropDown && (
              <div className="add-farm-content">
                <ul className="add-farm-options">
                  {districts.map((district, index) => (
                    <li
                      key={index}
                      onClick={() => {
                        setSelectedDistrict(district);
                        setShowDistrictDropDown(false);
                      }}
                    >
                      {district}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
          {/* Farm Size */}
          <div className="farm-size-container">
            <label>
              Farm Size
              <span className="required-asterisk"> *</span>
            </label>
            <div className="farm-size-sub-container">
              <input
                type="number"
                min="0"
                step="0.01"
                placeholder="Enter size"
                value={farmSize}
                onChange={(e) => setFarmSize(e.target.value)}
              />
              <div
                className="add-farm-size-select-btn"
                onClick={() => {
                  setShowDistrictDropDown(false);
                  setShowStateDropDown(false);
                  setMetricDropDown(!metricDropDown);
                }}
              >
                <span>{selectedMetric || "Select Metric"}</span>
                <i className="fa-solid fa-angle-down"></i>
                {metricDropDown && (
                  <div className="add-farm-size-content">
                    <ul className="add-farm-size-options">
                      {["yards", "hectares", "acres"].map((metric, index) => (
                        <li
                          key={index}
                          onClick={() => {
                            setSelectedMetric(metric);
                            setMetricDropDown(false);
                          }}
                        >
                          {metric}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
          <button
            type="submit"
            className={`add-farm-button-tm ${
              isFormValid && !loading ? "enabled" : "disabled"
            }`}
            disabled={!isFormValid || loading}
            onClick={handleAddFarm}
          >
            {loading ? "Adding Farm..." : "Add Farm"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default AddFarm;
