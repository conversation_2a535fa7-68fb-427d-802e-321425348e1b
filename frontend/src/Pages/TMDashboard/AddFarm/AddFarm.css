* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.add-farm-container {
  margin-top: 2rem;
  padding: 0 64px 32px 64px;
  background-color: transparent;
}

.add-farm-sub-container {
  background-color: #e9fac8;
  height: 100vh;
  border-radius: 16px;
}

.add-farm-sub-container p {
  text-align: center;
  padding: 16px;
  font-size: 24px;
  font-weight: 600;
  color: #fafafa;
  border-radius: 16px 16px 0px 0px;
  background: linear-gradient(45deg, #006400, #00b500, #00b500, #006400);
  background-size: 400% 400%;
  animation: gradientAnimation 5s ease infinite;
}

.add-farm-farmer-identity {
  text-align: center;
  padding-top: 1rem;
  font-weight: 600;
}

.add-farm-form {
  padding: 40px 0px 20px 0px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  row-gap: 3rem;
}

.required-asterisk {
  color: red;
  font-weight: 600;
  font-size: 1.3rem;
}

.add-farm-form label {
  font-weight: 600;
  font-size: 1.2rem;
}

.add-farm-state-name-container {
  width: 40%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: relative;
}
.add-farm-district-name-container {
  width: 40%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: relative;
}

.add-farm-state-name-container label {
  display: block;
  font-weight: 500;
}
.add-farm-district-name-container label {
  display: block;
  font-weight: 500;
}

.add-farm-select-btn,
.add-farm-options li {
  display: flex;
  cursor: pointer;
  align-items: center;
}

.add-farm-select-btn {
  border: 2px solid transparent;
  padding: 1rem;
  width: 100%;
  border-radius: 1rem;
  cursor: pointer;
  font-size: 1.2rem;
  background-color: #c0e96e;
  justify-content: space-between;
  cursor: pointer;
}

.add-farm-content {
  background: #c0e96e;
  margin-top: 15px;
  padding: 10px 20px;
  font-size: 1.2rem;
  width: 100%;
  border-radius: 1rem;
  position: absolute;
  top: 100px;
  z-index: 20;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
}

.add-farm-content .add-farm-options {
  max-height: 200px;
  overflow-y: scroll;
}

.add-farm-options::-webkit-scrollbar {
  width: 7px;
}

.add-farm-options::-webkit-scrollbar-track {
  background: #c0e96e;
  border-radius: 25px;
}

.add-farm-options::-webkit-scrollbar-thumb {
  background: #dfff9e;
  border-radius: 25px;
}

.add-farm-options li {
  height: 50px;
  padding: 0 13px;
  width: 95%;
  border-radius: 5px;
  /* height: fit-content; */
}

.add-farm-options li:hover {
  background: #dfff9e;
}

.farm-size-container {
  width: 40%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.farm-size-container label {
  display: block;
  font-weight: 500;
}

.farm-size-sub-container {
  display: flex;
  column-gap: 1.5rem;
}

.farm-size-sub-container input {
  border: 2px solid transparent;
  padding: 1rem;
  width: 50%;
  border-radius: 1rem;
  font-size: 1.2rem;
  background-color: #c0e96e;
}

.add-farm-select-district-btn.enabled {
  border: 2px solid transparent;
  padding: 1rem;
  width: 100%;
  border-radius: 1rem;
  cursor: pointer;
  font-size: 1.2rem;
  background-color: #c0e96e;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}

.add-farm-select-district-btn.disabled {
  border: 2px solid transparent;
  padding: 1rem;
  width: 100%;
  border-radius: 1rem;
  cursor: pointer;
  font-size: 1.2rem;
  background-color: #c0e96e;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}

.add-farm-size-select-btn {
  border: 2px solid transparent;
  padding: 1rem;
  width: 50%;
  border-radius: 1rem;
  cursor: pointer;
  font-size: 1.2rem;
  background-color: #c0e96e;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  position: relative;
}

.add-farm-size-content {
  background: #c0e96e;
  padding: 10px 20px;
  font-size: 1.2rem;
  width: 100%;
  border-radius: 1rem;
  position: absolute;
  top: 70px;
  z-index: 20;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
  left: 0;
}

.add-farm-size-content .add-farm-size-options {
  max-height: 200px;
  overflow-y: scroll;
}

.add-farm-size-options li {
  height: 50px;
  padding: 0 13px;
  width: 95%;
  border-radius: 5px;
  place-content: center;
}

.add-farm-size-options li:hover {
  background: #dfff9e;
}

.add-farm-button-tm.disabled {
  font-size: 1.5rem;
  padding: 1rem;
  border: none;
  background-color: #c0e96e;
  color: gray;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 600;
  transition: transform 0.3s ease;
  margin-top: 3rem;
  cursor: not-allowed;
  transition: background-color 0.4s ease, color 0.4s ease;
}

.add-farm-button-tm.enabled {
  font-size: 1.5rem;
  padding: 1rem;
  border: none;
  background-color: #006400;
  color: white;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 600;
  margin-top: 3rem;
  transition: background-color 0.4s ease, color 0.4s ease, transform 0.3s ease;
}

.add-farm-button-tm.enabled:hover {
  transform: scale(1.1);
}
