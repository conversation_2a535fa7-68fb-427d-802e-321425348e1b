import React, { useState, useEffect } from "react";
import "./FarmsInfo.css";
import axios from 'axios';
import { useAuth } from '../../../contexts/AuthContext';
import FarmDetails from '../FarmDetails/FarmDetails';

const FarmsInfo = ({ farms, farmer, setFarms }) => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [selectedFarmId, setSelectedFarmId] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  const handleDeleteFarm = async (farmId) => {
    if (!window.confirm("Are you sure you want to delete this farm?")) {
      return;
    }

    setLoading(true);
    setError("");

    try {
      const idToken = await currentUser.firebaseUser.getIdToken();
      await axios.delete(
        `${import.meta.env.VITE_API_URL}/api/farms/${farmId}`,
        {
          headers: { Authorization: `Bearer ${idToken}` }
        }
      );

      // Update local state
      setFarms(farms.filter((farm) => farm._id !== farmId));
      if (selectedFarmId === farmId) {
        setSelectedFarmId(null);
        setShowDetails(false);
      }
    } catch (error) {
      console.error('Error deleting farm:', error);
      setError('Failed to delete farm. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleFarmClick = (farmId) => {
    console.log('Farm clicked:', farmId);
    if (selectedFarmId === farmId) {
      setSelectedFarmId(null);
      setShowDetails(false);
    } else {
      setSelectedFarmId(farmId);
      setShowDetails(true);
    }
  };

  console.log('Current farms:', farms);
  console.log('Selected farm ID:', selectedFarmId);
  console.log('Show details:', showDetails);

  return (
    <div className="farms-info-container">
      <div className="farms-info-sub-container">
        <p>Farms Info</p>
        <h2 className="farmer-identity">
          {farmer.name} (ID: {farmer.id})
        </h2>
        {error && <div className="error-message">{error}</div>}
        <div
          className={`farm-cards-container ${
            farms.length > 0 ? "grid-layout" : "full-width"
          } ${showDetails ? 'with-details' : ''}`}
        >
          {farms.length > 0 ? (
            farms.map((farm, index) => (
              <div 
                className={`farm-details-card ${selectedFarmId === farm._id ? 'selected' : ''}`} 
                key={farm._id}
                onClick={() => handleFarmClick(farm._id)}
              >
                <p>Farm {index + 1}</p>
                <div className="farm-info-delete-container">
                  <div className="farm-details">
                    <span>
                      <strong>Farm ID:</strong> {farm._id}
                    </span>
                    <span>
                      <strong>State:</strong> {farm.state}
                    </span>
                    <span>
                      <strong>District:</strong> {farm.district}
                    </span>
                    <span>
                      <strong>Farm Size:</strong> {farm.farmSize}
                    </span>
                    <span>
                      <strong>Crop Type:</strong> {farm.cropType || 'Not specified'}
                    </span>
                    <span>
                      <strong>Irrigation Status:</strong>{" "}
                      {farm.irrigationStatus}
                    </span>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteFarm(farm._id);
                    }}
                    disabled={loading}
                    className="delete-button"
                  >
                    {loading ? "Deleting..." : "Delete Farm"}
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className="no-data-message">No Farm Data Available!</div>
          )}
        </div>
        
        {showDetails && selectedFarmId && (
          <div className="selected-farm-details">
            <FarmDetails farmId={selectedFarmId} />
          </div>
        )}
      </div>
    </div>
  );
};

export default FarmsInfo;
