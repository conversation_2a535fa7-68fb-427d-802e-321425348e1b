* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.farms-info-container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.farms-info-sub-container {
  width: 100%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.farms-info-sub-container p {
  text-align: center;
  padding: 16px;
  font-size: 24px;
  font-weight: 600;
  color: #fafafa;
  border-radius: 16px 16px 0px 0px;
  background: linear-gradient(45deg, #006400, #00b500, #00b500, #006400);
  background-size: 400% 400%;
  animation: gradientAnimation 5s ease infinite;
}

.farm-cards-container {
  display: grid;
  gap: 20px;
  margin-top: 20px;
  transition: all 0.3s ease;
}

.grid-layout {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.grid-layout.with-details {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

.farm-cards-container.grid-layout::-webkit-scrollbar {
  width: 8px;
}

.farm-cards-container.grid-layout::-webkit-scrollbar-thumb {
  background-color: #228b22;
  border-radius: 4px;
}

.farm-cards-container.grid-layout::-webkit-scrollbar-thumb:hover {
  background-color: #1e7b1e;
}

.farm-cards-container.grid-layout::-webkit-scrollbar-button {
  display: none;
}

.farm-details-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(240, 255, 240, 0.9));
  border: 1px solid rgba(0, 100, 0, 0.1);
  border-radius: 10px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.farm-details-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 100, 0, 0.15);
  border-color: rgba(0, 100, 0, 0.3);
}

.farm-details-card.selected {
  border: 2px solid #006400;
  background: linear-gradient(145deg, rgba(240, 255, 240, 0.95), rgba(230, 255, 230, 0.95));
  box-shadow: 0 4px 12px rgba(0, 100, 0, 0.2);
}

.farm-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.farm-details span {
  color: #333;
  font-size: 0.9rem;
}

.farm-details strong {
  color: #006400;
  margin-right: 5px;
}

.farm-info-delete-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.delete-button {
  background-color: #ff4444;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 0.9rem;
}

.delete-button:hover {
  background-color: #cc0000;
}

.delete-button:disabled {
  background-color: #ffaaaa;
  cursor: not-allowed;
}

.error-message {
  color: #cc0000;
  background-color: #ffe6e6;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
  border: 1px solid #ffcccc;
}

.no-data-message {
  text-align: center;
  padding: 20px;
  color: #666;
  background: rgba(0, 100, 0, 0.05);
  border-radius: 10px;
  grid-column: 1 / -1;
}

.selected-farm-details {
  grid-column: 1 / -1;
  margin-top: 20px;
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.farmer-identity {
  color: #006400;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #00640033;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@media (max-width: 768px) {
  .grid-layout {
    grid-template-columns: 1fr;
  }
  
  .grid-layout.with-details {
    grid-template-columns: 1fr;
  }
  
  .farms-info-container {
    padding: 10px;
  }
}
