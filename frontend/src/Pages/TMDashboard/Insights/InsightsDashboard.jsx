import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Divider,
} from '@mui/material';
import {
  Lightbulb as LightbulbIcon,
  TrendingUp as TrendingUpIcon,
  LocalOffer as LocalOfferIcon,
  BugReport as PestIcon,  // Updated: using BugReport instead of invalid Pest export
  MonetizationOn as MonetizationOnIcon,
  Campaign as CampaignIcon,
} from '@mui/icons-material';

const InsightsDashboard = () => {
  const [recommendations, setRecommendations] = useState([]);
  const [marketPrices, setMarketPrices] = useState([]);
  const [aiInsights, setAiInsights] = useState([]);

  useEffect(() => {
    // TODO: Implement API calls to fetch real data
    // Mock data for demonstration
    setRecommendations([
      {
        id: 1,
        type: 'crop',
        title: 'Optimal Crop Selection',
        description: 'Based on soil analysis, consider planting wheat in the next season',
        confidence: 85,
      },
      {
        id: 2,
        type: 'pest',
        title: 'Pest Control Alert',
        description: 'High risk of stem borer infestation. Implement preventive measures.',
        confidence: 75,
      },
    ]);

    setMarketPrices([
      {
        crop: 'Wheat',
        price: 2200,
        trend: 'up',
        change: '+5%',
      },
      {
        crop: 'Rice',
        price: 3100,
        trend: 'down',
        change: '-2%',
      },
    ]);

    setAiInsights([
      {
        id: 1,
        category: 'Weather Impact',
        message: 'Expected 15% increase in rainfall next month. Plan irrigation accordingly.',
      },
      {
        id: 2,
        category: 'Market Trend',
        message: 'Wheat prices expected to rise by 10% in the next quarter.',
      },
    ]);
  }, []);

  const renderRecommendations = () => (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" mb={2}>
          <LightbulbIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h6">AI Recommendations</Typography>
        </Box>
        <List>
          {recommendations.map((rec) => (
            <React.Fragment key={rec.id}>
              <ListItem>
                <ListItemIcon>
                  {rec.type === 'crop' ? <LocalOfferIcon /> : <PestIcon />}
                </ListItemIcon>
                <ListItemText
                  primary={rec.title}
                  secondary={rec.description}
                />
                <Chip
                  label={`${rec.confidence}% confidence`}
                  color="primary"
                  size="small"
                />
              </ListItem>
              <Divider />
            </React.Fragment>
          ))}
        </List>
      </CardContent>
    </Card>
  );

  const renderMarketPrices = () => (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" mb={2}>
          <MonetizationOnIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h6">Market Prices</Typography>
        </Box>
        <Grid container spacing={2}>
          {marketPrices.map((item) => (
            <Grid item xs={12} sm={6} key={item.crop}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="subtitle1">{item.crop}</Typography>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Typography variant="h6">₹{item.price}/quintal</Typography>
                  <Chip
                    icon={<TrendingUpIcon />}
                    label={item.change}
                    color={item.trend === 'up' ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </CardContent>
    </Card>
  );

  const renderAIInsights = () => (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center">
            <CampaignIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="h6">AI Insights</Typography>
          </Box>
          <Button variant="outlined" size="small">
            Generate New Insights
          </Button>
        </Box>
        <List>
          {aiInsights.map((insight) => (
            <React.Fragment key={insight.id}>
              <ListItem>
                <ListItemText
                  primary={insight.category}
                  secondary={insight.message}
                />
              </ListItem>
              <Divider />
            </React.Fragment>
          ))}
        </List>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2, display: 'flex', alignItems: 'center' }}>
            <LightbulbIcon sx={{ fontSize: 40, mr: 2 }} />
            <Typography variant="h5">AI-Powered Insights</Typography>
          </Paper>
        </Grid>

        {/* AI Recommendations */}
        <Grid item xs={12} md={6}>
          {renderRecommendations()}
        </Grid>

        {/* Market Prices */}
        <Grid item xs={12} md={6}>
          {renderMarketPrices()}
        </Grid>

        {/* AI Insights */}
        <Grid item xs={12}>
          {renderAIInsights()}
        </Grid>
      </Grid>
    </Box>
  );
};

export default InsightsDashboard;

