import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useAuth } from '../../../contexts/AuthContext';
import axios from 'axios';

const IOTConfig = () => {
  const { currentUser } = useAuth();
  const [sensors, setSensors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingSensor, setEditingSensor] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    type: '',
    location: '',
    status: 'active',
  });

  const sensorTypes = [
    'Temperature',
    'Humidity',
    'Soil Moisture',
    'Light',
    'Wind Speed',
    'Rainfall',
    'pH Level',
  ];

  const fetchSensors = async () => {
    try {
      setLoading(true);
      const userData = JSON.parse(localStorage.getItem('user'));
      if (!userData) {
        throw new Error('User not authenticated');
      }

      const response = await axios.get('/api/iot/sensors', {
        headers: { 
          Authorization: `Bearer ${userData.id}`,
          'Content-Type': 'application/json'
        }
      });
      setSensors(response.data.data || []);
      setError(null);
    } catch (err) {
      console.error('Error fetching sensors:', err);
      setError('Failed to load sensors. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSensors();
  }, [currentUser]);

  const handleOpenDialog = (sensor = null) => {
    if (sensor) {
      setEditingSensor(sensor);
      setFormData({
        name: sensor.name,
        type: sensor.type,
        location: sensor.location,
        status: sensor.status,
      });
    } else {
      setEditingSensor(null);
      setFormData({
        name: '',
        type: '',
        location: '',
        status: 'active',
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingSensor(null);
    setFormData({
      name: '',
      type: '',
      location: '',
      status: 'active',
    });
  };

  const handleSubmit = async () => {
    try {
      const userData = JSON.parse(localStorage.getItem('user'));
      if (!userData) {
        throw new Error('User not authenticated');
      }

      if (editingSensor) {
        await axios.put(`/api/iot/sensors/${editingSensor.id}`, formData, {
          headers: { 
            Authorization: `Bearer ${userData.id}`,
            'Content-Type': 'application/json'
          }
        });
      } else {
        await axios.post('/api/iot/sensors', formData, {
          headers: { 
            Authorization: `Bearer ${userData.id}`,
            'Content-Type': 'application/json'
          }
        });
      }
      fetchSensors();
      handleCloseDialog();
    } catch (err) {
      console.error('Error saving sensor:', err);
      setError('Failed to save sensor. Please try again.');
    }
  };

  const handleDelete = async (sensorId) => {
    if (window.confirm('Are you sure you want to delete this sensor?')) {
      try {
        const userData = JSON.parse(localStorage.getItem('user'));
        if (!userData) {
          throw new Error('User not authenticated');
        }

        await axios.delete(`/api/iot/sensors/${sensorId}`, {
          headers: { 
            Authorization: `Bearer ${userData.id}`,
            'Content-Type': 'application/json'
          }
        });
        fetchSensors();
      } catch (err) {
        console.error('Error deleting sensor:', err);
        setError('Failed to delete sensor. Please try again.');
      }
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h5">IoT Configuration</Typography>
          <Box>
            <IconButton onClick={fetchSensors} sx={{ mr: 1 }}>
              <RefreshIcon />
            </IconButton>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenDialog()}
            >
              Add Sensor
            </Button>
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Location</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Last Updated</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {sensors.map((sensor) => (
                <TableRow key={sensor.id}>
                  <TableCell>{sensor.name}</TableCell>
                  <TableCell>{sensor.type}</TableCell>
                  <TableCell>{sensor.location}</TableCell>
                  <TableCell>
                    <Typography
                      color={sensor.status === 'active' ? 'success.main' : 'error.main'}
                    >
                      {sensor.status}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    {new Date(sensor.lastUpdated).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleOpenDialog(sensor)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDelete(sensor.id)}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      <Dialog open={openDialog} onClose={handleCloseDialog}>
        <DialogTitle>
          {editingSensor ? 'Edit Sensor' : 'Add New Sensor'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Sensor Name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Sensor Type</InputLabel>
                <Select
                  value={formData.type}
                  label="Sensor Type"
                  onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                >
                  {sensorTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Location"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  label="Status"
                  onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                  <MenuItem value="maintenance">Maintenance</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingSensor ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default IOTConfig; 