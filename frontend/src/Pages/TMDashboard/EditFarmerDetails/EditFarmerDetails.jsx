import { useState, useEffect } from "react";
import axios from "axios";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSearchParams } from "react-router-dom";
import isEqual from "lodash.isequal";
import { useNavigate } from "react-router-dom";
import {
  Paper,
  Typography,
  Box,
  TextField,
  Button,
  Stack,
} from "@mui/material";

const editSchema = z.object({
  name: z.string().min(1, "Name is required"),
  mobile: z.string().regex(/^\d{10}$/, "Mobile number must be 10 digits"),
  aadharNumber: z.string().regex(/^\d{12}$/, "Aadhar number must be 12 digits"),
  panNumber: z
    .string()
    .regex(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, "Invalid PAN number format"),
  geoLocation: z.object({
    type: z.literal("Point"), // only allow "Point"
    coordinates: z
      .tuple([z.number(), z.number()])
      .refine(
        ([lng, lat]) => lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90,
        {
          message: "Coordinates must be valid longitude and latitude",
        }
      ),
  }),
  state: z.string().min(1, "State is required"),
  district: z.string().min(1, "District is required"),
  farmSize: z.number().min(0, "Farm size must be a positive number"),
  cropType: z.string().min(1, "Crop type is required"),
  irrigationStatus: z.string().min(1, "Irrigation status is required"),
});

const EditFarmerDetails = () => {
  const [farmerData, setFarmerData] = useState(null);
  const [searchParams] = useSearchParams();
  const farmerId = searchParams.get("farmer");
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    watch,
  } = useForm({
    resolver: zodResolver(editSchema),
    defaultValues: {
      name: "",
      mobile: "",
      aadharNumber: "",
      panNumber: "",
      geoLocation: { type: "Point", coordinates: [0, 0] },
      state: "",
      district: "",
      farmSize: 0,
      cropType: "",
      irrigationStatus: "",
    },
    mode: "onChange",
  });

  //* Fetching farmer data from backend to prefill the form
  useEffect(() => {
    const fetchFarmerData = async () => {
      if (!farmerId) return;

      try {
        const response = await axios.get(`/api/farmers/${farmerId}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        });
        const fetchedData = response.data.data;
        setFarmerData(fetchedData);
      } catch (error) {
        console.error("Fetch error:", error);
        navigate(-1);
      }
    };

    fetchFarmerData();
  }, [farmerId, reset]);

  //* Reset form after data is fetched
  useEffect(() => {
    if (farmerData) {
      reset(farmerData);
    }
  }, [farmerData, reset]);

  const watchedFields = watch();

  const normalize = (data) => ({
    ...data,
    farmSize: Number(data.farmSize),
    geoLocation: {
      type: data.geoLocation.type,
      coordinates: [
        Number(data.geoLocation.coordinates[0]),
        Number(data.geoLocation.coordinates[1]),
      ],
    },
  });

  const isUnchanged =
    farmerData && isEqual(normalize(watchedFields), normalize(farmerData));
  const isButtonDisabled = !isValid || isUnchanged;

  const onSubmit = async (data) => {
    try {
      await axios.put(`/api/farmers/${farmerId}`, data, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });
      alert("Farmer updated successfully");
      navigate(-1);
    } catch (error) {
      console.error("Update failed:", error);
      alert(error.response?.data?.message || "Update failed");
    }
  };

  return (
    <>
      <Paper sx={{ padding: 2 }}>
        <Typography variant="h6" gutterBottom>
          Edit Farmer Details
        </Typography>
        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          noValidate
          sx={{ mt: 4, px: 4 }}
        >
          <Stack direction="row" spacing={2}>
            <TextField
              label="Full Name"
              fullWidth
              margin="normal"
              {...register("name")}
              error={!!errors.name}
              helperText={errors.name?.message}
              InputLabelProps={{ shrink: true }}
            />

            <TextField
              label="Mobile Number"
              fullWidth
              margin="normal"
              {...register("mobile")}
              error={!!errors.mobile}
              helperText={errors.mobile?.message}
              InputLabelProps={{ shrink: true }}
            />
          </Stack>

          <Stack direction="row" spacing={2} mt={4}>
            <TextField
              label="Aadhar Number"
              fullWidth
              margin="normal"
              {...register("aadharNumber")}
              error={!!errors.aadharNumber}
              helperText={errors.aadharNumber?.message}
              InputLabelProps={{ shrink: true }}
            />

            <TextField
              label="Pan Number"
              fullWidth
              margin="normal"
              {...register("panNumber")}
              error={!!errors.panNumber}
              helperText={errors.panNumber?.message}
              InputLabelProps={{ shrink: true }}
            />
          </Stack>

          <Stack direction="row" spacing={2} mt={4}>
            <TextField
              label="Longitude"
              fullWidth
              margin="normal"
              type="number"
              {...register("geoLocation.coordinates.0", {
                valueAsNumber: true,
              })}
              error={!!errors.geoLocation?.coordinates}
              helperText={
                errors.geoLocation?.coordinates?.message ||
                errors.geoLocation?.coordinates?.[0]?.message
              }
              InputLabelProps={{ shrink: true }}
            />

            <TextField
              label="Latitude"
              fullWidth
              margin="normal"
              type="number"
              {...register("geoLocation.coordinates.1", {
                valueAsNumber: true,
              })}
              error={!!errors.geoLocation?.coordinates}
              helperText={
                errors.geoLocation?.coordinates?.message ||
                errors.geoLocation?.coordinates?.[1]?.message
              }
              InputLabelProps={{ shrink: true }}
            />
          </Stack>

          <Stack direction="row" spacing={2} mt={4}>
            <TextField
              label="State"
              fullWidth
              margin="normal"
              {...register("state")}
              error={!!errors.state}
              helperText={errors.state?.message}
              InputLabelProps={{ shrink: true }}
            />

            <TextField
              label="District"
              fullWidth
              margin="normal"
              {...register("district")}
              error={!!errors.district}
              helperText={errors.district?.message}
              InputLabelProps={{ shrink: true }}
            />
          </Stack>

          <TextField
            label="Farm Size (in acres)"
            fullWidth
            margin="normal"
            {...register("farmSize", { valueAsNumber: true })}
            type="number"
            error={!!errors.farmSize}
            helperText={errors.farmSize?.message}
            sx={{ mt: 4 }}
            InputLabelProps={{ shrink: true }}
          />

          <TextField
            label="Crop Type"
            fullWidth
            margin="normal"
            {...register("cropType")}
            error={!!errors.cropType}
            helperText={errors.cropType?.message}
            sx={{ mt: 4 }}
            InputLabelProps={{ shrink: true }}
          />

          <TextField
            label="Irrigation Status"
            fullWidth
            margin="normal"
            {...register("irrigationStatus")}
            error={!!errors.irrigationStatus}
            helperText={errors.irrigationStatus?.message}
            sx={{ mt: 4 }}
            InputLabelProps={{ shrink: true }}
          />

          <Box sx={{ display: "flex", justifyContent: "center", mt: 3 }}>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={isButtonDisabled}
              sx={{ width: "50%" }}
            >
              Save Changes
            </Button>
          </Box>
        </Box>
      </Paper>
    </>
  );
};

export default EditFarmerDetails;
