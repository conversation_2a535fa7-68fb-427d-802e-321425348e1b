import React from "react";
import "./TasksPending.css";

// Mock data for emulator mode
const MOCK_TASKS = [
  "Schedule soil testing",
  "Review irrigation system",
  "Plan crop rotation"
];

const TasksPending = ({ farmer }) => {
  // If no farmer is selected, show a message
  if (!farmer) {
    return (
      <div className="tasks-pending-card">
        <h2>Tasks Pending</h2>
        <div className="no-tasks-message">
          Select a farmer to view pending tasks
        </div>
      </div>
    );
  }

  // Use mock data if pendingTasks is not available
  const pendingTasks = farmer.pendingTasks || (farmer.isEmulator ? MOCK_TASKS : []);

  return (
    <div className="tasks-pending-card">
      <h2>Tasks Pending</h2>
      <p>
        {farmer.name} {farmer.id && `(ID: ${farmer.id})`}
      </p>
      <div className="pending-tasks-container">
        <ul className="tasks-list">
          {pendingTasks.length > 0 ? (
            pendingTasks.map((task, index) => (
              <li key={index}>{task}</li>
            ))
          ) : (
            <div className="no-tasks-message">
              No pending tasks at the moment!
            </div>
          )}
        </ul>
      </div>
    </div>
  );
};

export default TasksPending;
