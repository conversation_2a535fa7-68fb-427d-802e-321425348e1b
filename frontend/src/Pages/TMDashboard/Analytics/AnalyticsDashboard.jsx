import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
} from '@mui/material';
import {
  <PERSON><PERSON>hart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import {
  Assessment as AssessmentIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';

const AnalyticsDashboard = () => {
  const [timeRange, setTimeRange] = useState('month');
  const [yieldData, setYieldData] = useState([]);
  const [soilHealthData, setSoilHealthData] = useState([]);
  const [livestockData, setLivestockData] = useState([]);

  useEffect(() => {
    // TODO: Implement API calls to fetch real data
    // Mock data for demonstration
    const mockYieldData = [
      { month: 'Jan', wheat: 450, rice: 300, cotton: 200 },
      { month: 'Feb', wheat: 500, rice: 400, cotton: 250 },
      { month: 'Mar', wheat: 550, rice: 450, cotton: 300 },
      { month: 'Apr', wheat: 600, rice: 500, cotton: 350 },
    ];

    const mockSoilData = [
      { date: '2024-01', nitrogen: 45, phosphorus: 30, potassium: 35 },
      { date: '2024-02', nitrogen: 42, phosphorus: 32, potassium: 38 },
      { date: '2024-03', nitrogen: 48, phosphorus: 35, potassium: 40 },
      { date: '2024-04', nitrogen: 50, phosphorus: 38, potassium: 42 },
    ];

    const mockLivestockData = [
      { category: 'Cattle', healthy: 85, sick: 15 },
      { category: 'Goats', healthy: 90, sick: 10 },
      { category: 'Poultry', healthy: 95, sick: 5 },
    ];

    setYieldData(mockYieldData);
    setSoilHealthData(mockSoilData);
    setLivestockData(mockLivestockData);
  }, []);

  const handleTimeRangeChange = (event) => {
    setTimeRange(event.target.value);
    // TODO: Fetch new data based on time range
  };

  const renderYieldChart = () => (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">Crop Yield Analysis</Typography>
          <Button
            startIcon={<DownloadIcon />}
            size="small"
            onClick={() => {/* TODO: Implement export */}}
          >
            Export
          </Button>
        </Box>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={yieldData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="wheat" fill="#8884d8" name="Wheat" />
            <Bar dataKey="rice" fill="#82ca9d" name="Rice" />
            <Bar dataKey="cotton" fill="#ffc658" name="Cotton" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );

  const renderSoilHealthChart = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Soil Health Trends
        </Typography>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={soilHealthData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="nitrogen" stroke="#8884d8" name="Nitrogen" />
            <Line type="monotone" dataKey="phosphorus" stroke="#82ca9d" name="Phosphorus" />
            <Line type="monotone" dataKey="potassium" stroke="#ffc658" name="Potassium" />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );

  const renderLivestockChart = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Livestock Health Statistics
        </Typography>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={livestockData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="category" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="healthy" fill="#82ca9d" name="Healthy" />
            <Bar dataKey="sick" fill="#ff8042" name="Requires Attention" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box display="flex" alignItems="center">
              <AssessmentIcon sx={{ fontSize: 40, mr: 2 }} />
              <Typography variant="h5">Analytics Dashboard</Typography>
            </Box>
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel>Time Range</InputLabel>
              <Select
                value={timeRange}
                label="Time Range"
                onChange={handleTimeRangeChange}
              >
                <MenuItem value="week">Last Week</MenuItem>
                <MenuItem value="month">Last Month</MenuItem>
                <MenuItem value="quarter">Last Quarter</MenuItem>
                <MenuItem value="year">Last Year</MenuItem>
              </Select>
            </FormControl>
          </Paper>
        </Grid>

        {/* Yield Analysis */}
        <Grid item xs={12}>
          {renderYieldChart()}
        </Grid>

        {/* Soil Health */}
        <Grid item xs={12} md={6}>
          {renderSoilHealthChart()}
        </Grid>

        {/* Livestock Statistics */}
        <Grid item xs={12} md={6}>
          {renderLivestockChart()}
        </Grid>
      </Grid>
    </Box>
  );
};

export default AnalyticsDashboard; 