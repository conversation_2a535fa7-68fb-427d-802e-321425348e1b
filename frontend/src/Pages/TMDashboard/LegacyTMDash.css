* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.tm-dash-container {
  max-width: 1400px;
  margin: 2rem auto;
  padding: 0 2rem;
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(12, 1fr);
}

/* Assigned Farmers takes up 4 columns */
.tm-dash-container > :nth-child(1) {
  grid-column: span 4;
}

/* Tasks Completed takes up 8 columns */
.tm-dash-container > :nth-child(2) {
  grid-column: span 8;
}

/* Bottom right section takes up full width */
.tm-dash-bottom-right {
  grid-column: 1 / -1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

/* Farms Info and Add Farm sections */
.farms-info-section,
.add-farm-section {
  grid-column: 1 / -1;
  margin-top: 2rem;
  padding: 0 2rem;
}

/* Emulator mode indicator */
.emulator-indicator {
  background: #f0f0f0;
  padding: 1rem;
  margin: 1rem 2rem;
  border-radius: 8px;
  font-size: 14px;
  text-align: center;
  border: 1px solid #ddd;
}

/* Loading and error states */
.loading,
.error {
  text-align: center;
  padding: 2rem;
  margin: 2rem;
  border-radius: 8px;
}

.loading {
  background: #f5f5f5;
}

.error {
  background: #fff5f5;
  color: #e53e3e;
}

/* Responsive design */
@media (max-width: 1200px) {
  .tm-dash-container {
    grid-template-columns: repeat(6, 1fr);
  }

  .tm-dash-container > :nth-child(1),
  .tm-dash-container > :nth-child(2) {
    grid-column: span 6;
  }
}

@media (max-width: 768px) {
  .tm-dash-container {
    padding: 0 1rem;
    gap: 1rem;
  }

  .tm-dash-bottom-right {
    grid-template-columns: 1fr;
  }

  .farms-info-section,
  .add-farm-section {
    padding: 0 1rem;
  }
}
