.alerts-card {
  background-color: #f7b4b4;
  border-radius: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.alerts-card h2 {
  text-align: center;
  padding: 16px;
  font-size: 24px;
  font-weight: 600;
  color: #fafafa;
  margin: 0;
  border-radius: 16px 16px 0px 0px;
  background: linear-gradient(45deg, #dc2626, #ef4444, #ef4444, #dc2626);
  background-size: 400% 400%;
  animation: gradientAnimation 5s ease infinite;
}

.alerts-card p {
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  color: #1a1a1a;
  margin: 16px 0;
}

.alerts-container {
  overflow-y: auto;
  padding: 16px;
  direction: rtl;
  flex-grow: 1;
  min-height: 350px;
  max-height: calc(100vh - 300px);
}

.alerts-container ul {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px 32px;
  line-height: 32px;
  list-style: decimal;
}

.alerts-container * {
  direction: ltr;
}

.alerts-container li {
  font-size: 20px;
  font-weight: 500;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.alerts-container li:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateX(5px);
}

.alerts-container::-webkit-scrollbar {
  width: 8px;
  background-color: #b91c1c;
}

.alerts-container::-webkit-scrollbar-button {
  display: none;
}

.alerts-container::-webkit-scrollbar-thumb {
  background-color: #dc2626;
  border-radius: 4px;
}

.no-alerts-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 280px;
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
  color: #666;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}