import React from "react";
import "./Alerts.css";

// Mock data for emulator mode
const MOCK_ALERTS = [
  "Weather alert: Rain expected tomorrow",
  "Pest alert: Monitor for aphids",
  "Market alert: Wheat prices trending up"
];

const Alerts = ({ farmer }) => {
  // If no farmer is selected, show a message
  if (!farmer) {
    return (
      <div className="alerts-card">
        <h2>Alerts</h2>
        <div className="no-alerts-message">
          Select a farmer to view alerts
        </div>
      </div>
    );
  }

  // Use mock data if alerts is not available
  const alerts = farmer.alerts || (farmer.isEmulator ? MOCK_ALERTS : []);

  return (
    //  <div className="alerts-card">Alerts</div>
    <div className="alerts-card">
      <h2>Alerts</h2>
      <p>
        {farmer.name} {farmer.id && `(ID: ${farmer.id})`}
      </p>
      <div className="alerts-container">
        <ul className="alerts-list">
          {alerts.length > 0 ? (
            alerts.map((alert, index) => <li key={index}>{alert}</li>)
          ) : (
            <div className="no-alerts-message">
              No alerts at the moment!
            </div>
          )}
        </ul>
      </div>
    </div>
  );
};

export default Alerts;
