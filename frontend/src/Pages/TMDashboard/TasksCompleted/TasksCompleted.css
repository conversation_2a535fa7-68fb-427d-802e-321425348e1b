.tasks-completed-card {
  grid-row: span 3;
  background: #ffec99;
  border-radius: 16px;
}

.tasks-completed-card p {
  text-align: center;
  padding: 16px;
  font-size: 24px;
  font-weight: 600;
  color: #fafafa;
  border-radius: 16px 16px 0px 0px;
  background: linear-gradient(45deg, #006400, #00b500, #00b500, #006400);
  background-size: 400% 400%;
  animation: gradientAnimation 5s ease infinite;
}

.tasks-completed-data {
  background: none;
  padding: 1rem 0 0 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 3rem;
}

.tasks-completed-data p {
  color: black;
  background: none;
}

.tasks-completed-data h1 {
  font-size: 100px;
}
.tasks-completed-data h1 span {
  font-size: 100px;
  color: #9b6c00;
}

.tasks-complete-message {
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
}
