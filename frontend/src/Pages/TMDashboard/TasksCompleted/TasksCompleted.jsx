import React from "react";
import "./TasksCompleted.css";

const TasksCompleted = ({ farmer }) => {
  return (
    <div className="tasks-completed-card">
      <p>Tasks Completed</p>
      <div className="tasks-completed-data">
        <p>
          {farmer.name} (ID: {farmer.id})
        </p>
        {farmer.completedTasks === farmer.totalTasks ? (
          <div className="tasks-complete-message">All tasks completed!</div>
        ) : (
          <h1>
            <span>{farmer.completedTasks}</span> / {farmer.totalTasks}
          </h1>
        )}
      </div>
    </div>
  );
};

export default TasksCompleted;
