import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';

const LivestockMonitoring = () => {
  const [livestockData, setLivestockData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // TODO: Replace with actual API call
    const mockData = [
      {
        farmerId: 'F001',
        farmerName: '<PERSON>',
        totalLivestock: 45,
        healthyCount: 42,
        alerts: 2,
        lastUpdated: '2024-03-20',
        location: 'Karnataka',
      },
      {
        farmerId: 'F002',
        farmerName: '<PERSON>',
        totalLivestock: 30,
        healthyCount: 29,
        alerts: 1,
        lastUpdated: '2024-03-21',
        location: 'Punjab',
      },
      // Add more mock data as needed
    ];

    setLivestockData(mockData);
    setLoading(false);
  }, []);

  const getHealthStatus = (total, healthy) => {
    const percentage = (healthy / total) * 100;
    if (percentage >= 90) return { label: 'Excellent', color: 'success' };
    if (percentage >= 75) return { label: 'Good', color: 'info' };
    return { label: 'Needs Attention', color: 'warning' };
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Livestock Monitoring
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Livestock
              </Typography>
              <Typography variant="h3">
                {livestockData.reduce((acc, curr) => acc + curr.totalLivestock, 0)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Overall Health Rate
              </Typography>
              <Typography variant="h3" color="success.main">
                {Math.round(
                  (livestockData.reduce((acc, curr) => acc + curr.healthyCount, 0) /
                    livestockData.reduce((acc, curr) => acc + curr.totalLivestock, 0)) *
                    100
                )}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active Alerts
              </Typography>
              <Typography variant="h3" color="warning.main">
                {livestockData.reduce((acc, curr) => acc + curr.alerts, 0)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Farmers Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Farmer ID</TableCell>
              <TableCell>Name</TableCell>
              <TableCell>Location</TableCell>
              <TableCell align="right">Total Livestock</TableCell>
              <TableCell align="center">Health Status</TableCell>
              <TableCell align="center">Alerts</TableCell>
              <TableCell>Last Updated</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {livestockData.map((farmer) => (
              <TableRow key={farmer.farmerId}>
                <TableCell>{farmer.farmerId}</TableCell>
                <TableCell>{farmer.farmerName}</TableCell>
                <TableCell>{farmer.location}</TableCell>
                <TableCell align="right">{farmer.totalLivestock}</TableCell>
                <TableCell align="center">
                  <Chip
                    label={getHealthStatus(farmer.totalLivestock, farmer.healthyCount).label}
                    color={getHealthStatus(farmer.totalLivestock, farmer.healthyCount).color}
                    size="small"
                  />
                </TableCell>
                <TableCell align="center">
                  {farmer.alerts > 0 ? (
                    <Chip
                      icon={<WarningIcon />}
                      label={farmer.alerts}
                      color="warning"
                      size="small"
                    />
                  ) : (
                    <Chip label="None" color="success" size="small" />
                  )}
                </TableCell>
                <TableCell>{farmer.lastUpdated}</TableCell>
                <TableCell align="center">
                  <Tooltip title="View Details">
                    <IconButton size="small">
                      <VisibilityIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="View Trends">
                    <IconButton size="small">
                      <TrendingUpIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default LivestockMonitoring; 