import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  Warning as WarningIcon,
  Agriculture as AgricultureIcon,
  WaterDrop as WaterDropIcon,
  Thermostat as ThermostatIcon,
  BugReport as BugReportIcon,
} from '@mui/icons-material';

const MonitoringDashboard = () => {
  const [weatherData, setWeatherData] = useState(null);
  const [soilData, setSoilData] = useState(null);
  const [alerts, setAlerts] = useState([]);

  useEffect(() => {
    // TODO: Implement real-time data fetching
    // Mock data for demonstration
    setWeatherData({
      temperature: 28,
      humidity: 65,
      rainfall: 2.5,
      forecast: 'Clear sky',
    });

    setSoilData({
      moisture: 45,
      pH: 6.8,
      nitrogen: 42,
      phosphorus: 38,
      potassium: 45,
    });

    setAlerts([
      {
        id: 1,
        type: 'warning',
        message: 'High temperature alert in Sector A',
        timestamp: new Date().toISOString(),
      },
      {
        id: 2,
        type: 'critical',
        message: 'Low soil moisture detected in Sector B',
        timestamp: new Date().toISOString(),
      },
    ]);
  }, []);

  const renderWeatherCard = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Weather Conditions
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Box display="flex" alignItems="center">
              <ThermostatIcon color="primary" />
              <Typography variant="body1" sx={{ ml: 1 }}>
                {weatherData?.temperature}°C
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box display="flex" alignItems="center">
              <WaterDropIcon color="primary" />
              <Typography variant="body1" sx={{ ml: 1 }}>
                {weatherData?.humidity}%
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  const renderSoilCard = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Soil Health
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={4}>
            <Typography variant="subtitle2">Moisture</Typography>
            <Typography variant="h6">{soilData?.moisture}%</Typography>
          </Grid>
          <Grid item xs={4}>
            <Typography variant="subtitle2">pH</Typography>
            <Typography variant="h6">{soilData?.pH}</Typography>
          </Grid>
          <Grid item xs={4}>
            <Typography variant="subtitle2">NPK</Typography>
            <Typography variant="body2">
              N: {soilData?.nitrogen}%
              P: {soilData?.phosphorus}%
              K: {soilData?.potassium}%
            </Typography>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  const renderAlerts = () => (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Alerts & Notifications</Typography>
          <IconButton>
            <NotificationsIcon />
          </IconButton>
        </Box>
        <List>
          {alerts.map((alert) => (
            <React.Fragment key={alert.id}>
              <ListItem>
                <ListItemIcon>
                  {alert.type === 'warning' ? (
                    <WarningIcon color="warning" />
                  ) : (
                    <BugReportIcon color="error" />
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={alert.message}
                  secondary={new Date(alert.timestamp).toLocaleString()}
                />
              </ListItem>
              <Divider />
            </React.Fragment>
          ))}
        </List>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2, display: 'flex', alignItems: 'center' }}>
            <AgricultureIcon sx={{ fontSize: 40, mr: 2 }} />
            <Typography variant="h5">Farm Monitoring Dashboard</Typography>
          </Paper>
        </Grid>

        {/* Weather and Soil Data */}
        <Grid item xs={12} md={6}>
          {weatherData && renderWeatherCard()}
        </Grid>
        <Grid item xs={12} md={6}>
          {soilData && renderSoilCard()}
        </Grid>

        {/* Alerts Section */}
        <Grid item xs={12}>
          {renderAlerts()}
        </Grid>
      </Grid>
    </Box>
  );
};

export default MonitoringDashboard; 