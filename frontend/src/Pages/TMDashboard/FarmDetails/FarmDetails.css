.farm-details-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.farm-details-container h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: center;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.detail-card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.detail-card h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.service-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  transition: transform 0.2s;
}

.service-card:hover {
  transform: translateY(-2px);
}

.service-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.service-icon {
  font-size: 1.5rem;
}

.service-card h4 {
  margin: 0;
  color: #2c3e50;
}

.service-card p {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.service-status {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: auto;
}

.subscribed {
  color: #2196f3;
  font-weight: 500;
  font-size: 0.9rem;
}

.subscribe-btn,
.activation-btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.subscribe-btn {
  background: #2196f3;
  color: white;
}

.subscribe-btn:hover {
  background: #1976d2;
}

.activation-btn {
  background: #e3f2fd;
  color: #2196f3;
}

.activation-btn.active {
  background: #4caf50;
  color: white;
}

.activation-btn.inactive {
  background: #f44336;
  color: white;
}

.activation-btn:hover {
  opacity: 0.9;
}

button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Soil Health Card */
.soil-metrics,
.weather-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.metric span:first-child {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.metric .value {
  color: #2c3e50;
  font-weight: 600;
  font-size: 1.1rem;
}

/* Tasks List */
.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.task-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.task-header h4 {
  margin: 0;
  color: #2c3e50;
}

.task-status {
  font-size: 0.9rem;
  padding: 4px 8px;
  border-radius: 4px;
  background: #e3f2fd;
  color: #2196f3;
}

.task-item.high {
  border-left: 4px solid #f44336;
}

.task-item.medium {
  border-left: 4px solid #ff9800;
}

.task-item.low {
  border-left: 4px solid #4caf50;
}

.due-date {
  display: block;
  margin-top: 10px;
  font-size: 0.9rem;
  color: #666;
}

/* Alerts List */
.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.alert-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.alert-type {
  font-size: 0.9rem;
  font-weight: 500;
}

.alert-item.info .alert-type {
  color: #2196f3;
}

.alert-item.warning .alert-type {
  color: #ff9800;
}

.alert-item.critical .alert-type {
  color: #f44336;
}

.alert-time {
  font-size: 0.8rem;
  color: #666;
}

/* Loading and Error States */
.loading,
.error {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 10px;
  margin: 20px;
}

.error {
  color: #f44336;
}

.loading {
  color: #2196f3;
}

/* Scrollbar Styling */
.tasks-list, .alerts-list {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 100, 0, 0.3) transparent;
}

.tasks-list::-webkit-scrollbar,
.alerts-list::-webkit-scrollbar {
  width: 6px;
}

.tasks-list::-webkit-scrollbar-track,
.alerts-list::-webkit-scrollbar-track {
  background: transparent;
}

.tasks-list::-webkit-scrollbar-thumb,
.alerts-list::-webkit-scrollbar-thumb {
  background-color: #228b22;
  border-radius: 3px;
}

.tasks-list::-webkit-scrollbar-thumb:hover,
.alerts-list::-webkit-scrollbar-thumb:hover {
  background-color: #1e7b1e;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .details-grid {
    grid-template-columns: 1fr;
  }

  .farm-details-container {
    padding: 0.5rem;
  }

  .detail-card {
    padding: 1rem;
  }
} 