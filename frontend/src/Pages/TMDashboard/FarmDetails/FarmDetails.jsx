import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './FarmDetails.css';
import { useAuth } from '../../../contexts/AuthContext';

const FarmDetails = ({ farmId }) => {
  const { currentUser } = useAuth();
  const [farmDetails, setFarmDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [serviceLoading, setServiceLoading] = useState(false);

  const SERVICES = {
    cropRecommendation: {
      title: 'Crop Recommendations',
      description: 'Get AI-powered crop suggestions based on soil and climate conditions',
      icon: '🌾'
    },
    weatherInsights: {
      title: 'Weather Insights',
      description: 'Receive detailed weather forecasts and agricultural advisories',
      icon: '🌤️'
    },
    soilAnalysis: {
      title: 'Soil Analysis',
      description: 'Regular soil health monitoring and recommendations',
      icon: '🌱'
    },
    pestDetection: {
      title: 'Pest Detection',
      description: 'AI-powered pest identification and treatment suggestions',
      icon: '🐛'
    }
  };

  useEffect(() => {
    const fetchFarmDetails = async () => {
      if (!farmId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log('Fetching details for farm:', farmId);
        
        const idToken = await currentUser.firebaseUser.getIdToken();
        const response = await axios.get(
          `${import.meta.env.VITE_API_URL}/api/farms/${farmId}`,
          {
            headers: { Authorization: `Bearer ${idToken}` }
          }
        );

        console.log('Farm details response:', response.data);
        
        const farmData = response.data.data || response.data;
        setFarmDetails(farmData);
        setError(null);
      } catch (err) {
        console.error('Error fetching farm details:', err);
        setError(err.response?.data?.message || 'Failed to fetch farm details');
        setFarmDetails(null);
      } finally {
        setLoading(false);
      }
    };

    fetchFarmDetails();
  }, [farmId, currentUser]);

  const handleServiceSubscription = async (serviceName) => {
    try {
      setServiceLoading(true);
      const idToken = await currentUser.firebaseUser.getIdToken();
      await axios.post(
        `${import.meta.env.VITE_API_URL}/api/farms/${farmId}/services/subscribe`,
        {
          services: [serviceName]
        },
        {
          headers: { Authorization: `Bearer ${idToken}` }
        }
      );

      // Refresh farm details
      const response = await axios.get(
        `${import.meta.env.VITE_API_URL}/api/farms/${farmId}`,
        {
          headers: { Authorization: `Bearer ${idToken}` }
        }
      );
      
      setFarmDetails(response.data.data || response.data);
    } catch (error) {
      console.error('Error managing service subscription:', error);
      setError('Failed to update service subscription');
    } finally {
      setServiceLoading(false);
    }
  };

  const handleServiceActivation = async (serviceName, activate) => {
    try {
      setServiceLoading(true);
      const idToken = await currentUser.firebaseUser.getIdToken();
      await axios.post(
        `${import.meta.env.VITE_API_URL}/api/farms/${farmId}/services/activate`,
        {
          services: [serviceName],
          activate
        },
        {
          headers: { Authorization: `Bearer ${idToken}` }
        }
      );

      // Refresh farm details
      const response = await axios.get(
        `${import.meta.env.VITE_API_URL}/api/farms/${farmId}`,
        {
          headers: { Authorization: `Bearer ${idToken}` }
        }
      );
      
      setFarmDetails(response.data.data || response.data);
    } catch (error) {
      console.error('Error managing service activation:', error);
      setError('Failed to update service activation');
    } finally {
      setServiceLoading(false);
    }
  };

  if (loading) return <div className="loading">Loading farm details...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!farmDetails) return <div className="error">No farm details available</div>;

  return (
    <div className="farm-details-container">
      <h2>{farmDetails.farmerName}'s Farm Details</h2>
      
      <div className="details-grid">
        {/* AgriCare AI Services */}
        <div className="detail-card services">
          <h3>AgriCare AI Services</h3>
          <div className="services-grid">
            {Object.entries(SERVICES).map(([key, service]) => (
              <div key={key} className="service-card">
                <div className="service-header">
                  <span className="service-icon">{service.icon}</span>
                  <h4>{service.title}</h4>
                </div>
                <p>{service.description}</p>
                <div className="service-status">
                  {farmDetails.services?.[key]?.isSubscribed ? (
                    <>
                      <span className="subscribed">Subscribed</span>
                      <button
                        className={`activation-btn ${farmDetails.services[key].isActive ? 'active' : 'inactive'}`}
                        onClick={() => handleServiceActivation(key, !farmDetails.services[key].isActive)}
                        disabled={serviceLoading}
                      >
                        {farmDetails.services[key].isActive ? 'Deactivate' : 'Activate'}
                      </button>
                    </>
                  ) : (
                    <button
                      className="subscribe-btn"
                      onClick={() => handleServiceSubscription(key)}
                      disabled={serviceLoading}
                    >
                      Subscribe
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Soil Health Card */}
        <div className="detail-card soil-health">
          <h3>Soil Health</h3>
          <div className="soil-metrics">
            <div className="metric">
              <span>Nitrogen (N)</span>
              <span className="value">{farmDetails.sensorData?.nitrogen || 'N/A'}</span>
            </div>
            <div className="metric">
              <span>Phosphorus (P)</span>
              <span className="value">{farmDetails.sensorData?.phosphorus || 'N/A'}</span>
            </div>
            <div className="metric">
              <span>Potassium (K)</span>
              <span className="value">{farmDetails.sensorData?.potassium || 'N/A'}</span>
            </div>
            <div className="metric">
              <span>pH Level</span>
              <span className="value">{farmDetails.sensorData?.ph || 'N/A'}</span>
            </div>
            <div className="metric">
              <span>Soil Moisture</span>
              <span className="value">{farmDetails.sensorData?.moisture || 'N/A'}</span>
            </div>
          </div>
        </div>

        {/* Weather Data Card */}
        <div className="detail-card weather">
          <h3>Weather Conditions</h3>
          <div className="weather-metrics">
            <div className="metric">
              <span>Temperature</span>
              <span className="value">{farmDetails.weatherData?.temperature || 'N/A'}</span>
            </div>
            <div className="metric">
              <span>Humidity</span>
              <span className="value">{farmDetails.weatherData?.humidity || 'N/A'}</span>
            </div>
            <div className="metric">
              <span>Rainfall</span>
              <span className="value">{farmDetails.weatherData?.rainfall || 'N/A'}</span>
            </div>
          </div>
        </div>

        {/* Tasks Card */}
        <div className="detail-card tasks">
          <h3>Active Tasks</h3>
          <div className="tasks-list">
            {farmDetails.tasks && farmDetails.tasks.length > 0 ? (
              farmDetails.tasks.map((task, index) => (
                <div key={index} className={`task-item ${task.priority?.toLowerCase() || 'normal'}`}>
                  <div className="task-header">
                    <h4>{task.title}</h4>
                    <span className="task-status">{task.status}</span>
                  </div>
                  <p>{task.description}</p>
                  {task.dueDate && (
                    <span className="due-date">
                      Due: {new Date(task.dueDate).toLocaleDateString()}
                    </span>
                  )}
                </div>
              ))
            ) : (
              <p>No active tasks. Add tasks to track farm activities.</p>
            )}
          </div>
        </div>

        {/* Alerts Card */}
        <div className="detail-card alerts">
          <h3>Recent Alerts</h3>
          <div className="alerts-list">
            {farmDetails.alerts && farmDetails.alerts.length > 0 ? (
              farmDetails.alerts.map((alert, index) => (
                <div key={index} className={`alert-item ${alert.type?.toLowerCase() || 'info'}`}>
                  <span className="alert-type">{alert.type}</span>
                  <p>{alert.message}</p>
                  <span className="alert-time">
                    {new Date(alert.timestamp).toLocaleString()}
                  </span>
                </div>
              ))
            ) : (
              <p>No recent alerts. System will notify you of important events.</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FarmDetails; 