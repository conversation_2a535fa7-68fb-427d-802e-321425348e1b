import React from 'react';
import { Box, Container } from '@mui/material';
import { Outlet } from 'react-router-dom';
import TMNavigation from '../../components/TMDashboard/TMNavigation';

const TMDashboard = () => {
  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* Navigation */}
      <TMNavigation />

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - 240px)` },
          ml: { sm: '240px' },
        }}
      >
        {/* Toolbar offset */}
        <Box sx={{ height: 64 }} />
        
        {/* Page Content */}
        <Container maxWidth="xl">
          <Outlet />
        </Container>
      </Box>
    </Box>
  );
};

export default TMDashboard; 