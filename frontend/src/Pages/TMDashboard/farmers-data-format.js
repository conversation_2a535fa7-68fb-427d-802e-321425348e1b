const farmersData = [
  {
    id: "**********",
    name: "<PERSON><PERSON>",
    totalTasks: 20,
    completedTasks: 18,
    pendingTasks: [
      "Inspect Crops – Check plants for pests, diseases, and overall health.",
      "Watering – Ensure crops receive the right amount of water, either manually or via irrigation.",
      "Feeding Livestock – Provide proper nutrition and fresh water for animals.",
      "Weeding – Remove unwanted plants that compete for nutrients and space.",
    ],
    alerts: [
      // "⚠️ Low soil moisture detected in Field A. Irrigation recommended.",
      // "🌱 Crop growth in Field B is ahead of schedule. Consider adjusting fertilizer application.",
    ],
    mobile: "**********",
    farms: [
      {
        farmId: "FM001",
        farmLocation: "Uttar Pradesh, Varanasi",
        farmSize: "5 acres",
        cropType: "Wheat",
        irrigationStatus: "Drip Irrigation",
      },
      {
        farmId: "FM002",
        farmLocation: "Uttar Pradesh, Prayagraj",
        farmSize: "8 acres",
        cropType: "Rice",
        irrigationStatus: "Canal Irrigation",
      },
    ],
  },
  {
    id: "**********",
    name: "<PERSON><PERSON>",
    totalTasks: 18,
    completedTasks: 12,
    pendingTasks: [
      // "Harvesting – Pick ripe produce to maintain quality and avoid spoilage.",
      // "Soil Maintenance – Check soil moisture, pH levels, and add fertilizers or compost if needed.",
    ],
    alerts: [
      "🌡️ High temperature alert! Monitor crops for heat stress in Field F.",
    ],
    mobile: "9876543211",
    farms: [
      {
        farmId: "FM001",
        farmLocation: "Uttar Pradesh, Varanasi",
        farmSize: "5 acres",
        cropType: "Wheat",
        irrigationStatus: "Drip Irrigation",
      },
      {
        farmId: "FM002",
        farmLocation: "Uttar Pradesh, Prayagraj",
        farmSize: "8 acres",
        cropType: "Rice",
        irrigationStatus: "Canal Irrigation",
      },
      {
        farmId: "FM003",
        farmLocation: "Uttar Pradesh, Varanasi",
        farmSize: "5 acres",
        cropType: "Wheat",
        irrigationStatus: "Drip Irrigation",
      },
      {
        farmId: "FM004",
        farmLocation: "Uttar Pradesh, Prayagraj",
        farmSize: "8 acres",
        cropType: "Rice",
        irrigationStatus: "Canal Irrigation",
      },
      {
        farmId: "FM005",
        farmLocation: "Uttar Pradesh, Varanasi",
        farmSize: "5 acres",
        cropType: "Wheat",
        irrigationStatus: "Drip Irrigation",
      },
      {
        farmId: "FM006",
        farmLocation: "Uttar Pradesh, Prayagraj",
        farmSize: "8 acres",
        cropType: "Rice",
        irrigationStatus: "Canal Irrigation",
      },
    ],
  },
  {
    id: "1234567892",
    name: "Suresh Yadav",
    totalTasks: 15,
    completedTasks: 15,
    pendingTasks: [
      "Record Keeping – Track expenses, yields, and farm activities for better planning.",
    ],
    alerts: [
      "🚜 Tractor maintenance due in 7 days. Schedule service to avoid downtime.",
    ],
    mobile: "9876543212",
    farms: [
      {
        farmId: "FM001",
        farmLocation: "Uttar Pradesh, Varanasi",
        farmSize: "5 acres",
        cropType: "Wheat",
        irrigationStatus: "Drip Irrigation",
      },
      {
        farmId: "FM002",
        farmLocation: "Uttar Pradesh, Prayagraj",
        farmSize: "8 acres",
        cropType: "Rice",
        irrigationStatus: "Canal Irrigation",
      },
    ],
  },
  {
    id: "1234567893",
    name: "Sunil Verma",
    totalTasks: 22,
    completedTasks: 18,
    pendingTasks: [
      "Market Preparation – Sort, clean, and package harvested crops for sale or storage.",
    ],
    alerts: [
      "🚨 Unauthorized activity detected near Field H. Check security cameras.",
    ],
    mobile: "9876543213",
  },
];

export default farmersData;
