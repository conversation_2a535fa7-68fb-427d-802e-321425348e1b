import React, { useState, useEffect } from "react";
import TmBanner from "../../Components/TM-Banner/TmBanner";
import AssignedFarmers from "./AssignedFarmers/AssignedFarmers";
import TasksCompleted from "./TasksCompleted/TasksCompleted";
import TasksPending from "./TasksPending/TasksPending";
import Alerts from "./Alerts/Alerts";
import "./LegacyTMDash.css";
import FarmsInfo from "./FarmsInfo/FarmsInfo";
import AddFarm from "./AddFarm/AddFarm";
import axios from "axios";
import { useAuth } from "../../contexts/AuthContext";

// Add mock data at the top
const MOCK_FARMERS = [
  {
    id: "mock-farmer-1",
    name: "Test Farmer 1",
    phone: "+919611966747",
    location: "Test Location 1",
    farms: 2,
    isEmulator: true,
    pendingTasks: [
      "Schedule soil testing",
      "Review irrigation system",
      "Plan crop rotation"
    ],
    alerts: [
      "Weather alert: Rain expected tomorrow",
      "Pest alert: Monitor for aphids",
      "Market alert: Wheat prices trending up"
    ]
  }
];

const MOCK_FARMS = [
  {
    _id: "mock-farm-1",
    name: "Test Farm 1",
    location: "Test Location 1",
    size: "5 acres",
    crops: ["Wheat", "Rice"],
    status: "Active",
    lastUpdated: new Date().toISOString()
  }
];

const TmDash = () => {
  const { currentUser } = useAuth();
  const [selectedFarmer, setSelectedFarmer] = useState(null);
  const [farmers, setFarmers] = useState([]);
  const [farms, setFarms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Add isEmulator check
  const isEmulator = currentUser?.isEmulator || window.location.hostname === "localhost";

  // Update the fetchFarmers function
  useEffect(() => {
    const fetchFarmers = async () => {
      try {
        setLoading(true);
        
        if (isEmulator) {
          console.log('Using mock farmers data in emulator mode');
          setFarmers(MOCK_FARMERS);
          setSelectedFarmer(MOCK_FARMERS[0]);
          setFarms(MOCK_FARMS);
          setLoading(false);
          return;
        }

        const idToken = await currentUser.getIdToken();
        const response = await axios.get(
          `${import.meta.env.VITE_API_URL}/api/farmers`,
          {
            headers: { Authorization: `Bearer ${idToken}` }
          }
        );
        console.log('Fetched farmers:', response.data);
        const farmersData = response.data.data || response.data;
        setFarmers(farmersData);
        
        // If there's a selected farmer, keep it selected
        if (selectedFarmer) {
          const existingFarmer = farmersData.find(f => f.id === selectedFarmer.id);
          if (existingFarmer) {
            setSelectedFarmer(existingFarmer);
            await fetchFarmerFarms(existingFarmer.id);
          }
        } else if (farmersData.length > 0) {
          setSelectedFarmer(farmersData[0]);
          await fetchFarmerFarms(farmersData[0].id);
        }
      } catch (error) {
        console.error('Error fetching farmers:', error);
        setError('Failed to load farmers data');
      } finally {
        setLoading(false);
      }
    };

    fetchFarmers();
  }, [currentUser, isEmulator]);

  // Fetch farms whenever selected farmer changes
  useEffect(() => {
    if (selectedFarmer) {
      fetchFarmerFarms(selectedFarmer.id);
    }
  }, [selectedFarmer?.id]);

  // Update the fetchFarmerFarms function
  const fetchFarmerFarms = async (farmerId) => {
    if (isEmulator) {
      console.log('Using mock farms data in emulator mode');
      setFarms(MOCK_FARMS);
      return;
    }

    try {
      const idToken = await currentUser.getIdToken();
      console.log('Fetching farms for farmer:', farmerId);
      const response = await axios.get(
        `${import.meta.env.VITE_API_URL}/api/farmers/${farmerId}/farms`,
        {
          headers: { Authorization: `Bearer ${idToken}` }
        }
      );
      console.log('Fetched farms:', response.data);
      const farmsData = response.data.data || response.data;
      setFarms(farmsData);
    } catch (error) {
      console.error('Error fetching farms:', error);
      setError('Failed to load farms data');
    }
  };

  const handleSelectFarmer = async (farmerId) => {
    console.log('Selecting farmer:', farmerId);
    const farmer = farmers.find((f) => f.id === farmerId);
    if (farmer) {
      setSelectedFarmer(farmer);
      // fetchFarmerFarms will be called by the useEffect
    }
  };

  const handleFarmAdded = (newFarm) => {
    console.log('New farm added:', newFarm);
    setFarms(prevFarms => {
      // Check if farm already exists
      const exists = prevFarms.some(farm => farm._id === newFarm._id);
      if (!exists) {
        return [...prevFarms, newFarm];
      }
      return prevFarms;
    });
  };

  if (loading) {
    return <div className="loading">Loading dashboard...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  return (
    <div className="tm-dashboard">
      <TmBanner />
      
      {isEmulator && (
        <div className="emulator-indicator">
          Running in emulator mode with mock data
        </div>
      )}

      <div className="tm-dash-container">
        <AssignedFarmers
          farmers={farmers}
          onSelectFarmer={handleSelectFarmer}
          farms={farms}
          selectedFarmer={selectedFarmer}
        />
        
        <TasksCompleted farmer={selectedFarmer} />
        
        <div className="tm-dash-bottom-right">
          <TasksPending farmer={selectedFarmer} />
          <Alerts farmer={selectedFarmer} />
        </div>
      </div>

      {selectedFarmer && (
        <>
          <div className="farms-info-section">
            <FarmsInfo 
              farms={farms} 
              farmer={selectedFarmer} 
              setFarms={setFarms}
            />
          </div>
          
          <div className="add-farm-section">
            <AddFarm 
              farmer={selectedFarmer}
              onFarmAdded={handleFarmAdded}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default TmDash;
