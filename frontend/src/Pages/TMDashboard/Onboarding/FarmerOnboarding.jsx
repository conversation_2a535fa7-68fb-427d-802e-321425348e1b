import React, { useState, useEffect } from 'react';
import {
  Box,
  Stepper,
  Step,
  StepLabel,
  Button,
  Typography,
  Paper,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  IconButton,
} from '@mui/material';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PhotoCamera from '@mui/icons-material/PhotoCamera';
import { useAuth } from '../../../contexts/AuthContext';
import axios from 'axios';

// Indian states and districts data
const stateDistrictMap = {
  'Bihar': [
    'Patna', 'Gaya', 'Bhagalpur', 'Muzaffarpur', 'Purnia', 'Darbhanga',
    'Arrah', 'Bihar Sharif', 'Begusarai', 'Katihar', 'Mu<PERSON>', 'Chha<PERSON>',
    'Danapur', 'Saharsa', 'Sasaram', 'Hajipur', 'Dehri', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    'Nawa<PERSON>', 'Baga<PERSON>', 'B<PERSON>ar', 'Kishanganj', 'Sitamarhi', 'Jamalpur',
    'Jehanabad', 'Aurangabad', 'Madhepura', 'Madhubani', 'Samastipur',
    'Bettiah', 'Mokama', 'Araria', 'Bhabua', 'Forbesganj'
  ],
  'Uttar Pradesh': [
    'Lucknow', 'Kanpur', 'Varanasi', 'Agra', 'Meerut', 'Allahabad',
    'Ghaziabad', 'Noida', 'Bareilly', 'Moradabad', 'Aligarh', 'Gorakhpur',
    'Saharanpur', 'Faizabad', 'Jhansi', 'Muzaffarnagar', 'Mathura',
    'Budaun', 'Rampur', 'Shahjahanpur', 'Farrukhabad', 'Ayodhya',
    'Firozabad', 'Etawah', 'Mirzapur', 'Bulandshahr', 'Etah', 'Hardoi',
    'Amroha', 'Fatehpur'
  ],
  'Karnataka': [
    'Bangalore Urban', 'Bangalore Rural', 'Mysore', 'Belgaum', 'Bellary',
    'Hubli-Dharwad', 'Mangalore', 'Gulbarga', 'Davanagere', 'Shimoga',
    'Tumkur', 'Raichur', 'Bidar', 'Hassan', 'Gadag', 'Udupi', 'Mandya',
    'Chitradurga', 'Bagalkot', 'Haveri', 'Vijayapura', 'Kolar',
    'Chikmagalur', 'Koppal', 'Kodagu'
  ],
  'Maharashtra': [
    'Mumbai City', 'Mumbai Suburban', 'Pune', 'Nagpur', 'Thane',
    'Nashik', 'Aurangabad', 'Solapur', 'Kolhapur', 'Amravati',
    'Nanded', 'Sangli', 'Yavatmal', 'Raigad', 'Satara', 'Chandrapur',
    'Dhule', 'Jalgaon', 'Ahmednagar', 'Latur', 'Akola', 'Bid',
    'Parbhani', 'Buldhana', 'Wardha'
  ],
  'Tamil Nadu': [
    'Chennai', 'Coimbatore', 'Madurai', 'Salem', 'Tiruchirappalli',
    'Tirunelveli', 'Tiruppur', 'Vellore', 'Erode', 'Thoothukkudi',
    'Dindigul', 'Thanjavur', 'Ranipet', 'Sivakasi', 'Karur',
    'Udhagamandalam', 'Hosur', 'Nagercoil', 'Kanchipuram',
    'Kumarapalayam', 'Karaikkudi', 'Neyveli', 'Cuddalore',
    'Kumbakonam', 'Tiruvannamalai'
  ],
  'Gujarat': [
    'Ahmedabad', 'Surat', 'Vadodara', 'Rajkot', 'Bhavnagar',
    'Jamnagar', 'Junagadh', 'Gandhinagar', 'Gandhidham', 'Nadiad',
    'Morbi', 'Surendranagar', 'Bharuch', 'Anand', 'Porbandar',
    'Godhra', 'Navsari', 'Veraval', 'Ankleshwar', 'Vapi',
    'Mehsana', 'Bhuj', 'Palanpur', 'Patan', 'Dahod'
  ]
};

const states = Object.keys(stateDistrictMap);

const steps = ['Basic Details', 'Farm Information', 'Location & KYC', 'Digital Contract'];

const FarmerOnboarding = () => {
  const { currentUser } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState('');
  const [submitSuccess, setSubmitSuccess] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    mobile: '',
    aadharNumber: '',
    panNumber: '',
    geoLocation: null,
    state: '',
    district: '',
    farmSize: '',
    cropType: '',
    irrigationStatus: '',
    kycStatus: 'pending',
    contractAccepted: false,
    photo: null,
    photoPreview: null
  });

  const [locationError, setLocationError] = useState('');
  const [isCapturingLocation, setIsCapturingLocation] = useState(false);

  const [availableDistricts, setAvailableDistricts] = useState([]);

  useEffect(() => {
    if (formData.state) {
      setAvailableDistricts(stateDistrictMap[formData.state] || []);
    }
  }, [formData.state]);

  const captureLocation = () => {
    setIsCapturingLocation(true);
    setLocationError('');

    if (!navigator.geolocation) {
      setLocationError('Geolocation is not supported by your browser');
      setIsCapturingLocation(false);
      return;
    }

    const options = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 0
    };

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { longitude, latitude } = position.coords;
        setFormData(prev => ({
          ...prev,
          geoLocation: {
            type: 'Point',
            coordinates: [longitude, latitude]
          }
        }));
        setIsCapturingLocation(false);
      },
      (error) => {
        let errorMessage = 'Failed to get location. ';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage += 'Location permission denied.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage += 'Location information unavailable.';
            break;
          case error.TIMEOUT:
            errorMessage += 'Location request timed out.';
            break;
          default:
            errorMessage += error.message;
        }
        setLocationError(errorMessage);
        setIsCapturingLocation(false);
      },
      options
    );
  };

  const handleNext = () => {
    // Validate current step before proceeding
    if (activeStep === 0) { // Basic Details step
      if (!formData.name || !formData.mobile || !formData.aadharNumber || !formData.panNumber) {
        setSubmitError('Please fill all required fields in Basic Details');
        return;
      }
    } else if (activeStep === 1) { // Farm Information step
      if (!formData.farmSize || !formData.cropType || !formData.irrigationStatus) {
        setSubmitError('Please fill all required fields in Farm Information');
        return;
      }
    } else if (activeStep === 2) { // Location step - verify KYC and location before moving to contract
      if (!formData.state || !formData.district) {
        setSubmitError('Please select both state and district');
        return;
      }
      if (!formData.geoLocation) {
        setSubmitError('Please capture the farm location');
        return;
      }
      // Verify KYC when moving from location step to contract step
      verifyKYC();
      return;
    }

    setSubmitError(''); // Clear any existing errors
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const verifyKYC = async () => {
    setIsSubmitting(true);
    setSubmitError('');

    try {
      // Client-side validation for Aadhar (12 digits) and PAN (10 alphanumeric)
      const isAadharValid = /^\d{12}$/.test(formData.aadharNumber);
      const isPanValid = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(formData.panNumber.toUpperCase());

      if (!isAadharValid) {
        setSubmitError('Invalid Aadhar number. Please enter 12 digits.');
        setIsSubmitting(false);
        return;
      }

      if (!isPanValid) {
        setSubmitError('Invalid PAN number. Format should be **********');
        setIsSubmitting(false);
        return;
      }

      // If both validations pass
      setFormData(prev => ({
        ...prev,
        kycStatus: 'verified'
      }));
      setActiveStep((prevStep) => prevStep + 1);
    } catch (error) {
      console.error('KYC verification error:', error);
      setSubmitError('KYC verification failed. Please check your documents and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePhotoUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        setSubmitError('Photo size should be less than 5MB');
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        setFormData(prev => ({
          ...prev,
          photo: file,
          photoPreview: reader.result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async () => {
    if (!formData.contractAccepted) {
      setSubmitError('Please accept the contract to proceed');
      return;
    }

    // Validate required fields before submission
    if (!formData.state || !formData.district) {
      setSubmitError('Please select both state and district before submitting');
      return;
    }

    if (!formData.geoLocation) {
      setSubmitError('Please capture the farm location before submitting');
      return;
    }

    setIsSubmitting(true);
    setSubmitError('');
    setSubmitSuccess('');

    try {
      const idToken = currentUser.getIdToken;

      // Create FormData to handle file upload
      const submitData = new FormData();
      Object.keys(formData).forEach(key => {
        if (key === 'photo') {
          if (formData.photo) {
            submitData.append('photo', formData.photo);
          }
        } else if (key === 'photoPreview') {
          // Skip photoPreview
        } else if (key === 'geoLocation' && formData[key]) {
          submitData.append(key, JSON.stringify(formData[key]));
        } else {
          submitData.append(key, formData[key]);
        }
      });

      const response = await axios.post(
        `/api/farmers`,
        submitData,
        {
          headers: {
            Authorization: `Bearer ${idToken}`,
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      console.log('Farmer onboarded successfully:', response.data);

      // Set success message
      setSubmitSuccess(`Farmer ${formData.name} has been successfully onboarded!`);

      // Dispatch custom event to notify dashboard
      window.dispatchEvent(new Event('farmerAdded'));

      // Reset form after successful submission
      setFormData({
        name: '',
        mobile: '',
        aadharNumber: '',
        panNumber: '',
        geoLocation: null,
        state: '',
        district: '',
        farmSize: '',
        cropType: '',
        irrigationStatus: '',
        kycStatus: 'pending',
        contractAccepted: false,
        photo: null,
        photoPreview: null
      });
      setActiveStep(0);
    } catch (error) {
      console.error('Error onboarding farmer:', error);
      setSubmitError(error.response?.data?.message || 'Failed to onboard farmer');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label="Farmer Name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label="Phone Number"
                name="mobile"
                value={formData.mobile}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Aadhar Number"
                name="aadharNumber"
                value={formData.aadharNumber}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="PAN Number"
                name="panNumber"
                value={formData.panNumber}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Button
                  variant="contained"
                  component="label"
                  startIcon={<PhotoCamera />}
                >
                  Upload Photo
                  <input
                    type="file"
                    hidden
                    accept="image/*"
                    onChange={handlePhotoUpload}
                  />
                </Button>
                {formData.photoPreview && (
                  <Box
                    component="img"
                    src={formData.photoPreview}
                    alt="Farmer photo preview"
                    sx={{
                      width: 100,
                      height: 100,
                      objectFit: 'cover',
                      borderRadius: 1
                    }}
                  />
                )}
              </Box>
              <Typography variant="caption" color="textSecondary">
                Max file size: 5MB. Recommended: Passport-size photo
              </Typography>
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label="Land Area (in acres)"
                name="farmSize"
                type="number"
                value={formData.farmSize}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Crop Type</InputLabel>
                <Select
                  name="cropType"
                  value={formData.cropType}
                  onChange={handleInputChange}
                  label="Crop Type"
                >
                  <MenuItem value="organic">Organic</MenuItem>
                  <MenuItem value="conventional">Conventional</MenuItem>
                  <MenuItem value="mixed">Mixed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                required
                fullWidth
                label="Irrigation Status"
                name="irrigationStatus"
                value={formData.irrigationStatus}
                onChange={handleInputChange}
              />
            </Grid>
          </Grid>
        );

      case 2:
        return renderLocationStep();

      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Digital Contract with Quamin
            </Typography>
            <Typography variant="body1" paragraph>
              By accepting this contract, the farmer agrees to:
            </Typography>
            <Typography component="ul">
              <li>Share farm data with Quamin for analysis and improvement</li>
              <li>Follow recommended agricultural practices</li>
              <li>Participate in sustainable farming initiatives</li>
              <li>Allow periodic monitoring of farm conditions</li>
            </Typography>
            <Button
              variant="contained"
              color="primary"
              onClick={() => setFormData((prev) => ({ ...prev, contractAccepted: true }))}
              sx={{ mt: 2 }}
            >
              Accept Contract
            </Button>
          </Box>
        );

      default:
        return null;
    }
  };

  const renderLocationStep = () => {
    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Farm Location Details
          </Typography>
        </Grid>
        <Grid item xs={12}>
          <Alert severity="info">
            Please click "Capture Location" while at the farm to record its precise location.
            This helps in providing accurate weather forecasts and agricultural recommendations.
          </Alert>
        </Grid>
        <Grid item xs={12}>
          <Box display="flex" alignItems="center" gap={2}>
            <Button
              variant="contained"
              color="primary"
              onClick={captureLocation}
              disabled={isCapturingLocation}
              startIcon={<LocationOnIcon />}
            >
              {isCapturingLocation ? 'Capturing Location...' : 'Capture Location'}
            </Button>
            {formData.geoLocation && (
              <Typography variant="body2" color="success.main">
                ✓ Location captured successfully
              </Typography>
            )}
          </Box>
          {locationError && (
            <Typography color="error" variant="body2" sx={{ mt: 1 }}>
              {locationError}
            </Typography>
          )}
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth required>
            <InputLabel>State</InputLabel>
            <Select
              name="state"
              value={formData.state}
              onChange={handleInputChange}
              label="State"
            >
              {states.map((state) => (
                <MenuItem key={state} value={state}>
                  {state}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth required>
            <InputLabel>District</InputLabel>
            <Select
              name="district"
              value={formData.district}
              onChange={handleInputChange}
              label="District"
              disabled={!formData.state}
            >
              {availableDistricts.map((district) => (
                <MenuItem key={district} value={district}>
                  {district}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        {formData.geoLocation && (
          <Grid item xs={12}>
            <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
              <Typography variant="subtitle2" gutterBottom>
                Captured Coordinates:
              </Typography>
              <Typography variant="body2">
                Longitude: {formData.geoLocation.coordinates[0].toFixed(6)}°
                <br />
                Latitude: {formData.geoLocation.coordinates[1].toFixed(6)}°
              </Typography>
            </Paper>
          </Grid>
        )}
      </Grid>
    );
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h5" gutterBottom>
          Farmer Onboarding
        </Typography>
        <Stepper activeStep={activeStep} sx={{ pt: 3, pb: 5 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {activeStep === steps.length ? (
          <Box>
            <Typography sx={{ mt: 2, mb: 1 }}>
              All steps completed - Farmer successfully onboarded!
            </Typography>
            <Button onClick={() => setActiveStep(0)}>
              Onboard Another Farmer
            </Button>
          </Box>
        ) : (
          <Box>
            {submitError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {submitError}
              </Alert>
            )}
            {submitSuccess && (
              <Alert severity="success" sx={{ mb: 2 }}>
                {submitSuccess}
              </Alert>
            )}
            {renderStepContent(activeStep)}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
              {activeStep !== 0 && (
                <Button onClick={handleBack} sx={{ mr: 1 }}>
                  Back
                </Button>
              )}
              <Button
                variant="contained"
                onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  activeStep === steps.length - 1 ? 'Submit' : 'Next'
                )}
              </Button>
            </Box>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default FarmerOnboarding;