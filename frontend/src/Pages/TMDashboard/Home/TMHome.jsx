import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  TextField,
  CircularProgress,
} from '@mui/material';
import {
  PersonAdd as PersonAddIcon,
  Notifications as NotificationsIcon,
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  Agriculture as AgricultureIcon,
  Search as SearchIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
} from '@mui/icons-material';
import { useAuth } from '../../../contexts/AuthContext';
import axios from 'axios';

const TMHome = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalFarmers: 0,
    activeAlerts: 0,
    pendingTasks: 0,
    recentOnboarding: 0,
  });
  const [recentFarmers, setRecentFarmers] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredFarmers, setFilteredFarmers] = useState([]);

  const quickActions = [
    {
      title: 'Onboard New Farmer',
      icon: <PersonAddIcon />,
      path: '/tm/onboarding',
      color: 'primary',
    },
    {
      title: 'View Alerts',
      icon: <NotificationsIcon />,
      path: '/tm/monitoring',
      color: 'error',
    },
    {
      title: 'Check Analytics',
      icon: <TrendingUpIcon />,
      path: '/tm/analytics',
      color: 'success',
    },
  ];

  const recentAlerts = [
    {
      id: 1,
      type: 'warning',
      message: 'Low soil moisture detected in Farm #123',
      time: '2 hours ago',
    },
    {
      id: 2,
      type: 'critical',
      message: 'Pest detection alert in Farm #456',
      time: '4 hours ago',
    },
    {
      id: 3,
      type: 'info',
      message: 'New government scheme available for farmers',
      time: '1 day ago',
    },
  ];

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      // Get the user token from localStorage
      const userData = JSON.parse(localStorage.getItem('user'));
      if (!userData) {
        throw new Error('User not authenticated');
      }

      const [farmersResponse, alertsResponse] = await Promise.all([
        axios.get('/api/farmers/stats', {
          headers: {
            Authorization: `Bearer ${userData.id}`,
            'Content-Type': 'application/json'
          }
        }),
        axios.get('/api/farmers/alerts/stats', {
          headers: {
            Authorization: `Bearer ${userData.id}`,
            'Content-Type': 'application/json'
          }
        })
      ]);

      setStats({
        totalFarmers: farmersResponse.data.data?.totalFarmers || 0,
        activeAlerts: alertsResponse.data.data?.totalAlerts || 0,
        pendingTasks: farmersResponse.data.data?.pendingTasks || 0,
        recentOnboarding: farmersResponse.data.data?.recentOnboarding || 0,
      });

      // Fetch recent farmers
      const recentFarmersResponse = await axios.get('/api/farmers/recent', {
        headers: {
          Authorization: `Bearer ${userData.id}`,
          'Content-Type': 'application/json'
        }
      });
      setRecentFarmers(recentFarmersResponse.data.data?.farmers || []);
      setFilteredFarmers(recentFarmersResponse.data.data?.farmers || []);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Handle unauthorized access
      if (error.response?.status === 401) {
        navigate('/login');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, [currentUser]);

  // Add event listener for farmer onboarding completion
  useEffect(() => {
    const handleFarmerAdded = () => {
      fetchDashboardData();
    };

    window.addEventListener('farmerAdded', handleFarmerAdded);
    return () => {
      window.removeEventListener('farmerAdded', handleFarmerAdded);
    };
  }, []);

  useEffect(() => {
    // Filter farmers based on search query
    const filtered = recentFarmers.filter(farmer =>
      farmer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      farmer.mobile.includes(searchQuery) ||
      farmer.district.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredFarmers(filtered);
  }, [searchQuery, recentFarmers]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Welcome Section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box display="flex" alignItems="center">
          <AgricultureIcon sx={{ fontSize: 40, mr: 2 }} />
          <Box>
            <Typography variant="h4" gutterBottom>
              Welcome back, {currentUser?.displayName || 'Territory Manager'}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Here's what's happening in your territory today
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Farmers
              </Typography>
              <Typography variant="h3">{stats.totalFarmers}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active Alerts
              </Typography>
              <Typography variant="h3" color="error">
                {stats.activeAlerts}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Pending Tasks
              </Typography>
              <Typography variant="h3" color="warning.main">
                {stats.pendingTasks}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Recent Onboarding
              </Typography>
              <Typography variant="h3" color="success.main">
                {stats.recentOnboarding}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions and Recent Alerts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Grid container spacing={2}>
              {quickActions.map((action) => (
                <Grid item xs={12} sm={6} key={action.title}>
                  <Button
                    variant="outlined"
                    color={action.color}
                    startIcon={action.icon}
                    fullWidth
                    onClick={() => navigate(action.path)}
                    sx={{ justifyContent: 'flex-start', py: 1 }}
                  >
                    {action.title}
                  </Button>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Recent Alerts
            </Typography>
            <List>
              {recentAlerts.map((alert) => (
                <React.Fragment key={alert.id}>
                  <ListItem>
                    <ListItemIcon>
                      <WarningIcon color={alert.type === 'critical' ? 'error' : 'warning'} />
                    </ListItemIcon>
                    <ListItemText
                      primary={alert.message}
                      secondary={alert.time}
                    />
                  </ListItem>
                  <Divider />
                </React.Fragment>
              ))}
            </List>
            <Box sx={{ mt: 2 }}>
              <Button
                variant="text"
                onClick={() => navigate('/tm/monitoring')}
              >
                View All Alerts
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Recently Onboarded Farmers */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Recently Onboarded Farmers
        </Typography>
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            placeholder="Search by name, phone number, or location"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />,
            }}
            sx={{ bgcolor: 'background.paper' }}
          />
        </Box>
        <List>
          {filteredFarmers.length > 0 ? (
            filteredFarmers.map((farmer) => (
              <React.Fragment key={farmer._id}>
                <ListItem
                  secondaryAction={
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => navigate(`/tm/edit?farmer=${farmer._id}`)}
                      sx={{ minWidth: '110px' }} // Fixed width for all buttons
                    >
                      Edit Details
                    </Button>
                  }
                >
                  <ListItemText
                    primary={farmer.name}
                    secondary={
                      <Box>
                        <Box display="flex" alignItems="center" gap={2}>
                          <Box display="flex" alignItems="center">
                            <PhoneIcon sx={{ fontSize: 16, mr: 0.5 }} />
                            {farmer.mobile}
                          </Box>
                          <Box display="flex" alignItems="center">
                            <LocationIcon sx={{ fontSize: 16, mr: 0.5 }} />
                            {farmer.district}, {farmer.state}
                          </Box>
                        </Box>
                        <Typography variant="caption" color="text.secondary">
                          Onboarded: {new Date(farmer.createdAt).toLocaleDateString()}
                        </Typography>
                      </Box>
                    }
                    sx={{ pr: 2 }} // Add padding to the right to prevent text from getting too close to the button
                  />
                </ListItem>
                <Divider />
              </React.Fragment>
            ))
          ) : (
            <ListItem>
              <ListItemText
                primary={
                  searchQuery
                    ? "No farmers found matching your search"
                    : "No recently onboarded farmers"
                }
              />
            </ListItem>
          )}
        </List>
      </Paper>
    </Box>
  );
};

export default TMHome;