import React, { useState, useEffect } from 'react';
import {
  TextField, Select, MenuItem, Button, Paper,
  FormControl, InputLabel, CircularProgress, Alert
} from '@mui/material';
import axios from 'axios';

const SoilDataForm = ({ coordinates, onSubmit }) => {
  const [dataSource, setDataSource] = useState('satellite');
  const [sensorId, setSensorId] = useState('');
  const [labInfo, setLabInfo] = useState({});
  const [soilData, setSoilData] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [sensors, setSensors] = useState([]);

  // Load registered IoT sensors
  useEffect(() => {
    const fetchSensors = async () => {
      const response = await axios.get('/api/sensors');
      setSensors(response.data);
    };
    fetchSensors();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      const payload = {
        dataSource,
        coordinates,
        ...(dataSource === 'iot' && { sensorId }),
        ...(dataSource === 'lab_testing' && { labInfo }),
        ...(dataSource === 'manual' && { soilData })
      };

      await onSubmit(payload);
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const renderFormFields = () => {
    switch(dataSource) {
      case 'satellite':
        return (
          <Alert severity="info">
            Satellite data will be automatically fetched for selected coordinates
          </Alert>
        );
        
      case 'iot':
        return (
          <FormControl fullWidth>
            <InputLabel>Registered Sensor</InputLabel>
            <Select
              value={sensorId}
              onChange={(e) => setSensorId(e.target.value)}
              label="Registered Sensor"
            >
              {sensors.map(sensor => (
                <MenuItem key={sensor._id} value={sensor._id}>
                  {sensor.name} ({sensor.location})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );
        
      case 'lab_testing':
        return (
          <>
            <TextField
              label="Lab ID"
              fullWidth
              value={labInfo.labId || ''}
              onChange={(e) => setLabInfo({...labInfo, labId: e.target.value})}
            />
            {/* Add other lab info fields */}
          </>
        );
        
      case 'manual':
        return (
          <>
            <TextField
              label="Nitrogen (ppm)"
              type="number"
              fullWidth
              value={soilData.nitrogen || ''}
              onChange={(e) => setSoilData({...soilData, nitrogen: e.target.value})}
            />
            <TextField
              label="Phosphorus (ppm)"
              type="number"
              fullWidth
              value={soilData.phosphorus || ''}
              onChange={(e) => setSoilData({...soilData, phosphorus: e.target.value})}
            />
            {/* Add other soil data fields */}
          </>
        );
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <FormControl fullWidth margin="normal">
        <InputLabel>Data Source</InputLabel>
        <Select
          value={dataSource}
          onChange={(e) => setDataSource(e.target.value)}
          label="Data Source"
        >
          <MenuItem value="satellite">Satellite Data</MenuItem>
          <MenuItem value="iot">IoT Sensor</MenuItem>
          <MenuItem value="lab_testing">Lab Testing</MenuItem>
          <MenuItem value="manual">Manual Entry</MenuItem>
        </Select>
      </FormControl>

      {renderFormFields()}

      {error && <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>}

      <Button 
        type="submit" 
        variant="contained" 
        disabled={loading}
        sx={{ mt: 3 }}
      >
        {loading ? <CircularProgress size={24} /> : 'Submit Soil Data'}
      </Button>
    </form>
  );
};

export default SoilDataForm;

