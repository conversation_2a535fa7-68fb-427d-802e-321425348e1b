.livestock-dashboard {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.dashboard-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    gap: 20px;
}

.back-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: #f0f0f0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    transition: background-color 0.2s;
}

.back-button:hover {
    background-color: #e0e0e0;
}

.back-button i {
    font-size: 16px;
}

.livestock-dashboard h1 {
    color: #2c3e50;
    margin-bottom: 2rem;
    text-align: center;
}

/* Upload Section */
.upload-section {
    background: #fff;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.preview {
    max-width: 500px;
    width: 100%;
    margin-top: 1rem;
}

.preview img {
    width: 100%;
    height: auto;
    border-radius: 4px;
    object-fit: cover;
}

/* Analysis Section */
.analysis-section {
    background: #fff;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.analysis-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 1rem;
}

.analysis-item {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.analysis-item h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background: #e9ecef;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    color: #495057;
}

/* History Section */
.history-section {
    background: #fff;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.history-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 1rem;
}

.history-item {
    background: #f8f9fa;
    border-radius: 4px;
    overflow: hidden;
    transition: transform 0.2s;
}

.history-item:hover {
    transform: translateY(-4px);
}

.history-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.history-item h3 {
    padding: 1rem;
    margin: 0;
    color: #2c3e50;
    border-bottom: 1px solid #dee2e6;
}

.analysis-summary {
    padding: 1rem;
}

/* Loading State */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error State */
.error {
    background: #fee2e2;
    color: #dc2626;
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .livestock-dashboard {
        padding: 1rem;
    }

    .analysis-container,
    .history-container {
        grid-template-columns: 1fr;
    }
} 