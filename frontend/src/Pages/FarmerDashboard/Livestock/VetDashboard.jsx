import React, { useState, useEffect } from "react";
import axios from "axios";
import {
  Box,
  Typography,
  Paper,
  Grid,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  TextField,
  InputAdornment,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from "@mui/material";
import {
  Search as SearchIcon,
  Pets as PetsIcon,
  LocalHospital as HospitalIcon,
  Person as PersonIcon,
  CalendarMonth as CalendarIcon,
  Assignment as AssignmentIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  HealthAndSafety as HealthIcon,
  Medication as MedicationIcon,
  Restaurant as FoodIcon,
  Description as DescriptionIcon,
  Download as DownloadIcon
} from '@mui/icons-material';

// TabPanel component for tab content
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`vet-tabpanel-${index}`}
      aria-labelledby={`vet-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 2 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const VetDashboard = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [appointments, setAppointments] = useState([]);
  const [patients, setPatients] = useState([]);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [healthRecords, setHealthRecords] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [openDialog, setOpenDialog] = useState(false);
  const [treatmentNotes, setTreatmentNotes] = useState("");
  const [selectedAppointment, setSelectedAppointment] = useState(null);

  // Mock data for appointments
  const mockAppointments = [
    {
      id: "app1",
      animalType: "Cow",
      animalBreed: "Holstein",
      animalAge: "3",
      animalWeight: "450",
      appointmentDate: new Date(2023, 5, 15, 10, 30),
      reason: "Regular checkup",
      notes: "Milk production has decreased slightly",
      status: "confirmed",
      farmerId: "farmer1",
      farmerName: "Rajesh Kumar",
      farmerContact: "+91 98765 43210"
    },
    {
      id: "app2",
      animalType: "Chicken",
      animalBreed: "Broiler",
      animalAge: "0.5",
      animalWeight: "2",
      appointmentDate: new Date(2023, 5, 18, 14, 0),
      reason: "Vaccination",
      notes: "Flock vaccination for Newcastle disease",
      status: "pending",
      farmerId: "farmer2",
      farmerName: "Suresh Patel",
      farmerContact: "+91 87654 32109"
    },
    {
      id: "app3",
      animalType: "Goat",
      animalBreed: "Boer",
      animalAge: "2",
      animalWeight: "65",
      appointmentDate: new Date(2023, 5, 12, 9, 0),
      reason: "Lameness",
      notes: "Difficulty walking for past 2 days",
      status: "completed",
      farmerId: "farmer3",
      farmerName: "Amit Singh",
      farmerContact: "+91 76543 21098",
      treatmentNotes: "Diagnosed with hoof rot. Trimmed hooves and prescribed antibiotics for 7 days. Follow-up in 2 weeks."
    }
  ];

  // Mock data for patients (livestock)
  const mockPatients = [
    {
      id: "ls1",
      type: "Cow",
      breed: "Holstein",
      age: "3",
      weight: "450",
      farmerId: "farmer1",
      farmerName: "Rajesh Kumar",
      healthStatus: "healthy",
      lastCheckup: new Date(2023, 4, 15),
      imageUrl: "https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80"
    },
    {
      id: "ls2",
      type: "Chicken",
      breed: "Broiler",
      age: "0.5",
      weight: "2",
      farmerId: "farmer2",
      farmerName: "Suresh Patel",
      healthStatus: "concerning",
      lastCheckup: new Date(2023, 4, 10),
      imageUrl: "https://images.unsplash.com/photo-**********-2bdb3c5beed7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80"
    },
    {
      id: "ls3",
      type: "Goat",
      breed: "Boer",
      age: "2",
      weight: "65",
      farmerId: "farmer3",
      farmerName: "Amit Singh",
      healthStatus: "critical",
      lastCheckup: new Date(2023, 5, 12),
      imageUrl: "https://images.unsplash.com/photo-**********-cae28c68512c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80"
    },
    {
      id: "ls4",
      type: "Buffalo",
      breed: "Murrah",
      age: "5",
      weight: "550",
      farmerId: "farmer1",
      farmerName: "Rajesh Kumar",
      healthStatus: "healthy",
      lastCheckup: new Date(2023, 3, 20),
      imageUrl: "https://images.unsplash.com/photo-1599911753561-8c33e5b06f7a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1169&q=80"
    }
  ];

  // Mock health records
  const mockHealthRecords = [
    {
      id: "hr1",
      livestockId: "ls1",
      date: new Date(2023, 4, 15),
      type: "Regular Checkup",
      diagnosis: "Healthy condition",
      treatment: "No treatment required",
      notes: "Good milk production, normal vital signs",
      vetName: "Dr. Sharma"
    },
    {
      id: "hr2",
      livestockId: "ls1",
      date: new Date(2023, 2, 10),
      type: "Vaccination",
      diagnosis: "Preventive care",
      treatment: "FMD vaccination administered",
      notes: "Next vaccination due in 6 months",
      vetName: "Dr. Sharma"
    },
    {
      id: "hr3",
      livestockId: "ls2",
      date: new Date(2023, 4, 10),
      type: "Disease Treatment",
      diagnosis: "Early signs of respiratory infection",
      treatment: "Antibiotics prescribed for 5 days",
      notes: "Monitor closely for improvement",
      vetName: "Dr. Patel"
    },
    {
      id: "hr4",
      livestockId: "ls3",
      date: new Date(2023, 5, 12),
      type: "Emergency",
      diagnosis: "Severe lameness due to hoof rot",
      treatment: "Hoof trimming, antibiotics for 7 days",
      notes: "Follow-up required in 2 weeks",
      vetName: "Dr. Singh"
    }
  ];

  useEffect(() => {
    // In a real app, fetch data from API
    // For now, use mock data
    setAppointments(mockAppointments);
    setPatients(mockPatients);
    setHealthRecords(mockHealthRecords);
  }, []);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const handlePatientSelect = (patient) => {
    setSelectedPatient(patient);
    // Filter health records for this patient
    const patientRecords = healthRecords.filter(record => record.livestockId === patient.id);
    setHealthRecords(patientRecords);
  };

  const handleAppointmentComplete = (appointment) => {
    setSelectedAppointment(appointment);
    setOpenDialog(true);
  };

  const handleTreatmentNotesChange = (event) => {
    setTreatmentNotes(event.target.value);
  };

  const handleSubmitTreatment = () => {
    setLoading(true);
    
    // In a real app, send data to API
    // For now, simulate API call
    setTimeout(() => {
      // Update appointment status
      const updatedAppointments = appointments.map(app => 
        app.id === selectedAppointment.id 
          ? { ...app, status: 'completed', treatmentNotes } 
          : app
      );
      setAppointments(updatedAppointments);
      
      // Add new health record
      const newRecord = {
        id: `hr${healthRecords.length + 1}`,
        livestockId: `ls${Math.floor(Math.random() * 4) + 1}`, // Mock ID
        date: new Date(),
        type: selectedAppointment.reason,
        diagnosis: "Based on examination",
        treatment: treatmentNotes,
        notes: selectedAppointment.notes,
        vetName: "Dr. Sharma" // Mock vet name
      };
      
      setHealthRecords([...healthRecords, newRecord]);
      
      // Close dialog and reset
      setOpenDialog(false);
      setTreatmentNotes("");
      setSelectedAppointment(null);
      setLoading(false);
    }, 1000);
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleString('en-US', {
      weekday: 'short',
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "healthy":
      case "confirmed":
        return "success";
      case "concerning":
      case "pending":
        return "warning";
      case "critical":
        return "error";
      case "completed":
        return "info";
      default:
        return "default";
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "healthy":
        return <CheckCircleIcon color="success" />;
      case "concerning":
        return <WarningIcon color="warning" />;
      case "critical":
        return <ErrorIcon color="error" />;
      default:
        return <InfoIcon color="info" />;
    }
  };

  // Filter appointments based on search term
  const filteredAppointments = appointments.filter(app => 
    app.animalType.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.animalBreed.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.farmerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.reason.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filter patients based on search term
  const filteredPatients = patients.filter(patient => 
    patient.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.breed.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.farmerName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h1">
          <HospitalIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Veterinarian Dashboard
        </Typography>
        <TextField
          placeholder="Search..."
          size="small"
          value={searchTerm}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ width: 250 }}
        />
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab icon={<CalendarIcon />} label="Appointments" />
          <Tab icon={<PetsIcon />} label="Patients" />
          <Tab icon={<AssignmentIcon />} label="Health Records" />
        </Tabs>

        {/* Appointments Tab */}
        <TabPanel value={activeTab} index={0}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Date & Time</TableCell>
                  <TableCell>Animal</TableCell>
                  <TableCell>Farmer</TableCell>
                  <TableCell>Reason</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredAppointments.length > 0 ? (
                  filteredAppointments.map((appointment) => (
                    <TableRow key={appointment.id}>
                      <TableCell>{formatDate(appointment.appointmentDate)}</TableCell>
                      <TableCell>
                        {appointment.animalType} ({appointment.animalBreed})
                        <Typography variant="caption" display="block">
                          Age: {appointment.animalAge} years, Weight: {appointment.animalWeight} kg
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {appointment.farmerName}
                        <Typography variant="caption" display="block">
                          {appointment.farmerContact}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {appointment.reason}
                        {appointment.notes && (
                          <Typography variant="caption" display="block">
                            Note: {appointment.notes}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={appointment.status}
                          color={getStatusColor(appointment.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {appointment.status === 'confirmed' || appointment.status === 'pending' ? (
                          <Button
                            variant="contained"
                            size="small"
                            color="primary"
                            onClick={() => handleAppointmentComplete(appointment)}
                          >
                            Complete
                          </Button>
                        ) : (
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<DescriptionIcon />}
                            disabled={!appointment.treatmentNotes}
                          >
                            View Notes
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <Typography variant="body1" sx={{ py: 2 }}>
                        No appointments found
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Patients Tab */}
        <TabPanel value={activeTab} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={selectedPatient ? 6 : 12}>
              <Grid container spacing={2}>
                {filteredPatients.length > 0 ? (
                  filteredPatients.map((patient) => (
                    <Grid item xs={12} sm={6} md={selectedPatient ? 6 : 4} key={patient.id}>
                      <Card 
                        sx={{ 
                          cursor: 'pointer',
                          transition: 'transform 0.2s',
                          '&:hover': { transform: 'translateY(-5px)' },
                          border: selectedPatient?.id === patient.id ? '2px solid #1976d2' : 'none'
                        }}
                        onClick={() => handlePatientSelect(patient)}
                      >
                        <CardMedia
                          component="img"
                          height="140"
                          image={patient.imageUrl}
                          alt={`${patient.type} - ${patient.breed}`}
                        />
                        <CardContent>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                            <Typography variant="h6" component="div">
                              {patient.type}
                            </Typography>
                            <Chip
                              label={patient.healthStatus}
                              color={getStatusColor(patient.healthStatus)}
                              size="small"
                              icon={getStatusIcon(patient.healthStatus)}
                            />
                          </Box>
                          <Typography variant="body2" color="text.secondary">
                            Breed: {patient.breed}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Age: {patient.age} years, Weight: {patient.weight} kg
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Owner: {patient.farmerName}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Last Checkup: {formatDate(patient.lastCheckup)}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))
                ) : (
                  <Grid item xs={12}>
                    <Box sx={{ textAlign: 'center', py: 3 }}>
                      <PetsIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
                      <Typography variant="body1" color="text.secondary">
                        No patients found
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </Grid>

            {selectedPatient && (
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 2, height: '100%' }}>
                  <Typography variant="h6" gutterBottom>
                    Health History
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  
                  {healthRecords.length > 0 ? (
                    <List>
                      {healthRecords.map((record) => (
                        <React.Fragment key={record.id}>
                          <ListItem alignItems="flex-start">
                            <ListItemIcon>
                              {record.type.toLowerCase().includes('emergency') ? (
                                <ErrorIcon color="error" />
                              ) : record.type.toLowerCase().includes('vaccination') ? (
                                <HealthIcon color="success" />
                              ) : record.type.toLowerCase().includes('treatment') ? (
                                <MedicationIcon color="warning" />
                              ) : (
                                <AssignmentIcon color="info" />
                              )}
                            </ListItemIcon>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography variant="subtitle1">{record.type}</Typography>
                                  <Typography variant="body2" color="text.secondary">
                                    {formatDate(record.date)}
                                  </Typography>
                                </Box>
                              }
                              secondary={
                                <>
                                  <Typography variant="body2" component="span" color="text.primary">
                                    Diagnosis: {record.diagnosis}
                                  </Typography>
                                  <Typography variant="body2" display="block">
                                    Treatment: {record.treatment}
                                  </Typography>
                                  {record.notes && (
                                    <Typography variant="body2" display="block" color="text.secondary">
                                      Notes: {record.notes}
                                    </Typography>
                                  )}
                                  <Typography variant="caption" display="block" color="text.secondary">
                                    Veterinarian: {record.vetName}
                                  </Typography>
                                </>
                              }
                            />
                          </ListItem>
                          <Divider variant="inset" component="li" />
                        </React.Fragment>
                      ))}
                    </List>
                  ) : (
                    <Box sx={{ textAlign: 'center', py: 3 }}>
                      <AssignmentIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
                      <Typography variant="body1" color="text.secondary">
                        No health records found
                      </Typography>
                    </Box>
                  )}
                  
                  <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                    <Button
                      variant="outlined"
                      startIcon={<DownloadIcon />}
                      disabled={healthRecords.length === 0}
                    >
                      Download Health Report
                    </Button>
                  </Box>
                </Paper>
              </Grid>
            )}
          </Grid>
        </TabPanel>

        {/* Health Records Tab */}
        <TabPanel value={activeTab} index={2}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Date</TableCell>
                  <TableCell>Animal</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Diagnosis</TableCell>
                  <TableCell>Treatment</TableCell>
                  <TableCell>Veterinarian</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {mockHealthRecords.length > 0 ? (
                  mockHealthRecords.map((record) => {
                    const patient = patients.find(p => p.id === record.livestockId);
                    return (
                      <TableRow key={record.id}>
                        <TableCell>{formatDate(record.date)}</TableCell>
                        <TableCell>
                          {patient ? (
                            <>
                              {patient.type} ({patient.breed})
                              <Typography variant="caption" display="block">
                                Owner: {patient.farmerName}
                              </Typography>
                            </>
                          ) : (
                            "Unknown Animal"
                          )}
                        </TableCell>
                        <TableCell>{record.type}</TableCell>
                        <TableCell>{record.diagnosis}</TableCell>
                        <TableCell>{record.treatment}</TableCell>
                        <TableCell>{record.vetName}</TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <Typography variant="body1" sx={{ py: 2 }}>
                        No health records found
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>
      </Paper>

      {/* Treatment Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Complete Appointment</DialogTitle>
        <DialogContent>
          {selectedAppointment && (
            <Box>
              <Typography variant="subtitle1" gutterBottom>
                {selectedAppointment.animalType} ({selectedAppointment.animalBreed})
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Owner: {selectedAppointment.farmerName}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Reason: {selectedAppointment.reason}
              </Typography>
              
              <TextField
                fullWidth
                label="Treatment Notes"
                multiline
                rows={4}
                value={treatmentNotes}
                onChange={handleTreatmentNotesChange}
                margin="normal"
                placeholder="Enter diagnosis, treatment provided, and any follow-up instructions..."
                required
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button
            onClick={handleSubmitTreatment}
            variant="contained"
            color="primary"
            disabled={loading || !treatmentNotes.trim()}
          >
            {loading ? <CircularProgress size={24} /> : "Complete & Save"}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VetDashboard;
