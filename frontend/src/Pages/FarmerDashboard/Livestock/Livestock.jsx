import React, { useState } from "react";
import axios from "axios";
import {
    Box,
    Button,
    TextField,
    Typography,
    Paper,
    CircularProgress,
    Tabs,
    Tab,
    Divider
} from "@mui/material";
import {
    Pets as PetsIcon,
    LocalHospital as HospitalIcon,
    VideoLibrary as VideoIcon,
    Person as PersonIcon
} from '@mui/icons-material';
import { getAuth } from "firebase/auth"; // ✅ Import Firebase Auth

// Import our new components
import VetAppointment from "./VetAppointment";
import VetDashboard from "./VetDashboard";

// TabPanel component for tab content
function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`livestock-tabpanel-${index}`}
            aria-labelledby={`livestock-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 2 }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

const LiveStock = () => {
    const [imageUrl, setImageUrl] = useState("");
    const [analysis, setAnalysis] = useState(null);
    const [loading, setLoading] = useState(false);
    const [activeTab, setActiveTab] = useState(0);
    const [userRole, setUserRole] = useState("farmer"); // In a real app, get this from auth

    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };

    const analyzeImage = async () => {
        setLoading(true);
        const auth = getAuth();
        const user = auth.currentUser;

        if (!user) {
            alert("You must be logged in to analyze livestock.");
            setLoading(false);
            return;
        }

        try {
            const token = await user.getIdToken(); // ✅ Get Firebase Auth Token
            const response = await axios.post(
                "http://localhost:8000/api/livestock/analyze",
                { imageUrl },
                { headers: { Authorization: `Bearer ${token}` } }
            );
            setAnalysis(response.data.analysis);
        } catch (error) {
            console.error("Error analyzing image", error);
        }
        setLoading(false);
    };

    return (
        <Box sx={{ p: 2 }}>
            <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <PetsIcon sx={{ mr: 1, fontSize: '2rem' }} />
                Livestock Management
            </Typography>

            <Paper sx={{ mb: 3 }}>
                <Tabs
                    value={activeTab}
                    onChange={handleTabChange}
                    variant="fullWidth"
                    indicatorColor="primary"
                    textColor="primary"
                >
                    <Tab icon={<VideoIcon />} label="CCTV Analysis" />
                    <Tab icon={<HospitalIcon />} label="Vet Appointments" />
                    {userRole === "vet" && <Tab icon={<PersonIcon />} label="Vet Dashboard" />}
                </Tabs>

                {/* CCTV Analysis Tab */}
                <TabPanel value={activeTab} index={0}>
                    <Typography variant="h6" gutterBottom>Livestock CCTV Analysis</Typography>
                    <Divider sx={{ mb: 2 }} />

                    <Paper sx={{ p: 3, mb: 3 }}>
                        <TextField fullWidth label="Image URL" value={imageUrl} onChange={(e) => setImageUrl(e.target.value)} />
                        <Button variant="contained" color="primary" sx={{ mt: 2 }} onClick={analyzeImage} disabled={loading}>
                            {loading ? <CircularProgress size={24} /> : "Analyze Image"}
                        </Button>
                    </Paper>
                    {analysis && (
                        <Paper sx={{ p: 3 }}>
                            <Typography variant="h6">Analysis Result:</Typography>
                            <pre>{JSON.stringify(analysis, null, 2)}</pre>
                        </Paper>
                    )}
                </TabPanel>

                {/* Vet Appointments Tab */}
                <TabPanel value={activeTab} index={1}>
                    <VetAppointment />
                </TabPanel>

                {/* Vet Dashboard Tab - only visible to vets */}
                {userRole === "vet" && (
                    <TabPanel value={activeTab} index={2}>
                        <VetDashboard />
                    </TabPanel>
                )}
            </Paper>

            {/* Toggle button to switch between farmer and vet roles (for demo purposes) */}
            <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Button
                    variant="outlined"
                    onClick={() => setUserRole(userRole === "farmer" ? "vet" : "farmer")}
                >
                    Switch to {userRole === "farmer" ? "Vet" : "Farmer"} View
                </Button>
                <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                    Currently viewing as: <strong>{userRole === "farmer" ? "Farmer" : "Veterinarian"}</strong>
                </Typography>
            </Box>
        </Box>
    );
};

export default LiveStock;

