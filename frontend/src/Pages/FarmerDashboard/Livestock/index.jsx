import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { makeApiCall, ENDPOINTS } from '../../../config/api';
import { Box, Button, Container, Typography, Paper, Tabs, Tab } from '@mui/material';
import {
    ArrowBack as ArrowBackIcon,
    Pets as PetsIcon,
    LocalHospital as HospitalIcon,
    VideoLibrary as VideoIcon,
    MonitorHeart as HealthIcon,
    LocationOn as TrackingIcon,
    ImageSearch as AnalysisIcon
} from '@mui/icons-material';
import LiveFeed from './components/LiveFeed';
import HealthMonitoring from './components/HealthMonitoring';
import LivestockTracking from './components/LivestockTracking';
import LivestockAnalysis from './components/LivestockAnalysis';
import VetAppointment from './VetAppointment';
import { useTranslation } from 'react-i18next';
import './Livestock.css';

const LivestockDashboard = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState(0);

    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };

    const handleBack = () => {
        navigate('/dashboard');
    };

    return (
        <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Button
                    variant="contained"
                    startIcon={<ArrowBackIcon />}
                    onClick={handleBack}
                    sx={{
                        mr: 2,
                        bgcolor: '#2e7d32',
                        '&:hover': {
                            bgcolor: '#1b5e20'
                        }
                    }}
                >
                    {t("back_to_dash_msg")}
                </Button>
                <Typography variant="h4" component="h1">
                    {t("livestock_msg")}
                </Typography>
            </Box>

            <Paper sx={{ mb: 3 }}>
                <Tabs
                    value={activeTab}
                    onChange={handleTabChange}
                    variant="fullWidth"
                    indicatorColor="primary"
                    textColor="primary"
                    sx={{
                        '& .MuiTab-root': {
                            fontWeight: 'bold',
                            py: 2
                        }
                    }}
                >
                    <Tab icon={<VideoIcon />} label="Live CCTV Feed" />
                    <Tab icon={<HealthIcon />} label="Health Monitoring" />
                    <Tab icon={<TrackingIcon />} label="Livestock Tracking" />
                    <Tab icon={<AnalysisIcon />} label="Image Analysis" />
                    <Tab icon={<HospitalIcon />} label="Vet Appointments" />
                </Tabs>
            </Paper>

            {/* Tab Panels */}
            <Box sx={{ mt: 2 }}>
                {activeTab === 0 && <LiveFeed />}
                {activeTab === 1 && <HealthMonitoring />}
                {activeTab === 2 && <LivestockTracking />}
                {activeTab === 3 && <LivestockAnalysis />}
                {activeTab === 4 && <VetAppointment />}
            </Box>
        </Container>
    );
};

export default LivestockDashboard;