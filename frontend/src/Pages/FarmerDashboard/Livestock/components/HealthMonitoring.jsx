import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Grid, 
  Card, 
  CardContent,
  LinearProgress,
  Divider,
  Chip,
  Stack
} from '@mui/material';
import {
  Favorite as HeartIcon,
  LocalHospital as MedicalIcon,
  Restaurant as FoodIcon,
  Opacity as WaterIcon,
  DirectionsWalk as ActivityIcon,
  Thermostat as TemperatureIcon
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

// Mock data for health metrics
const mockHealthData = {
  currentMetrics: {
    heartRate: 72,
    temperature: 38.5,
    feedIntake: 85,
    waterIntake: 90,
    activityLevel: 78,
    weight: 450
  },
  status: 'Healthy',
  lastUpdated: new Date().toLocaleString(),
  historicalData: [
    { time: '06:00', heartRate: 68, temperature: 38.2, activityLevel: 45 },
    { time: '09:00', heartRate: 72, temperature: 38.4, activityLevel: 80 },
    { time: '12:00', heartRate: 75, temperature: 38.6, activityLevel: 85 },
    { time: '15:00', heartRate: 73, temperature: 38.5, activityLevel: 75 },
    { time: '18:00', heartRate: 70, temperature: 38.3, activityLevel: 60 },
    { time: '21:00', heartRate: 65, temperature: 38.1, activityLevel: 30 }
  ]
};

const HealthMonitoring = () => {
  const [healthData, setHealthData] = useState(mockHealthData);
  const [selectedMetric, setSelectedMetric] = useState('heartRate');

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setHealthData(prev => {
        const heartRateChange = Math.floor(Math.random() * 5) - 2; // -2 to +2
        const temperatureChange = (Math.random() * 0.4 - 0.2).toFixed(1); // -0.2 to +0.2
        const activityChange = Math.floor(Math.random() * 10) - 5; // -5 to +5
        
        const newHeartRate = Math.max(60, Math.min(85, prev.currentMetrics.heartRate + heartRateChange));
        const newTemperature = Math.max(37.8, Math.min(39.2, parseFloat(prev.currentMetrics.temperature) + parseFloat(temperatureChange)));
        const newActivity = Math.max(30, Math.min(95, prev.currentMetrics.activityLevel + activityChange));
        
        // Add new data point occasionally
        if (Math.random() > 0.7) {
          const now = new Date();
          const time = now.getHours().toString().padStart(2, '0') + ':' + 
                      now.getMinutes().toString().padStart(2, '0');
          
          const newDataPoint = {
            time,
            heartRate: newHeartRate,
            temperature: newTemperature,
            activityLevel: newActivity
          };
          
          return {
            ...prev,
            currentMetrics: {
              ...prev.currentMetrics,
              heartRate: newHeartRate,
              temperature: newTemperature,
              activityLevel: newActivity
            },
            lastUpdated: now.toLocaleString(),
            historicalData: [...prev.historicalData.slice(-5), newDataPoint]
          };
        }
        
        return {
          ...prev,
          currentMetrics: {
            ...prev.currentMetrics,
            heartRate: newHeartRate,
            temperature: newTemperature,
            activityLevel: newActivity
          },
          lastUpdated: new Date().toLocaleString()
        };
      });
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);

  // Get status color
  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'healthy':
        return '#4caf50';
      case 'warning':
        return '#ff9800';
      case 'critical':
        return '#f44336';
      default:
        return '#2196f3';
    }
  };

  // Get metric color
  const getMetricColor = (metric, value) => {
    switch (metric) {
      case 'heartRate':
        return value < 65 ? '#ff9800' : value > 80 ? '#ff9800' : '#4caf50';
      case 'temperature':
        return value < 38.0 ? '#ff9800' : value > 39.0 ? '#f44336' : '#4caf50';
      case 'feedIntake':
      case 'waterIntake':
        return value < 70 ? '#ff9800' : '#4caf50';
      case 'activityLevel':
        return value < 50 ? '#ff9800' : value > 90 ? '#ff9800' : '#4caf50';
      default:
        return '#2196f3';
    }
  };

  // Get metric icon
  const getMetricIcon = (metric) => {
    switch (metric) {
      case 'heartRate':
        return <HeartIcon sx={{ color: '#f44336' }} />;
      case 'temperature':
        return <TemperatureIcon sx={{ color: '#ff9800' }} />;
      case 'feedIntake':
        return <FoodIcon sx={{ color: '#4caf50' }} />;
      case 'waterIntake':
        return <WaterIcon sx={{ color: '#2196f3' }} />;
      case 'activityLevel':
        return <ActivityIcon sx={{ color: '#9c27b0' }} />;
      default:
        return <MedicalIcon sx={{ color: '#2196f3' }} />;
    }
  };

  // Get metric unit
  const getMetricUnit = (metric) => {
    switch (metric) {
      case 'heartRate':
        return 'bpm';
      case 'temperature':
        return '°C';
      case 'feedIntake':
      case 'waterIntake':
      case 'activityLevel':
        return '%';
      case 'weight':
        return 'kg';
      default:
        return '';
    }
  };

  // Get metric name
  const getMetricName = (metric) => {
    switch (metric) {
      case 'heartRate':
        return 'Heart Rate';
      case 'temperature':
        return 'Temperature';
      case 'feedIntake':
        return 'Feed Intake';
      case 'waterIntake':
        return 'Water Intake';
      case 'activityLevel':
        return 'Activity Level';
      case 'weight':
        return 'Weight';
      default:
        return metric;
    }
  };

  return (
    <Box sx={{ width: '100%', mb: 4 }}>
      <Paper elevation={3} sx={{ p: 2, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <MedicalIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h5">Health Monitoring</Typography>
          <Box sx={{ flexGrow: 1 }} />
          <Chip 
            label={healthData.status} 
            sx={{ 
              backgroundColor: getStatusColor(healthData.status),
              color: 'white',
              fontWeight: 'bold'
            }} 
          />
        </Box>
        
        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 3 }}>
          Last updated: {healthData.lastUpdated}
        </Typography>
        
        <Grid container spacing={3}>
          {/* Current Metrics */}
          <Grid item xs={12} md={6}>
            <Grid container spacing={2}>
              {Object.entries(healthData.currentMetrics).map(([metric, value]) => (
                <Grid item xs={6} sm={4} key={metric}>
                  <Card 
                    elevation={2} 
                    sx={{ 
                      cursor: 'pointer',
                      border: selectedMetric === metric ? `2px solid ${getMetricColor(metric, value)}` : 'none',
                      transition: 'all 0.2s'
                    }}
                    onClick={() => setSelectedMetric(metric)}
                  >
                    <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        {getMetricIcon(metric)}
                        <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                          {getMetricName(metric)}
                        </Typography>
                      </Box>
                      <Typography variant="h5" component="div" sx={{ mb: 1 }}>
                        {value} <Typography component="span" variant="caption" color="text.secondary">{getMetricUnit(metric)}</Typography>
                      </Typography>
                      <LinearProgress 
                        variant="determinate" 
                        value={metric === 'weight' ? 100 : value} 
                        sx={{ 
                          height: 6, 
                          borderRadius: 3,
                          backgroundColor: '#e0e0e0',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: getMetricColor(metric, value)
                          }
                        }}
                      />
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Grid>
          
          {/* Historical Chart */}
          <Grid item xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 2, height: '100%' }}>
              <Typography variant="subtitle1" gutterBottom>
                {getMetricName(selectedMetric)} Trend
              </Typography>
              <Box sx={{ height: 250 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={healthData.historicalData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="time" />
                    <YAxis 
                      domain={
                        selectedMetric === 'temperature' 
                          ? [37.5, 39.5] 
                          : selectedMetric === 'heartRate'
                            ? [60, 90]
                            : [0, 100]
                      }
                    />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey={selectedMetric}
                      stroke={
                        selectedMetric === 'heartRate' 
                          ? '#f44336' 
                          : selectedMetric === 'temperature'
                            ? '#ff9800'
                            : selectedMetric === 'activityLevel'
                              ? '#9c27b0'
                              : '#2196f3'
                      }
                      strokeWidth={2}
                      dot={{ r: 4 }}
                      activeDot={{ r: 6 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </Paper>
          </Grid>
          
          {/* Health Recommendations */}
          <Grid item xs={12}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Health Recommendations
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Stack spacing={1}>
                <Typography variant="body2">
                  • Maintain current feeding schedule as vital signs are within normal range
                </Typography>
                <Typography variant="body2">
                  • Monitor water intake during peak afternoon hours
                </Typography>
                <Typography variant="body2">
                  • Schedule routine health check-up within the next 2 weeks
                </Typography>
                <Typography variant="body2">
                  • Consider increasing shelter ventilation as temperature rises during midday
                </Typography>
              </Stack>
            </Paper>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default HealthMonitoring;
