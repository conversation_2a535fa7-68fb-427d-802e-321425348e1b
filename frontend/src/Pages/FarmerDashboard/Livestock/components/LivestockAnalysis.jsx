import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  MenuItem,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  FormControl,
  InputLabel,
  Select
} from '@mui/material';
import {
  Upload as UploadIcon,
  Check as CheckIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  LocalHospital as MedicalIcon,
  Restaurant as FoodIcon,
  Pets as PetsIcon
} from '@mui/icons-material';
import { makeApiCall, ENDPOINTS } from '../../../../config/api';

const LivestockAnalysis = () => {
  const [imageUrl, setImageUrl] = useState('');
  const [analysis, setAnalysis] = useState(null);
  const [historicalData, setHistoricalData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    livestockType: 'cattle',
    breed: '',
    age: '',
    weight: '',
    notes: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = () => {
      setImageUrl(reader.result);
    };
    reader.readAsDataURL(file);
  };

  const handleAnalyzeImage = async () => {
    // Validate form data
    if (!imageUrl) {
      setError('Please upload an image first');
      return;
    }

    if (!formData.breed || !formData.age || !formData.weight) {
      setError('Please fill in all required fields');
      return;
    }

    // Upload and analyze the image
    setLoading(true);
    setError('');

    try {
      // Convert data URL to File object
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const file = new File([blob], 'livestock-image.jpg', { type: 'image/jpeg' });

      const formDataToSend = new FormData();
      formDataToSend.append('image', file);
      formDataToSend.append('livestockType', formData.livestockType);
      formDataToSend.append('breed', formData.breed);
      formDataToSend.append('age', formData.age);
      formDataToSend.append('weight', formData.weight);
      formDataToSend.append('notes', formData.notes);

      const apiResponse = await makeApiCall(
        ENDPOINTS.LIVESTOCK.ANALYZE,
        {
          method: 'POST',
          body: formDataToSend,
          headers: {
            // Don't set Content-Type when sending FormData
          }
        }
      );

      if (apiResponse.success) {
        setAnalysis(apiResponse.analysis);
        // Fetch updated historical data
        fetchHistoricalData();
      } else {
        setError(apiResponse.message || 'Failed to analyze image');
      }
    } catch (err) {
      console.error('Error analyzing image:', err);
      setError('Failed to analyze image. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchHistoricalData = async () => {
    try {
      const currentYear = new Date().getFullYear();
      const response = await makeApiCall(
        `${ENDPOINTS.LIVESTOCK.HISTORY}?startYear=${currentYear - 5}&endYear=${currentYear}`
      );

      if (response.success) {
        setHistoricalData(response.historicalData || []);
      }
    } catch (err) {
      console.error('Error fetching historical data:', err);
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    if (!status) return '#9e9e9e';
    
    switch (status.toLowerCase()) {
      case 'healthy':
        return '#4caf50';
      case 'concerning':
        return '#ff9800';
      case 'critical':
        return '#f44336';
      default:
        return '#9e9e9e';
    }
  };

  return (
    <Box sx={{ width: '100%', mb: 4 }}>
      <Paper elevation={3} sx={{ p: 2, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <PetsIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h5">Livestock Image Analysis</Typography>
        </Box>

        <Grid container spacing={3}>
          {/* Form Section */}
          <Grid item xs={12} md={6}>
            <Paper elevation={2} sx={{ p: 2, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Analyze Livestock Health
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Upload an image of your livestock and provide details for AI-powered health analysis.
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Livestock Type</InputLabel>
                    <Select
                      name="livestockType"
                      value={formData.livestockType}
                      onChange={handleInputChange}
                      label="Livestock Type"
                    >
                      <MenuItem value="cattle">Cattle</MenuItem>
                      <MenuItem value="sheep">Sheep</MenuItem>
                      <MenuItem value="goat">Goat</MenuItem>
                      <MenuItem value="pig">Pig</MenuItem>
                      <MenuItem value="poultry">Poultry</MenuItem>
                      <MenuItem value="horse">Horse</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Breed"
                    name="breed"
                    value={formData.breed}
                    onChange={handleInputChange}
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Age"
                    name="age"
                    value={formData.age}
                    onChange={handleInputChange}
                    placeholder="e.g., 3 years"
                    required
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Weight (kg)"
                    name="weight"
                    type="number"
                    value={formData.weight}
                    onChange={handleInputChange}
                    required
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Additional Notes"
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    multiline
                    rows={3}
                    placeholder="Any specific concerns or observations..."
                  />
                </Grid>
                <Grid item xs={12}>
                  <Button
                    variant="outlined"
                    component="label"
                    startIcon={<UploadIcon />}
                    sx={{ mb: 2 }}
                    fullWidth
                  >
                    Upload Image
                    <input
                      type="file"
                      accept="image/*"
                      hidden
                      onChange={handleImageUpload}
                    />
                  </Button>
                  {imageUrl && (
                    <Box sx={{ mt: 2, mb: 2 }}>
                      <img
                        src={imageUrl}
                        alt="Livestock preview"
                        style={{ width: '100%', borderRadius: 8, maxHeight: '300px', objectFit: 'cover' }}
                      />
                    </Box>
                  )}
                </Grid>
                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleAnalyzeImage}
                    disabled={loading || !imageUrl}
                    fullWidth
                    sx={{ mt: 1 }}
                  >
                    {loading ? <CircularProgress size={24} /> : 'Analyze Image'}
                  </Button>
                </Grid>
                {error && (
                  <Grid item xs={12}>
                    <Alert severity="error">{error}</Alert>
                  </Grid>
                )}
              </Grid>
            </Paper>
          </Grid>

          {/* Analysis Results */}
          <Grid item xs={12} md={6}>
            <Paper elevation={2} sx={{ p: 2, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Analysis Results
              </Typography>
              
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
                  <CircularProgress />
                </Box>
              ) : analysis ? (
                <Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Typography variant="subtitle1" sx={{ mr: 1 }}>Health Status:</Typography>
                    <Chip
                      label={analysis.overallHealth || 'Unknown'}
                      sx={{
                        backgroundColor: getStatusColor(analysis.overallHealth),
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  </Box>

                  <Typography variant="subtitle1" gutterBottom>Physical Condition</Typography>
                  <Typography variant="body2" paragraph>
                    {analysis.physicalCondition || 'No data available'}
                  </Typography>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="subtitle1" gutterBottom>Potential Issues</Typography>
                  {analysis.potentialIssues && analysis.potentialIssues.length > 0 ? (
                    <List dense>
                      {analysis.potentialIssues.map((issue, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <WarningIcon color="warning" />
                          </ListItemIcon>
                          <ListItemText primary={issue} />
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Typography variant="body2">No issues detected</Typography>
                  )}

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="subtitle1" gutterBottom>Recommendations</Typography>
                  {analysis.recommendations && analysis.recommendations.length > 0 ? (
                    <List dense>
                      {analysis.recommendations.map((rec, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <CheckIcon color="success" />
                          </ListItemIcon>
                          <ListItemText primary={rec} />
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Typography variant="body2">No recommendations available</Typography>
                  )}

                  <Divider sx={{ my: 2 }} />

                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                    <FoodIcon color="primary" sx={{ mr: 1, mt: 0.5 }} />
                    <Box>
                      <Typography variant="subtitle1" gutterBottom>Nutritional Advice</Typography>
                      <Typography variant="body2">
                        {analysis.nutritionalAdvice || 'No nutritional advice available'}
                      </Typography>
                    </Box>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="subtitle1" gutterBottom>Summary</Typography>
                  <Typography variant="body2">
                    {analysis.summary || 'No summary available'}
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '300px' }}>
                  <InfoIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2, opacity: 0.5 }} />
                  <Typography variant="body1" color="text.secondary">
                    Upload an image and provide details to get AI-powered health analysis
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Historical Analysis */}
          {historicalData.length > 0 && (
            <Grid item xs={12}>
              <Paper elevation={2} sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Historical Analysis
                </Typography>
                <Grid container spacing={2}>
                  {historicalData.map((entry, index) => (
                    <Grid item xs={12} sm={6} md={4} key={index}>
                      <Card elevation={1}>
                        <CardContent>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Typography variant="subtitle1">
                              {entry.livestockType} - {entry.breed}
                            </Typography>
                            <Chip
                              label={entry.healthStatus || 'Unknown'}
                              size="small"
                              sx={{
                                backgroundColor: getStatusColor(entry.healthStatus),
                                color: 'white'
                              }}
                            />
                          </Box>
                          <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                            Analyzed on {new Date(entry.createdAt).toLocaleDateString()}
                          </Typography>
                          <Box sx={{ mt: 2, mb: 2 }}>
                            <img
                              src={entry.imageUrl}
                              alt={`${entry.livestockType} analysis`}
                              style={{ width: '100%', borderRadius: 4, height: '120px', objectFit: 'cover' }}
                              onError={(e) => {
                                e.target.onerror = null;
                                e.target.src = 'https://placehold.co/400x300/222/fff?text=Image+Not+Available';
                              }}
                            />
                          </Box>
                          <Typography variant="body2" noWrap>
                            {entry.analysis?.summary || 'No summary available'}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Paper>
            </Grid>
          )}
        </Grid>
      </Paper>
    </Box>
  );
};

export default LivestockAnalysis;
