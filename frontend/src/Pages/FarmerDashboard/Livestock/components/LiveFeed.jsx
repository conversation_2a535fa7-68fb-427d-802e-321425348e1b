import React, { useState, useEffect, useRef } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Grid, 
  Button, 
  CircularProgress,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Videocam as VideocamIcon,
  VideocamOff as VideocamOffIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  Warning as WarningIcon,
  PlayArrow as PlayArrowIcon,
  Pause as PauseIcon
} from '@mui/icons-material';

// Mock data for behavior analysis
const mockBehaviorData = {
  currentBehavior: 'Grazing',
  confidence: 92,
  duration: '00:15:32',
  recentBehaviors: [
    { behavior: 'Grazing', duration: '00:15:32', timestamp: '10:45 AM', confidence: 92 },
    { behavior: 'Walking', duration: '00:03:21', timestamp: '10:30 AM', confidence: 88 },
    { behavior: 'Standing', duration: '00:08:45', timestamp: '10:20 AM', confidence: 95 },
    { behavior: 'Drinking', duration: '00:01:12', timestamp: '10:15 AM', confidence: 90 }
  ],
  alerts: [
    { type: 'warning', message: 'Unusual movement pattern detected', timestamp: '09:45 AM' },
    { type: 'info', message: 'Drinking frequency below average', timestamp: '08:30 AM' }
  ]
};

// Behavior color mapping
const behaviorColors = {
  'Grazing': '#4caf50',
  'Walking': '#2196f3',
  'Standing': '#ff9800',
  'Drinking': '#9c27b0',
  'Lying': '#795548',
  'Running': '#f44336'
};

const LiveFeed = () => {
  const [isStreaming, setIsStreaming] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [loading, setLoading] = useState(false);
  const [behaviorData, setBehaviorData] = useState(mockBehaviorData);
  const [selectedCamera, setSelectedCamera] = useState('Camera 1');
  const videoRef = useRef(null);
  const containerRef = useRef(null);

  // Simulate behavior updates
  useEffect(() => {
    const interval = setInterval(() => {
      if (isStreaming && !isPaused) {
        const behaviors = ['Grazing', 'Walking', 'Standing', 'Drinking', 'Lying', 'Running'];
        const randomBehavior = behaviors[Math.floor(Math.random() * behaviors.length)];
        const confidence = Math.floor(Math.random() * 15) + 85; // 85-99
        
        // Update current behavior occasionally
        if (Math.random() > 0.7) {
          const hours = String(Math.floor(Math.random() * 1)).padStart(2, '0');
          const minutes = String(Math.floor(Math.random() * 20)).padStart(2, '0');
          const seconds = String(Math.floor(Math.random() * 60)).padStart(2, '0');
          const duration = `${hours}:${minutes}:${seconds}`;
          
          const now = new Date();
          const timestamp = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          
          const newBehavior = {
            behavior: randomBehavior,
            duration,
            timestamp,
            confidence
          };
          
          setBehaviorData(prev => ({
            ...prev,
            currentBehavior: randomBehavior,
            confidence,
            duration,
            recentBehaviors: [newBehavior, ...prev.recentBehaviors.slice(0, 3)]
          }));
        }
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [isStreaming, isPaused]);

  // Toggle streaming
  const toggleStreaming = () => {
    setLoading(true);
    setTimeout(() => {
      setIsStreaming(!isStreaming);
      setLoading(false);
    }, 1000);
  };

  // Toggle fullscreen
  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };

  // Toggle pause
  const togglePause = () => {
    setIsPaused(!isPaused);
  };

  // Handle camera selection
  const handleCameraChange = (camera) => {
    setLoading(true);
    setSelectedCamera(camera);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  return (
    <Box ref={containerRef} sx={{ width: '100%', mb: 4 }}>
      <Paper elevation={3} sx={{ p: 2, borderRadius: 2 }}>
        <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <VideocamIcon color="primary" /> Live CCTV Monitoring
        </Typography>
        
        <Grid container spacing={3}>
          {/* Video Feed */}
          <Grid item xs={12} md={8}>
            <Box 
              sx={{ 
                position: 'relative', 
                height: isFullscreen ? '80vh' : '400px',
                backgroundColor: '#000',
                borderRadius: 1,
                overflow: 'hidden'
              }}
            >
              {loading ? (
                <Box sx={{ 
                  display: 'flex', 
                  justifyContent: 'center', 
                  alignItems: 'center',
                  height: '100%'
                }}>
                  <CircularProgress />
                </Box>
              ) : isStreaming ? (
                <Box sx={{ height: '100%', width: '100%', position: 'relative' }}>
                  <img 
                    ref={videoRef}
                    src={`/livestock-feed-${selectedCamera.toLowerCase().replace(' ', '-')}.jpg`}
                    alt="Livestock CCTV Feed"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      filter: isPaused ? 'grayscale(100%)' : 'none'
                    }}
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = 'https://placehold.co/800x600/333/white?text=Livestock+Camera+Feed';
                    }}
                  />
                  
                  {/* Behavior Overlay */}
                  <Box sx={{ 
                    position: 'absolute', 
                    bottom: 16, 
                    left: 16, 
                    backgroundColor: 'rgba(0,0,0,0.7)',
                    color: 'white',
                    padding: '8px 12px',
                    borderRadius: 1,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                  }}>
                    <Chip 
                      label={behaviorData.currentBehavior} 
                      sx={{ 
                        backgroundColor: behaviorColors[behaviorData.currentBehavior] || '#666',
                        color: 'white',
                        fontWeight: 'bold'
                      }} 
                    />
                    <Typography variant="body2">
                      {behaviorData.confidence}% confidence
                    </Typography>
                  </Box>
                  
                  {/* Camera Info */}
                  <Box sx={{ 
                    position: 'absolute', 
                    top: 16, 
                    left: 16, 
                    backgroundColor: 'rgba(0,0,0,0.7)',
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: 1,
                    fontSize: '0.75rem'
                  }}>
                    {selectedCamera} • LIVE
                    <Box 
                      component="span" 
                      sx={{ 
                        display: 'inline-block', 
                        width: 8, 
                        height: 8, 
                        borderRadius: '50%', 
                        backgroundColor: '#f44336', 
                        ml: 1,
                        animation: 'pulse 1.5s infinite'
                      }} 
                    />
                  </Box>
                  
                  {/* Controls Overlay */}
                  <Box sx={{ 
                    position: 'absolute', 
                    bottom: 16, 
                    right: 16, 
                    display: 'flex',
                    gap: 1
                  }}>
                    <Tooltip title={isPaused ? "Resume" : "Pause"}>
                      <IconButton 
                        size="small" 
                        onClick={togglePause}
                        sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                      >
                        {isPaused ? <PlayArrowIcon /> : <PauseIcon />}
                      </IconButton>
                    </Tooltip>
                    <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
                      <IconButton 
                        size="small" 
                        onClick={toggleFullscreen}
                        sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                      >
                        {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              ) : (
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: 'column',
                  justifyContent: 'center', 
                  alignItems: 'center',
                  height: '100%',
                  color: 'white'
                }}>
                  <VideocamOffIcon sx={{ fontSize: 48, mb: 2, opacity: 0.7 }} />
                  <Typography variant="h6" sx={{ mb: 2 }}>Camera Feed Offline</Typography>
                  <Button 
                    variant="outlined" 
                    color="primary" 
                    onClick={toggleStreaming}
                    startIcon={<VideocamIcon />}
                  >
                    Start Stream
                  </Button>
                </Box>
              )}
            </Box>
            
            {/* Camera Selection */}
            <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
              {['Camera 1', 'Camera 2', 'Camera 3'].map(camera => (
                <Button 
                  key={camera}
                  variant={selectedCamera === camera ? "contained" : "outlined"}
                  size="small"
                  onClick={() => handleCameraChange(camera)}
                  disabled={!isStreaming || loading}
                >
                  {camera}
                </Button>
              ))}
              <Box sx={{ flexGrow: 1 }} />
              <Button
                variant={isStreaming ? "outlined" : "contained"}
                color={isStreaming ? "error" : "primary"}
                onClick={toggleStreaming}
                startIcon={isStreaming ? <VideocamOffIcon /> : <VideocamIcon />}
                disabled={loading}
              >
                {isStreaming ? "Stop Stream" : "Start Stream"}
              </Button>
            </Box>
          </Grid>
          
          {/* Behavior Analysis */}
          <Grid item xs={12} md={4}>
            <Paper elevation={2} sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
              <Typography variant="h6" gutterBottom>
                Behavior Analysis
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Current Behavior
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Chip 
                    label={behaviorData.currentBehavior} 
                    sx={{ 
                      backgroundColor: behaviorColors[behaviorData.currentBehavior] || '#666',
                      color: 'white',
                      fontWeight: 'bold'
                    }} 
                  />
                  <Typography variant="body2">
                    for {behaviorData.duration}
                  </Typography>
                </Box>
                <Box sx={{ 
                  width: '100%', 
                  height: 8, 
                  backgroundColor: '#eee', 
                  borderRadius: 5,
                  mb: 2
                }}>
                  <Box sx={{ 
                    width: `${behaviorData.confidence}%`, 
                    height: '100%', 
                    backgroundColor: behaviorData.confidence > 90 ? '#4caf50' : behaviorData.confidence > 80 ? '#ff9800' : '#f44336',
                    borderRadius: 5
                  }} />
                </Box>
              </Box>
              
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Recent Behaviors
              </Typography>
              <Box sx={{ mb: 2, flexGrow: 1, overflow: 'auto' }}>
                {behaviorData.recentBehaviors.map((item, index) => (
                  <Box 
                    key={index} 
                    sx={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'space-between',
                      p: 1,
                      borderBottom: index !== behaviorData.recentBehaviors.length - 1 ? '1px solid #eee' : 'none'
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Chip 
                        label={item.behavior} 
                        size="small"
                        sx={{ 
                          backgroundColor: behaviorColors[item.behavior] || '#666',
                          color: 'white'
                        }} 
                      />
                      <Typography variant="body2" color="text.secondary">
                        {item.duration}
                      </Typography>
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      {item.timestamp}
                    </Typography>
                  </Box>
                ))}
              </Box>
              
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Alerts
              </Typography>
              <Stack spacing={1}>
                {behaviorData.alerts.map((alert, index) => (
                  <Alert 
                    key={index} 
                    severity={alert.type} 
                    icon={alert.type === 'warning' ? <WarningIcon fontSize="inherit" /> : undefined}
                    sx={{ py: 0 }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                      <Typography variant="body2">{alert.message}</Typography>
                      <Typography variant="caption" color="text.secondary">{alert.timestamp}</Typography>
                    </Box>
                  </Alert>
                ))}
              </Stack>
            </Paper>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default LiveFeed;
