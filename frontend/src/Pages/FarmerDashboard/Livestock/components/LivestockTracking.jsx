import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Grid, 
  Card, 
  CardContent,
  CardMedia,
  Chip,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Badge
} from '@mui/material';
import {
  Pets as PetsIcon,
  LocationOn as LocationIcon,
  Info as InfoIcon,
  Visibility as VisibilityIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon
} from '@mui/icons-material';

// Mock data for livestock tracking
const mockLivestockData = [
  {
    id: 'LV-001',
    name: 'Bella',
    species: 'Cow',
    breed: 'Holstein',
    age: '3 years',
    weight: '550 kg',
    status: 'Healthy',
    location: 'Pasture A',
    lastSeen: '10 minutes ago',
    image: 'https://placehold.co/400x300/222/fff?text=Cow+ID:+LV-001',
    healthHistory: [
      { date: '2023-04-01', event: 'Vaccination', details: 'Annual vaccination completed' },
      { date: '2023-02-15', event: 'Health Check', details: 'Regular health check - all parameters normal' }
    ],
    alerts: []
  },
  {
    id: 'LV-002',
    name: '<PERSON>',
    species: 'Cow',
    breed: '<PERSON>',
    age: '4 years',
    weight: '620 kg',
    status: 'Warning',
    location: 'Barn 2',
    lastSeen: '5 minutes ago',
    image: 'https://placehold.co/400x300/222/fff?text=Cow+ID:+LV-002',
    healthHistory: [
      { date: '2023-03-20', event: 'Treatment', details: 'Treated for minor skin condition' },
      { date: '2023-01-10', event: 'Health Check', details: 'Regular health check - all parameters normal' }
    ],
    alerts: [
      { type: 'warning', message: 'Reduced feed intake in the last 24 hours' }
    ]
  },
  {
    id: 'LV-003',
    name: 'Daisy',
    species: 'Cow',
    breed: 'Jersey',
    age: '2 years',
    weight: '480 kg',
    status: 'Healthy',
    location: 'Pasture B',
    lastSeen: '15 minutes ago',
    image: 'https://placehold.co/400x300/222/fff?text=Cow+ID:+LV-003',
    healthHistory: [
      { date: '2023-03-05', event: 'Vaccination', details: 'Annual vaccination completed' },
      { date: '2023-01-20', event: 'Health Check', details: 'Regular health check - all parameters normal' }
    ],
    alerts: []
  },
  {
    id: 'LV-004',
    name: 'Rocky',
    species: 'Cow',
    breed: 'Hereford',
    age: '5 years',
    weight: '680 kg',
    status: 'Critical',
    location: 'Veterinary Pen',
    lastSeen: '2 hours ago',
    image: 'https://placehold.co/400x300/222/fff?text=Cow+ID:+LV-004',
    healthHistory: [
      { date: '2023-04-02', event: 'Veterinary Visit', details: 'Emergency treatment for acute infection' },
      { date: '2023-02-10', event: 'Health Check', details: 'Regular health check - elevated temperature noted' }
    ],
    alerts: [
      { type: 'error', message: 'Requires immediate veterinary attention' },
      { type: 'warning', message: 'Not eating for the past 12 hours' }
    ]
  }
];

const LivestockTracking = () => {
  const [selectedAnimal, setSelectedAnimal] = useState(mockLivestockData[0]);
  const [livestock, setLivestock] = useState(mockLivestockData);

  // Get status color
  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'healthy':
        return '#4caf50';
      case 'warning':
        return '#ff9800';
      case 'critical':
        return '#f44336';
      default:
        return '#2196f3';
    }
  };

  // Handle animal selection
  const handleAnimalSelect = (animal) => {
    setSelectedAnimal(animal);
  };

  return (
    <Box sx={{ width: '100%', mb: 4 }}>
      <Paper elevation={3} sx={{ p: 2, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <PetsIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h5">Livestock Identification & Tracking</Typography>
        </Box>
        
        <Grid container spacing={3}>
          {/* Livestock List */}
          <Grid item xs={12} md={5}>
            <TableContainer component={Paper} elevation={2}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>ID</TableCell>
                    <TableCell>Animal</TableCell>
                    <TableCell>Location</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Action</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {livestock.map((animal) => (
                    <TableRow 
                      key={animal.id}
                      sx={{ 
                        cursor: 'pointer',
                        backgroundColor: selectedAnimal.id === animal.id ? 'rgba(33, 150, 243, 0.08)' : 'inherit',
                        '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
                      }}
                      onClick={() => handleAnimalSelect(animal)}
                    >
                      <TableCell>{animal.id}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar 
                            src={animal.image} 
                            alt={animal.name}
                            sx={{ width: 30, height: 30, mr: 1 }}
                          />
                          {animal.name}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <LocationIcon fontSize="small" sx={{ mr: 0.5, color: '#2196f3' }} />
                          {animal.location}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={animal.status} 
                          size="small"
                          sx={{ 
                            backgroundColor: getStatusColor(animal.status),
                            color: 'white'
                          }} 
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title="View Details">
                          <IconButton 
                            size="small" 
                            onClick={() => handleAnimalSelect(animal)}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
          
          {/* Selected Animal Details */}
          <Grid item xs={12} md={7}>
            {selectedAnimal && (
              <Card elevation={2}>
                <Grid container>
                  <Grid item xs={12} md={5}>
                    <CardMedia
                      component="img"
                      height="100%"
                      image={selectedAnimal.image}
                      alt={selectedAnimal.name}
                      sx={{ objectFit: 'cover', height: '100%' }}
                    />
                  </Grid>
                  <Grid item xs={12} md={7}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                        <Typography variant="h6">
                          {selectedAnimal.name} ({selectedAnimal.id})
                        </Typography>
                        <Chip 
                          label={selectedAnimal.status} 
                          size="small"
                          sx={{ 
                            backgroundColor: getStatusColor(selectedAnimal.status),
                            color: 'white'
                          }} 
                        />
                      </Box>
                      
                      <Grid container spacing={2} sx={{ mb: 2 }}>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">Species</Typography>
                          <Typography variant="body1">{selectedAnimal.species}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">Breed</Typography>
                          <Typography variant="body1">{selectedAnimal.breed}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">Age</Typography>
                          <Typography variant="body1">{selectedAnimal.age}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">Weight</Typography>
                          <Typography variant="body1">{selectedAnimal.weight}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">Location</Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <LocationIcon fontSize="small" sx={{ mr: 0.5, color: '#2196f3' }} />
                            <Typography variant="body1">{selectedAnimal.location}</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">Last Seen</Typography>
                          <Typography variant="body1">{selectedAnimal.lastSeen}</Typography>
                        </Grid>
                      </Grid>
                      
                      {/* Alerts */}
                      {selectedAnimal.alerts.length > 0 && (
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Alerts
                          </Typography>
                          {selectedAnimal.alerts.map((alert, index) => (
                            <Box 
                              key={index} 
                              sx={{ 
                                display: 'flex', 
                                alignItems: 'center', 
                                mb: 1,
                                p: 1,
                                borderRadius: 1,
                                backgroundColor: alert.type === 'error' ? 'rgba(244, 67, 54, 0.1)' : 'rgba(255, 152, 0, 0.1)'
                              }}
                            >
                              {alert.type === 'error' ? (
                                <WarningIcon fontSize="small" color="error" sx={{ mr: 1 }} />
                              ) : (
                                <WarningIcon fontSize="small" color="warning" sx={{ mr: 1 }} />
                              )}
                              <Typography variant="body2">{alert.message}</Typography>
                            </Box>
                          ))}
                        </Box>
                      )}
                      
                      {/* Health History */}
                      <Typography variant="subtitle2" gutterBottom>
                        Health History
                      </Typography>
                      {selectedAnimal.healthHistory.map((event, index) => (
                        <Box 
                          key={index} 
                          sx={{ 
                            display: 'flex', 
                            mb: 1,
                            pb: 1,
                            borderBottom: index !== selectedAnimal.healthHistory.length - 1 ? '1px solid #eee' : 'none'
                          }}
                        >
                          <CheckCircleIcon fontSize="small" color="success" sx={{ mr: 1, mt: 0.5 }} />
                          <Box>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography variant="body2" fontWeight="bold">
                                {event.event}
                              </Typography>
                              <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                                {event.date}
                              </Typography>
                            </Box>
                            <Typography variant="body2" color="text.secondary">
                              {event.details}
                            </Typography>
                          </Box>
                        </Box>
                      ))}
                    </CardContent>
                  </Grid>
                </Grid>
              </Card>
            )}
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default LivestockTracking;
