import React, { useState, useEffect } from "react";
import axios from "axios";
import { getAuth } from "firebase/auth";
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Snackbar,
  Alert,
  Divider,
  Card,
  CardContent,
  Chip
} from "@mui/material";
// Using standard input for date and time instead of DateTimePicker
import {
  CalendarMonth as CalendarIcon,
  Pets as PetsIcon,
  LocalHospital as HospitalIcon,
  Person as PersonIcon,
  EventAvailable as EventAvailableIcon,
  EventBusy as EventBusyIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  AccessTime as TimeIcon
} from '@mui/icons-material';

const VetAppointment = () => {
  const [appointments, setAppointments] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success"
  });
  const [formData, setFormData] = useState({
    animalType: "",
    animalBreed: "",
    animalAge: "",
    animalWeight: "",
    appointmentDate: new Date(),
    reason: "",
    notes: "",
    vetId: ""
  });
  const [vets, setVets] = useState([]);

  // Mock vets data (in a real app, this would come from an API)
  const mockVets = [
    { id: "vet1", name: "Dr. Sharma", specialization: "Large Animals", experience: "15 years" },
    { id: "vet2", name: "Dr. Patel", specialization: "Dairy Cattle", experience: "10 years" },
    { id: "vet3", name: "Dr. Singh", specialization: "Poultry", experience: "8 years" },
    { id: "vet4", name: "Dr. Kumar", specialization: "General Livestock", experience: "12 years" }
  ];

  // Mock appointments data (in a real app, this would come from an API)
  const mockAppointments = [
    {
      id: "app1",
      animalType: "Cow",
      animalBreed: "Holstein",
      animalAge: "3",
      animalWeight: "450",
      appointmentDate: new Date(2023, 5, 15, 10, 30),
      reason: "Regular checkup",
      notes: "Milk production has decreased slightly",
      status: "confirmed",
      vetId: "vet1",
      vetName: "Dr. Sharma"
    },
    {
      id: "app2",
      animalType: "Chicken",
      animalBreed: "Broiler",
      animalAge: "0.5",
      animalWeight: "2",
      appointmentDate: new Date(2023, 5, 18, 14, 0),
      reason: "Vaccination",
      notes: "Flock vaccination for Newcastle disease",
      status: "pending",
      vetId: "vet3",
      vetName: "Dr. Singh"
    }
  ];

  useEffect(() => {
    // In a real app, fetch appointments from API
    // For now, use mock data
    setAppointments(mockAppointments);
    setVets(mockVets);
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleDateChange = (newDate) => {
    setFormData({
      ...formData,
      appointmentDate: newDate
    });
  };

  const handleSubmit = async () => {
    setLoading(true);

    try {
      // In a real app, send data to API
      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Get vet name from selected vet ID
      const selectedVet = vets.find(vet => vet.id === formData.vetId);

      // Create new appointment
      const newAppointment = {
        id: `app${appointments.length + 1}`,
        ...formData,
        status: "pending",
        vetName: selectedVet ? selectedVet.name : "Unknown"
      };

      // Update appointments list
      setAppointments([...appointments, newAppointment]);

      // Show success message
      setSnackbar({
        open: true,
        message: "Appointment booked successfully!",
        severity: "success"
      });

      // Close dialog
      setOpenDialog(false);

      // Reset form
      setFormData({
        animalType: "",
        animalBreed: "",
        animalAge: "",
        animalWeight: "",
        appointmentDate: new Date(),
        reason: "",
        notes: "",
        vetId: ""
      });
    } catch (error) {
      console.error("Error booking appointment:", error);
      setSnackbar({
        open: true,
        message: "Error booking appointment. Please try again.",
        severity: "error"
      });
    }

    setLoading(false);
  };

  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleString('en-US', {
      weekday: 'short',
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "confirmed":
        return "success";
      case "pending":
        return "warning";
      case "cancelled":
        return "error";
      case "completed":
        return "info";
      default:
        return "default";
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "confirmed":
        return <CheckCircleIcon />;
      case "pending":
        return <TimeIcon />;
      case "cancelled":
        return <CancelIcon />;
      case "completed":
        return <EventAvailableIcon />;
      default:
        return <EventBusyIcon />;
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h1">
          <HospitalIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Veterinary Appointments
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<CalendarIcon />}
          onClick={() => setOpenDialog(true)}
        >
          Book Appointment
        </Button>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Upcoming Appointments
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {appointments.length > 0 ? (
              <Grid container spacing={2}>
                {appointments.map((appointment) => (
                  <Grid item xs={12} md={6} key={appointment.id}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="subtitle1" component="div">
                            <PetsIcon sx={{ mr: 0.5, verticalAlign: 'middle', fontSize: '1rem' }} />
                            {appointment.animalType} ({appointment.animalBreed})
                          </Typography>
                          <Chip
                            size="small"
                            label={appointment.status}
                            color={getStatusColor(appointment.status)}
                            icon={getStatusIcon(appointment.status)}
                          />
                        </Box>

                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          <CalendarIcon sx={{ mr: 0.5, verticalAlign: 'middle', fontSize: '1rem' }} />
                          {formatDate(appointment.appointmentDate)}
                        </Typography>

                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          <PersonIcon sx={{ mr: 0.5, verticalAlign: 'middle', fontSize: '1rem' }} />
                          {appointment.vetName}
                        </Typography>

                        <Typography variant="body2" sx={{ mt: 1 }}>
                          <strong>Reason:</strong> {appointment.reason}
                        </Typography>

                        {appointment.notes && (
                          <Typography variant="body2" sx={{ mt: 0.5 }}>
                            <strong>Notes:</strong> {appointment.notes}
                          </Typography>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <EventBusyIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
                <Typography variant="body1" color="text.secondary">
                  No appointments scheduled
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Click "Book Appointment" to schedule a visit with a veterinarian
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Available Veterinarians
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {vets.map((vet) => (
              <Box key={vet.id} sx={{ mb: 2, pb: 2, borderBottom: '1px solid #eee' }}>
                <Typography variant="subtitle1">
                  <PersonIcon sx={{ mr: 0.5, verticalAlign: 'middle', fontSize: '1rem' }} />
                  {vet.name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Specialization: {vet.specialization}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Experience: {vet.experience}
                </Typography>
              </Box>
            ))}
          </Paper>
        </Grid>
      </Grid>

      {/* Appointment Booking Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Book Veterinary Appointment</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 0.5 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Animal Type"
                name="animalType"
                value={formData.animalType}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Breed"
                name="animalBreed"
                value={formData.animalBreed}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Age (years)"
                name="animalAge"
                type="number"
                value={formData.animalAge}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Weight (kg)"
                name="animalWeight"
                type="number"
                value={formData.animalWeight}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth required>
                <InputLabel id="vet-select-label">Select Veterinarian</InputLabel>
                <Select
                  labelId="vet-select-label"
                  name="vetId"
                  value={formData.vetId}
                  label="Select Veterinarian"
                  onChange={handleInputChange}
                >
                  {vets.map((vet) => (
                    <MenuItem key={vet.id} value={vet.id}>
                      {vet.name} - {vet.specialization}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Appointment Date"
                name="appointmentDate"
                type="date"
                value={formData.appointmentDate ? formData.appointmentDate.toISOString().split('T')[0] : ''}
                onChange={(e) => {
                  const date = new Date(e.target.value);
                  setFormData({
                    ...formData,
                    appointmentDate: date
                  });
                }}
                InputLabelProps={{ shrink: true }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Appointment Time"
                name="appointmentTime"
                type="time"
                value={formData.appointmentDate ?
                  `${formData.appointmentDate.getHours().toString().padStart(2, '0')}:${formData.appointmentDate.getMinutes().toString().padStart(2, '0')}` :
                  '10:00'}
                onChange={(e) => {
                  const [hours, minutes] = e.target.value.split(':').map(Number);
                  const date = new Date(formData.appointmentDate);
                  date.setHours(hours, minutes);
                  setFormData({
                    ...formData,
                    appointmentDate: date
                  });
                }}
                InputLabelProps={{ shrink: true }}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Reason for Visit"
                name="reason"
                value={formData.reason}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Additional Notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                multiline
                rows={3}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            color="primary"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : "Book Appointment"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default VetAppointment;
