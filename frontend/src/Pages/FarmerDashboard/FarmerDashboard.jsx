import React from 'react';
import { Box, Container } from '@mui/material';
import { Outlet } from 'react-router-dom';
import FarmerNavigation from '../../components/FarmerDashboard/FarmerNavigation';

const FarmerDashboard = () => {
  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* Navigation */}
      <FarmerNavigation />

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 2, md: 3 },
          width: { sm: `calc(100% - 240px)` },
          ml: { sm: '240px' },
        }}
      >
        {/* Toolbar offset */}
        <Box sx={{ height: 64 }} />
        
        {/* Page Content */}
        <Container maxWidth="xl">
          <Outlet />
        </Container>
      </Box>
    </Box>
  );
};

export default FarmerDashboard; 