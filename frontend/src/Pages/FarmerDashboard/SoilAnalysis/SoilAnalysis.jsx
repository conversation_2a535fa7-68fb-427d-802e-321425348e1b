import React, { useState, useEffect } from 'react';
import {
  Typography,
  Box,
  Paper,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  CircularProgress,
  Divider,
  Card,
  CardContent,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  IconButton,
  Alert,
  Stack
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Info as InfoIcon,
  ArrowBack as ArrowBackIcon,
  Map as MapIcon,
  Science as ScienceIcon,
  Grass as GrassIcon,
  WaterDrop as WaterDropIcon,
  Thermostat as ThermostatIcon,
  Agriculture as AgricultureIcon,
  LocationOn as LocationIcon,
  SwapHoriz as SwapHorizIcon,
  MyLocation as MyLocationIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { Doughnut, Bar } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip as ChartTooltip, Legend, CategoryScale, LinearScale, BarElement, Title } from 'chart.js';

// Register ChartJS components
ChartJS.register(ArcElement, ChartTooltip, Legend, CategoryScale, LinearScale, BarElement, Title);

const SoilAnalysis = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [selectedRegion, setSelectedRegion] = useState('Madhya Pradesh');
  const [availableRegions, setAvailableRegions] = useState([]);
  const [soilData, setSoilData] = useState(null);
  const [error, setError] = useState(null);
  const [showMockData, setShowMockData] = useState(false);

  // Safe data accessor function
  const safeGet = (obj, path, defaultValue = 'N/A') => {
    try {
      return path.split('.').reduce((current, key) => {
        return current && current[key] !== undefined ? current[key] : defaultValue;
      }, obj);
    } catch (error) {
      return defaultValue;
    }
  };

  // Safe number formatter
  const safeFormat = (value, decimals = 2) => {
    if (value === null || value === undefined || isNaN(value)) {
      return 'N/A';
    }
    return Number(value).toFixed(decimals);
  };

  // Fetch available regions
  useEffect(() => {
    const fetchRegions = async () => {
      try {
        // Set available regions directly since we know the supported regions
        setAvailableRegions([
          'Madhya Pradesh', 'Punjab', 'Haryana', 'Uttar Pradesh',
          'Tamil Nadu', 'Karnataka', 'Kerala', 'West Bengal',
          'Gujarat', 'Maharashtra'
        ]);
      } catch (error) {
        console.error('Error setting regions:', error);
        setError('Failed to set available regions');
        // Set fallback regions
        setAvailableRegions(['Madhya Pradesh', 'Punjab', 'Haryana', 'Uttar Pradesh']);
      }
    };

    fetchRegions();
  }, []);

  // Fetch soil data for selected region
  useEffect(() => {
    const fetchSoilData = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await axios.get('http://localhost:8000/api/satellite/soil-npk', {
          params: {
            region: selectedRegion,
            forceRefresh: false
          }
        });

        if (response.data?.success && response.data?.data) {
          // Transform the data to match our component structure
          const transformedData = {
            ...response.data.data,
            data: response.data.data, // Ensure data property exists
            interpretation: generateInterpretations(response.data.data),
            recommendations: generateRecommendations(response.data.data),
            suitableCrops: generateSuitableCrops(response.data.data)
          };
          setSoilData(transformedData);
          setShowMockData(false);
        } else {
          throw new Error('Invalid response format');
        }
      } catch (error) {
        console.error('Error fetching soil data:', error);
        // Display the specific error message from the backend if available
        if (error.response?.data?.error) {
          setError(error.response.data.error);
        } else if (error.response?.status === 404) {
          setError('Satellite data service not found. Please ensure the backend server is running correctly.');
        } else if (error.response?.status === 500) {
          setError('Server error while fetching satellite data. This may indicate that real-time data sources are unavailable.');
        } else if (error.message === 'Network Error') {
          setError('Network error. Please check your internet connection and ensure the backend server is running.');
        } else {
          setError('Unable to fetch real-time satellite data. Please try again later.');
        }

        // Generate fallback data with proper structure
        const fallbackData = generateMockSoilData(selectedRegion);
        setSoilData(fallbackData);
        setShowMockData(true);
      } finally {
        setLoading(false);
      }
    };

    if (selectedRegion) {
      fetchSoilData();
    }
  }, [selectedRegion]);

  // Handle region change
  const handleRegionChange = (event) => {
    setSelectedRegion(event.target.value);
  };

  // Handle refresh button click
  const handleRefresh = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.get('http://localhost:8000/api/satellite/soil-npk', {
        params: {
          region: selectedRegion,
          forceRefresh: true
        }
      });

      if (response.data.success) {
        // Transform the data to match our component structure
        const transformedData = {
          ...response.data.data,
          data: response.data.data,
          interpretation: generateInterpretations(response.data.data),
          recommendations: generateRecommendations(response.data.data),
          suitableCrops: generateSuitableCrops(response.data.data)
        };
        setSoilData(transformedData);
        setShowMockData(false);
      } else {
        setError('Failed to refresh soil data');
      }
    } catch (error) {
      console.error('Error refreshing soil data:', error);
      // Display the specific error message from the backend if available
      if (error.response && error.response.data && error.response.data.error) {
        setError(error.response.data.error);
      } else if (error.response && error.response.status === 404) {
        setError('Satellite data service not found. Please ensure the backend server is running correctly.');
      } else if (error.response && error.response.status === 500) {
        setError('Server error while fetching satellite data. This may indicate that real-time data sources are unavailable.');
      } else if (error.message === 'Network Error') {
        setError('Network error. Please check your internet connection and ensure the backend server is running.');
      } else {
        setError('Unable to fetch real-time satellite data. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch mock data for demonstration
  const fetchMockData = async () => {
    setLoading(true);
    try {
      // Create a mock data structure similar to what the API would return
      const mockData = generateMockSoilData(selectedRegion);
      setSoilData(mockData);
      setShowMockData(true);
    } catch (error) {
      console.error('Error generating mock data:', error);
      setError('Failed to generate mock data');
    } finally {
      setLoading(false);
    }
  };

  // Generate mock soil data for demonstration
  const generateMockSoilData = (region) => {
    // Base values that will be adjusted based on region
    const baseValues = {
      nitrogen: 280, // ppm
      phosphorus: 45, // ppm
      potassium: 190, // ppm
      ph: 6.5,
      organicMatter: 3.2, // percentage
      moisture: 22, // percentage
      temperature: 24, // celsius
    };

    // Region-specific adjustments (simplified)
    const regionFactors = {
      'Madhya Pradesh': { nitrogen: 0.8, phosphorus: 0.9, potassium: 0.85, ph: 1.05, organicMatter: 0.8, moisture: 0.7, temperature: 1.1 },
      'Punjab': { nitrogen: 1.2, phosphorus: 0.9, potassium: 1.1, ph: 1.05, organicMatter: 1.1, moisture: 0.8, temperature: 1.05 },
      'Karnataka': { nitrogen: 0.85, phosphorus: 1.2, potassium: 1.1, ph: 0.95, organicMatter: 0.9, moisture: 0.8, temperature: 1.05 },
      'Tamil Nadu': { nitrogen: 0.9, phosphorus: 1.1, potassium: 1.2, ph: 0.9, organicMatter: 1.0, moisture: 1.2, temperature: 1.1 }
    };

    const factors = regionFactors[region] || { nitrogen: 1.0, phosphorus: 1.0, potassium: 1.0, ph: 1.0, organicMatter: 1.0, moisture: 1.0, temperature: 1.0 };

    // Apply region-specific adjustments
    const adjustedData = {
      nitrogen: Math.round(baseValues.nitrogen * factors.nitrogen),
      phosphorus: Math.round(baseValues.phosphorus * factors.phosphorus),
      potassium: Math.round(baseValues.potassium * factors.potassium),
      ph: Number((baseValues.ph * factors.ph).toFixed(1)),
      organicMatter: Number((baseValues.organicMatter * factors.organicMatter).toFixed(1)),
      moisture: Math.round(baseValues.moisture * factors.moisture),
      temperature: Math.round(baseValues.temperature * factors.temperature),
      cec: 15.2,
      sand: 42,
      silt: 38,
      clay: 20,
      bulkDensity: 1.3,
      precipitation: 85,
      humidity: 65
    };

    // Generate interpretation
    const interpretation = {
      nitrogen: interpretNutrientLevel(adjustedData.nitrogen, 'nitrogen'),
      phosphorus: interpretNutrientLevel(adjustedData.phosphorus, 'phosphorus'),
      potassium: interpretNutrientLevel(adjustedData.potassium, 'potassium'),
      ph: interpretPHLevel(adjustedData.ph),
      organicMatter: interpretOrganicMatter(adjustedData.organicMatter),
      moisture: interpretMoisture(adjustedData.moisture)
    };

    // Generate recommendations
    const recommendations = [
      "Apply nitrogen-rich fertilizers to improve soil fertility",
      "Consider crop rotation with legumes to naturally fix nitrogen",
      "Maintain soil moisture through proper irrigation practices",
      "Add organic matter to improve soil structure and nutrient retention",
      "Monitor pH levels and adjust if necessary with appropriate amendments"
    ];

    // Generate suitable crops
    const suitableCrops = [
      { crop: "Wheat", suitabilityScore: 85, notes: "Excellent for this soil profile" },
      { crop: "Soybean", suitabilityScore: 78, notes: "Good nitrogen fixer" },
      { crop: "Maize", suitabilityScore: 72, notes: "Suitable with proper fertilization" },
      { crop: "Cotton", suitabilityScore: 68, notes: "Moderate suitability" },
      { crop: "Chickpea", suitabilityScore: 65, notes: "Good for crop rotation" }
    ];

    return {
      region,
      data: adjustedData,
      interpretation,
      recommendations,
      suitableCrops,
      lastUpdated: new Date().toISOString(),
      source: 'Mock Data (Demonstration Only)'
    };
  };

  // Helper functions for mock data interpretation
  const interpretNutrientLevel = (value, nutrient) => {
    if (nutrient === 'nitrogen') {
      if (value < 150) return { level: 'Very Low', description: 'Severe deficiency, immediate supplementation required' };
      if (value < 250) return { level: 'Low', description: 'Deficient, supplementation recommended' };
      if (value < 350) return { level: 'Medium', description: 'Adequate for most crops' };
      if (value < 450) return { level: 'High', description: 'Abundant, no supplementation needed' };
      return { level: 'Very High', description: 'Excessive, may cause imbalances' };
    } else if (nutrient === 'phosphorus') {
      if (value < 20) return { level: 'Very Low', description: 'Severe deficiency, immediate supplementation required' };
      if (value < 40) return { level: 'Low', description: 'Deficient, supplementation recommended' };
      if (value < 60) return { level: 'Medium', description: 'Adequate for most crops' };
      if (value < 80) return { level: 'High', description: 'Abundant, no supplementation needed' };
      return { level: 'Very High', description: 'Excessive, may cause imbalances' };
    } else if (nutrient === 'potassium') {
      if (value < 100) return { level: 'Very Low', description: 'Severe deficiency, immediate supplementation required' };
      if (value < 175) return { level: 'Low', description: 'Deficient, supplementation recommended' };
      if (value < 250) return { level: 'Medium', description: 'Adequate for most crops' };
      if (value < 325) return { level: 'High', description: 'Abundant, no supplementation needed' };
      return { level: 'Very High', description: 'Excessive, may cause imbalances' };
    }
    return { level: 'Unknown', description: 'Could not determine level' };
  };

  const interpretPHLevel = (ph) => {
    if (ph < 5.5) return { level: 'Acidic', description: 'Too acidic for most crops. Consider liming to raise pH.' };
    if (ph < 6.0) return { level: 'Moderately Acidic', description: 'Slightly acidic, suitable for acid-loving crops.' };
    if (ph < 7.5) return { level: 'Neutral', description: 'Ideal pH range for most crops.' };
    if (ph < 8.5) return { level: 'Moderately Alkaline', description: 'Slightly alkaline, monitor for nutrient availability.' };
    return { level: 'Alkaline', description: 'Too alkaline for most crops. Consider amendments to lower pH.' };
  };

  const interpretOrganicMatter = (value) => {
    if (value < 1.0) return { level: 'Very Low', description: 'Severely depleted, add organic amendments immediately.' };
    if (value < 2.0) return { level: 'Low', description: 'Insufficient, add compost or other organic matter.' };
    if (value < 4.0) return { level: 'Medium', description: 'Adequate for most crops, maintain with good practices.' };
    if (value < 6.0) return { level: 'High', description: 'Good organic matter content, excellent soil health.' };
    return { level: 'Very High', description: 'Excellent organic matter content, focus on maintenance.' };
  };

  const interpretMoisture = (value) => {
    if (value < 10) return { level: 'Very Dry', description: 'Immediate irrigation needed.' };
    if (value < 20) return { level: 'Dry', description: 'Irrigation recommended soon.' };
    if (value < 30) return { level: 'Moderate', description: 'Adequate moisture for most crops.' };
    if (value < 40) return { level: 'Moist', description: 'Good moisture level, monitor for changes.' };
    return { level: 'Wet', description: 'Excessive moisture, improve drainage if persistent.' };
  };

  // Generate interpretations for real API data
  const generateInterpretations = (data) => {
    return {
      nitrogen: interpretNutrientLevel(data.nitrogen || 0, 'nitrogen'),
      phosphorus: interpretNutrientLevel(data.phosphorus || 0, 'phosphorus'),
      potassium: interpretNutrientLevel(data.potassium || 0, 'potassium'),
      ph: interpretPHLevel(data.ph || 7.0),
      organicMatter: interpretOrganicMatter(data.organicMatter || 0),
      moisture: interpretMoisture(data.moisture || 0)
    };
  };

  // Generate recommendations based on soil data
  const generateRecommendations = (data) => {
    const recommendations = [];

    if (data.nitrogen < 200) {
      recommendations.push({
        nutrient: 'Nitrogen',
        action: 'Apply nitrogen fertilizer',
        dosage: '120-150 kg/ha',
        timing: 'Before sowing and top dressing'
      });
    }

    if (data.phosphorus < 30) {
      recommendations.push({
        nutrient: 'Phosphorus',
        action: 'Apply phosphate fertilizer',
        dosage: '60-80 kg/ha',
        timing: 'At the time of sowing'
      });
    }

    if (data.potassium < 150) {
      recommendations.push({
        nutrient: 'Potassium',
        action: 'Apply potash fertilizer',
        dosage: '40-60 kg/ha',
        timing: 'Split application during growth'
      });
    }

    if (data.ph < 6.0) {
      recommendations.push({
        nutrient: 'pH',
        action: 'Apply lime to increase pH',
        dosage: '2-4 tons/ha',
        timing: '2-3 months before sowing'
      });
    }

    if (data.ph > 8.0) {
      recommendations.push({
        nutrient: 'pH',
        action: 'Apply gypsum to reduce pH',
        dosage: '1-2 tons/ha',
        timing: 'Before land preparation'
      });
    }

    if (data.organicMatter < 2.0) {
      recommendations.push({
        nutrient: 'Organic Matter',
        action: 'Add compost or farmyard manure',
        dosage: '5-10 tons/ha',
        timing: 'Before land preparation'
      });
    }

    return recommendations;
  };

  // Generate suitable crops based on soil conditions
  const generateSuitableCrops = (data) => {
    const crops = [
      { crop: 'Wheat', baseScore: 75 },
      { crop: 'Rice', baseScore: 70 },
      { crop: 'Maize', baseScore: 80 },
      { crop: 'Soybean', baseScore: 65 },
      { crop: 'Cotton', baseScore: 60 },
      { crop: 'Sugarcane', baseScore: 55 },
      { crop: 'Chickpea', baseScore: 70 },
      { crop: 'Mustard', baseScore: 65 }
    ];

    return crops.map(crop => {
      let score = crop.baseScore;

      // Adjust score based on soil conditions
      if (data.nitrogen > 250) score += 10;
      else if (data.nitrogen < 150) score -= 15;

      if (data.phosphorus > 40) score += 8;
      else if (data.phosphorus < 20) score -= 12;

      if (data.potassium > 200) score += 8;
      else if (data.potassium < 100) score -= 12;

      if (data.ph >= 6.0 && data.ph <= 7.5) score += 15;
      else if (data.ph < 5.5 || data.ph > 8.5) score -= 20;

      if (data.organicMatter > 3.0) score += 10;
      else if (data.organicMatter < 1.0) score -= 15;

      // Ensure score is within bounds
      score = Math.max(0, Math.min(100, score));

      let suitabilityLevel = 'Poor';
      if (score >= 80) suitabilityLevel = 'Excellent';
      else if (score >= 65) suitabilityLevel = 'Good';
      else if (score >= 50) suitabilityLevel = 'Moderate';

      return {
        crop: crop.crop,
        suitabilityScore: score,
        suitabilityLevel: suitabilityLevel
      };
    }).sort((a, b) => b.suitabilityScore - a.suitabilityScore);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get color based on level
  const getLevelColor = (level) => {
    switch (level) {
      case 'Very Low':
        return '#d32f2f'; // Red
      case 'Low':
        return '#f57c00'; // Orange
      case 'Medium':
        return '#fbc02d'; // Yellow
      case 'High':
        return '#388e3c'; // Green
      case 'Very High':
        return '#1565c0'; // Blue
      case 'Acidic':
        return '#d32f2f'; // Red
      case 'Moderately Acidic':
        return '#f57c00'; // Orange
      case 'Neutral':
        return '#388e3c'; // Green
      case 'Moderately Alkaline':
        return '#f57c00'; // Orange
      case 'Alkaline':
        return '#d32f2f'; // Red
      case 'Very Dry':
        return '#d32f2f'; // Red
      case 'Dry':
        return '#f57c00'; // Orange
      case 'Moderate':
        return '#fbc02d'; // Yellow
      case 'Moist':
        return '#388e3c'; // Green
      case 'Wet':
        return '#1565c0'; // Blue
      default:
        return '#757575'; // Grey
    }
  };

  // Get icon based on nutrient
  const getNutrientIcon = (nutrient) => {
    switch (nutrient) {
      case 'Nitrogen':
        return <ScienceIcon />;
      case 'Phosphorus':
        return <ScienceIcon />;
      case 'Potassium':
        return <ScienceIcon />;
      case 'pH':
        return <WaterDropIcon />;
      case 'Organic Matter':
        return <GrassIcon />;
      case 'Moisture':
        return <WaterDropIcon />;
      default:
        return <InfoIcon />;
    }
  };

  // Prepare chart data for NPK levels
  const getNPKChartData = () => {
    if (!soilData) return null;

    // Use safeGet to handle undefined values
    const nitrogen = safeGet(soilData, 'nitrogen', 0);
    const phosphorus = safeGet(soilData, 'phosphorus', 0);
    const potassium = safeGet(soilData, 'potassium', 0);

    return {
      labels: ['Nitrogen (N)', 'Phosphorus (P)', 'Potassium (K)'],
      datasets: [
        {
          label: 'Nutrient Levels (ppm)',
          data: [nitrogen, phosphorus, potassium],
          backgroundColor: [
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 99, 132, 0.7)',
            'rgba(75, 192, 192, 0.7)'
          ],
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(75, 192, 192, 1)'
          ],
          borderWidth: 1
        }
      ]
    };
  };

  // Prepare chart data for soil properties
  const getSoilPropertiesChartData = () => {
    if (!soilData) return null;

    // Use safeGet to handle undefined values
    const ph = safeGet(soilData, 'ph', 0);
    const organicMatter = safeGet(soilData, 'organicMatter', 0);
    const moisture = safeGet(soilData, 'moisture', 0);

    return {
      labels: ['pH', 'Organic Matter (%)', 'Moisture (%)'],
      datasets: [
        {
          label: 'Soil Properties',
          data: [ph, organicMatter, moisture],
          backgroundColor: [
            'rgba(255, 206, 86, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(54, 162, 235, 0.7)'
          ],
          borderColor: [
            'rgba(255, 206, 86, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(54, 162, 235, 1)'
          ],
          borderWidth: 1
        }
      ]
    };
  };

  // Prepare chart data for crop suitability
  const getCropSuitabilityChartData = () => {
    if (!soilData || !soilData.suitableCrops || soilData.suitableCrops.length === 0) return null;

    // Take top 5 crops
    const topCrops = soilData.suitableCrops.slice(0, 5);

    return {
      labels: topCrops.map(crop => crop.crop),
      datasets: [
        {
          label: 'Suitability Score',
          data: topCrops.map(crop => crop.suitabilityScore),
          backgroundColor: [
            'rgba(75, 192, 192, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)',
            'rgba(255, 99, 132, 0.7)'
          ],
          borderColor: [
            'rgba(75, 192, 192, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(255, 99, 132, 1)'
          ],
          borderWidth: 1
        }
      ]
    };
  };

  // Get chart options
  const getChartOptions = (title) => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
        },
        title: {
          display: true,
          text: title,
          font: {
            size: 16
          }
        }
      }
    };
  };

  // Get bar chart options
  const getBarChartOptions = (title) => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        title: {
          display: true,
          text: title,
          font: {
            size: 16
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          max: 100
        }
      }
    };
  };

  return (
    <Box>
      {/* Header Section */}
      <Box sx={{
        mb: 4,
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', md: 'center' },
        gap: 2
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton onClick={() => navigate('/farmer-dashboard')} color="primary" title="Back to Dashboard">
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4" component="h1" gutterBottom sx={{ mb: 0 }}>
            Soil Analysis
          </Typography>
        </Box>
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ width: { xs: '100%', md: 'auto' } }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<MyLocationIcon />}
            onClick={() => navigate('/location-soil-analysis')}
            sx={{ minWidth: { xs: '100%', sm: '200px' } }}
          >
            Location Analysis
          </Button>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={loading}
            sx={{ minWidth: { xs: '100%', sm: 'auto' } }}
          >
            Refresh Data
          </Button>
        </Stack>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert 
          severity="warning" 
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={handleRefresh}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Region Selection */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel id="region-select-label">Select Region</InputLabel>
              <Select
                labelId="region-select-label"
                value={selectedRegion}
                label="Select Region"
                onChange={handleRegionChange}
                disabled={loading}
              >
                {availableRegions.map((region) => (
                  <MenuItem key={region} value={region}>
                    {region}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Chip
                icon={<ScienceIcon />}
                label={`Data Source: ${soilData?.dataSource || 'Loading...'}`}
                color="primary"
                variant="outlined"
              />
              <Chip
                icon={<InfoIcon />}
                label={`Last Updated: ${formatDate(soilData?.lastUpdated)}`}
                color="info"
                variant="outlined"
              />
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Loading State */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            Loading soil analysis data...
          </Typography>
        </Box>
      )}

      {/* Main Content - Only show when not loading and we have data */}
      {!loading && soilData && (
        <>
          {/* NPK Analysis Section */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
              NPK Analysis
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h3" color="primary" sx={{ fontWeight: 'bold' }}>
                      {safeFormat(safeGet(soilData, 'nitrogen'), 1)}
                    </Typography>
                    <Typography variant="h6" color="text.secondary">
                      Nitrogen (ppm)
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {safeGet(soilData, 'interpretation.nitrogen.description', 'Essential for leaf growth')}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h3" color="primary" sx={{ fontWeight: 'bold' }}>
                      {safeFormat(safeGet(soilData, 'phosphorus'), 1)}
                    </Typography>
                    <Typography variant="h6" color="text.secondary">
                      Phosphorus (ppm)
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {safeGet(soilData, 'interpretation.phosphorus.description', 'Important for root development')}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h3" color="primary" sx={{ fontWeight: 'bold' }}>
                      {safeFormat(safeGet(soilData, 'potassium'), 1)}
                    </Typography>
                    <Typography variant="h6" color="text.secondary">
                      Potassium (ppm)
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {safeGet(soilData, 'interpretation.potassium.description', 'Enhances disease resistance')}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Paper>

          {/* Overview */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h5" component="h2">
                Soil Health Overview: {selectedRegion}
              </Typography>
              <Box display="flex" alignItems="center">
                <MapIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  Last updated: {formatDate(safeGet(soilData, 'lastUpdated', new Date().toISOString()))}
                  {(showMockData || safeGet(soilData, 'isMockData', false)) && " (Demo Data)"}
                </Typography>
              </Box>
              <Box display="flex" alignItems="center" mt={1}>
                <Typography variant="body2" color="text.secondary">
                  <strong>Source:</strong> {safeGet(soilData, 'source', "Satellite Data (NASA POWER & SoilGrids)")}
                </Typography>
              </Box>
            </Box>

            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              {/* NPK Values */}
              <Grid item xs={12} md={4}>
                <Card sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Nitrogen (N)
                    </Typography>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="h4" color="primary">
                        {safeFormat(safeGet(soilData, 'data.nitrogen'), 1)} ppm
                      </Typography>
                      <Chip
                        label={safeGet(soilData, 'interpretation.nitrogen.level', 'Unknown')}
                        color="primary"
                        sx={{
                          bgcolor: getLevelColor(safeGet(soilData, 'interpretation.nitrogen.level', 'Unknown')),
                          color: 'white'
                        }}
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {safeGet(soilData, 'interpretation.nitrogen.description', 'Essential for leaf growth')}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Phosphorus (P)
                    </Typography>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="h4" color="primary">
                        {safeFormat(safeGet(soilData, 'data.phosphorus'), 1)} ppm
                      </Typography>
                      <Chip
                        label={safeGet(soilData, 'interpretation.phosphorus.level', 'Unknown')}
                        color="primary"
                        sx={{
                          bgcolor: getLevelColor(safeGet(soilData, 'interpretation.phosphorus.level', 'Unknown')),
                          color: 'white'
                        }}
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {safeGet(soilData, 'interpretation.phosphorus.description', 'Important for root development')}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Potassium (K)
                    </Typography>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="h4" color="primary">
                        {safeFormat(safeGet(soilData, 'data.potassium'), 1)} ppm
                      </Typography>
                      <Chip
                        label={safeGet(soilData, 'interpretation.potassium.level', 'Unknown')}
                        color="primary"
                        sx={{
                          bgcolor: getLevelColor(safeGet(soilData, 'interpretation.potassium.level', 'Unknown')),
                          color: 'white'
                        }}
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {safeGet(soilData, 'interpretation.potassium.description', 'Enhances disease resistance')}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              {/* Other Soil Properties */}
              <Grid item xs={12} md={4}>
                <Card sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Soil pH
                    </Typography>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="h4" color="primary">
                        {safeFormat(safeGet(soilData, 'data.ph'), 1)}
                      </Typography>
                      <Chip
                        label={safeGet(soilData, 'interpretation.ph.level', 'Unknown')}
                        color="primary"
                        sx={{
                          bgcolor: getLevelColor(safeGet(soilData, 'interpretation.ph.level', 'Unknown')),
                          color: 'white'
                        }}
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {safeGet(soilData, 'interpretation.ph.description', 'Soil acidity/alkalinity level')}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Organic Matter
                    </Typography>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="h4" color="primary">
                        {safeFormat(safeGet(soilData, 'data.organicMatter'), 1)}%
                      </Typography>
                      <Chip
                        label={safeGet(soilData, 'interpretation.organicMatter.level', 'Unknown')}
                        color="primary"
                        sx={{
                          bgcolor: getLevelColor(safeGet(soilData, 'interpretation.organicMatter.level', 'Unknown')),
                          color: 'white'
                        }}
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {safeGet(soilData, 'interpretation.organicMatter.description', 'Soil organic matter content')}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Soil Moisture
                    </Typography>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="h4" color="primary">
                        {safeFormat(safeGet(soilData, 'data.moisture'), 1)}%
                      </Typography>
                      <Chip
                        label={safeGet(soilData, 'interpretation.moisture.level', 'Unknown')}
                        color="primary"
                        sx={{
                          bgcolor: getLevelColor(safeGet(soilData, 'interpretation.moisture.level', 'Unknown')),
                          color: 'white'
                        }}
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {safeGet(soilData, 'interpretation.moisture.description', 'Current soil moisture level')}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Paper>

          {/* Charts */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Box height={300}>
                  {getNPKChartData() && (
                    <Doughnut
                      data={getNPKChartData()}
                      options={getChartOptions('NPK Distribution')}
                    />
                  )}
                </Box>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, height: '100%' }}>
                <Box height={300}>
                  {getCropSuitabilityChartData() && (
                    <Bar
                      data={getCropSuitabilityChartData()}
                      options={getBarChartOptions('Crop Suitability Scores')}
                    />
                  )}
                </Box>
              </Paper>
            </Grid>
          </Grid>

          {/* Recommendations */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h5" component="h2" gutterBottom>
              Recommendations
            </Typography>

            <Divider sx={{ mb: 2 }} />

            {safeGet(soilData, 'recommendations', []).length > 0 ? (
              <TableContainer sx={{ overflowX: 'auto' }}>
                <Table sx={{ minWidth: 650 }}>
                  <TableHead>
                    <TableRow>
                      <TableCell>Nutrient</TableCell>
                      <TableCell>Action</TableCell>
                      <TableCell>Dosage</TableCell>
                      <TableCell>Timing</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {safeGet(soilData, 'recommendations', []).map((rec, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Box display="flex" alignItems="center">
                            {getNutrientIcon(safeGet(rec, 'nutrient', 'Unknown'))}
                            <Typography sx={{ ml: 1 }}>{safeGet(rec, 'nutrient', 'Unknown')}</Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{safeGet(rec, 'action', 'N/A')}</TableCell>
                        <TableCell>{safeGet(rec, 'dosage', 'N/A')}</TableCell>
                        <TableCell>{safeGet(rec, 'timing', 'N/A')}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography>No specific recommendations needed. Soil health is optimal.</Typography>
            )}
          </Paper>

          {/* Suitable Crops */}
          <Paper sx={{ p: 3 }}>
            <Typography variant="h5" component="h2" gutterBottom>
              Suitable Crops
            </Typography>

            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={2}>
              {safeGet(soilData, 'suitableCrops', []).slice(0, 6).map((crop, index) => (
                <Grid item xs={12} sm={6} md={4} key={index}>
                  <Card>
                    <CardContent>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="h6">{safeGet(crop, 'crop', 'Unknown Crop')}</Typography>
                        <Chip
                          label={safeGet(crop, 'suitabilityLevel', 'Unknown')}
                          color={
                            safeGet(crop, 'suitabilityLevel') === 'Excellent' ? 'success' :
                            safeGet(crop, 'suitabilityLevel') === 'Good' ? 'primary' :
                            safeGet(crop, 'suitabilityLevel') === 'Moderate' ? 'warning' :
                            'error'
                          }
                          size="small"
                        />
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                          Suitability Score:
                        </Typography>
                        <Typography variant="body1" fontWeight="bold">
                          {safeFormat(safeGet(crop, 'suitabilityScore', 0))}/100
                        </Typography>
                      </Box>

                      <LinearProgress
                        variant="determinate"
                        value={safeGet(crop, 'suitabilityScore', 0)}
                        color={
                          safeGet(crop, 'suitabilityLevel') === 'Excellent' ? 'success' :
                          safeGet(crop, 'suitabilityLevel') === 'Good' ? 'primary' :
                          safeGet(crop, 'suitabilityLevel') === 'Moderate' ? 'warning' :
                          'error'
                        }
                        sx={{ height: 8, borderRadius: 5 }}
                      />
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </>
      )}
    </Box>
  );
};

export default SoilAnalysis;
