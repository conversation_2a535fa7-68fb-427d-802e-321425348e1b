import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Checkbox,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  WaterDrop as WaterIcon,
  LocalFlorist as PlantIcon,
  Pest as PestIcon,
  Fence as FenceIcon,
} from '@mui/icons-material';

const taskTypes = [
  { value: 'irrigation', label: 'Irrigation', icon: <WaterIcon /> },
  { value: 'planting', label: 'Planting', icon: <PlantIcon /> },
  { value: 'pestControl', label: 'Pest Control', icon: <PestIcon /> },
  { value: 'maintenance', label: 'Maintenance', icon: <FenceIcon /> },
];

const Tasks = () => {
  const [tasks, setTasks] = useState([
    { id: 1, type: 'irrigation', title: 'Water Field 1', completed: false },
    { id: 2, type: 'planting', title: 'Plant new seeds in Field 2', completed: true },
    { id: 3, type: 'pestControl', title: 'Apply organic pesticides', completed: false },
  ]);

  const [open, setOpen] = useState(false);
  const [newTask, setNewTask] = useState({ type: '', title: '' });

  const handleToggle = (taskId) => {
    setTasks(tasks.map(task =>
      task.id === taskId ? { ...task, completed: !task.completed } : task
    ));
  };

  const handleDelete = (taskId) => {
    setTasks(tasks.filter(task => task.id !== taskId));
  };

  const handleAdd = () => {
    if (newTask.type && newTask.title) {
      setTasks([...tasks, {
        id: Date.now(),
        ...newTask,
        completed: false,
      }]);
      setNewTask({ type: '', title: '' });
      setOpen(false);
    }
  };

  const getTaskIcon = (type) => {
    const taskType = taskTypes.find(t => t.value === type);
    return taskType ? taskType.icon : <PlantIcon />;
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">Farm Tasks</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpen(true)}
        >
          Add Task
        </Button>
      </Box>

      <Paper elevation={2}>
        <List>
          {tasks.map((task) => (
            <ListItem
              key={task.id}
              sx={{
                bgcolor: task.completed ? 'action.hover' : 'inherit',
                '&:hover': { bgcolor: 'action.hover' },
              }}
            >
              <ListItemIcon>{getTaskIcon(task.type)}</ListItemIcon>
              <ListItemText
                primary={task.title}
                sx={{ textDecoration: task.completed ? 'line-through' : 'none' }}
              />
              <ListItemSecondaryAction>
                <Checkbox
                  edge="end"
                  checked={task.completed}
                  onChange={() => handleToggle(task.id)}
                />
                <IconButton
                  edge="end"
                  onClick={() => handleDelete(task.id)}
                  sx={{ ml: 1 }}
                >
                  <DeleteIcon />
                </IconButton>
              </ListItemSecondaryAction>
            </ListItem>
          ))}
        </List>
      </Paper>

      {/* Add Task Dialog */}
      <Dialog open={open} onClose={() => setOpen(false)}>
        <DialogTitle>Add New Task</DialogTitle>
        <DialogContent>
          <TextField
            select
            fullWidth
            margin="normal"
            label="Task Type"
            value={newTask.type}
            onChange={(e) => setNewTask({ ...newTask, type: e.target.value })}
          >
            {taskTypes.map((type) => (
              <MenuItem key={type.value} value={type.value}>
                <Box display="flex" alignItems="center">
                  <Box mr={1}>{type.icon}</Box>
                  {type.label}
                </Box>
              </MenuItem>
            ))}
          </TextField>
          <TextField
            fullWidth
            margin="normal"
            label="Task Description"
            value={newTask.title}
            onChange={(e) => setNewTask({ ...newTask, title: e.target.value })}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>Cancel</Button>
          <Button onClick={handleAdd} variant="contained" disabled={!newTask.type || !newTask.title}>
            Add Task
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Tasks; 