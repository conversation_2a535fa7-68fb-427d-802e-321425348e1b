import React, { useState } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  TextField,
  MenuItem,
  Button,
  Chip,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Notifications as NotificationsIcon,
} from '@mui/icons-material';

// Mock market data (replace with actual API integration)
const mockMarketData = {
  crops: [
    {
      name: 'Rice',
      currentPrice: 2500,
      previousPrice: 2400,
      unit: 'per quintal',
      trend: 'up',
      demand: 'high',
    },
    {
      name: 'Wheat',
      currentPrice: 2200,
      previousPrice: 2300,
      unit: 'per quintal',
      trend: 'down',
      demand: 'medium',
    },
    {
      name: 'Corn',
      currentPrice: 1800,
      previousPrice: 1800,
      unit: 'per quintal',
      trend: 'flat',
      demand: 'high',
    },
    {
      name: 'Soybeans',
      currentPrice: 4200,
      previousPrice: 4000,
      unit: 'per quintal',
      trend: 'up',
      demand: 'high',
    },
  ],
  markets: ['Local Market', 'District Market', 'State Market', 'National Market'],
};

const Market = () => {
  const [selectedMarket, setSelectedMarket] = useState('Local Market');
  const [marketData, setMarketData] = useState(mockMarketData);

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon sx={{ color: '#4CAF50' }} />;
      case 'down':
        return <TrendingDownIcon sx={{ color: '#F44336' }} />;
      default:
        return <TrendingFlatIcon sx={{ color: '#9E9E9E' }} />;
    }
  };

  const getDemandColor = (demand) => {
    switch (demand.toLowerCase()) {
      case 'high':
        return 'success';
      case 'medium':
        return 'warning';
      case 'low':
        return 'error';
      default:
        return 'default';
    }
  };

  const getPriceChange = (current, previous) => {
    const change = ((current - previous) / previous) * 100;
    return change.toFixed(1);
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">Market Prices</Typography>
        <TextField
          select
          value={selectedMarket}
          onChange={(e) => setSelectedMarket(e.target.value)}
          sx={{ minWidth: 200 }}
        >
          {marketData.markets.map((market) => (
            <MenuItem key={market} value={market}>
              {market}
            </MenuItem>
          ))}
        </TextField>
      </Box>

      {/* Price Overview Cards */}
      <Grid container spacing={3} mb={4}>
        {marketData.crops.map((crop) => (
          <Grid item xs={12} sm={6} md={3} key={crop.name}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="h6" gutterBottom>
                    {crop.name}
                  </Typography>
                  {getTrendIcon(crop.trend)}
                </Box>
                <Typography variant="h4" gutterBottom>
                  ₹{crop.currentPrice}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  {crop.unit}
                </Typography>
                <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                  <Chip
                    label={`${getPriceChange(crop.currentPrice, crop.previousPrice)}%`}
                    color={crop.trend === 'up' ? 'success' : crop.trend === 'down' ? 'error' : 'default'}
                    size="small"
                  />
                  <Chip
                    label={`Demand: ${crop.demand}`}
                    color={getDemandColor(crop.demand)}
                    size="small"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Price History Table */}
      <Paper elevation={2}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Crop</TableCell>
                <TableCell align="right">Current Price</TableCell>
                <TableCell align="right">Previous Price</TableCell>
                <TableCell align="right">Change</TableCell>
                <TableCell>Trend</TableCell>
                <TableCell>Demand</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {marketData.crops.map((crop) => (
                <TableRow key={crop.name}>
                  <TableCell component="th" scope="row">
                    {crop.name}
                  </TableCell>
                  <TableCell align="right">₹{crop.currentPrice}</TableCell>
                  <TableCell align="right">₹{crop.previousPrice}</TableCell>
                  <TableCell align="right">
                    <Chip
                      label={`${getPriceChange(crop.currentPrice, crop.previousPrice)}%`}
                      color={crop.trend === 'up' ? 'success' : crop.trend === 'down' ? 'error' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{getTrendIcon(crop.trend)}</TableCell>
                  <TableCell>
                    <Chip
                      label={crop.demand}
                      color={getDemandColor(crop.demand)}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Price Alerts */}
      <Box mt={4}>
        <Button
          variant="outlined"
          startIcon={<NotificationsIcon />}
          onClick={() => {/* TODO: Implement price alerts */}}
        >
          Set Price Alerts
        </Button>
      </Box>
    </Box>
  );
};

export default Market; 