import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Divider,
} from '@mui/material';
import {
  WbSunny as SunIcon,
  Opacity as RainIcon,
  Air as WindIcon,
  Thermostat as TempIcon,
} from '@mui/icons-material';

// Mock weather data (replace with actual API integration)
const mockWeatherData = {
  current: {
    temperature: 28,
    humidity: 65,
    windSpeed: 12,
    rainfall: 0,
    condition: 'Sunny',
  },
  forecast: [
    { day: 'Tomorrow', high: 29, low: 22, condition: 'Partly Cloudy', rainfall: 20 },
    { day: 'Day 2', high: 27, low: 21, condition: 'Rain', rainfall: 60 },
    { day: 'Day 3', high: 26, low: 20, condition: 'Thunderstorm', rainfall: 80 },
    { day: 'Day 4', high: 28, low: 21, condition: 'Sunny', rainfall: 0 },
    { day: 'Day 5', high: 29, low: 22, condition: 'Clear', rainfall: 0 },
  ],
  alerts: [
    {
      type: 'warning',
      message: 'Heavy rainfall expected in the next 48 hours',
      timestamp: '2024-02-20T10:00:00Z',
    },
  ],
};

const Weather = () => {
  const [weatherData, setWeatherData] = useState(mockWeatherData);

  useEffect(() => {
    // TODO: Implement actual weather API integration
    setWeatherData(mockWeatherData);
  }, []);

  const getWeatherIcon = (condition) => {
    switch (condition.toLowerCase()) {
      case 'sunny':
      case 'clear':
        return <SunIcon sx={{ fontSize: 40, color: '#FF9800' }} />;
      case 'rain':
      case 'thunderstorm':
        return <RainIcon sx={{ fontSize: 40, color: '#2196F3' }} />;
      default:
        return <SunIcon sx={{ fontSize: 40, color: '#FF9800' }} />;
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Weather Forecast
      </Typography>

      {/* Current Weather */}
      <Paper elevation={2} sx={{ p: 3, mb: 4, bgcolor: '#f5f5f5' }}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Box display="flex" alignItems="center" gap={2}>
              {getWeatherIcon(weatherData.current.condition)}
              <Box>
                <Typography variant="h3">
                  {weatherData.current.temperature}°C
                </Typography>
                <Typography variant="subtitle1" color="textSecondary">
                  {weatherData.current.condition}
                </Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Box display="flex" alignItems="center" gap={1}>
                  <Opacity sx={{ color: '#2196F3' }} />
                  <Box>
                    <Typography variant="body2" color="textSecondary">
                      Humidity
                    </Typography>
                    <Typography variant="h6">
                      {weatherData.current.humidity}%
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box display="flex" alignItems="center" gap={1}>
                  <WindIcon sx={{ color: '#90A4AE' }} />
                  <Box>
                    <Typography variant="body2" color="textSecondary">
                      Wind
                    </Typography>
                    <Typography variant="h6">
                      {weatherData.current.windSpeed} km/h
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box display="flex" alignItems="center" gap={1}>
                  <RainIcon sx={{ color: '#2196F3' }} />
                  <Box>
                    <Typography variant="body2" color="textSecondary">
                      Rainfall
                    </Typography>
                    <Typography variant="h6">
                      {weatherData.current.rainfall} mm
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Paper>

      {/* 5-Day Forecast */}
      <Typography variant="h6" gutterBottom>
        5-Day Forecast
      </Typography>
      <Grid container spacing={2} mb={4}>
        {weatherData.forecast.map((day, index) => (
          <Grid item xs={12} sm={6} md={2.4} key={index}>
            <Card>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  {day.day}
                </Typography>
                {getWeatherIcon(day.condition)}
                <Box mt={1}>
                  <Typography variant="h6">
                    {day.high}° / {day.low}°
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {day.condition}
                  </Typography>
                  <Typography variant="body2" color="primary">
                    Rain: {day.rainfall}%
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Weather Alerts */}
      {weatherData.alerts.length > 0 && (
        <>
          <Typography variant="h6" gutterBottom>
            Weather Alerts
          </Typography>
          <Paper elevation={1} sx={{ p: 2, bgcolor: '#FFF3E0' }}>
            {weatherData.alerts.map((alert, index) => (
              <Box key={index}>
                <Typography variant="subtitle1" color="error">
                  {alert.message}
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  {new Date(alert.timestamp).toLocaleString()}
                </Typography>
                {index < weatherData.alerts.length - 1 && <Divider sx={{ my: 1 }} />}
              </Box>
            ))}
          </Paper>
        </>
      )}
    </Box>
  );
};

export default Weather; 