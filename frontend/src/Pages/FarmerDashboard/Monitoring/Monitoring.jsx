import React, { useState } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  LinearProgress,
} from '@mui/material';
import {
  WaterDrop as WaterIcon,
  Thermostat as TempIcon,
  Co2 as SoilIcon,
  BrightnessHigh as LightIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';

// Mock IoT sensor data (replace with actual API integration)
const mockSensorData = {
  fields: ['Field 1', 'Field 2', 'Field 3'],
  sensors: {
    'Field 1': {
      soilMoisture: 65,
      temperature: 28,
      humidity: 70,
      soilPH: 6.5,
      lightIntensity: 85,
      alerts: [
        {
          type: 'warning',
          message: 'Soil moisture below optimal level',
          timestamp: new Date().toISOString(),
        },
      ],
    },
    'Field 2': {
      soilMoisture: 75,
      temperature: 27,
      humidity: 65,
      soilPH: 7.0,
      lightIntensity: 90,
      alerts: [],
    },
    'Field 3': {
      soilMoisture: 55,
      temperature: 29,
      humidity: 60,
      soilPH: 6.8,
      lightIntensity: 80,
      alerts: [
        {
          type: 'critical',
          message: 'Temperature exceeding threshold',
          timestamp: new Date().toISOString(),
        },
      ],
    },
  },
};

const Monitoring = () => {
  const [selectedField, setSelectedField] = useState('Field 1');
  const [sensorData, setSensorData] = useState(mockSensorData);

  const getStatusColor = (value, type) => {
    switch (type) {
      case 'moisture':
        return value < 60 ? 'error' : value > 80 ? 'warning' : 'success';
      case 'temperature':
        return value > 30 ? 'error' : value < 20 ? 'warning' : 'success';
      case 'ph':
        return value < 6.0 || value > 7.5 ? 'warning' : 'success';
      default:
        return 'primary';
    }
  };

  const getProgressValue = (value, type) => {
    switch (type) {
      case 'moisture':
      case 'humidity':
      case 'light':
        return value;
      case 'temperature':
        return (value / 50) * 100;
      case 'ph':
        return ((value - 4) / 10) * 100;
      default:
        return value;
    }
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">Field Monitoring</Typography>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Select Field</InputLabel>
          <Select
            value={selectedField}
            label="Select Field"
            onChange={(e) => setSelectedField(e.target.value)}
          >
            {sensorData.fields.map((field) => (
              <MenuItem key={field} value={field}>
                {field}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      {/* Sensor Data Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" mb={2}>
                <Typography variant="h6">Soil Moisture</Typography>
                <WaterIcon color="primary" />
              </Box>
              <Typography variant="h4" gutterBottom>
                {sensorData.sensors[selectedField].soilMoisture}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={sensorData.sensors[selectedField].soilMoisture}
                color={getStatusColor(sensorData.sensors[selectedField].soilMoisture, 'moisture')}
                sx={{ height: 8, borderRadius: 4 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" mb={2}>
                <Typography variant="h6">Temperature</Typography>
                <TempIcon color="error" />
              </Box>
              <Typography variant="h4" gutterBottom>
                {sensorData.sensors[selectedField].temperature}°C
              </Typography>
              <LinearProgress
                variant="determinate"
                value={getProgressValue(sensorData.sensors[selectedField].temperature, 'temperature')}
                color={getStatusColor(sensorData.sensors[selectedField].temperature, 'temperature')}
                sx={{ height: 8, borderRadius: 4 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" mb={2}>
                <Typography variant="h6">Soil pH</Typography>
                <SoilIcon color="primary" />
              </Box>
              <Typography variant="h4" gutterBottom>
                {sensorData.sensors[selectedField].soilPH}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={getProgressValue(sensorData.sensors[selectedField].soilPH, 'ph')}
                color={getStatusColor(sensorData.sensors[selectedField].soilPH, 'ph')}
                sx={{ height: 8, borderRadius: 4 }}
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Additional Metrics */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" mb={2}>
                <Typography variant="h6">Humidity</Typography>
                <WaterIcon />
              </Box>
              <Typography variant="h4" gutterBottom>
                {sensorData.sensors[selectedField].humidity}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={sensorData.sensors[selectedField].humidity}
                color="primary"
                sx={{ height: 8, borderRadius: 4 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" mb={2}>
                <Typography variant="h6">Light Intensity</Typography>
                <LightIcon sx={{ color: '#FFB300' }} />
              </Box>
              <Typography variant="h4" gutterBottom>
                {sensorData.sensors[selectedField].lightIntensity}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={sensorData.sensors[selectedField].lightIntensity}
                color="warning"
                sx={{ height: 8, borderRadius: 4 }}
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Alerts */}
      {sensorData.sensors[selectedField].alerts.length > 0 && (
        <Box mt={4}>
          <Typography variant="h6" gutterBottom>
            Active Alerts
          </Typography>
          {sensorData.sensors[selectedField].alerts.map((alert, index) => (
            <Alert
              key={index}
              severity={alert.type}
              icon={<WarningIcon />}
              sx={{ mb: 2 }}
            >
              <Typography variant="subtitle2">{alert.message}</Typography>
              <Typography variant="caption" color="textSecondary">
                {new Date(alert.timestamp).toLocaleString()}
              </Typography>
            </Alert>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default Monitoring; 