import React from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Button,
  Card,
  CardContent,
  CardActions,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
} from '@mui/material';
import {
  WbSunny as SunIcon,
  WaterDrop as WaterIcon,
  Warning as WarningIcon,
  Task as TaskIcon,
  Pets as LivestockIcon,
  Storefront as MarketIcon,
  SmartToy as AIAgentIcon,
  NavigateNext as NextIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';

const weatherData = {
  temperature: '28°C',
  humidity: '65%',
  rainfall: '0mm',
  forecast: 'Sunny',
};

const quickActions = [
  { title: 'View Tasks', icon: <TaskIcon />, path: '/farmer/tasks', color: '#4CAF50' },
  { title: 'Check Weather', icon: <SunIcon />, path: '/farmer/weather', color: '#2196F3' },
  { title: 'Market Prices', icon: <MarketIcon />, path: '/farmer/market', color: '#FF9800' },
  { title: 'Ask AI Agent', icon: <AIAgentIcon />, path: '/farmer/ai-agent', color: '#9C27B0' },
];

const alerts = [
  {
    type: 'warning',
    message: 'Low soil moisture detected in Field 2',
    timestamp: '2 hours ago',
  },
  {
    type: 'info',
    message: 'Optimal time for crop irrigation approaching',
    timestamp: '3 hours ago',
  },
  {
    type: 'critical',
    message: 'Weather alert: Heavy rain expected tomorrow',
    timestamp: '1 hour ago',
  },
];

const FarmerHome = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  return (
    <Box>
      {/* Welcome Section */}
      <Box mb={4}>
        <Typography variant="h4" gutterBottom>
          Welcome back, {user?.displayName || 'Farmer'}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Here's your farm's overview for today
        </Typography>
      </Box>

      {/* Weather Summary */}
      <Paper elevation={2} sx={{ p: 3, mb: 4, bgcolor: '#f5f5f5' }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom>
              Current Weather
            </Typography>
            <Box display="flex" alignItems="center" gap={2}>
              <SunIcon sx={{ fontSize: 40, color: '#FF9800' }} />
              <Typography variant="h3">{weatherData.temperature}</Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Typography variant="body2" color="textSecondary">
                  Humidity
                </Typography>
                <Typography variant="h6">{weatherData.humidity}</Typography>
              </Grid>
              <Grid item xs={4}>
                <Typography variant="body2" color="textSecondary">
                  Rainfall
                </Typography>
                <Typography variant="h6">{weatherData.rainfall}</Typography>
              </Grid>
              <Grid item xs={4}>
                <Typography variant="body2" color="textSecondary">
                  Forecast
                </Typography>
                <Typography variant="h6">{weatherData.forecast}</Typography>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Paper>

      {/* Quick Actions */}
      <Typography variant="h6" gutterBottom>
        Quick Actions
      </Typography>
      <Grid container spacing={2} mb={4}>
        {quickActions.map((action) => (
          <Grid item xs={12} sm={6} md={3} key={action.title}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
                '&:hover': { transform: 'translateY(-4px)', transition: 'all 0.3s' },
              }}
              onClick={() => navigate(action.path)}
            >
              <CardContent>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 1,
                  }}
                >
                  <Box
                    sx={{
                      p: 1,
                      borderRadius: 1,
                      bgcolor: `${action.color}20`,
                      mr: 1,
                    }}
                  >
                    {React.cloneElement(action.icon, { sx: { color: action.color } })}
                  </Box>
                  <Typography variant="h6" component="div">
                    {action.title}
                  </Typography>
                </Box>
              </CardContent>
              <CardActions>
                <Button size="small" endIcon={<NextIcon />}>
                  Go to {action.title}
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Recent Alerts */}
      <Typography variant="h6" gutterBottom>
        Recent Alerts
      </Typography>
      <Paper elevation={1}>
        <List>
          {alerts.map((alert, index) => (
            <React.Fragment key={index}>
              <ListItem
                secondaryAction={
                  <IconButton edge="end">
                    <NextIcon />
                  </IconButton>
                }
              >
                <ListItemIcon>
                  <WarningIcon
                    sx={{
                      color:
                        alert.type === 'warning'
                          ? '#FF9800'
                          : alert.type === 'critical'
                          ? '#f44336'
                          : '#2196F3',
                    }}
                  />
                </ListItemIcon>
                <ListItemText
                  primary={alert.message}
                  secondary={alert.timestamp}
                />
              </ListItem>
              {index < alerts.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>
      </Paper>
    </Box>
  );
};

export default FarmerHome; 