import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import './FarmerDash.css';

const FarmerDash = () => {
    const { currentUser } = useAuth();
    const isEmulator = currentUser?.isEmulator || window.location.hostname === "localhost";

    return (
        <div className="farmer-dashboard">
            {isEmulator && (
                <div className="emulator-indicator">
                    Running in emulator mode with mock data
                </div>
            )}
            
            <div className="farmer-dash-container">
                <header className="farmer-header">
                    <h1>Welcome, Farmer</h1>
                    <p>Manage your farms and view recommendations</p>
                </header>

                <div className="dashboard-grid">
                    <div className="dashboard-card weather-card">
                        <h2>Weather Forecast</h2>
                        <div className="card-content">
                            <p>Today's forecast information will appear here</p>
                        </div>
                    </div>

                    <div className="dashboard-card crops-card">
                        <h2>My Crops</h2>
                        <div className="card-content">
                            <p>Your active crops and their status will appear here</p>
                        </div>
                    </div>

                    <div className="dashboard-card tasks-card">
                        <h2>Pending Tasks</h2>
                        <div className="card-content">
                            <p>Your upcoming tasks and activities will appear here</p>
                        </div>
                    </div>

                    <div className="dashboard-card recommendations-card">
                        <h2>Recommendations</h2>
                        <div className="card-content">
                            <p>Personalized recommendations will appear here</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FarmerDash; 