import React, { useState } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import {
  Box,
  Container,
  Paper,
  Typography,
  Grid,
  Switch,
  FormControlLabel,
  Divider,
  Button,
  IconButton,
  Alert,
  Snackbar,
  Card,
  CardContent,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
  CircularProgress
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Notifications as NotificationsIcon,
  Language as LanguageIcon,
  Palette as PaletteIcon,
  Security as SecurityIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const Settings = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();

  // Loading states
  const [loading, setLoading] = useState(false);
  const [changingPassword, setChangingPassword] = useState(false);

  // Settings states
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [smsNotifications, setSmsNotifications] = useState(false);
  // Use global language context instead of local state
  const { selectedLanguage: language, setSelectedLanguage: setLanguage } = useLanguage();
  const [theme, setTheme] = useState('light');
  const [dataPrivacy, setDataPrivacy] = useState('private');

  // Password change states
  const [showPassword, setShowPassword] = useState(false);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Snackbar state
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Handle back navigation
  const handleBack = () => {
    navigate('/dashboard');
  };

  // Handle notification toggle
  const handleNotificationsToggle = () => {
    setNotificationsEnabled(!notificationsEnabled);
  };

  // Handle email notifications toggle
  const handleEmailNotificationsToggle = () => {
    setEmailNotifications(!emailNotifications);
  };

  // Handle SMS notifications toggle
  const handleSmsNotificationsToggle = () => {
    setSmsNotifications(!smsNotifications);
  };

  // Handle language change
  const handleLanguageChange = (event) => {
    setLanguage(event.target.value);
  };

  // Handle theme change
  const handleThemeChange = (event) => {
    setTheme(event.target.value);
  };

  // Handle data privacy change
  const handleDataPrivacyChange = (event) => {
    setDataPrivacy(event.target.value);
  };

  // Handle password visibility toggle
  const handlePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Handle password form change
  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle password update
  const handlePasswordUpdate = async () => {
    // Password validation
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setSnackbar({
        open: true,
        message: 'New passwords do not match',
        severity: 'error'
      });
      return;
    }

    if (passwordForm.newPassword.length < 8) {
      setSnackbar({
        open: true,
        message: 'Password must be at least 8 characters long',
        severity: 'error'
      });
      return;
    }

    try {
      setChangingPassword(true);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSnackbar({
        open: true,
        message: 'Password updated successfully',
        severity: 'success'
      });

      // Reset form
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (err) {
      setSnackbar({
        open: true,
        message: 'Failed to update password',
        severity: 'error'
      });
      console.error('Error updating password:', err);
    } finally {
      setChangingPassword(false);
    }
  };

  // Handle save settings
  const handleSaveSettings = async () => {
    try {
      setLoading(true);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSnackbar({
        open: true,
        message: 'Settings saved successfully',
        severity: 'success'
      });
    } catch (err) {
      setSnackbar({
        open: true,
        message: 'Failed to save settings',
        severity: 'error'
      });
      console.error('Error saving settings:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };



  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', mb: 2 }}>
          <IconButton onClick={handleBack} sx={{ mr: 1 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, lineHeight: '40px' }}>
            Back to Dashboard
          </Typography>
        </Box>

        <Typography variant="h4" component="h1" gutterBottom>
          Settings
        </Typography>

        <Grid container spacing={3}>
          {/* Notifications Settings */}
          <Grid item xs={12} md={6}>
            <Paper elevation={3} sx={{ p: 3, height: '100%' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <NotificationsIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Notifications</Typography>
              </Box>
              <Divider sx={{ mb: 2 }} />

              <FormControlLabel
                control={
                  <Switch
                    checked={notificationsEnabled}
                    onChange={handleNotificationsToggle}
                    color="primary"
                  />
                }
                label="Enable Notifications"
                sx={{ mb: 2, display: 'block' }}
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={emailNotifications}
                    onChange={handleEmailNotificationsToggle}
                    color="primary"
                    disabled={!notificationsEnabled}
                  />
                }
                label="Email Notifications"
                sx={{ mb: 1, display: 'block', ml: 2 }}
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={smsNotifications}
                    onChange={handleSmsNotificationsToggle}
                    color="primary"
                    disabled={!notificationsEnabled}
                  />
                }
                label="SMS Notifications"
                sx={{ mb: 1, display: 'block', ml: 2 }}
              />

              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Receive notifications about weather alerts, market updates, and farm activities.
              </Typography>
            </Paper>
          </Grid>

          {/* Language and Appearance */}
          <Grid item xs={12} md={6}>
            <Paper elevation={3} sx={{ p: 3, height: '100%' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <LanguageIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Language & Appearance</Typography>
              </Box>
              <Divider sx={{ mb: 2 }} />

              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel id="language-select-label">Language</InputLabel>
                <Select
                  labelId="language-select-label"
                  id="language-select"
                  value={language}
                  label="Language"
                  onChange={handleLanguageChange}
                >
                  <MenuItem value="en-IN">English (India)</MenuItem>
                  <MenuItem value="hi-IN">Hindi</MenuItem>
                  <MenuItem value="ta-IN">Tamil</MenuItem>
                  <MenuItem value="te-IN">Telugu</MenuItem>
                  <MenuItem value="mr-IN">Marathi</MenuItem>
                  <MenuItem value="bn-IN">Bengali</MenuItem>
                  <MenuItem value="gu-IN">Gujarati</MenuItem>
                  <MenuItem value="kn-IN">Kannada</MenuItem>
                  <MenuItem value="ml-IN">Malayalam</MenuItem>
                  <MenuItem value="pa-IN">Punjabi</MenuItem>
                </Select>
              </FormControl>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PaletteIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Theme</Typography>
              </Box>

              <FormControl fullWidth>
                <InputLabel id="theme-select-label">Theme</InputLabel>
                <Select
                  labelId="theme-select-label"
                  id="theme-select"
                  value={theme}
                  label="Theme"
                  onChange={handleThemeChange}
                >
                  <MenuItem value="light">Light</MenuItem>
                  <MenuItem value="dark">Dark</MenuItem>
                  <MenuItem value="system">System Default</MenuItem>
                </Select>
              </FormControl>
            </Paper>
          </Grid>

          {/* Security Settings */}
          <Grid item xs={12} md={6}>
            <Paper elevation={3} sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SecurityIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Security</Typography>
              </Box>
              <Divider sx={{ mb: 2 }} />

              <Typography variant="subtitle1" gutterBottom>
                Change Password
              </Typography>

              <TextField
                margin="normal"
                required
                fullWidth
                name="currentPassword"
                label="Current Password"
                type={showPassword ? 'text' : 'password'}
                id="currentPassword"
                value={passwordForm.currentPassword}
                onChange={handlePasswordChange}
                InputProps={{
                  endAdornment: (
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handlePasswordVisibility}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  ),
                }}
                sx={{ mb: 2 }}
              />

              <TextField
                margin="normal"
                required
                fullWidth
                name="newPassword"
                label="New Password"
                type={showPassword ? 'text' : 'password'}
                id="newPassword"
                value={passwordForm.newPassword}
                onChange={handlePasswordChange}
                sx={{ mb: 2 }}
              />

              <TextField
                margin="normal"
                required
                fullWidth
                name="confirmPassword"
                label="Confirm New Password"
                type={showPassword ? 'text' : 'password'}
                id="confirmPassword"
                value={passwordForm.confirmPassword}
                onChange={handlePasswordChange}
                sx={{ mb: 2 }}
              />

              <Button
                variant="contained"
                color="primary"
                onClick={handlePasswordUpdate}
                disabled={changingPassword || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
                startIcon={changingPassword ? <CircularProgress size={20} color="inherit" /> : null}
              >
                {changingPassword ? 'Updating...' : 'Update Password'}
              </Button>
            </Paper>
          </Grid>

          {/* Privacy Settings */}
          <Grid item xs={12} md={6}>
            <Paper elevation={3} sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SecurityIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Privacy</Typography>
              </Box>
              <Divider sx={{ mb: 2 }} />

              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel id="privacy-select-label">Data Privacy</InputLabel>
                <Select
                  labelId="privacy-select-label"
                  id="privacy-select"
                  value={dataPrivacy}
                  label="Data Privacy"
                  onChange={handleDataPrivacyChange}
                >
                  <MenuItem value="private">Private - Only you can see your data</MenuItem>
                  <MenuItem value="shared">Shared - Share with agricultural experts</MenuItem>
                  <MenuItem value="public">Public - Contribute to agricultural research</MenuItem>
                </Select>
              </FormControl>

              <Typography variant="body2" color="text.secondary">
                Control how your farm data is used and shared. Your personal information is always kept private.
              </Typography>

              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Data Export
                </Typography>
                <Button variant="outlined" color="primary">
                  Export My Data
                </Button>
              </Box>
            </Paper>
          </Grid>

          {/* Save Button */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button
                variant="contained"
                color="primary"
                size="large"
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
                onClick={handleSaveSettings}
                disabled={loading}
              >
                {loading ? 'Saving...' : 'Save Settings'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Settings;
