import React, { useState, useEffect } from "react";
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Tabs,
  Tab,
  <PERSON>ton,
  IconButton,
  Chip,
  Divider,
  CircularProgress,
  Alert,
  Tooltip,
  Snackbar,
} from "@mui/material";
import {
  SmartToy as DroneIcon,
  FlightTakeoff as TakeoffIcon,
  FlightLand as LandIcon,
  Pause as PauseIcon,
  PlayArrow as PlayIcon,
  Videocam as VideocamIcon,
  CameraAlt as CameraIcon,
  Healing as SprayIcon,
  BugReport as PestIcon,
  LocalHospital as DiseaseIcon,
  Map as MapIcon,
  Battery90 as BatteryIcon,
  Speed as SpeedIcon,
  Thermostat as TempIcon,
  ArrowBack as ArrowBackIcon,
  ViewList as TableIcon,
  GridView as GridViewIcon,
  LocationOn as LocationIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";

// Import components
import DroneMap from "./components/DroneMap";
import DroneControls from "./components/DroneControls";
import DroneStatus from "./components/DroneStatus";
import DroneCameraFeed from "./components/DroneCameraFeed";
import CropAnalysis from "./components/CropAnalysis";
import SprayingControls from "./components/SprayingControls";
import MissionPlanner from "./components/MissionPlanner";
import DiseaseDetection from "./components/DiseaseDetection";
import DroneTelemetry from "./components/DroneTelemetry";
import DroneMaintenance from "./components/DroneMaintenance";
import WeatherConditions from "./components/WeatherConditions";
import BatteryManagement from "./components/BatteryManagement";
import AdvancedFeatures from "./components/AdvancedFeatures";
import DataDashboard from "./components/DataDashboard";
import { useTranslation } from "react-i18next";

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`drone-tabpanel-${index}`}
      aria-labelledby={`drone-tab-${index}`}
      style={{ height: "100%" }}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 2, height: "100%", overflow: "auto" }}>{children}</Box>
      )}
    </div>
  );
}

const DroneMonitoring = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(0);
  const [droneStatus, setDroneStatus] = useState({
    status: "idle", // idle, flying, returning, spraying, analyzing
    battery: 85,
    altitude: 0,
    speed: 0,
    temperature: 28,
    signalStrength: 92,
    lastUpdated: new Date().toISOString(),
  });
  const [dronePosition, setDronePosition] = useState({
    lat: 12.8592,
    lng: 77.5397,
  });
  const [isFlying, setIsFlying] = useState(false);
  const [isSpraying, setIsSpraying] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeMission, setActiveMission] = useState(null);
  const [detectedDiseases, setDetectedDiseases] = useState([]);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // Drone movement simulation
  useEffect(() => {
    let interval;

    if (isFlying) {
      interval = setInterval(() => {
        // Update drone position
        setDronePosition((prevPosition) => ({
          lat: prevPosition.lat + (Math.random() - 0.5) * 0.0005,
          lng: prevPosition.lng + (Math.random() - 0.5) * 0.0005,
        }));

        // Update drone status
        setDroneStatus((prevStatus) => ({
          ...prevStatus,
          status: isSpraying
            ? "spraying"
            : isAnalyzing
            ? "analyzing"
            : "flying",
          altitude: isSpraying
            ? 2 + Math.random() * 0.5
            : 10 + Math.random() * 2,
          speed: 5 + Math.random() * 2,
          battery: Math.max(prevStatus.battery - 0.05, 0),
          temperature: 28 + Math.random() * 2,
          signalStrength: Math.min(
            Math.max(prevStatus.signalStrength + (Math.random() - 0.5) * 2, 70),
            100
          ),
          lastUpdated: new Date().toISOString(),
        }));
      }, 1000);
    } else {
      setDroneStatus((prevStatus) => ({
        ...prevStatus,
        status: "idle",
        altitude: 0,
        speed: 0,
        lastUpdated: new Date().toISOString(),
      }));
    }

    return () => clearInterval(interval);
  }, [isFlying, isSpraying, isAnalyzing]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle drone takeoff
  const handleTakeoff = () => {
    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsFlying(true);
      setLoading(false);
    }, 2000);
  };

  // Handle drone landing
  const handleLand = () => {
    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsFlying(false);
      setIsSpraying(false);
      setIsAnalyzing(false);
      setLoading(false);
    }, 2000);
  };

  // Handle drone pause/resume
  const handlePauseResume = () => {
    setDroneStatus((prevStatus) => ({
      ...prevStatus,
      status:
        prevStatus.status === "paused"
          ? isSpraying
            ? "spraying"
            : isAnalyzing
            ? "analyzing"
            : "flying"
          : "paused",
      lastUpdated: new Date().toISOString(),
    }));
  };

  // Handle spraying toggle
  const handleSprayingToggle = () => {
    if (isFlying) {
      setIsSpraying(!isSpraying);
      if (!isSpraying) {
        setIsAnalyzing(false);
      }
    } else {
      setError("Drone must be flying to start spraying");
      setTimeout(() => setError(null), 3000);
    }
  };

  // Handle analysis toggle
  const handleAnalysisToggle = () => {
    if (isFlying) {
      setIsAnalyzing(!isAnalyzing);
      if (!isAnalyzing) {
        setIsSpraying(false);
      }
    } else {
      setError("Drone must be flying to start analysis");
      setTimeout(() => setError(null), 3000);
    }
  };

  // Handle mission start
  const handleStartMission = (mission) => {
    if (isFlying) {
      setActiveMission(mission);
    } else {
      setError("Drone must be flying to start a mission");
      setTimeout(() => setError(null), 3000);
    }
  };

  // Handle mission cancel
  const handleCancelMission = () => {
    setActiveMission(null);
  };

  // Handle back navigation
  const handleBack = () => {
    navigate("/dashboard");
  };

  // Handle notification close
  const handleNotificationClose = () => {
    setNotification((prev) => ({ ...prev, open: false }));
  };

  // Simulate disease detection
  useEffect(() => {
    if (isAnalyzing) {
      const interval = setInterval(() => {
        const diseases = [
          {
            id: 1,
            name: "Leaf Blight",
            severity: "Medium",
            location: { ...dronePosition },
            timestamp: new Date().toISOString(),
          },
          {
            id: 2,
            name: "Powdery Mildew",
            severity: "Low",
            location: { ...dronePosition },
            timestamp: new Date().toISOString(),
          },
          {
            id: 3,
            name: "Rust",
            severity: "High",
            location: { ...dronePosition },
            timestamp: new Date().toISOString(),
          },
          {
            id: 4,
            name: "Bacterial Spot",
            severity: "Medium",
            location: { ...dronePosition },
            timestamp: new Date().toISOString(),
          },
          {
            id: 5,
            name: "Early Blight",
            severity: "High",
            location: { ...dronePosition },
            timestamp: new Date().toISOString(),
          },
        ];

        // Randomly detect a disease
        if (Math.random() > 0.7) {
          const randomDisease =
            diseases[Math.floor(Math.random() * diseases.length)];
          const newDisease = { ...randomDisease, id: Date.now() };
          setDetectedDiseases((prev) => [...prev, newDisease]);

          // Show notification for high severity diseases
          if (newDisease.severity === "High") {
            setNotification({
              open: true,
              message: `Alert: ${newDisease.name} detected with high severity!`,
              severity: "error",
            });
          } else if (newDisease.severity === "Medium") {
            setNotification({
              open: true,
              message: `Warning: ${newDisease.name} detected with medium severity`,
              severity: "warning",
            });
          }
        }
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [isAnalyzing, dronePosition]);

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box
        sx={{ display: "flex", alignItems: "center", mb: 2, flexWrap: "wrap" }}
      >
        <IconButton onClick={handleBack} sx={{ mr: 1 }} size="small">
          <ArrowBackIcon fontSize="small" />
        </IconButton>
        <Typography
          variant="h5"
          component="h1"
          sx={{ flexGrow: 1, display: "flex", alignItems: "center" }}
        >
          <DroneIcon sx={{ mr: 0.5, fontSize: "1.2rem" }} />
          {t("drone_monitoring_msg")}
        </Typography>
        <Box sx={{ display: "flex", flexWrap: "wrap" }}>
          <Chip
            icon={<BatteryIcon />}
            label={`Battery: ${droneStatus.battery}%`}
            color={
              droneStatus.battery > 50
                ? "success"
                : droneStatus.battery > 20
                ? "warning"
                : "error"
            }
            sx={{ mr: 1, mb: { xs: 1, sm: 0 } }}
            size="small"
          />
          <Chip
            label={droneStatus.status.toUpperCase()}
            color={
              droneStatus.status === "idle"
                ? "default"
                : droneStatus.status === "flying"
                ? "primary"
                : droneStatus.status === "spraying"
                ? "success"
                : droneStatus.status === "analyzing"
                ? "info"
                : droneStatus.status === "paused"
                ? "warning"
                : "default"
            }
            size="small"
          />
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Loading indicator */}
      {loading && (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            my: 2,
          }}
        >
          <CircularProgress size={30} sx={{ mr: 2 }} />
          <Typography variant="body1">Loading drone systems...</Typography>
        </Box>
      )}

      {/* Weather and Battery Management - Top Row */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <WeatherConditions />
        </Grid>
        <Grid item xs={12} md={6}>
          <BatteryManagement droneStatus={droneStatus} />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Map and Camera Feed */}
        <Grid item xs={12} lg={8}>
          <Paper
            sx={{ p: 0, display: "flex", flexDirection: "column", height: 500 }}
          >
            <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                aria-label="drone monitoring tabs"
                variant="scrollable"
                scrollButtons="auto"
              >
                <Tab icon={<MapIcon />} label="Map View" />
                <Tab icon={<VideocamIcon />} label="Camera Feed" />
                <Tab icon={<DiseaseIcon />} label="Disease Detection" />
                <Tab icon={<SprayIcon />} label="Spraying" />
                <Tab icon={<MapIcon />} label="Mission Planner" />
                <Tab icon={<TableIcon />} label="Telemetry" />
                <Tab icon={<GridViewIcon />} label="Maintenance" />
              </Tabs>
            </Box>

            <TabPanel value={activeTab} index={0}>
              <DroneMap
                dronePosition={dronePosition}
                isFlying={isFlying}
                isSpraying={isSpraying}
                isAnalyzing={isAnalyzing}
                detectedDiseases={detectedDiseases}
                activeMission={activeMission}
              />
            </TabPanel>

            <TabPanel value={activeTab} index={1}>
              <DroneCameraFeed
                isFlying={isFlying}
                dronePosition={dronePosition}
              />
            </TabPanel>

            <TabPanel value={activeTab} index={2}>
              <DiseaseDetection
                isAnalyzing={isAnalyzing}
                onToggleAnalysis={handleAnalysisToggle}
                detectedDiseases={detectedDiseases}
                dronePosition={dronePosition}
              />
            </TabPanel>

            <TabPanel value={activeTab} index={3}>
              <SprayingControls
                isSpraying={isSpraying}
                isFlying={isFlying}
                onToggleSpraying={handleSprayingToggle}
                dronePosition={dronePosition}
              />
            </TabPanel>

            <TabPanel value={activeTab} index={4}>
              <MissionPlanner
                isFlying={isFlying}
                activeMission={activeMission}
                onStartMission={handleStartMission}
                onCancelMission={handleCancelMission}
              />
            </TabPanel>

            <TabPanel value={activeTab} index={5}>
              <DroneTelemetry droneStatus={droneStatus} isFlying={isFlying} />
            </TabPanel>

            <TabPanel value={activeTab} index={6}>
              <DroneMaintenance />
            </TabPanel>
          </Paper>

          {/* Advanced Features and Data Dashboard */}
          <Box sx={{ width: "100%" }}>
            <AdvancedFeatures />
            <DataDashboard />
          </Box>
        </Grid>

        {/* Controls and Status */}
        <Grid item xs={12} lg={4}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Paper sx={{ p: 2, display: "flex", flexDirection: "column" }}>
                <Typography variant="h6" gutterBottom>
                  Drone Controls
                </Typography>
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: { xs: "column", sm: "row" },
                    justifyContent: "space-between",
                    mb: 2,
                    gap: 1,
                  }}
                >
                  <Button
                    variant="contained"
                    color="success"
                    startIcon={loading && !isFlying ? null : <TakeoffIcon />}
                    onClick={handleTakeoff}
                    disabled={isFlying || loading}
                    fullWidth
                  >
                    {loading && !isFlying ? (
                      <>
                        <CircularProgress size={20} sx={{ mr: 1 }} />
                        Loading...
                      </>
                    ) : (
                      "Take Off"
                    )}
                  </Button>

                  <Button
                    variant="contained"
                    color={
                      droneStatus.status === "paused" ? "success" : "warning"
                    }
                    startIcon={
                      droneStatus.status === "paused" ? (
                        <PlayIcon />
                      ) : (
                        <PauseIcon />
                      )
                    }
                    onClick={handlePauseResume}
                    disabled={!isFlying || loading}
                    fullWidth
                  >
                    {droneStatus.status === "paused" ? "Resume" : "Pause"}
                  </Button>

                  <Button
                    variant="contained"
                    color="error"
                    startIcon={loading && isFlying ? null : <LandIcon />}
                    onClick={handleLand}
                    disabled={!isFlying || loading}
                    fullWidth
                  >
                    {loading && isFlying ? (
                      <>
                        <CircularProgress size={20} sx={{ mr: 1 }} />
                        Landing...
                      </>
                    ) : (
                      "Land"
                    )}
                  </Button>
                </Box>

                <Box
                  sx={{
                    display: "flex",
                    flexDirection: { xs: "column", sm: "row" },
                    justifyContent: "space-between",
                    gap: 1,
                  }}
                >
                  <Button
                    variant="outlined"
                    color={isAnalyzing ? "info" : "primary"}
                    startIcon={<CameraIcon />}
                    onClick={handleAnalysisToggle}
                    disabled={!isFlying || loading}
                    fullWidth
                    size="small"
                  >
                    {isAnalyzing ? "Stop Analysis" : "Start Analysis"}
                  </Button>

                  <Button
                    variant="outlined"
                    color={isSpraying ? "success" : "primary"}
                    startIcon={<SprayIcon />}
                    onClick={handleSprayingToggle}
                    disabled={!isFlying || loading}
                    fullWidth
                    size="small"
                  >
                    {isSpraying ? "Stop Spraying" : "Start Spraying"}
                  </Button>
                </Box>
              </Paper>
            </Grid>

            <Grid item xs={12}>
              <DroneStatus status={droneStatus} />
            </Grid>

            <Grid item xs={12}>
              <CropAnalysis
                isAnalyzing={isAnalyzing}
                detectedDiseases={detectedDiseases}
              />
            </Grid>

            {/* Quick Help */}
            <Grid item xs={12}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Quick Help
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Getting Started:</strong> Click "Take Off" to launch
                  the drone. Use the Map View to monitor drone position.
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Camera Feed:</strong> View real-time footage from the
                  drone camera with different visualization modes (RGB, Thermal,
                  NDVI).
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Disease Detection:</strong> Click "Start Analysis" to
                  scan crops for diseases. Results will appear in the Crop
                  Analysis panel.
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Organic Spraying:</strong> Use the Spraying tab to
                  configure and apply organic pesticides or fertilizers to
                  affected areas.
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Telemetry:</strong> View detailed flight data,
                  including altitude, speed, battery level, and flight logs.
                </Typography>
                <Typography variant="body2">
                  <strong>Maintenance:</strong> Monitor drone health, schedule
                  maintenance, and check for firmware updates.
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleNotificationClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleNotificationClose}
          severity={notification.severity}
          sx={{ width: "100%" }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default DroneMonitoring;
