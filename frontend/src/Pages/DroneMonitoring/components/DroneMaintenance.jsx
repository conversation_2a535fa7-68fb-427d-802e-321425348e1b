import React from 'react';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Chip,
  Button,
  LinearProgress
} from '@mui/material';
import {
  Build as BuildIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
  BatteryChargingFull as BatteryIcon,
  RotateLeft as RotateLeftIcon,
  Settings as SettingsIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';

const DroneMaintenance = () => {
  // Mock maintenance data
  const maintenanceItems = [
    { 
      name: 'Propeller Inspection', 
      status: 'good', 
      lastCheck: '2 days ago',
      nextDue: '28 days'
    },
    { 
      name: 'Battery Health', 
      status: 'good', 
      lastCheck: '2 days ago',
      nextDue: '28 days'
    },
    { 
      name: 'Motor Performance', 
      status: 'warning', 
      lastCheck: '15 days ago',
      nextDue: '15 days',
      note: 'Slight vibration detected in rear-left motor'
    },
    { 
      name: 'Firmware Update', 
      status: 'update', 
      lastCheck: '30 days ago',
      nextDue: 'Now',
      note: 'New firmware v2.4.5 available'
    },
    { 
      name: 'Calibration', 
      status: 'good', 
      lastCheck: '7 days ago',
      nextDue: '23 days'
    }
  ];
  
  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'good':
        return <CheckCircleIcon color="success" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'critical':
        return <WarningIcon color="error" />;
      case 'update':
        return <CloudUploadIcon color="info" />;
      default:
        return <ScheduleIcon color="action" />;
    }
  };
  
  // Get status text
  const getStatusText = (status) => {
    switch (status) {
      case 'good':
        return 'Good';
      case 'warning':
        return 'Attention Needed';
      case 'critical':
        return 'Service Required';
      case 'update':
        return 'Update Available';
      default:
        return 'Unknown';
    }
  };
  
  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'good':
        return 'success';
      case 'warning':
        return 'warning';
      case 'critical':
        return 'error';
      case 'update':
        return 'info';
      default:
        return 'default';
    }
  };
  
  // Calculate overall health percentage
  const calculateHealth = () => {
    const statusValues = {
      'good': 100,
      'warning': 60,
      'critical': 20,
      'update': 80
    };
    
    const total = maintenanceItems.reduce((sum, item) => sum + statusValues[item.status], 0);
    return Math.round(total / maintenanceItems.length);
  };
  
  const healthPercentage = calculateHealth();
  
  // Get health color
  const getHealthColor = (percentage) => {
    if (percentage > 80) return 'success';
    if (percentage > 50) return 'warning';
    return 'error';
  };
  
  return (
    <Paper sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <BuildIcon color="primary" sx={{ mr: 1 }} />
        <Typography variant="h6" sx={{ flexGrow: 1 }}>
          Drone Maintenance
        </Typography>
        <Chip 
          label={`Health: ${healthPercentage}%`}
          color={getHealthColor(healthPercentage)}
        />
      </Box>
      
      <Box sx={{ mb: 2 }}>
        <LinearProgress 
          variant="determinate" 
          value={healthPercentage} 
          color={getHealthColor(healthPercentage)}
          sx={{ height: 8, borderRadius: 5 }}
        />
      </Box>
      
      <List sx={{ flexGrow: 1, overflow: 'auto' }}>
        {maintenanceItems.map((item, index) => (
          <React.Fragment key={index}>
            <ListItem alignItems="flex-start">
              <ListItemIcon>
                {getStatusIcon(item.status)}
              </ListItemIcon>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="subtitle2">{item.name}</Typography>
                    <Chip 
                      label={getStatusText(item.status)} 
                      color={getStatusColor(item.status)}
                      size="small"
                    />
                  </Box>
                }
                secondary={
                  <>
                    <Typography variant="body2" component="span" color="text.secondary">
                      Last check: {item.lastCheck} • Next due: {item.nextDue}
                    </Typography>
                    {item.note && (
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                        Note: {item.note}
                      </Typography>
                    )}
                  </>
                }
              />
            </ListItem>
            {index < maintenanceItems.length - 1 && <Divider variant="inset" component="li" />}
          </React.Fragment>
        ))}
      </List>
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
        <Button
          variant="outlined"
          startIcon={<RotateLeftIcon />}
          size="small"
        >
          Calibrate
        </Button>
        
        <Button
          variant="outlined"
          startIcon={<SettingsIcon />}
          size="small"
        >
          Diagnostics
        </Button>
        
        <Button
          variant="outlined"
          startIcon={<CloudUploadIcon />}
          size="small"
          color="primary"
        >
          Update
        </Button>
      </Box>
    </Paper>
  );
};

export default DroneMaintenance;
