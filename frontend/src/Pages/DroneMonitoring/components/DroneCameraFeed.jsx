import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  CircularProgress,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Videocam as VideocamIcon,
  CameraAlt as CameraIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  Cameraswitch as CameraswitchIcon,
  FiberManualRecord as RecordIcon,
  Stop as StopIcon,
  SmartToy as DroneIcon
} from '@mui/icons-material';

// Mock drone camera images - aerial views of farms
const mockImages = [
  'https://images.unsplash.com/photo-1560493676-04071c5f467b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1074&q=80', // Aerial farm view
  'https://images.unsplash.com/photo-1570586437263-ab629fccc818?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80', // Drone view of fields
  'https://images.unsplash.com/photo-1523348837708-15d4a09cfac2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8YWdyaWN1bHR1cmV8ZW58MHx8MHx8&auto=format&fit=crop&w=1074&q=80', // Crop rows
  'https://images.unsplash.com/photo-1625246333195-78d9c38ad449?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80', // Aerial irrigation
  'https://images.unsplash.com/photo-1561048262-4d3c5dd9b0ff?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1074&q=80', // Drone farm view
  'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8YWdyaWN1bHR1cmV8ZW58MHx8MHx8&auto=format&fit=crop&w=1074&q=80', // Fields
];

// Drone image URL - using a more reliable source
const droneImageUrl = 'https://cdn-icons-png.flaticon.com/512/2168/2168281.png';

const DroneCameraFeed = ({ isFlying, dronePosition: droneLoc }) => {
  const [currentImage, setCurrentImage] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [cameraMode, setCameraMode] = useState('rgb'); // rgb, thermal, ndvi
  const [capturedImages, setCapturedImages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [droneUIPosition, setDroneUIPosition] = useState({ x: 50, y: 50 });
  const [droneSize, setDroneSize] = useState(40);

  // Simulate camera feed by cycling through images
  useEffect(() => {
    let interval;

    if (isFlying) {
      interval = setInterval(() => {
        setCurrentImage((prev) => (prev + 1) % mockImages.length);
      }, 3000);
    }

    return () => clearInterval(interval);
  }, [isFlying]);

  // Handle camera mode change
  const handleCameraModeChange = (mode) => {
    setLoading(true);

    // Simulate mode change delay
    setTimeout(() => {
      setCameraMode(mode);
      setLoading(false);
    }, 1000);
  };

  // Handle zoom in
  const handleZoomIn = () => {
    if (zoomLevel < 5) {
      setZoomLevel(prev => prev + 0.5);
    }
  };

  // Handle zoom out
  const handleZoomOut = () => {
    if (zoomLevel > 1) {
      setZoomLevel(prev => prev - 0.5);
    }
  };

  // Handle capture image
  const handleCaptureImage = () => {
    setCapturedImages(prev => [
      ...prev,
      {
        id: Date.now(),
        url: mockImages[currentImage],
        timestamp: new Date().toISOString(),
        location: { ...droneLoc },
        mode: cameraMode
      }
    ]);
  };

  // Animate drone in camera view
  useEffect(() => {
    if (isFlying) {
      const interval = setInterval(() => {
        // Randomly move drone around the center of the screen
        setDroneUIPosition(prev => ({
          x: 50 + (Math.random() - 0.5) * 20, // Move around center horizontally
          y: 50 + (Math.random() - 0.5) * 10  // Move around center vertically
        }));

        // Slightly vary drone size to simulate altitude changes
        setDroneSize(40 + (Math.random() - 0.5) * 5);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [isFlying]);

  // Handle recording toggle
  const handleRecordingToggle = () => {
    setIsRecording(!isRecording);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <Box>
      {/* Camera feed */}
      <Box
        sx={{
          position: 'relative',
          height: 300,
          backgroundColor: '#000',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          overflow: 'hidden',
          borderRadius: 1
        }}
      >
        {isFlying ? (
          <>
            <Box
              component="img"
              src={mockImages[currentImage]}
              alt="Drone Camera Feed"
              sx={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                transform: `scale(${zoomLevel})`,
                transition: 'transform 0.3s ease',
                filter: cameraMode === 'thermal'
                  ? 'hue-rotate(180deg) saturate(200%)'
                  : cameraMode === 'ndvi'
                    ? 'hue-rotate(90deg) saturate(300%) contrast(150%)'
                    : 'none'
              }}
            />

            {/* Drone visualization */}
            <Box
              sx={{
                position: 'absolute',
                left: `${droneUIPosition.x}%`,
                top: `${droneUIPosition.y}%`,
                transform: 'translate(-50%, -50%)',
                transition: 'all 0.5s ease',
                zIndex: 10,
                animation: isFlying ? 'float 2s ease-in-out infinite alternate' : 'none',
                '@keyframes float': {
                  '0%': { transform: 'translate(-50%, -50%)' },
                  '100%': { transform: 'translate(-50%, -55%)' }
                },
                width: droneSize * 1.5,
                height: droneSize,
              }}
            >
              <Box
                component="img"
                src={droneImageUrl}
                alt="Drone"
                sx={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  filter: 'drop-shadow(0px 0px 8px rgba(0,0,0,0.7))',
                  animation: isFlying ? 'pulse 1.5s ease-in-out infinite' : 'none',
                  '@keyframes pulse': {
                    '0%': { opacity: 0.8 },
                    '50%': { opacity: 1 },
                    '100%': { opacity: 0.8 }
                  }
                }}
              />
            </Box>

            {/* Camera info overlay */}
            <Box
              sx={{
                position: 'absolute',
                bottom: 10,
                left: 10,
                right: 10,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}
            >
              <Chip
                label={`Mode: ${cameraMode.toUpperCase()}`}
                color={
                  cameraMode === 'rgb' ? 'primary' :
                  cameraMode === 'thermal' ? 'error' : 'success'
                }
                size="small"
              />

              <Chip
                label={`Zoom: ${zoomLevel.toFixed(1)}x`}
                color="primary"
                size="small"
              />

              {isRecording && (
                <Chip
                  icon={<RecordIcon sx={{ color: 'red !important' }} />}
                  label="REC"
                  color="error"
                  size="small"
                />
              )}
            </Box>

            {/* Loading overlay */}
            {loading && (
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'column'
                }}
              >
                <CircularProgress color="primary" />
                <Typography variant="body2" color="white" sx={{ mt: 2 }}>
                  Switching to {cameraMode.toUpperCase()} mode...
                </Typography>
              </Box>
            )}
          </>
        ) : (
          <Box sx={{ textAlign: 'center', p: 2 }}>
            <Box
              component="img"
              src={droneImageUrl}
              alt="Drone"
              sx={{
                width: 100,
                height: 70,
                objectFit: 'contain',
                opacity: 0.7,
                filter: 'brightness(1.5) drop-shadow(0px 0px 10px rgba(255,255,255,0.3))',
                mb: 2
              }}
            />
            <Typography variant="h6" color="white" gutterBottom>
              Drone camera feed unavailable
            </Typography>
            <Typography variant="body2" color="rgba(255, 255, 255, 0.7)">
              Please click "Take Off" to launch the drone and activate the camera feed.
            </Typography>
          </Box>
        )}
      </Box>

      {/* Camera controls */}
      <Box sx={{ mt: 2 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Button
                variant="outlined"
                color={cameraMode === 'rgb' ? 'primary' : 'inherit'}
                onClick={() => handleCameraModeChange('rgb')}
                disabled={!isFlying || loading}
                size="small"
              >
                RGB
              </Button>

              <Button
                variant="outlined"
                color={cameraMode === 'thermal' ? 'error' : 'inherit'}
                onClick={() => handleCameraModeChange('thermal')}
                disabled={!isFlying || loading}
                size="small"
              >
                Thermal
              </Button>

              <Button
                variant="outlined"
                color={cameraMode === 'ndvi' ? 'success' : 'inherit'}
                onClick={() => handleCameraModeChange('ndvi')}
                disabled={!isFlying || loading}
                size="small"
              >
                NDVI
              </Button>
            </Box>
          </Grid>

          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="Zoom Out">
                <span>
                  <IconButton
                    color="primary"
                    onClick={handleZoomOut}
                    disabled={!isFlying || zoomLevel <= 1}
                  >
                    <ZoomOutIcon />
                  </IconButton>
                </span>
              </Tooltip>

              <Tooltip title="Capture Image">
                <span>
                  <IconButton
                    color="primary"
                    onClick={handleCaptureImage}
                    disabled={!isFlying}
                  >
                    <CameraIcon />
                  </IconButton>
                </span>
              </Tooltip>

              <Tooltip title={isRecording ? "Stop Recording" : "Start Recording"}>
                <span>
                  <IconButton
                    color={isRecording ? "error" : "primary"}
                    onClick={handleRecordingToggle}
                    disabled={!isFlying}
                  >
                    {isRecording ? <StopIcon /> : <RecordIcon />}
                  </IconButton>
                </span>
              </Tooltip>

              <Tooltip title="Zoom In">
                <span>
                  <IconButton
                    color="primary"
                    onClick={handleZoomIn}
                    disabled={!isFlying || zoomLevel >= 5}
                  >
                    <ZoomInIcon />
                  </IconButton>
                </span>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Captured images */}
      {capturedImages.length > 0 && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Recent Captures
          </Typography>
          <Box sx={{ display: 'flex', overflowX: 'auto', pb: 1 }}>
            {capturedImages.slice(-5).reverse().map((image) => (
              <Box
                key={image.id}
                sx={{
                  mr: 1,
                  position: 'relative',
                  borderRadius: 1,
                  overflow: 'hidden',
                  flexShrink: 0
                }}
              >
                <Box
                  component="img"
                  src={image.url}
                  alt="Captured Image"
                  sx={{
                    width: 80,
                    height: 60,
                    objectFit: 'cover',
                    filter: image.mode === 'thermal'
                      ? 'hue-rotate(180deg) saturate(200%)'
                      : image.mode === 'ndvi'
                        ? 'hue-rotate(90deg) saturate(300%) contrast(150%)'
                        : 'none'
                  }}
                />
                <Typography
                  variant="caption"
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.6)',
                    color: 'white',
                    padding: '2px 4px',
                    fontSize: '0.6rem',
                    textAlign: 'center'
                  }}
                >
                  {formatDate(image.timestamp)}
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default DroneCameraFeed;
