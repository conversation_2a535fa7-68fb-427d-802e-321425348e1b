import React from 'react';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Chip,
  CircularProgress
} from '@mui/material';
import {
  BugReport as PestIcon,
  LocalHospital as DiseaseIcon,
  Opacity as MoistureIcon,
  Grass as PlantIcon,
  Warning as WarningIcon
} from '@mui/icons-material';

const CropAnalysis = ({ isAnalyzing, detectedDiseases }) => {
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };
  
  // Get severity color
  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'High':
        return 'error';
      case 'Medium':
        return 'warning';
      case 'Low':
        return 'success';
      default:
        return 'default';
    }
  };
  
  // Get crop health status
  const getCropHealthStatus = () => {
    if (detectedDiseases.length === 0) return 'Healthy';
    
    const highSeverity = detectedDiseases.some(disease => disease.severity === 'High');
    const mediumSeverity = detectedDiseases.some(disease => disease.severity === 'Medium');
    
    if (highSeverity) return 'Critical';
    if (mediumSeverity) return 'Warning';
    return 'Good';
  };
  
  // Get crop health color
  const getCropHealthColor = (status) => {
    switch (status) {
      case 'Healthy':
      case 'Good':
        return 'success';
      case 'Warning':
        return 'warning';
      case 'Critical':
        return 'error';
      default:
        return 'default';
    }
  };
  
  const cropHealthStatus = getCropHealthStatus();
  
  return (
    <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column' }}>
      <Typography variant="h6" gutterBottom>
        Crop Analysis
      </Typography>
      
      {/* Crop health status */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <PlantIcon color={getCropHealthColor(cropHealthStatus)} sx={{ mr: 1 }} />
        <Typography variant="body1" sx={{ flexGrow: 1 }}>
          Crop Health Status
        </Typography>
        <Chip 
          label={cropHealthStatus} 
          color={getCropHealthColor(cropHealthStatus)}
          size="small"
        />
      </Box>
      
      <Divider sx={{ mb: 2 }} />
      
      {/* Analysis status */}
      {isAnalyzing ? (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <CircularProgress size={20} sx={{ mr: 1 }} />
          <Typography variant="body2" color="text.secondary">
            Analyzing crop health...
          </Typography>
        </Box>
      ) : (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {detectedDiseases.length > 0 
            ? 'Detected issues in crop health' 
            : 'No issues detected in crop health'}
        </Typography>
      )}
      
      {/* Detected diseases */}
      {detectedDiseases.length > 0 ? (
        <List dense disablePadding>
          {detectedDiseases.slice(-3).map((disease) => (
            <ListItem key={disease.id} disablePadding sx={{ mb: 1 }}>
              <ListItemIcon sx={{ minWidth: 36 }}>
                <DiseaseIcon color={getSeverityColor(disease.severity)} />
              </ListItemIcon>
              <ListItemText
                primary={disease.name}
                secondary={`Severity: ${disease.severity} • Detected at ${formatDate(disease.timestamp)}`}
                primaryTypographyProps={{ variant: 'body2' }}
                secondaryTypographyProps={{ variant: 'caption' }}
              />
            </ListItem>
          ))}
          
          {detectedDiseases.length > 3 && (
            <Typography variant="caption" color="text.secondary" sx={{ pl: 4, display: 'block' }}>
              + {detectedDiseases.length - 3} more issues detected
            </Typography>
          )}
        </List>
      ) : (
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 2 }}>
          <Typography variant="body2" color="text.secondary">
            No diseases detected
          </Typography>
        </Box>
      )}
      
      <Divider sx={{ my: 2 }} />
      
      {/* Recommendations */}
      <Typography variant="subtitle2" gutterBottom>
        Recommendations
      </Typography>
      
      {detectedDiseases.length > 0 ? (
        <List dense disablePadding>
          {detectedDiseases.some(d => d.severity === 'High') && (
            <ListItem disablePadding sx={{ mb: 1 }}>
              <ListItemIcon sx={{ minWidth: 36 }}>
                <WarningIcon color="error" />
              </ListItemIcon>
              <ListItemText
                primary="Immediate treatment required"
                secondary="Apply fungicide/pesticide as soon as possible"
                primaryTypographyProps={{ variant: 'body2' }}
                secondaryTypographyProps={{ variant: 'caption' }}
              />
            </ListItem>
          )}
          
          {detectedDiseases.some(d => d.name.includes('Blight') || d.name.includes('Mildew')) && (
            <ListItem disablePadding sx={{ mb: 1 }}>
              <ListItemIcon sx={{ minWidth: 36 }}>
                <MoistureIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="Reduce moisture levels"
                secondary="Improve drainage and air circulation"
                primaryTypographyProps={{ variant: 'body2' }}
                secondaryTypographyProps={{ variant: 'caption' }}
              />
            </ListItem>
          )}
          
          <ListItem disablePadding>
            <ListItemIcon sx={{ minWidth: 36 }}>
              <PestIcon color="primary" />
            </ListItemIcon>
            <ListItemText
              primary="Schedule follow-up inspection"
              secondary="Monitor affected areas in 3-5 days"
              primaryTypographyProps={{ variant: 'body2' }}
              secondaryTypographyProps={{ variant: 'caption' }}
            />
          </ListItem>
        </List>
      ) : (
        <List dense disablePadding>
          <ListItem disablePadding>
            <ListItemIcon sx={{ minWidth: 36 }}>
              <PlantIcon color="success" />
            </ListItemIcon>
            <ListItemText
              primary="Continue regular monitoring"
              secondary="Maintain current agricultural practices"
              primaryTypographyProps={{ variant: 'body2' }}
              secondaryTypographyProps={{ variant: 'caption' }}
            />
          </ListItem>
        </List>
      )}
    </Paper>
  );
};

export default CropAnalysis;
