import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  LinearProgress,
  Divider,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Speed as SpeedIcon,
  Height as HeightIcon,
  Thermostat as TempIcon,
  Battery90 as BatteryIcon,
  SignalCellular4Bar as SignalIcon,
  Explore as CompassIcon,
  Air as WindIcon,
  AccessTime as TimeIcon
} from '@mui/icons-material';

const DroneTelemetry = ({ droneStatus, isFlying }) => {
  const [flightData, setFlightData] = useState([]);
  const [flightTime, setFlightTime] = useState(0);
  
  // Update flight time when drone is flying
  useEffect(() => {
    let interval;
    
    if (isFlying) {
      interval = setInterval(() => {
        setFlightTime(prev => prev + 1);
      }, 1000);
    }
    
    return () => clearInterval(interval);
  }, [isFlying]);
  
  // Format flight time
  const formatFlightTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Record flight data every 10 seconds
  useEffect(() => {
    if (isFlying && flightTime % 10 === 0 && flightTime > 0) {
      setFlightData(prev => [
        {
          time: formatFlightTime(flightTime),
          altitude: droneStatus.altitude.toFixed(1),
          speed: droneStatus.speed.toFixed(1),
          battery: droneStatus.battery,
          status: droneStatus.status
        },
        ...prev.slice(0, 4) // Keep only the last 5 records
      ]);
    }
  }, [flightTime, isFlying, droneStatus]);
  
  // Get battery color
  const getBatteryColor = (battery) => {
    if (battery > 50) return 'success';
    if (battery > 20) return 'warning';
    return 'error';
  };
  
  // Get signal color
  const getSignalColor = (signal) => {
    if (signal > 80) return 'success';
    if (signal > 50) return 'warning';
    return 'error';
  };
  
  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'flying':
        return 'primary';
      case 'spraying':
        return 'success';
      case 'analyzing':
        return 'info';
      case 'paused':
        return 'warning';
      default:
        return 'default';
    }
  };
  
  return (
    <Paper sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Typography variant="h6" gutterBottom>
        Drone Telemetry
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={6} sm={3}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <HeightIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="body2">
              Altitude
            </Typography>
          </Box>
          <Typography variant="h6">
            {droneStatus.altitude.toFixed(1)} m
          </Typography>
        </Grid>
        
        <Grid item xs={6} sm={3}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SpeedIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="body2">
              Speed
            </Typography>
          </Box>
          <Typography variant="h6">
            {droneStatus.speed.toFixed(1)} m/s
          </Typography>
        </Grid>
        
        <Grid item xs={6} sm={3}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <CompassIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="body2">
              Heading
            </Typography>
          </Box>
          <Typography variant="h6">
            {Math.floor(Math.random() * 360)}°
          </Typography>
        </Grid>
        
        <Grid item xs={6} sm={3}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <WindIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="body2">
              Wind
            </Typography>
          </Box>
          <Typography variant="h6">
            {(Math.random() * 5).toFixed(1)} m/s
          </Typography>
        </Grid>
      </Grid>
      
      <Divider sx={{ my: 1 }} />
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TimeIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="body2">
            Flight Time
          </Typography>
        </Box>
        <Typography variant="h6">
          {formatFlightTime(flightTime)}
        </Typography>
      </Box>
      
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        <BatteryIcon color={getBatteryColor(droneStatus.battery)} sx={{ mr: 1 }} />
        <Typography variant="body2" sx={{ flexGrow: 1 }}>
          Battery
        </Typography>
        <Typography variant="body2" fontWeight="bold">
          {droneStatus.battery}%
        </Typography>
      </Box>
      <LinearProgress 
        variant="determinate" 
        value={droneStatus.battery} 
        color={getBatteryColor(droneStatus.battery)}
        sx={{ height: 8, borderRadius: 5, mb: 2 }}
      />
      
      <Divider sx={{ my: 1 }} />
      
      <Typography variant="subtitle2" gutterBottom>
        Flight Log
      </Typography>
      
      {flightData.length > 0 ? (
        <TableContainer sx={{ flexGrow: 1 }}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Time</TableCell>
                <TableCell align="right">Alt (m)</TableCell>
                <TableCell align="right">Speed (m/s)</TableCell>
                <TableCell align="right">Battery</TableCell>
                <TableCell align="right">Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {flightData.map((row, index) => (
                <TableRow key={index}>
                  <TableCell component="th" scope="row">
                    {row.time}
                  </TableCell>
                  <TableCell align="right">{row.altitude}</TableCell>
                  <TableCell align="right">{row.speed}</TableCell>
                  <TableCell align="right">{row.battery}%</TableCell>
                  <TableCell align="right">
                    <Chip 
                      label={row.status.toUpperCase()} 
                      color={getStatusColor(row.status)}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      ) : (
        <Box sx={{ textAlign: 'center', py: 2, flexGrow: 1 }}>
          <Typography variant="body2" color="text.secondary">
            {isFlying ? 'Recording flight data...' : 'No flight data available'}
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

export default DroneTelemetry;
