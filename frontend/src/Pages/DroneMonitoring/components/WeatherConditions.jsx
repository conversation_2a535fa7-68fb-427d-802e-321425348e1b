import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Divider,
  Chip
} from '@mui/material';
import {
  WbSunny as SunnyIcon,
  Air as WindIcon,
  Opacity as HumidityIcon,
  Thermostat as TempIcon,
  Visibility as VisibilityIcon,
  WbTwilight as SunsetIcon
} from '@mui/icons-material';

const WeatherConditions = () => {
  // Mock weather data
  const weatherData = {
    temperature: 28.5,
    humidity: 65,
    windSpeed: 3.2,
    windDirection: 'NE',
    visibility: 8.5,
    pressure: 1012,
    sunrise: '06:15',
    sunset: '18:45',
    condition: 'Clear',
    icon: <SunnyIcon />
  };
  
  // Get weather condition color
  const getWeatherColor = (condition) => {
    switch (condition.toLowerCase()) {
      case 'clear':
      case 'sunny':
        return '#FFB300'; // Amber
      case 'partly cloudy':
        return '#78909C'; // Blue Grey
      case 'cloudy':
        return '#607D8B'; // Blue Grey Dark
      case 'rain':
      case 'showers':
        return '#0288D1'; // Light Blue
      case 'thunderstorm':
        return '#0D47A1'; // Dark Blue
      default:
        return '#78909C'; // Blue Grey
    }
  };
  
  // Get flight condition status
  const getFlightConditionStatus = () => {
    // Check if weather conditions are suitable for flying
    if (weatherData.windSpeed > 8) return 'poor';
    if (weatherData.visibility < 5) return 'moderate';
    if (weatherData.condition.toLowerCase().includes('rain') || 
        weatherData.condition.toLowerCase().includes('storm')) return 'poor';
    
    return 'good';
  };
  
  const flightCondition = getFlightConditionStatus();
  
  // Get flight condition color
  const getFlightConditionColor = (condition) => {
    switch (condition) {
      case 'good':
        return 'success';
      case 'moderate':
        return 'warning';
      case 'poor':
        return 'error';
      default:
        return 'default';
    }
  };
  
  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Weather Conditions</Typography>
        <Chip 
          label={`Flight Conditions: ${flightCondition.toUpperCase()}`}
          color={getFlightConditionColor(flightCondition)}
          size="small"
        />
      </Box>
      
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        mb: 2,
        p: 1,
        bgcolor: 'rgba(0, 0, 0, 0.03)',
        borderRadius: 1
      }}>
        <Box sx={{ 
          color: getWeatherColor(weatherData.condition),
          mr: 2,
          fontSize: '2.5rem'
        }}>
          {weatherData.icon}
        </Box>
        <Box>
          <Typography variant="h4" component="span">
            {weatherData.temperature}°C
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {weatherData.condition}
          </Typography>
        </Box>
      </Box>
      
      <Grid container spacing={2}>
        <Grid item xs={6}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <WindIcon color="primary" sx={{ mr: 1, fontSize: '1.2rem' }} />
            <Typography variant="body2" color="text.secondary">
              Wind
            </Typography>
          </Box>
          <Typography variant="body1">
            {weatherData.windSpeed} m/s {weatherData.windDirection}
          </Typography>
        </Grid>
        
        <Grid item xs={6}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <HumidityIcon color="primary" sx={{ mr: 1, fontSize: '1.2rem' }} />
            <Typography variant="body2" color="text.secondary">
              Humidity
            </Typography>
          </Box>
          <Typography variant="body1">
            {weatherData.humidity}%
          </Typography>
        </Grid>
        
        <Grid item xs={6}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <VisibilityIcon color="primary" sx={{ mr: 1, fontSize: '1.2rem' }} />
            <Typography variant="body2" color="text.secondary">
              Visibility
            </Typography>
          </Box>
          <Typography variant="body1">
            {weatherData.visibility} km
          </Typography>
        </Grid>
        
        <Grid item xs={6}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SunsetIcon color="primary" sx={{ mr: 1, fontSize: '1.2rem' }} />
            <Typography variant="body2" color="text.secondary">
              Sunset
            </Typography>
          </Box>
          <Typography variant="body1">
            {weatherData.sunset}
          </Typography>
        </Grid>
      </Grid>
      
      <Divider sx={{ my: 2 }} />
      
      <Typography variant="body2" color="text.secondary">
        Flight Recommendation: {flightCondition === 'good' 
          ? 'Weather conditions are optimal for drone operations.' 
          : flightCondition === 'moderate'
            ? 'Exercise caution due to moderate weather conditions.'
            : 'Not recommended to fly due to poor weather conditions.'
        }
      </Typography>
    </Paper>
  );
};

export default WeatherConditions;
