import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Slider,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  FlightTakeoff as TakeoffIcon,
  FlightLand as LandIcon,
  Pause as PauseIcon,
  PlayArrow as PlayIcon,
  ArrowUpward as UpIcon,
  ArrowDownward as DownIcon,
  ArrowForward as ForwardIcon,
  ArrowBack as BackIcon,
  RotateLeft as RotateLeftIcon,
  RotateRight as RotateRightIcon,
  Speed as SpeedIcon,
  Home as HomeIcon
} from '@mui/icons-material';

const DroneControls = ({ 
  isFlying, 
  onTakeoff, 
  onLand, 
  onPauseResume, 
  status,
  loading
}) => {
  const [altitude, setAltitude] = React.useState(10);
  const [speed, setSpeed] = React.useState(5);
  const [flightMode, setFlightMode] = React.useState('manual');
  
  const handleAltitudeChange = (event, newValue) => {
    setAltitude(newValue);
  };
  
  const handleSpeedChange = (event, newValue) => {
    setSpeed(newValue);
  };
  
  const handleFlightModeChange = (event) => {
    setFlightMode(event.target.value);
  };
  
  return (
    <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column' }}>
      <Typography variant="h6" gutterBottom>
        Drone Controls
      </Typography>
      
      {/* Main flight controls */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Button
          variant="contained"
          color="success"
          startIcon={<TakeoffIcon />}
          onClick={onTakeoff}
          disabled={isFlying || loading}
        >
          Take Off
        </Button>
        
        <Button
          variant="contained"
          color={status === 'paused' ? 'success' : 'warning'}
          startIcon={status === 'paused' ? <PlayIcon /> : <PauseIcon />}
          onClick={onPauseResume}
          disabled={!isFlying || loading}
        >
          {status === 'paused' ? 'Resume' : 'Pause'}
        </Button>
        
        <Button
          variant="contained"
          color="error"
          startIcon={<LandIcon />}
          onClick={onLand}
          disabled={!isFlying || loading}
        >
          Land
        </Button>
      </Box>
      
      {/* Flight mode selector */}
      <FormControl fullWidth sx={{ mb: 2 }}>
        <InputLabel id="flight-mode-label">Flight Mode</InputLabel>
        <Select
          labelId="flight-mode-label"
          id="flight-mode"
          value={flightMode}
          label="Flight Mode"
          onChange={handleFlightModeChange}
          disabled={!isFlying}
        >
          <MenuItem value="manual">Manual Control</MenuItem>
          <MenuItem value="waypoint">Waypoint Navigation</MenuItem>
          <MenuItem value="follow">Follow Me</MenuItem>
          <MenuItem value="orbit">Orbit Point</MenuItem>
        </Select>
      </FormControl>
      
      {/* Altitude and speed controls */}
      <Grid container spacing={2}>
        <Grid item xs={6}>
          <Typography id="altitude-slider" gutterBottom>
            Altitude (m)
          </Typography>
          <Slider
            value={altitude}
            onChange={handleAltitudeChange}
            aria-labelledby="altitude-slider"
            valueLabelDisplay="auto"
            step={1}
            marks
            min={2}
            max={30}
            disabled={!isFlying}
          />
        </Grid>
        <Grid item xs={6}>
          <Typography id="speed-slider" gutterBottom>
            Speed (m/s)
          </Typography>
          <Slider
            value={speed}
            onChange={handleSpeedChange}
            aria-labelledby="speed-slider"
            valueLabelDisplay="auto"
            step={1}
            marks
            min={1}
            max={10}
            disabled={!isFlying}
          />
        </Grid>
      </Grid>
      
      {/* Directional controls */}
      {flightMode === 'manual' && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Directional Controls
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <IconButton disabled={!isFlying} color="primary">
              <UpIcon />
            </IconButton>
            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
              <IconButton disabled={!isFlying} color="primary">
                <RotateLeftIcon />
              </IconButton>
              <IconButton disabled={!isFlying} color="primary" sx={{ mx: 2 }}>
                <ForwardIcon />
              </IconButton>
              <IconButton disabled={!isFlying} color="primary">
                <RotateRightIcon />
              </IconButton>
            </Box>
            <IconButton disabled={!isFlying} color="primary">
              <BackIcon />
            </IconButton>
          </Box>
        </Box>
      )}
      
      {/* Return to home button */}
      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
        <Tooltip title="Return to Home">
          <span>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<HomeIcon />}
              disabled={!isFlying}
            >
              Return to Home
            </Button>
          </span>
        </Tooltip>
      </Box>
    </Paper>
  );
};

export default DroneControls;
