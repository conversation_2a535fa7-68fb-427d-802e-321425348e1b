import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActionArea,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Route as RouteIcon,
  Cloud as CloudIcon,
  Map as MapIcon,
  Spa as CropIcon,
  BugReport as PestIcon,
  Grass as GrowthIcon,
  WaterDrop as IrrigationIcon,
  Timeline as YieldIcon,
  Dashboard as DashboardIcon,
  Check as CheckIcon,
  ArrowForward as ArrowIcon
} from '@mui/icons-material';

const AdvancedFeatures = () => {
  const [selectedFeature, setSelectedFeature] = useState(null);

  // Advanced drone features
  const features = [
    {
      id: 1,
      title: 'Autonomous Path Planning',
      icon: <RouteIcon fontSize="large" />,
      color: '#1976d2',
      image: 'https://images.unsplash.com/photo-1508614589041-895b88991e3e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
      description: 'Create and execute autonomous flight paths for efficient field coverage.',
      benefits: [
        'Automated flight planning based on field boundaries',
        'Optimized coverage patterns for different missions',
        'Obstacle avoidance and safety features',
        'Save and reuse flight plans'
      ],
      status: 'available'
    },
    {
      id: 2,
      title: 'Remote Data Syncing',
      icon: <CloudIcon fontSize="large" />,
      color: '#0288d1',
      image: 'https://images.unsplash.com/photo-1544197150-b99a580bb7a8?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
      description: 'Automatically sync drone data with cloud storage for analysis and sharing.',
      benefits: [
        'Real-time data transmission to cloud storage',
        'Secure and encrypted data transfer',
        'Automatic organization and categorization',
        'Access data from anywhere, anytime'
      ],
      status: 'available'
    },
    {
      id: 3,
      title: 'Field Mapping Module',
      icon: <MapIcon fontSize="large" />,
      color: '#388e3c',
      image: 'https://images.unsplash.com/photo-1506012787146-f92b2d7d6d96?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1169&q=80',
      description: 'Create orthomosaic maps, elevation maps, and field boundaries.',
      benefits: [
        'High-resolution orthomosaic maps',
        'Detailed elevation models and contour maps',
        'Precise field boundary mapping',
        'Export in various formats (GeoTIFF, KML, etc.)'
      ],
      status: 'available'
    },
    {
      id: 4,
      title: 'Crop Health Monitoring',
      icon: <CropIcon fontSize="large" />,
      color: '#43a047',
      image: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1332&q=80',
      description: 'Uses NDVI and other indices to monitor plant stress and disease.',
      benefits: [
        'Multiple vegetation indices (NDVI, NDRE, etc.)',
        'Early stress detection before visible symptoms',
        'Historical comparison of crop health',
        'Customizable alert thresholds'
      ],
      status: 'available'
    },
    {
      id: 5,
      title: 'Pest & Weed Detection',
      icon: <PestIcon fontSize="large" />,
      color: '#e53935',
      image: 'https://images.unsplash.com/photo-1530836369250-ef72a3f5cda8?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
      description: 'Image classification and detection using deep learning models.',
      benefits: [
        'Identify and locate pest infestations',
        'Detect weed patches and invasive species',
        'Generate treatment recommendation maps',
        'Reduce chemical usage through targeted application'
      ],
      status: 'available'
    },
    {
      id: 6,
      title: 'Growth Stage Monitoring',
      icon: <GrowthIcon fontSize="large" />,
      color: '#8bc34a',
      image: 'https://images.unsplash.com/photo-1492496913980-501348b61469?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1374&q=80',
      description: 'Analyze plant size, height, and leaf density over time.',
      benefits: [
        'Track crop development through growth stages',
        'Measure plant height and canopy coverage',
        'Compare growth rates across fields',
        'Identify areas of stunted growth'
      ],
      status: 'coming-soon'
    },
    {
      id: 7,
      title: 'Irrigation Monitoring',
      icon: <IrrigationIcon fontSize="large" />,
      color: '#03a9f4',
      image: 'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      description: 'Integrates thermal + multispectral data to identify irrigation issues.',
      benefits: [
        'Detect over- and under-irrigated areas',
        'Monitor soil moisture levels',
        'Identify irrigation system failures',
        'Optimize water usage and reduce costs'
      ],
      status: 'coming-soon'
    },
    {
      id: 8,
      title: 'Yield Prediction',
      icon: <YieldIcon fontSize="large" />,
      color: '#ffa000',
      image: 'https://images.unsplash.com/photo-1625246333195-78d9c38ad449?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
      description: 'Uses historical + real-time imagery to forecast crop yields.',
      benefits: [
        'Early yield forecasting for planning',
        'Field-level and zone-level predictions',
        'Integration with weather and soil data',
        'Identify high and low-performing areas'
      ],
      status: 'coming-soon'
    }
  ];

  // Handle feature click
  const handleFeatureClick = (feature) => {
    setSelectedFeature(feature);
  };

  // Handle dialog close
  const handleClose = () => {
    setSelectedFeature(null);
  };

  // Get status chip color
  const getStatusColor = (status) => {
    switch (status) {
      case 'available':
        return 'success';
      case 'coming-soon':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Get status label
  const getStatusLabel = (status) => {
    switch (status) {
      case 'available':
        return 'Available';
      case 'coming-soon':
        return 'Coming Soon';
      default:
        return 'Unavailable';
    }
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" component="h2" sx={{ fontSize: { xs: '1.25rem', md: '1.5rem' }, fontWeight: 'bold', m: 0 }}>
          Advanced Drone Capabilities
        </Typography>
        <Chip
          label="8 Modules Available"
          color="primary"
          variant="outlined"
          size="small"
        />
      </Box>

      <Grid container spacing={2}>
        {features.map((feature) => (
          <Grid item xs={12} sm={6} md={3} key={feature.id}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: 6
                }
              }}
            >
              <CardActionArea onClick={() => handleFeatureClick(feature)}>
                <Box sx={{ position: 'relative' }}>
                  <CardMedia
                    component="img"
                    height="160"
                    image={feature.image}
                    alt={feature.title}
                    sx={{ objectFit: 'cover' }}
                  />
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      bgcolor: 'rgba(0, 0, 0, 0.3)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <Box
                      sx={{
                        bgcolor: 'rgba(255, 255, 255, 0.9)',
                        borderRadius: '50%',
                        p: 1,
                        color: feature.color
                      }}
                    >
                      {feature.icon}
                    </Box>
                  </Box>
                </Box>
                <CardContent sx={{ flexGrow: 1, p: { xs: 1.5, md: 2 } }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Typography gutterBottom variant="subtitle1" component="div" sx={{ fontWeight: 'bold', fontSize: { xs: '0.875rem', md: '1rem' }, mb: 0.5 }}>
                      {feature.title}
                    </Typography>
                    <Chip
                      label={getStatusLabel(feature.status)}
                      color={getStatusColor(feature.status)}
                      size="small"
                      sx={{ fontSize: '0.7rem', height: 20 }}
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.75rem', md: '0.875rem' }, lineHeight: 1.4 }}>
                    {feature.description}
                  </Typography>
                </CardContent>
              </CardActionArea>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Feature Detail Dialog */}
      <Dialog
        open={Boolean(selectedFeature)}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
      >
        {selectedFeature && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{
                  mr: 2,
                  bgcolor: selectedFeature.color,
                  color: 'white',
                  p: 1,
                  borderRadius: '50%'
                }}>
                  {selectedFeature.icon}
                </Box>
                <Typography variant="h6">{selectedFeature.title}</Typography>
                <Box sx={{ flexGrow: 1 }} />
                <Chip
                  label={getStatusLabel(selectedFeature.status)}
                  color={getStatusColor(selectedFeature.status)}
                />
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box
                    component="img"
                    src={selectedFeature.image}
                    alt={selectedFeature.title}
                    sx={{
                      width: '100%',
                      height: 'auto',
                      borderRadius: 1,
                      mb: 2
                    }}
                  />
                  <Typography variant="body1" paragraph>
                    {selectedFeature.description}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom>
                    Key Benefits
                  </Typography>
                  <List>
                    {selectedFeature.benefits.map((benefit, index) => (
                      <React.Fragment key={index}>
                        <ListItem>
                          <ListItemIcon>
                            <CheckIcon color="success" />
                          </ListItemIcon>
                          <ListItemText primary={benefit} />
                        </ListItem>
                        {index < selectedFeature.benefits.length - 1 && <Divider variant="inset" component="li" />}
                      </React.Fragment>
                    ))}
                  </List>

                  <Box sx={{ mt: 3 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      How to Use
                    </Typography>
                    <Typography variant="body2" paragraph>
                      {selectedFeature.status === 'available'
                        ? `Access the ${selectedFeature.title} module through the drone monitoring interface. Configure your settings and start collecting data.`
                        : `The ${selectedFeature.title} module will be available in an upcoming update. Stay tuned for more information.`
                      }
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleClose}>Close</Button>
              {selectedFeature.status === 'available' && (
                <Button
                  variant="contained"
                  color="primary"
                  endIcon={<ArrowIcon />}
                >
                  Launch Module
                </Button>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default AdvancedFeatures;
