import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Divider,
  Chip,
  CircularProgress,
  LinearProgress,
  Alert
} from '@mui/material';
import {
  Map as MapIcon,
  FlightTakeoff as TakeoffIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Save as SaveIcon,
  Folder as FolderIcon,
  Route as RouteIcon,
  Healing as SprayIcon,
  CameraAlt as CameraIcon,
  Videocam as VideocamIcon
} from '@mui/icons-material';

// Predefined missions
const predefinedMissions = [
  {
    id: 'mission1',
    name: 'Full Farm Survey',
    type: 'survey',
    description: 'Complete survey of the entire farm area',
    duration: '15 min',
    waypoints: [
      { lat: 12.8600, lng: 77.5385 },
      { lat: 12.8600, lng: 77.5405 },
      { lat: 12.8585, lng: 77.5405 },
      { lat: 12.8585, lng: 77.5385 }
    ],
    actions: ['photo', 'video']
  },
  {
    id: 'mission2',
    name: 'Crop Health Analysis',
    type: 'analysis',
    description: 'Analyze crop health in the eastern section',
    duration: '8 min',
    waypoints: [
      { lat: 12.8595, lng: 77.5395 },
      { lat: 12.8595, lng: 77.5405 },
      { lat: 12.8590, lng: 77.5405 },
      { lat: 12.8590, lng: 77.5395 }
    ],
    actions: ['photo', 'analysis']
  },
  {
    id: 'mission3',
    name: 'Targeted Spraying',
    type: 'spray',
    description: 'Apply organic pesticide to affected areas',
    duration: '12 min',
    waypoints: [
      { lat: 12.8592, lng: 77.5390 },
      { lat: 12.8592, lng: 77.5395 },
      { lat: 12.8588, lng: 77.5395 },
      { lat: 12.8588, lng: 77.5390 }
    ],
    actions: ['spray']
  }
];

const MissionPlanner = ({ isFlying, activeMission, onStartMission, onCancelMission }) => {
  const [selectedMission, setSelectedMission] = useState(null);
  const [customMission, setCustomMission] = useState({
    name: '',
    type: 'survey',
    description: '',
    waypoints: []
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showCustomForm, setShowCustomForm] = useState(false);

  // Handle mission selection
  const handleMissionSelect = (mission) => {
    setSelectedMission(mission);
  };

  // Handle start mission
  const handleStartMission = () => {
    if (!isFlying) {
      setError('Drone must be flying to start a mission');
      setTimeout(() => setError(null), 3000);
      return;
    }

    if (!selectedMission) {
      setError('Please select a mission first');
      setTimeout(() => setError(null), 3000);
      return;
    }

    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      onStartMission(selectedMission);
      setLoading(false);
    }, 1500);
  };

  // Handle cancel mission
  const handleCancelMission = () => {
    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      onCancelMission();
      setLoading(false);
    }, 1000);
  };

  // Handle custom mission form change
  const handleCustomMissionChange = (e) => {
    const { name, value } = e.target;
    setCustomMission(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle toggle custom form
  const handleToggleCustomForm = () => {
    setShowCustomForm(!showCustomForm);
  };

  // Get mission type icon
  const getMissionTypeIcon = (type) => {
    switch (type) {
      case 'survey':
        return <MapIcon />;
      case 'analysis':
        return <CameraIcon />;
      case 'spray':
        return <SprayIcon />;
      default:
        return <RouteIcon />;
    }
  };

  // Get mission type color
  const getMissionTypeColor = (type) => {
    switch (type) {
      case 'survey':
        return 'primary';
      case 'analysis':
        return 'info';
      case 'spray':
        return 'success';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Mission Planner
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {activeMission ? (
        <Box>
          <Alert severity="info" sx={{ mb: 2 }}>
            Currently executing mission: <strong>{activeMission.name}</strong>
          </Alert>

          <Paper sx={{ p: 2, mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {getMissionTypeIcon(activeMission.type)}
                <Typography variant="h6" sx={{ ml: 1 }}>
                  {activeMission.name}
                </Typography>
              </Box>
              <Chip
                label={activeMission.type.toUpperCase()}
                color={getMissionTypeColor(activeMission.type)}
              />
            </Box>

            <Typography variant="body2" paragraph>
              {activeMission.description}
            </Typography>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="body2">
                Estimated duration: {activeMission.duration}
              </Typography>
              <Typography variant="body2">
                Waypoints: {activeMission.waypoints.length}
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
              {activeMission.actions.map((action) => (
                <Chip
                  key={action}
                  label={action.toUpperCase()}
                  size="small"
                  icon={
                    action === 'photo' ? <CameraIcon /> :
                    action === 'video' ? <VideocamIcon /> :
                    action === 'spray' ? <SprayIcon /> :
                    action === 'analysis' ? <CameraIcon /> : null
                  }
                />
              ))}
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
              <Button
                variant="contained"
                color="error"
                startIcon={<StopIcon />}
                onClick={handleCancelMission}
                disabled={loading}
              >
                {loading ? <CircularProgress size={24} color="inherit" /> : 'Cancel Mission'}
              </Button>
            </Box>
          </Paper>

          <Typography variant="subtitle2" gutterBottom>
            Mission Progress
          </Typography>

          <LinearProgressWithLabel value={45} />

          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="body2" color="text.secondary">
              Waypoint: 2/4
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Estimated time remaining: 8 min
            </Typography>
          </Box>
        </Box>
      ) : (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Typography variant="subtitle1" gutterBottom>
                Predefined Missions
              </Typography>

              <List>
                {predefinedMissions.map((mission) => (
                  <ListItem
                    key={mission.id}
                    button
                    onClick={() => handleMissionSelect(mission)}
                    selected={selectedMission?.id === mission.id}
                    sx={{
                      mb: 1,
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: 'divider'
                    }}
                  >
                    <ListItemIcon>
                      {getMissionTypeIcon(mission.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={mission.name}
                      secondary={`${mission.description} • ${mission.duration}`}
                    />
                    <Chip
                      label={mission.type.toUpperCase()}
                      color={getMissionTypeColor(mission.type)}
                      size="small"
                    />
                  </ListItem>
                ))}
              </List>

              <Button
                variant="outlined"
                startIcon={showCustomForm ? <DeleteIcon /> : <AddIcon />}
                onClick={handleToggleCustomForm}
                fullWidth
                sx={{ mt: 2 }}
              >
                {showCustomForm ? 'Cancel Custom Mission' : 'Create Custom Mission'}
              </Button>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2, height: '100%' }}>
              {showCustomForm ? (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>
                    Create Custom Mission
                  </Typography>

                  <TextField
                    label="Mission Name"
                    name="name"
                    value={customMission.name}
                    onChange={handleCustomMissionChange}
                    fullWidth
                    margin="normal"
                  />

                  <FormControl fullWidth margin="normal">
                    <InputLabel id="mission-type-label">Mission Type</InputLabel>
                    <Select
                      labelId="mission-type-label"
                      name="type"
                      value={customMission.type}
                      label="Mission Type"
                      onChange={handleCustomMissionChange}
                    >
                      <MenuItem value="survey">Survey</MenuItem>
                      <MenuItem value="analysis">Analysis</MenuItem>
                      <MenuItem value="spray">Spray</MenuItem>
                    </Select>
                  </FormControl>

                  <TextField
                    label="Description"
                    name="description"
                    value={customMission.description}
                    onChange={handleCustomMissionChange}
                    fullWidth
                    margin="normal"
                    multiline
                    rows={2}
                  />

                  <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
                    Waypoints
                  </Typography>

                  <Alert severity="info" sx={{ mb: 2 }}>
                    Use the map to add waypoints for your custom mission
                  </Alert>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                    <Button
                      variant="outlined"
                      startIcon={<DeleteIcon />}
                    >
                      Clear
                    </Button>

                    <Button
                      variant="contained"
                      startIcon={<SaveIcon />}
                    >
                      Save Mission
                    </Button>
                  </Box>
                </Box>
              ) : selectedMission ? (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {getMissionTypeIcon(selectedMission.type)}
                      <Typography variant="h6" sx={{ ml: 1 }}>
                        {selectedMission.name}
                      </Typography>
                    </Box>
                    <Chip
                      label={selectedMission.type.toUpperCase()}
                      color={getMissionTypeColor(selectedMission.type)}
                    />
                  </Box>

                  <Typography variant="body1" paragraph>
                    {selectedMission.description}
                  </Typography>

                  <Divider sx={{ my: 2 }} />

                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">
                        Duration
                      </Typography>
                      <Typography variant="body1">
                        {selectedMission.duration}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2">
                        Waypoints
                      </Typography>
                      <Typography variant="body1">
                        {selectedMission.waypoints.length}
                      </Typography>
                    </Grid>
                  </Grid>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="subtitle2" gutterBottom>
                    Actions
                  </Typography>

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
                    {selectedMission.actions.map((action) => (
                      <Chip
                        key={action}
                        label={action.toUpperCase()}
                        icon={
                          action === 'photo' ? <CameraIcon /> :
                          action === 'video' ? <VideocamIcon /> :
                          action === 'spray' ? <SprayIcon /> :
                          action === 'analysis' ? <CameraIcon /> : null
                        }
                      />
                    ))}
                  </Box>

                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<PlayIcon />}
                    onClick={handleStartMission}
                    disabled={loading || !isFlying}
                    fullWidth
                  >
                    {loading ? <CircularProgress size={24} color="inherit" /> : 'Start Mission'}
                  </Button>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%', py: 4 }}>
                  <RouteIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="body1" color="text.secondary">
                    Select a mission to view details
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Mission details and controls will be shown here
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

// Linear progress with label component
function LinearProgressWithLabel(props) {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Box sx={{ width: '100%', mr: 1 }}>
        <LinearProgress variant="determinate" {...props} />
      </Box>
      <Box sx={{ minWidth: 35 }}>
        <Typography variant="body2" color="text.secondary">{`${Math.round(
          props.value,
        )}%`}</Typography>
      </Box>
    </Box>
  );
}

export default MissionPlanner;
