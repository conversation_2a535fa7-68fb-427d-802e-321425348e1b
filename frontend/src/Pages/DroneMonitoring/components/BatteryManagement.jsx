import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  LinearProgress,
  Button,
  Divider,
  Chip
} from '@mui/material';
import {
  BatteryChargingFull as BatteryIcon,
  FlightTakeoff as FlightIcon,
  AccessTime as TimeIcon,
  BatteryAlert as BatteryAlertIcon,
  BatteryStd as BatteryStdIcon,
  SwapHoriz as SwapIcon
} from '@mui/icons-material';

const BatteryManagement = ({ droneStatus }) => {
  // Mock battery data
  const batteryData = {
    current: droneStatus.battery,
    voltage: 11.8,
    temperature: 32,
    cycles: 48,
    health: 92,
    estimatedFlightTime: Math.floor(droneStatus.battery / 5), // minutes
    backupBatteries: [
      { id: 1, charge: 100, health: 95 },
      { id: 2, charge: 85, health: 90 }
    ]
  };
  
  // Get battery color
  const getBatteryColor = (percentage) => {
    if (percentage > 50) return 'success';
    if (percentage > 20) return 'warning';
    return 'error';
  };
  
  // Get health color
  const getHealthColor = (percentage) => {
    if (percentage > 80) return 'success';
    if (percentage > 60) return 'warning';
    return 'error';
  };
  
  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Typography variant="h6" gutterBottom>
        Battery Management
      </Typography>
      
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <BatteryIcon color={getBatteryColor(batteryData.current)} sx={{ mr: 1 }} />
            <Typography variant="body2">
              Current Battery
            </Typography>
          </Box>
          <Typography variant="h6" color={getBatteryColor(batteryData.current)}>
            {batteryData.current}%
          </Typography>
        </Box>
        <LinearProgress 
          variant="determinate" 
          value={batteryData.current} 
          color={getBatteryColor(batteryData.current)}
          sx={{ height: 10, borderRadius: 5 }}
        />
      </Box>
      
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={6}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <TimeIcon color="primary" sx={{ mr: 1, fontSize: '1.2rem' }} />
            <Typography variant="body2" color="text.secondary">
              Est. Flight Time
            </Typography>
          </Box>
          <Typography variant="body1">
            {batteryData.estimatedFlightTime} min
          </Typography>
        </Grid>
        
        <Grid item xs={6}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <BatteryStdIcon color="primary" sx={{ mr: 1, fontSize: '1.2rem' }} />
            <Typography variant="body2" color="text.secondary">
              Battery Health
            </Typography>
          </Box>
          <Typography variant="body1">
            {batteryData.health}%
          </Typography>
        </Grid>
      </Grid>
      
      <Divider sx={{ my: 2 }} />
      
      <Typography variant="subtitle2" gutterBottom>
        Backup Batteries
      </Typography>
      
      {batteryData.backupBatteries.map((battery) => (
        <Box key={battery.id} sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
            <Typography variant="body2">
              Battery #{battery.id}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Chip 
                label={`${battery.charge}%`} 
                color={getBatteryColor(battery.charge)}
                size="small"
                sx={{ mr: 1 }}
              />
              <Chip 
                label={`Health: ${battery.health}%`} 
                color={getHealthColor(battery.health)}
                size="small"
              />
            </Box>
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={battery.charge} 
            color={getBatteryColor(battery.charge)}
            sx={{ height: 6, borderRadius: 5 }}
          />
        </Box>
      ))}
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
        <Button
          variant="outlined"
          startIcon={<SwapIcon />}
          size="small"
        >
          Swap Battery
        </Button>
        
        <Button
          variant="contained"
          color={batteryData.current < 20 ? 'error' : 'primary'}
          startIcon={<BatteryAlertIcon />}
          size="small"
          disabled={batteryData.current > 20}
        >
          Low Battery RTH
        </Button>
      </Box>
    </Paper>
  );
};

export default BatteryManagement;
