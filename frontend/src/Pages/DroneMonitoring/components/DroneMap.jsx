import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography, Paper, Chip, CircularProgress } from '@mui/material';
import {
  SmartToy as DroneIcon,
  Healing as SprayIcon,
  BugReport as PestIcon,
  LocalHospital as DiseaseIcon,
  LocationOn as LocationIcon
} from '@mui/icons-material';

const DroneMap = ({
  dronePosition,
  isFlying,
  isSpraying,
  isAnalyzing,
  detectedDiseases,
  activeMission
}) => {
  const [selectedDisease, setSelectedDisease] = useState(null);
  const [sprayPath, setSprayPath] = useState([]);
  const [loading, setLoading] = useState(false);
  const canvasRef = useRef(null);

  // Farm boundary coordinates
  const farmBoundary = [
    { lat: 12.8600, lng: 77.5385 },
    { lat: 12.8600, lng: 77.5405 },
    { lat: 12.8585, lng: 77.5405 },
    { lat: 12.8585, lng: 77.5385 },
  ];

  // Map dimensions
  const mapWidth = 400;
  const mapHeight = 400;

  // Convert lat/lng to x/y coordinates on the canvas
  const latLngToPoint = (lat, lng) => {
    // Define map boundaries
    const minLat = 12.8580;
    const maxLat = 12.8605;
    const minLng = 77.5380;
    const maxLng = 77.5410;

    // Calculate x and y positions
    const x = ((lng - minLng) / (maxLng - minLng)) * mapWidth;
    const y = mapHeight - ((lat - minLat) / (maxLat - minLat)) * mapHeight;

    return { x, y };
  };

  // Update spray path when drone is spraying
  useEffect(() => {
    if (isSpraying) {
      setSprayPath(prev => {
        // Keep only the last 20 positions to avoid cluttering the map
        const newPath = [...prev, { ...dronePosition }];
        if (newPath.length > 20) {
          return newPath.slice(newPath.length - 20);
        }
        return newPath;
      });
    }
  }, [dronePosition, isSpraying]);

  // Draw the map and drone position
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, mapWidth, mapHeight);

    // Draw background
    ctx.fillStyle = '#e8f5e9';
    ctx.fillRect(0, 0, mapWidth, mapHeight);

    // Draw farm boundary
    ctx.beginPath();
    const firstPoint = latLngToPoint(farmBoundary[0].lat, farmBoundary[0].lng);
    ctx.moveTo(firstPoint.x, firstPoint.y);

    for (let i = 1; i < farmBoundary.length; i++) {
      const point = latLngToPoint(farmBoundary[i].lat, farmBoundary[i].lng);
      ctx.lineTo(point.x, point.y);
    }

    ctx.closePath();
    ctx.fillStyle = 'rgba(0, 255, 0, 0.2)';
    ctx.fill();
    ctx.strokeStyle = '#00FF00';
    ctx.lineWidth = 2;
    ctx.stroke();

    // Draw spray path
    if (sprayPath.length > 1) {
      ctx.beginPath();
      const firstSprayPoint = latLngToPoint(sprayPath[0].lat, sprayPath[0].lng);
      ctx.moveTo(firstSprayPoint.x, firstSprayPoint.y);

      for (let i = 1; i < sprayPath.length; i++) {
        const point = latLngToPoint(sprayPath[i].lat, sprayPath[i].lng);
        ctx.lineTo(point.x, point.y);
      }

      ctx.strokeStyle = '#00FF00';
      ctx.lineWidth = 3;
      ctx.stroke();
    }

    // Draw disease markers
    detectedDiseases.forEach(disease => {
      const point = latLngToPoint(disease.location.lat, disease.location.lng);

      ctx.beginPath();
      ctx.arc(point.x, point.y, 5, 0, Math.PI * 2);
      ctx.fillStyle = disease.severity === 'High' ? '#f44336' :
                     disease.severity === 'Medium' ? '#ff9800' : '#4caf50';
      ctx.fill();
      ctx.strokeStyle = '#fff';
      ctx.lineWidth = 1;
      ctx.stroke();
    });

    // Draw drone position
    const dronePoint = latLngToPoint(dronePosition.lat, dronePosition.lng);

    // Draw spray or analysis radius
    if (isSpraying) {
      ctx.beginPath();
      ctx.arc(dronePoint.x, dronePoint.y, 15, 0, Math.PI * 2);
      ctx.fillStyle = 'rgba(0, 255, 0, 0.3)';
      ctx.fill();
    } else if (isAnalyzing) {
      ctx.beginPath();
      ctx.arc(dronePoint.x, dronePoint.y, 20, 0, Math.PI * 2);
      ctx.fillStyle = 'rgba(0, 0, 255, 0.2)';
      ctx.fill();
    }

    // Draw drone
    ctx.beginPath();
    ctx.arc(dronePoint.x, dronePoint.y, 8, 0, Math.PI * 2);
    ctx.fillStyle = '#2196f3';
    ctx.fill();
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 2;
    ctx.stroke();

  }, [dronePosition, farmBoundary, sprayPath, isSpraying, isAnalyzing, detectedDiseases]);

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <>
      <Box sx={{ position: 'relative', width: mapWidth, height: mapHeight, margin: '0 auto', border: '1px solid #ccc', borderRadius: 1, overflow: 'hidden' }}>
        <canvas
          ref={canvasRef}
          width={mapWidth}
          height={mapHeight}
          style={{ display: 'block' }}
        />

        {/* Disease info popup */}
        {selectedDisease && (
          <Paper
            elevation={3}
            sx={{
              position: 'absolute',
              bottom: 10,
              left: 10,
              p: 1,
              maxWidth: 200,
              zIndex: 1000
            }}
          >
            <Typography variant="subtitle2" gutterBottom>
              {selectedDisease.name}
            </Typography>
            <Chip
              label={`Severity: ${selectedDisease.severity}`}
              color={
                selectedDisease.severity === 'High' ? 'error' :
                selectedDisease.severity === 'Medium' ? 'warning' : 'success'
              }
              size="small"
              sx={{ mb: 1 }}
            />
            <Typography variant="caption" display="block">
              Detected at: {formatDate(selectedDisease.timestamp)}
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
              <Chip
                label="Close"
                size="small"
                onClick={() => setSelectedDisease(null)}
                sx={{ cursor: 'pointer' }}
              />
            </Box>
          </Paper>
        )}

        {/* Legend */}
        <Paper
          elevation={2}
          sx={{
            position: 'absolute',
            top: 10,
            right: 10,
            p: 1,
            backgroundColor: 'rgba(255, 255, 255, 0.8)'
          }}
        >
          <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
            <Box sx={{ width: 10, height: 10, borderRadius: '50%', bgcolor: '#2196f3', mr: 1 }} />
            Drone
          </Typography>
          {isSpraying && (
            <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <Box sx={{ width: 10, height: 10, borderRadius: '50%', bgcolor: 'rgba(0, 255, 0, 0.3)', mr: 1 }} />
              Spray Area
            </Typography>
          )}
          {isAnalyzing && (
            <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <Box sx={{ width: 10, height: 10, borderRadius: '50%', bgcolor: 'rgba(0, 0, 255, 0.2)', mr: 1 }} />
              Analysis Area
            </Typography>
          )}
          {detectedDiseases.length > 0 && (
            <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{ width: 10, height: 10, borderRadius: '50%', bgcolor: '#f44336', mr: 1 }} />
              Disease Detected
            </Typography>
          )}
        </Paper>

        {/* Click handler for disease selection */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            cursor: 'pointer',
            zIndex: 10,
            backgroundColor: 'transparent'
          }}
          onClick={(e) => {
            // Get click coordinates relative to canvas
            const rect = e.currentTarget.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Check if clicked on a disease marker
            for (const disease of detectedDiseases) {
              const point = latLngToPoint(disease.location.lat, disease.location.lng);
              const dx = point.x - x;
              const dy = point.y - y;
              const distance = Math.sqrt(dx * dx + dy * dy);

              if (distance <= 10) {
                setSelectedDisease(disease);
                return;
              }
            }

            // If not clicked on a disease marker, clear selection
            setSelectedDisease(null);
          }}
        />
      </Box>

      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="body2">
          Latitude: {dronePosition.lat.toFixed(6)}
        </Typography>
        <Typography variant="body2">
          Longitude: {dronePosition.lng.toFixed(6)}
        </Typography>
        <Typography variant="body2">
          Status: {isFlying ? (isSpraying ? 'Spraying' : isAnalyzing ? 'Analyzing' : 'Flying') : 'Idle'}
        </Typography>
      </Box>
    </>
  );
};

export default DroneMap;
