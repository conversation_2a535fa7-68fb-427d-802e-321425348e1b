import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  LinearProgress,
  Divider,
  Chip
} from '@mui/material';
import {
  Battery90 as BatteryIcon,
  Speed as SpeedIcon,
  Height as HeightIcon,
  SignalCellular4Bar as SignalIcon,
  Thermostat as TempIcon,
  AccessTime as TimeIcon
} from '@mui/icons-material';

const DroneStatus = ({ status }) => {
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };
  
  // Get battery color
  const getBatteryColor = (battery) => {
    if (battery > 50) return 'success';
    if (battery > 20) return 'warning';
    return 'error';
  };
  
  // Get signal color
  const getSignalColor = (signal) => {
    if (signal > 80) return 'success';
    if (signal > 50) return 'warning';
    return 'error';
  };
  
  return (
    <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column' }}>
      <Typography variant="h6" gutterBottom>
        Drone Status
      </Typography>
      
      <Grid container spacing={2}>
        {/* Battery status */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <BatteryIcon color={getBatteryColor(status.battery)} sx={{ mr: 1 }} />
            <Typography variant="body2" sx={{ flexGrow: 1 }}>
              Battery
            </Typography>
            <Typography variant="body2" fontWeight="bold">
              {status.battery}%
            </Typography>
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={status.battery} 
            color={getBatteryColor(status.battery)}
            sx={{ height: 8, borderRadius: 5 }}
          />
        </Grid>
        
        <Grid item xs={12}>
          <Divider />
        </Grid>
        
        {/* Flight data */}
        <Grid item xs={6}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <HeightIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="body2">
              Altitude
            </Typography>
          </Box>
          <Typography variant="h6">
            {status.altitude.toFixed(1)} m
          </Typography>
        </Grid>
        
        <Grid item xs={6}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SpeedIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="body2">
              Speed
            </Typography>
          </Box>
          <Typography variant="h6">
            {status.speed.toFixed(1)} m/s
          </Typography>
        </Grid>
        
        <Grid item xs={12}>
          <Divider />
        </Grid>
        
        {/* Signal strength */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <SignalIcon color={getSignalColor(status.signalStrength)} sx={{ mr: 1 }} />
            <Typography variant="body2" sx={{ flexGrow: 1 }}>
              Signal Strength
            </Typography>
            <Typography variant="body2" fontWeight="bold">
              {status.signalStrength}%
            </Typography>
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={status.signalStrength} 
            color={getSignalColor(status.signalStrength)}
            sx={{ height: 8, borderRadius: 5 }}
          />
        </Grid>
        
        <Grid item xs={12}>
          <Divider />
        </Grid>
        
        {/* Temperature */}
        <Grid item xs={6}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <TempIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="body2">
              Temperature
            </Typography>
          </Box>
          <Typography variant="h6">
            {status.temperature.toFixed(1)} °C
          </Typography>
        </Grid>
        
        {/* Status */}
        <Grid item xs={6}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <TimeIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="body2">
              Status
            </Typography>
          </Box>
          <Chip 
            label={status.status.toUpperCase()} 
            color={
              status.status === 'idle' ? 'default' :
              status.status === 'flying' ? 'primary' :
              status.status === 'spraying' ? 'success' :
              status.status === 'analyzing' ? 'info' :
              status.status === 'paused' ? 'warning' : 'default'
            }
            size="small"
            sx={{ mt: 1 }}
          />
        </Grid>
      </Grid>
      
      <Box sx={{ mt: 2, textAlign: 'right' }}>
        <Typography variant="caption" color="text.secondary">
          Last updated: {formatDate(status.lastUpdated)}
        </Typography>
      </Box>
    </Paper>
  );
};

export default DroneStatus;
