import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Divider,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tabs,
  Tab,
  CircularProgress
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Trending<PERSON>lat as Trending<PERSON>latIcon,
  <PERSON><PERSON><PERSON> as BarChartIcon,
  <PERSON><PERSON><PERSON> as Pie<PERSON><PERSON>Icon,
  Timeline as TimelineIcon,
  Map as MapIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

// Tab Panel Component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      style={{ width: '100%' }}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: { xs: 1, md: 2 }, width: '100%' }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const DataDashboard = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [isDownloading, setIsDownloading] = useState(false);

  // Function to generate and download report
  const handleDownloadReport = () => {
    setIsDownloading(true);

    // Determine which data to include based on active tab
    let reportData;
    let fileName;

    if (activeTab === 0) {
      reportData = cropHealthData;
      fileName = 'crop_health_report.csv';
    } else if (activeTab === 1) {
      reportData = irrigationData;
      fileName = 'irrigation_report.csv';
    } else {
      reportData = recentScans;
      fileName = 'recent_scans_report.csv';
    }

    // Convert data to CSV format
    let csvContent = '';

    // Add headers
    if (activeTab === 0) {
      csvContent = 'Crop,Field,Health Index,Trend,Last Scan\n';
      // Add rows
      reportData.forEach(row => {
        csvContent += `${row.crop},${row.field},${row.health},${row.change},${row.lastScan}\n`;
      });
    } else if (activeTab === 1) {
      csvContent = 'Zone,Moisture Level,Status,Last Check\n';
      // Add rows
      reportData.forEach(row => {
        csvContent += `${row.zone},${row.moisture},${row.status},${row.lastCheck}\n`;
      });
    } else {
      csvContent = 'Scan Type,Field,Date,Status\n';
      // Add rows
      reportData.forEach(row => {
        csvContent += `${row.type},${row.field},${row.date},${row.status}\n`;
      });
    }

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);

    // Simulate a delay for download feedback
    setTimeout(() => {
      link.click();
      document.body.removeChild(link);
      setIsDownloading(false);
    }, 1000);
  };

  // Function to handle individual scan download
  const handleDownloadScan = (scan) => {
    // Create a simple text report for the scan
    const reportContent = `Scan Report\n\nType: ${scan.type}\nField: ${scan.field}\nDate: ${scan.date}\nStatus: ${scan.status}\n\nGenerated on: ${new Date().toLocaleString()}`;

    // Create a blob and download link
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `${scan.type.toLowerCase().replace(' ', '_')}_${scan.field.toLowerCase().replace(' ', '_')}.txt`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Mock data for dashboard
  const cropHealthData = [
    { id: 1, crop: 'Wheat', field: 'North Field', health: 87, change: 'up', lastScan: '2 hours ago' },
    { id: 2, crop: 'Corn', field: 'East Field', health: 92, change: 'up', lastScan: '3 hours ago' },
    { id: 3, crop: 'Soybeans', field: 'West Field', health: 78, change: 'down', lastScan: '1 day ago' },
    { id: 4, crop: 'Rice', field: 'South Field', health: 85, change: 'flat', lastScan: '5 hours ago' }
  ];

  const irrigationData = [
    { id: 1, zone: 'Zone A', moisture: 72, status: 'Optimal', lastCheck: '3 hours ago' },
    { id: 2, zone: 'Zone B', moisture: 45, status: 'Low', lastCheck: '3 hours ago' },
    { id: 3, zone: 'Zone C', moisture: 88, status: 'High', lastCheck: '3 hours ago' },
    { id: 4, zone: 'Zone D', moisture: 65, status: 'Optimal', lastCheck: '3 hours ago' }
  ];

  const recentScans = [
    { id: 1, type: 'NDVI Scan', field: 'North Field', date: '2023-05-15 09:30', status: 'Completed' },
    { id: 2, type: 'Thermal Mapping', field: 'East Field', date: '2023-05-14 14:45', status: 'Completed' },
    { id: 3, type: 'RGB Survey', field: 'West Field', date: '2023-05-14 11:20', status: 'Completed' },
    { id: 4, type: 'Pest Detection', field: 'South Field', date: '2023-05-13 16:15', status: 'Processing' }
  ];

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'optimal':
        return 'success';
      case 'low':
      case 'high':
        return 'warning';
      case 'critical':
        return 'error';
      case 'completed':
        return 'success';
      case 'processing':
        return 'info';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  // Get trend icon
  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon color="success" />;
      case 'down':
        return <TrendingDownIcon color="error" />;
      default:
        return <TrendingFlatIcon color="action" />;
    }
  };

  // Get health color
  const getHealthColor = (health) => {
    if (health >= 80) return 'success';
    if (health >= 60) return 'warning';
    return 'error';
  };

  // Get moisture color
  const getMoistureColor = (moisture, status) => {
    if (status === 'Optimal') return 'success';
    return 'warning';
  };

  return (
    <Box sx={{ mt: 3, width: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" component="h2" sx={{ fontSize: { xs: '1.25rem', md: '1.5rem' }, fontWeight: 'bold', m: 0 }}>
          Data Dashboard
        </Typography>
        <Box>
          <IconButton color="primary" size="small" sx={{ mr: 1 }}>
            <RefreshIcon fontSize="small" />
          </IconButton>
          <IconButton
            color="primary"
            size="small"
            onClick={handleDownloadReport}
            disabled={isDownloading}
          >
            {isDownloading ? (
              <CircularProgress size={20} color="inherit" />
            ) : (
              <DownloadIcon fontSize="small" />
            )}
          </IconButton>
        </Box>
      </Box>

      <Paper sx={{ mb: 3, width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            aria-label="dashboard tabs"
            variant="fullWidth"
          >
            <Tab icon={<BarChartIcon />} label="Crop Health" />
            <Tab icon={<TimelineIcon />} label="Irrigation" />
            <Tab icon={<MapIcon />} label="Recent Scans" />
          </Tabs>
        </Box>

        {/* Crop Health Tab */}
        <TabPanel value={activeTab} index={0}>
          <TableContainer sx={{ width: '100%', overflowX: 'auto' }}>
            <Table size="small" sx={{ minWidth: 650 }}>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontSize: { xs: '0.75rem', md: '0.875rem' }, py: 1 }}>Crop</TableCell>
                  <TableCell sx={{ fontSize: { xs: '0.75rem', md: '0.875rem' }, py: 1 }}>Field</TableCell>
                  <TableCell sx={{ fontSize: { xs: '0.75rem', md: '0.875rem' }, py: 1 }}>Health Index</TableCell>
                  <TableCell sx={{ fontSize: { xs: '0.75rem', md: '0.875rem' }, py: 1 }}>Trend</TableCell>
                  <TableCell sx={{ fontSize: { xs: '0.75rem', md: '0.875rem' }, py: 1 }}>Last Scan</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {cropHealthData.map((row) => (
                  <TableRow key={row.id}>
                    <TableCell component="th" scope="row" sx={{ fontSize: { xs: '0.75rem', md: '0.875rem' }, py: 1 }}>
                      {row.crop}
                    </TableCell>
                    <TableCell sx={{ fontSize: { xs: '0.75rem', md: '0.875rem' }, py: 1 }}>{row.field}</TableCell>
                    <TableCell sx={{ py: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ width: '100%', mr: 1 }}>
                          <LinearProgress
                            variant="determinate"
                            value={row.health}
                            color={getHealthColor(row.health)}
                            sx={{ height: 6, borderRadius: 5 }}
                          />
                        </Box>
                        <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.7rem', md: '0.75rem' } }}>
                          {row.health}%
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell sx={{ py: 1 }}>{getTrendIcon(row.change)}</TableCell>
                    <TableCell sx={{ fontSize: { xs: '0.75rem', md: '0.875rem' }, py: 1 }}>{row.lastScan}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Irrigation Tab */}
        <TabPanel value={activeTab} index={1}>
          <Grid container spacing={2}>
            {irrigationData.map((zone) => (
              <Grid item xs={12} sm={6} md={3} key={zone.id}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {zone.zone}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Box sx={{ width: '100%', mr: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={zone.moisture}
                          color={getMoistureColor(zone.moisture, zone.status)}
                          sx={{ height: 10, borderRadius: 5 }}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {zone.moisture}%
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        {zone.lastCheck}
                      </Typography>
                      <Chip
                        label={zone.status}
                        color={getStatusColor(zone.status)}
                        size="small"
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Recent Scans Tab */}
        <TabPanel value={activeTab} index={2}>
          <TableContainer sx={{ width: '100%', overflowX: 'auto' }}>
            <Table size="small" sx={{ minWidth: 650 }}>
              <TableHead>
                <TableRow>
                  <TableCell>Scan Type</TableCell>
                  <TableCell>Field</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {recentScans.map((scan) => (
                  <TableRow key={scan.id}>
                    <TableCell component="th" scope="row">
                      {scan.type}
                    </TableCell>
                    <TableCell>{scan.field}</TableCell>
                    <TableCell>{scan.date}</TableCell>
                    <TableCell>
                      <Chip
                        label={scan.status}
                        color={getStatusColor(scan.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        size="small"
                        onClick={() => handleDownloadScan(scan)}
                        color="primary"
                      >
                        <DownloadIcon fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default DataDashboard;
