import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  BugReport as PestIcon,
  LocalHospital as DiseaseIcon,
  CameraAlt as CameraIcon,
  Science as AnalysisIcon,
  PlayArrow as StartIcon,
  Stop as StopIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Error as ErrorIcon
} from '@mui/icons-material';

// Mock disease images
const diseaseImages = {
  'Leaf Blight': 'https://www.gardeningknowhow.com/wp-content/uploads/2019/11/leaf-blight.jpg',
  'Powdery Mildew': 'https://www.planetnatural.com/wp-content/uploads/2012/12/powdery-mildew-1.jpg',
  'Rust': 'https://extension.umn.edu/sites/extension.umn.edu/files/styles/large/public/rust.jpg?itok=KJw_-OGF',
  'Bacterial Spot': 'https://extension.umn.edu/sites/extension.umn.edu/files/bacterial-spot-tomato.jpg',
  'Early Blight': 'https://extension.umn.edu/sites/extension.umn.edu/files/early-blight-tomato.jpg'
};

// Disease treatments
const diseaseTreatments = {
  'Leaf Blight': [
    'Apply copper-based fungicide',
    'Improve air circulation between plants',
    'Remove and destroy infected leaves',
    'Avoid overhead watering'
  ],
  'Powdery Mildew': [
    'Apply neem oil or potassium bicarbonate',
    'Increase plant spacing for better air circulation',
    'Water at the base of plants',
    'Remove severely infected plants'
  ],
  'Rust': [
    'Apply sulfur-based fungicide',
    'Remove infected plant parts',
    'Avoid working with plants when wet',
    'Rotate crops in subsequent seasons'
  ],
  'Bacterial Spot': [
    'Apply copper-based bactericide',
    'Rotate crops',
    'Avoid overhead irrigation',
    'Remove and destroy infected plants'
  ],
  'Early Blight': [
    'Apply fungicide with chlorothalonil',
    'Remove lower infected leaves',
    'Mulch around plants',
    'Ensure adequate plant nutrition'
  ]
};

const DiseaseDetection = ({ isAnalyzing, onToggleAnalysis, detectedDiseases, dronePosition }) => {
  const [selectedDisease, setSelectedDisease] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };
  
  // Get severity color
  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'High':
        return 'error';
      case 'Medium':
        return 'warning';
      case 'Low':
        return 'success';
      default:
        return 'default';
    }
  };
  
  // Get severity icon
  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'High':
        return <ErrorIcon color="error" />;
      case 'Medium':
        return <WarningIcon color="warning" />;
      case 'Low':
        return <CheckIcon color="success" />;
      default:
        return <CheckIcon />;
    }
  };
  
  // Handle start/stop analysis
  const handleToggleAnalysis = () => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      onToggleAnalysis();
      setLoading(false);
    }, 1500);
  };
  
  // Handle disease selection
  const handleDiseaseSelect = (disease) => {
    setSelectedDisease(disease);
  };
  
  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Disease Detection & Analysis
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="subtitle1">
                Detected Issues
              </Typography>
              
              <Button
                variant="contained"
                color={isAnalyzing ? 'error' : 'primary'}
                startIcon={isAnalyzing ? <StopIcon /> : <StartIcon />}
                onClick={handleToggleAnalysis}
                disabled={loading}
                size="small"
              >
                {loading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  isAnalyzing ? 'Stop Analysis' : 'Start Analysis'
                )}
              </Button>
            </Box>
            
            {isAnalyzing && (
              <Alert severity="info" sx={{ mb: 2 }}>
                Analyzing crop health at coordinates: Lat: {dronePosition.lat.toFixed(6)}, Lng: {dronePosition.lng.toFixed(6)}
              </Alert>
            )}
            
            {detectedDiseases.length > 0 ? (
              <List>
                {detectedDiseases.map((disease) => (
                  <ListItem 
                    key={disease.id} 
                    button 
                    onClick={() => handleDiseaseSelect(disease)}
                    selected={selectedDisease?.id === disease.id}
                    sx={{ 
                      mb: 1, 
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: 'divider'
                    }}
                  >
                    <ListItemIcon>
                      {getSeverityIcon(disease.severity)}
                    </ListItemIcon>
                    <ListItemText
                      primary={disease.name}
                      secondary={`Severity: ${disease.severity} • Detected at ${formatDate(disease.timestamp)}`}
                    />
                    <Chip 
                      label={disease.severity} 
                      color={getSeverityColor(disease.severity)}
                      size="small"
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 4 }}>
                <DiseaseIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="body1" color="text.secondary">
                  No diseases detected
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {isAnalyzing ? 'Analyzing crop health...' : 'Start analysis to detect crop diseases'}
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            {selectedDisease ? (
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  {selectedDisease.name} Details
                </Typography>
                
                <Card sx={{ mb: 2 }}>
                  <CardMedia
                    component="img"
                    height="160"
                    image={diseaseImages[selectedDisease.name] || 'https://via.placeholder.com/400x200?text=Disease+Image'}
                    alt={selectedDisease.name}
                  />
                  <CardContent sx={{ pt: 1, pb: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Detected at: {formatDate(selectedDisease.timestamp)}
                      </Typography>
                      <Chip 
                        label={selectedDisease.severity} 
                        color={getSeverityColor(selectedDisease.severity)}
                        size="small"
                      />
                    </Box>
                  </CardContent>
                </Card>
                
                <Typography variant="subtitle2" gutterBottom>
                  Treatment Recommendations
                </Typography>
                
                <List dense>
                  {(diseaseTreatments[selectedDisease.name] || []).map((treatment, index) => (
                    <ListItem key={index}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <CheckIcon color="primary" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary={treatment} />
                    </ListItem>
                  ))}
                </List>
                
                <Divider sx={{ my: 2 }} />
                
                <Typography variant="subtitle2" gutterBottom>
                  Impact Assessment
                </Typography>
                
                <Typography variant="body2" paragraph>
                  {selectedDisease.severity === 'High' 
                    ? `${selectedDisease.name} can cause significant crop loss if not treated promptly. Immediate action is recommended to prevent spread to healthy plants.`
                    : selectedDisease.severity === 'Medium'
                      ? `${selectedDisease.name} may reduce crop yield by 15-30% if left untreated. Early intervention can minimize impact.`
                      : `${selectedDisease.name} is in early stages and can be effectively managed with proper treatment. Expected yield impact is minimal if treated.`
                  }
                </Typography>
                
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<CameraIcon />}
                  fullWidth
                >
                  Capture Detailed Images
                </Button>
              </Box>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%', py: 4 }}>
                <AnalysisIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="body1" color="text.secondary">
                  Select a detected issue to view details
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Treatment recommendations and impact assessment will be shown here
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DiseaseDetection;
