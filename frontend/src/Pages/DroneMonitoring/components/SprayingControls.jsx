import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Healing as SprayIcon,
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Science as ChemicalIcon,
  Opacity as DensityIcon,
  Speed as SpeedIcon,
  Height as HeightIcon
} from '@mui/icons-material';

const SprayingControls = ({ isSpraying, isFlying, onToggleSpraying, dronePosition }) => {
  const [sprayType, setSprayType] = useState('organic');
  const [sprayDensity, setSprayDensity] = useState(50);
  const [sprayHeight, setSprayHeight] = useState(2);
  const [spraySpeed, setSpraySpeed] = useState(3);
  const [autoAdjust, setAutoAdjust] = useState(true);
  const [loading, setLoading] = useState(false);
  const [sprayArea, setSprayArea] = useState('');
  const [sprayVolume, setSprayVolume] = useState(5);
  const [error, setError] = useState(null);
  
  // Spray types
  const sprayTypes = [
    { value: 'organic', label: 'Organic Pesticide', description: 'Neem oil-based organic pesticide safe for beneficial insects' },
    { value: 'fungicide', label: 'Organic Fungicide', description: 'Copper-based fungicide for controlling fungal diseases' },
    { value: 'fertilizer', label: 'Organic Fertilizer', description: 'Liquid seaweed and fish emulsion fertilizer' },
    { value: 'herbicide', label: 'Organic Herbicide', description: 'Vinegar and salt-based weed control solution' }
  ];
  
  // Handle spray type change
  const handleSprayTypeChange = (event) => {
    setSprayType(event.target.value);
  };
  
  // Handle spray density change
  const handleSprayDensityChange = (event, newValue) => {
    setSprayDensity(newValue);
  };
  
  // Handle spray height change
  const handleSprayHeightChange = (event, newValue) => {
    setSprayHeight(newValue);
  };
  
  // Handle spray speed change
  const handleSpraySpeedChange = (event, newValue) => {
    setSpraySpeed(newValue);
  };
  
  // Handle auto adjust toggle
  const handleAutoAdjustToggle = (event) => {
    setAutoAdjust(event.target.checked);
  };
  
  // Handle spray area change
  const handleSprayAreaChange = (event) => {
    setSprayArea(event.target.value);
  };
  
  // Handle spray volume change
  const handleSprayVolumeChange = (event) => {
    setSprayVolume(event.target.value);
  };
  
  // Handle start spraying
  const handleStartSpraying = () => {
    if (!isFlying) {
      setError('Drone must be flying to start spraying');
      setTimeout(() => setError(null), 3000);
      return;
    }
    
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      onToggleSpraying();
      setLoading(false);
    }, 1500);
  };
  
  // Calculate estimated coverage
  const calculateCoverage = () => {
    // Simple calculation based on spray parameters
    const areaValue = parseFloat(sprayArea) || 0;
    const volumeValue = parseFloat(sprayVolume) || 0;
    
    if (areaValue === 0 || volumeValue === 0) return 'N/A';
    
    // Adjust coverage based on density and speed
    const densityFactor = sprayDensity / 50; // 1 at default density
    const speedFactor = 5 / spraySpeed; // 1 at default speed
    
    const coverage = (areaValue / volumeValue) * densityFactor * speedFactor;
    
    return `${coverage.toFixed(2)} acres/liter`;
  };
  
  // Get selected spray type details
  const selectedSprayType = sprayTypes.find(type => type.value === sprayType) || sprayTypes[0];
  
  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Spraying Controls
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom>
              Spray Configuration
            </Typography>
            
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel id="spray-type-label">Spray Type</InputLabel>
              <Select
                labelId="spray-type-label"
                id="spray-type"
                value={sprayType}
                label="Spray Type"
                onChange={handleSprayTypeChange}
                disabled={isSpraying}
              >
                {sprayTypes.map((type) => (
                  <MenuItem key={type.value} value={type.value}>
                    {type.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {selectedSprayType.description}
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Spray Area (acres)"
                  type="number"
                  fullWidth
                  value={sprayArea}
                  onChange={handleSprayAreaChange}
                  disabled={isSpraying}
                  InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Spray Volume (liters)"
                  type="number"
                  fullWidth
                  value={sprayVolume}
                  onChange={handleSprayVolumeChange}
                  disabled={isSpraying}
                  InputProps={{ inputProps: { min: 0, step: 0.5 } }}
                />
              </Grid>
            </Grid>
            
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2">
                Estimated Coverage: <strong>{calculateCoverage()}</strong>
              </Typography>
            </Box>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="subtitle1" gutterBottom>
              Spray Parameters
            </Typography>
            
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <DensityIcon color="primary" sx={{ mr: 1 }} />
                <Typography id="spray-density-slider" gutterBottom>
                  Spray Density
                </Typography>
              </Box>
              <Slider
                value={sprayDensity}
                onChange={handleSprayDensityChange}
                aria-labelledby="spray-density-slider"
                valueLabelDisplay="auto"
                step={5}
                marks
                min={10}
                max={100}
                disabled={isSpraying || autoAdjust}
              />
            </Box>
            
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <HeightIcon color="primary" sx={{ mr: 1 }} />
                <Typography id="spray-height-slider" gutterBottom>
                  Spray Height (meters)
                </Typography>
              </Box>
              <Slider
                value={sprayHeight}
                onChange={handleSprayHeightChange}
                aria-labelledby="spray-height-slider"
                valueLabelDisplay="auto"
                step={0.5}
                marks
                min={1}
                max={5}
                disabled={isSpraying || autoAdjust}
              />
            </Box>
            
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <SpeedIcon color="primary" sx={{ mr: 1 }} />
                <Typography id="spray-speed-slider" gutterBottom>
                  Spray Speed (m/s)
                </Typography>
              </Box>
              <Slider
                value={spraySpeed}
                onChange={handleSpraySpeedChange}
                aria-labelledby="spray-speed-slider"
                valueLabelDisplay="auto"
                step={0.5}
                marks
                min={1}
                max={5}
                disabled={isSpraying || autoAdjust}
              />
            </Box>
            
            <FormControlLabel
              control={
                <Switch
                  checked={autoAdjust}
                  onChange={handleAutoAdjustToggle}
                  disabled={isSpraying}
                />
              }
              label="Auto-adjust spray parameters based on crop conditions"
            />
          </Paper>
        </Grid>
        
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
            <Button
              variant="contained"
              color={isSpraying ? 'error' : 'success'}
              startIcon={isSpraying ? <StopIcon /> : <StartIcon />}
              onClick={handleStartSpraying}
              disabled={loading || !isFlying}
              sx={{ px: 4, py: 1 }}
            >
              {loading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                isSpraying ? 'Stop Spraying' : 'Start Spraying'
              )}
            </Button>
          </Box>
          
          {isSpraying && (
            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                Currently spraying {selectedSprayType.label} at coordinates: 
                Lat: {dronePosition.lat.toFixed(6)}, Lng: {dronePosition.lng.toFixed(6)}
              </Typography>
            </Box>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default SprayingControls;
