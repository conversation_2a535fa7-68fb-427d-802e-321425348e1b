import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Typography,
  Divider,
  Box,
  Chip,
  Paper,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Card,
  CardContent,
  CircularProgress
} from '@mui/material';
import {
  LocationOn as LocationIcon,
  Grass as GrassIcon,
  WaterDrop as WaterIcon,
  Agriculture as AgricultureIcon,
  Person as PersonIcon,
  CalendarMonth as CalendarIcon,
  Map as MapIcon,
  Inventory as InventoryIcon,
  AttachMoney as MoneyIcon,
  Assignment as TaskIcon,
  WbSunny as WeatherIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import { 
  getCropPlanningByFarmId,
  getInventoryByFarmId,
  getFinancialDataByFarmId,
  getTasksByFarmId,
  getWeatherByFarmId,
  getYieldDataByFarmId
} from '../../../services/farmManagementService';

const ViewFarmDialog = ({ open, onClose, farm }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [cropPlanning, setCropPlanning] = useState(null);
  const [inventory, setInventory] = useState(null);
  const [financialData, setFinancialData] = useState(null);
  const [tasks, setTasks] = useState(null);
  const [weather, setWeather] = useState(null);
  const [yieldData, setYieldData] = useState(null);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    fetchTabData(newValue);
  };

  // Fetch data based on active tab
  const fetchTabData = async (tabIndex) => {
    if (!farm) return;

    setLoading(true);
    try {
      switch (tabIndex) {
        case 1: // Crop Planning
          if (!cropPlanning) {
            const data = await getCropPlanningByFarmId(farm.id);
            setCropPlanning(data);
          }
          break;
        case 2: // Inventory
          if (!inventory) {
            const data = await getInventoryByFarmId(farm.id);
            setInventory(data);
          }
          break;
        case 3: // Financial
          if (!financialData) {
            const data = await getFinancialDataByFarmId(farm.id);
            setFinancialData(data);
          }
          break;
        case 4: // Tasks
          if (!tasks) {
            const data = await getTasksByFarmId(farm.id);
            setTasks(data);
          }
          break;
        case 5: // Weather
          if (!weather) {
            const data = await getWeatherByFarmId(farm.id);
            setWeather(data);
          }
          break;
        case 6: // Yield
          if (!yieldData) {
            const data = await getYieldDataByFarmId(farm.id);
            setYieldData(data);
          }
          break;
        default:
          break;
      }
    } catch (err) {
      console.error('Error fetching tab data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  if (!farm) {
    return null;
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">{farm.name}</Typography>
          <Chip 
            label={farm.status} 
            color={farm.status === 'Active' ? 'success' : 'default'} 
            size="small" 
          />
        </Box>
      </DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Farm Image */}
          {farm.image && (
            <Grid item xs={12} md={4}>
              <Box
                component="img"
                src={farm.image}
                alt={farm.name}
                sx={{
                  width: '100%',
                  height: 'auto',
                  maxHeight: 250,
                  objectFit: 'cover',
                  borderRadius: 1
                }}
              />
            </Grid>
          )}

          {/* Farm Details */}
          <Grid item xs={12} md={farm.image ? 8 : 12}>
            <Typography variant="subtitle1" gutterBottom fontWeight="medium">
              Farm Details
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <List dense disablePadding>
                  <ListItem disablePadding sx={{ mb: 1 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <LocationIcon fontSize="small" color="primary" />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Location" 
                      secondary={farm.location} 
                    />
                  </ListItem>
                  <ListItem disablePadding sx={{ mb: 1 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <AgricultureIcon fontSize="small" color="primary" />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Size" 
                      secondary={`${farm.size} acres`} 
                    />
                  </ListItem>
                  <ListItem disablePadding sx={{ mb: 1 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <GrassIcon fontSize="small" color="primary" />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Soil Type" 
                      secondary={farm.soilType || 'Not specified'} 
                    />
                  </ListItem>
                </List>
              </Grid>
              <Grid item xs={12} sm={6}>
                <List dense disablePadding>
                  <ListItem disablePadding sx={{ mb: 1 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <WaterIcon fontSize="small" color="primary" />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Irrigation" 
                      secondary={farm.irrigation || 'Not specified'} 
                    />
                  </ListItem>
                  <ListItem disablePadding sx={{ mb: 1 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <PersonIcon fontSize="small" color="primary" />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Owner/Manager" 
                      secondary={farm.owner || 'Not specified'} 
                    />
                  </ListItem>
                  <ListItem disablePadding sx={{ mb: 1 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <CalendarIcon fontSize="small" color="primary" />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Last Updated" 
                      secondary={formatDate(farm.updatedAt)} 
                    />
                  </ListItem>
                </List>
              </Grid>
            </Grid>

            {/* Description */}
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
              Description
            </Typography>
            <Typography variant="body2" paragraph>
              {farm.description || 'No description available.'}
            </Typography>

            {/* Crops */}
            <Typography variant="subtitle2" gutterBottom>
              Crops
            </Typography>
            {farm.crops && farm.crops.length > 0 ? (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {farm.crops.map((crop) => (
                  <Chip key={crop} label={crop} size="small" />
                ))}
              </Box>
            ) : (
              <Typography variant="body2">No crops specified.</Typography>
            )}
          </Grid>

          {/* Tabs for additional information */}
          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Paper sx={{ width: '100%' }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                variant="scrollable"
                scrollButtons="auto"
                aria-label="farm details tabs"
              >
                <Tab icon={<MapIcon />} label="Overview" />
                <Tab icon={<CalendarIcon />} label="Crop Planning" />
                <Tab icon={<InventoryIcon />} label="Inventory" />
                <Tab icon={<MoneyIcon />} label="Financial" />
                <Tab icon={<TaskIcon />} label="Tasks" />
                <Tab icon={<WeatherIcon />} label="Weather" />
                <Tab icon={<TimelineIcon />} label="Yield Data" />
              </Tabs>
            </Paper>

            {/* Tab Content */}
            <Box sx={{ mt: 2, minHeight: 300 }}>
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <>
                  {/* Overview Tab */}
                  {activeTab === 0 && (
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Card>
                          <CardContent>
                            <Typography variant="h6" gutterBottom>
                              Farm Type
                            </Typography>
                            <Typography variant="body1">
                              {farm.type || 'Not specified'}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                              {farm.type === 'Organic' 
                                ? 'Organic farming practices focus on sustainable agriculture without synthetic pesticides or fertilizers.' 
                                : farm.type === 'Conventional'
                                  ? 'Conventional farming uses modern techniques and technologies to maximize production efficiency.'
                                  : 'Farm type details not available.'}
                            </Typography>
                          </CardContent>
                        </Card>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Card>
                          <CardContent>
                            <Typography variant="h6" gutterBottom>
                              Location Details
                            </Typography>
                            {farm.coordinates && (farm.coordinates.lat !== 0 || farm.coordinates.lng !== 0) ? (
                              <>
                                <Typography variant="body2">
                                  Latitude: {farm.coordinates.lat}
                                </Typography>
                                <Typography variant="body2">
                                  Longitude: {farm.coordinates.lng}
                                </Typography>
                                <Button 
                                  variant="outlined" 
                                  size="small" 
                                  sx={{ mt: 1 }}
                                  onClick={() => window.open(`https://www.google.com/maps/search/?api=1&query=${farm.coordinates.lat},${farm.coordinates.lng}`, '_blank')}
                                >
                                  View on Map
                                </Button>
                              </>
                            ) : (
                              <Typography variant="body2">
                                Precise coordinates not available.
                              </Typography>
                            )}
                          </CardContent>
                        </Card>
                      </Grid>
                    </Grid>
                  )}

                  {/* Crop Planning Tab */}
                  {activeTab === 1 && cropPlanning && (
                    <Box>
                      {cropPlanning.plans && cropPlanning.plans.length > 0 ? (
                        <Grid container spacing={2}>
                          {cropPlanning.plans.map((plan) => (
                            <Grid item xs={12} md={6} key={plan.id}>
                              <Card>
                                <CardContent>
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                                    <Typography variant="h6" gutterBottom>
                                      {plan.cropName}
                                    </Typography>
                                    <Chip 
                                      label={plan.status} 
                                      color={plan.status === 'Active' ? 'success' : plan.status === 'Completed' ? 'info' : 'default'} 
                                      size="small" 
                                    />
                                  </Box>
                                  <Typography variant="body2" color="text.secondary" gutterBottom>
                                    Season: {plan.season}
                                  </Typography>
                                  <Grid container spacing={1}>
                                    <Grid item xs={6}>
                                      <Typography variant="body2">
                                        Area: {plan.area} acres
                                      </Typography>
                                    </Grid>
                                    <Grid item xs={6}>
                                      <Typography variant="body2">
                                        Expected Yield: {plan.expectedYield} kg
                                      </Typography>
                                    </Grid>
                                    <Grid item xs={6}>
                                      <Typography variant="body2">
                                        Start Date: {formatDate(plan.startDate)}
                                      </Typography>
                                    </Grid>
                                    <Grid item xs={6}>
                                      <Typography variant="body2">
                                        Harvest Date: {formatDate(plan.harvestDate)}
                                      </Typography>
                                    </Grid>
                                  </Grid>
                                  <Typography variant="body2" sx={{ mt: 1 }}>
                                    Variety: {plan.variety}
                                  </Typography>
                                  <Typography variant="body2">
                                    Seed Source: {plan.seedSource}
                                  </Typography>
                                  {plan.notes && (
                                    <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
                                      Notes: {plan.notes}
                                    </Typography>
                                  )}
                                </CardContent>
                              </Card>
                            </Grid>
                          ))}
                        </Grid>
                      ) : (
                        <Typography variant="body1" sx={{ textAlign: 'center', py: 4 }}>
                          No crop planning data available for this farm.
                        </Typography>
                      )}
                    </Box>
                  )}

                  {/* Inventory Tab */}
                  {activeTab === 2 && inventory && (
                    <Box>
                      {inventory.inventory && inventory.inventory.length > 0 ? (
                        <Grid container spacing={2}>
                          {inventory.inventory.map((item) => (
                            <Grid item xs={12} md={6} key={item.id}>
                              <Card>
                                <CardContent>
                                  <Typography variant="h6" gutterBottom>
                                    {item.name}
                                  </Typography>
                                  <Chip 
                                    label={item.category} 
                                    size="small" 
                                    sx={{ mb: 1 }}
                                  />
                                  <Grid container spacing={1}>
                                    <Grid item xs={6}>
                                      <Typography variant="body2">
                                        Quantity: {item.quantity} {item.unit}
                                      </Typography>
                                    </Grid>
                                    <Grid item xs={6}>
                                      <Typography variant="body2">
                                        Cost: ₹{item.cost}
                                      </Typography>
                                    </Grid>
                                    <Grid item xs={6}>
                                      <Typography variant="body2">
                                        Purchase Date: {formatDate(item.purchaseDate)}
                                      </Typography>
                                    </Grid>
                                    <Grid item xs={6}>
                                      <Typography variant="body2">
                                        Expiry Date: {item.expiryDate ? formatDate(item.expiryDate) : 'N/A'}
                                      </Typography>
                                    </Grid>
                                  </Grid>
                                  <Typography variant="body2" sx={{ mt: 1 }}>
                                    Supplier: {item.supplier}
                                  </Typography>
                                </CardContent>
                              </Card>
                            </Grid>
                          ))}
                        </Grid>
                      ) : (
                        <Typography variant="body1" sx={{ textAlign: 'center', py: 4 }}>
                          No inventory data available for this farm.
                        </Typography>
                      )}
                    </Box>
                  )}

                  {/* Financial Tab */}
                  {activeTab === 3 && financialData && (
                    <Box>
                      {financialData.finances && 
                       (financialData.finances.income.length > 0 || financialData.finances.expenses.length > 0) ? (
                        <Grid container spacing={3}>
                          <Grid item xs={12} md={6}>
                            <Typography variant="h6" gutterBottom>
                              Income
                            </Typography>
                            {financialData.finances.income.length > 0 ? (
                              financialData.finances.income.map((item) => (
                                <Card key={item.id} sx={{ mb: 2 }}>
                                  <CardContent>
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                                      <Typography variant="subtitle1">
                                        {item.description}
                                      </Typography>
                                      <Typography variant="subtitle1" color="success.main">
                                        ₹{item.amount}
                                      </Typography>
                                    </Box>
                                    <Typography variant="body2" color="text.secondary">
                                      Category: {item.category}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                      Date: {formatDate(item.date)}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                      Buyer: {item.buyer}
                                    </Typography>
                                  </CardContent>
                                </Card>
                              ))
                            ) : (
                              <Typography variant="body2">No income data available.</Typography>
                            )}
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <Typography variant="h6" gutterBottom>
                              Expenses
                            </Typography>
                            {financialData.finances.expenses.length > 0 ? (
                              financialData.finances.expenses.map((item) => (
                                <Card key={item.id} sx={{ mb: 2 }}>
                                  <CardContent>
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                                      <Typography variant="subtitle1">
                                        {item.description}
                                      </Typography>
                                      <Typography variant="subtitle1" color="error.main">
                                        ₹{item.amount}
                                      </Typography>
                                    </Box>
                                    <Typography variant="body2" color="text.secondary">
                                      Category: {item.category}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                      Date: {formatDate(item.date)}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                      Vendor: {item.vendor}
                                    </Typography>
                                  </CardContent>
                                </Card>
                              ))
                            ) : (
                              <Typography variant="body2">No expense data available.</Typography>
                            )}
                          </Grid>
                        </Grid>
                      ) : (
                        <Typography variant="body1" sx={{ textAlign: 'center', py: 4 }}>
                          No financial data available for this farm.
                        </Typography>
                      )}
                    </Box>
                  )}

                  {/* Tasks Tab */}
                  {activeTab === 4 && tasks && (
                    <Box>
                      {tasks.tasks && tasks.tasks.length > 0 ? (
                        <Grid container spacing={2}>
                          {tasks.tasks.map((task) => (
                            <Grid item xs={12} md={6} key={task.id}>
                              <Card>
                                <CardContent>
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                                    <Typography variant="h6" gutterBottom>
                                      {task.title}
                                    </Typography>
                                    <Chip 
                                      label={task.status} 
                                      color={
                                        task.status === 'Completed' ? 'success' : 
                                        task.status === 'In Progress' ? 'info' : 
                                        task.status === 'Pending' ? 'warning' : 'default'
                                      } 
                                      size="small" 
                                    />
                                  </Box>
                                  <Typography variant="body2" paragraph>
                                    {task.description}
                                  </Typography>
                                  <Grid container spacing={1}>
                                    <Grid item xs={6}>
                                      <Typography variant="body2" color="text.secondary">
                                        Due Date: {formatDate(task.dueDate)}
                                      </Typography>
                                    </Grid>
                                    <Grid item xs={6}>
                                      <Typography variant="body2" color="text.secondary">
                                        Priority: {task.priority}
                                      </Typography>
                                    </Grid>
                                  </Grid>
                                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                    Assigned To: {task.assignedTo}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary">
                                    Category: {task.category}
                                  </Typography>
                                </CardContent>
                              </Card>
                            </Grid>
                          ))}
                        </Grid>
                      ) : (
                        <Typography variant="body1" sx={{ textAlign: 'center', py: 4 }}>
                          No tasks available for this farm.
                        </Typography>
                      )}
                    </Box>
                  )}

                  {/* Weather Tab */}
                  {activeTab === 5 && weather && (
                    <Box>
                      {weather.forecast && weather.forecast.length > 0 ? (
                        <Grid container spacing={2}>
                          {weather.forecast.map((day, index) => (
                            <Grid item xs={12} sm={6} md={2.4} key={index}>
                              <Card>
                                <CardContent sx={{ textAlign: 'center' }}>
                                  <Typography variant="subtitle1" gutterBottom>
                                    {formatDate(day.date)}
                                  </Typography>
                                  <Typography variant="h5" gutterBottom>
                                    {day.description}
                                  </Typography>
                                  <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mb: 2 }}>
                                    <Typography variant="body1">
                                      {day.temperature.min}°C
                                    </Typography>
                                    <Typography variant="body1">
                                      {day.temperature.max}°C
                                    </Typography>
                                  </Box>
                                  <Divider sx={{ my: 1 }} />
                                  <Typography variant="body2">
                                    Humidity: {day.humidity}%
                                  </Typography>
                                  <Typography variant="body2">
                                    Precipitation: {day.precipitation}%
                                  </Typography>
                                  <Typography variant="body2">
                                    Wind: {day.windSpeed} km/h
                                  </Typography>
                                </CardContent>
                              </Card>
                            </Grid>
                          ))}
                        </Grid>
                      ) : (
                        <Typography variant="body1" sx={{ textAlign: 'center', py: 4 }}>
                          No weather forecast available for this farm.
                        </Typography>
                      )}
                    </Box>
                  )}

                  {/* Yield Data Tab */}
                  {activeTab === 6 && yieldData && (
                    <Box>
                      {yieldData.yields && yieldData.yields.length > 0 ? (
                        <Grid container spacing={3}>
                          {yieldData.yields.map((yearData) => (
                            <Grid item xs={12} key={yearData.year}>
                              <Typography variant="h6" gutterBottom>
                                {yearData.year}
                              </Typography>
                              <Grid container spacing={2}>
                                {yearData.crops.map((crop, index) => (
                                  <Grid item xs={12} sm={6} md={4} key={index}>
                                    <Card>
                                      <CardContent>
                                        <Typography variant="subtitle1" gutterBottom>
                                          {crop.name}
                                        </Typography>
                                        <Typography variant="body1">
                                          Yield: {crop.yield} {crop.unit}
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                          Area: {crop.area} acres
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                          Total Production: {crop.yield * crop.area} {crop.unit.split('/')[0]}
                                        </Typography>
                                      </CardContent>
                                    </Card>
                                  </Grid>
                                ))}
                              </Grid>
                              {yearData !== yieldData.yields[yieldData.yields.length - 1] && (
                                <Divider sx={{ my: 2 }} />
                              )}
                            </Grid>
                          ))}
                        </Grid>
                      ) : (
                        <Typography variant="body1" sx={{ textAlign: 'center', py: 4 }}>
                          No yield data available for this farm.
                        </Typography>
                      )}
                    </Box>
                  )}
                </>
              )}
            </Box>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ViewFarmDialog;
