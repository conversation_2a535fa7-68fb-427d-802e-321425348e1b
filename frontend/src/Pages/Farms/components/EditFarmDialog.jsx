import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Box,
  Typography,
  Divider,
  FormControlLabel,
  Switch
} from '@mui/material';

const EditFarmDialog = ({ 
  open, 
  onClose, 
  formData, 
  onChange, 
  onCoordinatesChange, 
  onCropsChange, 
  onSubmit 
}) => {
  // Available crop options
  const cropOptions = [
    'Wheat', 'Rice', 'Maize', '<PERSON>war', '<PERSON><PERSON>ra', 'Ragi', 'Pulses', 'Gram', 'Tur', 'Moong', 
    'Urad', 'Sugarcane', 'Cotton', 'Jute', 'Groundnut', 'Soybean', 'Sunflower', 'Mustard', 
    'Coconut', 'Tea', 'Coffee', 'Rubber', 'Potato', 'Onion', 'Tomato'
  ];

  // Soil type options
  const soilTypes = [
    '<PERSON>am<PERSON>', '<PERSON>', '<PERSON>', 'Silt', 'Peat', 'Chalky', 'Sandy Loam', 'Clay Loam', 'Silt Loam', 'Red Soil', 'Black Soil'
  ];

  // Irrigation options
  const irrigationTypes = [
    'Drip Irrigation', 'Sprinkler System', 'Flood Irrigation', 'Furrow Irrigation', 'Rainwater Harvesting', 'Canal Irrigation', 'Well Irrigation'
  ];

  // Farm type options
  const farmTypes = [
    'Organic', 'Conventional', 'Mixed', 'Permaculture', 'Hydroponic', 'Aquaponic'
  ];

  // Handle status change
  const handleStatusChange = (e) => {
    onChange({
      target: {
        name: 'status',
        value: e.target.checked ? 'Active' : 'Inactive'
      }
    });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Edit Farm</DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom fontWeight="medium">
              Basic Information
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              autoFocus
              name="name"
              label="Farm Name"
              fullWidth
              variant="outlined"
              value={formData.name}
              onChange={onChange}
              required
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              name="location"
              label="Location"
              fullWidth
              variant="outlined"
              value={formData.location}
              onChange={onChange}
              required
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              name="size"
              label="Size (acres)"
              type="number"
              fullWidth
              variant="outlined"
              value={formData.size}
              onChange={onChange}
              required
              inputProps={{ min: 0, step: 0.1 }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required>
              <InputLabel>Farm Type</InputLabel>
              <Select
                name="type"
                value={formData.type}
                label="Farm Type"
                onChange={onChange}
              >
                {farmTypes.map((type) => (
                  <MenuItem key={type} value={type}>{type}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <TextField
              name="description"
              label="Description"
              fullWidth
              variant="outlined"
              value={formData.description}
              onChange={onChange}
              multiline
              rows={3}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch 
                  checked={formData.status === 'Active'} 
                  onChange={handleStatusChange}
                  color="success"
                />
              }
              label={`Status: ${formData.status}`}
            />
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 1 }} />
          </Grid>

          {/* Soil and Irrigation */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom fontWeight="medium">
              Soil & Irrigation
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Soil Type</InputLabel>
              <Select
                name="soilType"
                value={formData.soilType}
                label="Soil Type"
                onChange={onChange}
              >
                {soilTypes.map((type) => (
                  <MenuItem key={type} value={type}>{type}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Irrigation Type</InputLabel>
              <Select
                name="irrigation"
                value={formData.irrigation}
                label="Irrigation Type"
                onChange={onChange}
              >
                {irrigationTypes.map((type) => (
                  <MenuItem key={type} value={type}>{type}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 1 }} />
          </Grid>

          {/* Crops and Owner */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom fontWeight="medium">
              Crops & Ownership
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Crops</InputLabel>
              <Select
                name="crops"
                multiple
                value={formData.crops}
                label="Crops"
                onChange={onCropsChange}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip key={value} label={value} />
                    ))}
                  </Box>
                )}
              >
                {cropOptions.map((crop) => (
                  <MenuItem key={crop} value={crop}>{crop}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <TextField
              name="owner"
              label="Owner/Manager Name"
              fullWidth
              variant="outlined"
              value={formData.owner}
              onChange={onChange}
            />
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 1 }} />
          </Grid>

          {/* Location Coordinates */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom fontWeight="medium">
              Location Coordinates (Optional)
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              name="lat"
              label="Latitude"
              type="number"
              fullWidth
              variant="outlined"
              value={formData.coordinates.lat}
              onChange={onCoordinatesChange}
              inputProps={{ step: 0.000001 }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              name="lng"
              label="Longitude"
              type="number"
              fullWidth
              variant="outlined"
              value={formData.coordinates.lng}
              onChange={onCoordinatesChange}
              inputProps={{ step: 0.000001 }}
            />
          </Grid>

          {/* Farm Image */}
          <Grid item xs={12}>
            <TextField
              name="image"
              label="Farm Image URL"
              fullWidth
              variant="outlined"
              value={formData.image}
              onChange={onChange}
              helperText="Enter URL for farm image (optional)"
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button 
          onClick={onSubmit} 
          variant="contained" 
          color="primary"
          disabled={!formData.name || !formData.location || !formData.size || !formData.type}
        >
          Update Farm
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditFarmDialog;
