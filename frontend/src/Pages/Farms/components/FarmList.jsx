import React from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Box,
  Typography,
  Chip,
  IconButton,
  Button,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Paper
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  LocationOn as LocationIcon
} from '@mui/icons-material';

const FarmList = ({ farms, viewMode, onMenuOpen, onView }) => {
  if (farms.length === 0) {
    return (
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No farms found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Add a new farm to get started or adjust your filters
        </Typography>
      </Paper>
    );
  }

  return viewMode === 'grid' ? (
    <Grid container spacing={3}>
      {farms.map((farm) => (
        <Grid item xs={12} sm={6} md={4} key={farm.id}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {farm.image && (
              <Box sx={{ position: 'relative', pt: '56.25%' }}>
                <Box
                  component="img"
                  src={farm.image}
                  alt={farm.name}
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                />
              </Box>
            )}
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Typography variant="h6" component="div" gutterBottom>
                  {farm.name}
                </Typography>
                <IconButton size="small" onClick={(e) => onMenuOpen(e, farm.id)}>
                  <MoreVertIcon />
                </IconButton>
              </Box>
              <Box sx={{ mb: 1 }}>
                <Chip 
                  label={farm.status} 
                  size="small" 
                  color={farm.status === 'Active' ? 'success' : 'default'}
                  sx={{ mr: 1 }}
                />
                <Chip 
                  label={farm.type} 
                  size="small" 
                  color="primary" 
                  variant="outlined"
                />
              </Box>
              <Typography color="text.secondary" gutterBottom>
                <LocationIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                {farm.location}
              </Typography>
              <Typography color="text.secondary" gutterBottom>
                Size: {farm.size} acres
              </Typography>
              <Typography variant="body2" sx={{ mt: 1, mb: 1 }}>
                {farm.description}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Last updated: {new Date(farm.updatedAt).toLocaleDateString()}
              </Typography>
            </CardContent>
            <Box sx={{ p: 2, pt: 0 }}>
              <Button 
                variant="outlined" 
                fullWidth
                onClick={() => onView(farm.id)}
                startIcon={<VisibilityIcon />}
              >
                View Details
              </Button>
            </Box>
          </Card>
        </Grid>
      ))}
    </Grid>
  ) : (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Name</TableCell>
            <TableCell>Location</TableCell>
            <TableCell>Size (acres)</TableCell>
            <TableCell>Type</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Last Updated</TableCell>
            <TableCell align="right">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {farms.map((farm) => (
            <TableRow key={farm.id}>
              <TableCell>{farm.name}</TableCell>
              <TableCell>{farm.location}</TableCell>
              <TableCell>{farm.size}</TableCell>
              <TableCell>{farm.type}</TableCell>
              <TableCell>
                <Chip 
                  label={farm.status} 
                  size="small" 
                  color={farm.status === 'Active' ? 'success' : 'default'}
                />
              </TableCell>
              <TableCell>{new Date(farm.updatedAt).toLocaleDateString()}</TableCell>
              <TableCell align="right">
                <IconButton size="small" onClick={() => onView(farm.id)}>
                  <VisibilityIcon fontSize="small" />
                </IconButton>
                <IconButton size="small" onClick={(e) => onMenuOpen(e, farm.id)}>
                  <MoreVertIcon fontSize="small" />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default FarmList;
