import React, { useState, useEffect } from "react";
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  CircularProgress,
  IconButton,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Alert,
  Snackbar,
  Tooltip,
  Menu,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import {
  Add as AddIcon,
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  ViewList as TableIcon,
  GridView as GridViewIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import {
  getAllFarms,
  getFarmById,
  addFarm,
  updateFarm,
  deleteFarm,
} from "../../services/farmManagementService";
import { useTranslation } from "react-i18next";

// Import components
import FarmList from "./components/FarmList";
import AddFarmDialog from "./components/AddFarmDialog";
import EditFarmDialog from "./components/EditFarmDialog";
import ViewFarmDialog from "./components/ViewFarmDialog";

const Farms = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [farms, setFarms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [selectedFarm, setSelectedFarm] = useState(null);
  const [viewMode, setViewMode] = useState("grid"); // 'grid' or 'table'
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterType, setFilterType] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("name");
  const [sortOrder, setSortOrder] = useState("asc");

  // Dialog states
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openViewDialog, setOpenViewDialog] = useState(false);

  // Menu states
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [selectedFarmId, setSelectedFarmId] = useState(null);

  // Snackbar state
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  // Form state
  const [farmForm, setFarmForm] = useState({
    name: "",
    location: "",
    size: "",
    type: "",
    description: "",
    soilType: "",
    irrigation: "",
    crops: [],
    owner: "",
    coordinates: { lat: 0, lng: 0 },
    image: "",
  });

  // Fetch farms on component mount
  useEffect(() => {
    fetchFarms();
  }, []);

  // Fetch farms
  const fetchFarms = async () => {
    try {
      setLoading(true);
      const farmsData = await getAllFarms();
      setFarms(farmsData);
      setError(null);
    } catch (err) {
      setError("Failed to fetch farms. Please try again.");
      console.error("Error fetching farms:", err);
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle menu open
  const handleMenuOpen = (event, farmId) => {
    setMenuAnchorEl(event.currentTarget);
    setSelectedFarmId(farmId);
  };

  // Handle menu close
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setSelectedFarmId(null);
  };

  // Handle view farm
  const handleViewFarm = async (farmId) => {
    try {
      setLoading(true);
      const farmData = await getFarmById(farmId);
      setSelectedFarm(farmData);
      setOpenViewDialog(true);
      handleMenuClose();
    } catch (err) {
      setSnackbar({
        open: true,
        message: "Failed to fetch farm details",
        severity: "error",
      });
      console.error("Error fetching farm details:", err);
    } finally {
      setLoading(false);
    }
  };

  // Handle edit farm
  const handleEditFarm = async (farmId) => {
    try {
      setLoading(true);
      const farmData = await getFarmById(farmId);
      setFarmForm(farmData);
      setOpenEditDialog(true);
      handleMenuClose();
    } catch (err) {
      setSnackbar({
        open: true,
        message: "Failed to fetch farm details for editing",
        severity: "error",
      });
      console.error("Error fetching farm details for editing:", err);
    } finally {
      setLoading(false);
    }
  };

  // Handle delete farm
  const handleDeleteConfirm = async () => {
    try {
      await deleteFarm(selectedFarmId);
      setFarms((prev) => prev.filter((farm) => farm.id !== selectedFarmId));
      setOpenDeleteDialog(false);
      setSnackbar({
        open: true,
        message: "Farm deleted successfully",
        severity: "success",
      });
    } catch (err) {
      setSnackbar({
        open: true,
        message: "Failed to delete farm",
        severity: "error",
      });
      console.error("Error deleting farm:", err);
    }
  };

  // Handle form input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFarmForm((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle coordinates change
  const handleCoordinatesChange = (e) => {
    const { name, value } = e.target;
    setFarmForm((prev) => ({
      ...prev,
      coordinates: {
        ...prev.coordinates,
        [name]: parseFloat(value) || 0,
      },
    }));
  };

  // Handle crops change
  const handleCropsChange = (e) => {
    const { value } = e.target;
    setFarmForm((prev) => ({
      ...prev,
      crops: value,
    }));
  };

  // Handle add farm
  const handleAddFarm = async () => {
    try {
      const newFarm = await addFarm({
        ...farmForm,
        size: parseFloat(farmForm.size) || 0,
      });
      setFarms((prev) => [...prev, newFarm]);
      setOpenAddDialog(false);
      resetForm();
      setSnackbar({
        open: true,
        message: "Farm added successfully",
        severity: "success",
      });
    } catch (err) {
      setSnackbar({
        open: true,
        message: "Failed to add farm",
        severity: "error",
      });
      console.error("Error adding farm:", err);
    }
  };

  // Handle update farm
  const handleUpdateFarm = async () => {
    try {
      const updatedFarm = await updateFarm(farmForm.id, {
        ...farmForm,
        size: parseFloat(farmForm.size) || 0,
      });
      setFarms((prev) =>
        prev.map((farm) => (farm.id === updatedFarm.id ? updatedFarm : farm))
      );
      setOpenEditDialog(false);
      resetForm();
      setSnackbar({
        open: true,
        message: "Farm updated successfully",
        severity: "success",
      });
    } catch (err) {
      setSnackbar({
        open: true,
        message: "Failed to update farm",
        severity: "error",
      });
      console.error("Error updating farm:", err);
    }
  };

  // Reset form
  const resetForm = () => {
    setFarmForm({
      name: "",
      location: "",
      size: "",
      type: "",
      description: "",
      soilType: "",
      irrigation: "",
      crops: [],
      owner: "",
      coordinates: { lat: 0, lng: 0 },
      image: "",
    });
  };

  // Handle filter change
  const handleFilterChange = (type, value) => {
    if (type === "status") {
      setFilterStatus(value);
    } else if (type === "type") {
      setFilterType(value);
    }
  };

  // Handle search
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  // Handle sort
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("asc");
    }
  };

  // Handle view mode toggle
  const handleViewModeToggle = () => {
    setViewMode(viewMode === "grid" ? "table" : "grid");
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar((prev) => ({ ...prev, open: false }));
  };

  // Handle back
  const handleBack = () => {
    navigate("/dashboard");
  };

  // Filter and sort farms
  const filteredFarms = farms
    .filter((farm) => {
      // Apply status filter
      if (filterStatus !== "all" && farm.status !== filterStatus) {
        return false;
      }

      // Apply type filter
      if (filterType !== "all" && farm.type !== filterType) {
        return false;
      }

      // Apply search
      if (
        searchTerm &&
        !farm.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !farm.location.toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        return false;
      }

      return true;
    })
    .sort((a, b) => {
      // Apply sorting
      if (sortBy === "name") {
        return sortOrder === "asc"
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      } else if (sortBy === "location") {
        return sortOrder === "asc"
          ? a.location.localeCompare(b.location)
          : b.location.localeCompare(a.location);
      } else if (sortBy === "size") {
        return sortOrder === "asc" ? a.size - b.size : b.size - a.size;
      } else if (sortBy === "updatedAt") {
        return sortOrder === "asc"
          ? new Date(a.updatedAt) - new Date(b.updatedAt)
          : new Date(b.updatedAt) - new Date(a.updatedAt);
      }
      return 0;
    });

  // Render loading state
  if (loading && farms.length === 0) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <Button
            variant="contained"
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
            sx={{
              mr: 2,
              bgcolor: "#2e7d32",
              "&:hover": {
                bgcolor: "#1b5e20",
              },
            }}
          >
            {t("back_to_dash_msg")}
          </Button>
          <Typography variant="h4" component="h1">
            Farm Management
          </Typography>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "50vh",
          }}
        >
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{
            mr: 2,
            bgcolor: "#2e7d32",
            "&:hover": {
              bgcolor: "#1b5e20",
            },
          }}
        >
          {t("back_to_dash_msg")}
        </Button>
        <Typography variant="h4" component="h1">
          {t("farm_manage_msg")}
        </Typography>
        <Box sx={{ flexGrow: 1 }} />
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpenAddDialog(true)}
          sx={{
            bgcolor: "#2e7d32",
            "&:hover": {
              bgcolor: "#1b5e20",
            },
          }}
        >
          {t("add_farm_msg")}
        </Button>
      </Box>

      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Filters and controls */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              placeholder="Search farms..."
              value={searchTerm}
              onChange={handleSearch}
              InputProps={{
                startAdornment: (
                  <SearchIcon sx={{ color: "text.secondary", mr: 1 }} />
                ),
              }}
              size="small"
            />
          </Grid>
          <Grid item xs={6} sm={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Status</InputLabel>
              <Select
                value={filterStatus}
                label="Status"
                onChange={(e) => handleFilterChange("status", e.target.value)}
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="Active">Active</MenuItem>
                <MenuItem value="Inactive">Inactive</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={6} sm={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Type</InputLabel>
              <Select
                value={filterType}
                label="Type"
                onChange={(e) => handleFilterChange("type", e.target.value)}
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="Organic">Organic</MenuItem>
                <MenuItem value="Conventional">Conventional</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={6} sm={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Sort By</InputLabel>
              <Select
                value={sortBy}
                label="Sort By"
                onChange={(e) => handleSort(e.target.value)}
              >
                <MenuItem value="name">Name</MenuItem>
                <MenuItem value="location">Location</MenuItem>
                <MenuItem value="size">Size</MenuItem>
                <MenuItem value="updatedAt">Last Updated</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={6} sm={2}>
            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              <Tooltip
                title={
                  viewMode === "grid"
                    ? "Switch to Table View"
                    : "Switch to Grid View"
                }
              >
                <IconButton onClick={handleViewModeToggle}>
                  {viewMode === "grid" ? <TableIcon /> : <GridViewIcon />}
                </IconButton>
              </Tooltip>
              <Tooltip title="Refresh">
                <IconButton onClick={fetchFarms}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Farm list */}
      <FarmList
        farms={filteredFarms}
        viewMode={viewMode}
        onMenuOpen={handleMenuOpen}
        onView={handleViewFarm}
      />

      {/* Add Farm Dialog */}
      <AddFarmDialog
        open={openAddDialog}
        onClose={() => setOpenAddDialog(false)}
        formData={farmForm}
        onChange={handleInputChange}
        onCoordinatesChange={handleCoordinatesChange}
        onCropsChange={handleCropsChange}
        onSubmit={handleAddFarm}
      />

      {/* Edit Farm Dialog */}
      <EditFarmDialog
        open={openEditDialog}
        onClose={() => setOpenEditDialog(false)}
        formData={farmForm}
        onChange={handleInputChange}
        onCoordinatesChange={handleCoordinatesChange}
        onCropsChange={handleCropsChange}
        onSubmit={handleUpdateFarm}
      />

      {/* View Farm Dialog */}
      <ViewFarmDialog
        open={openViewDialog}
        onClose={() => setOpenViewDialog(false)}
        farm={selectedFarm}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this farm? This action cannot be
            undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Farm Actions Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleViewFarm(selectedFarmId)}>
          <ListItemIcon>
            <VisibilityIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleEditFarm(selectedFarmId)}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit</ListItemText>
        </MenuItem>
        <MenuItem
          onClick={() => {
            handleMenuClose();
            setOpenDeleteDialog(true);
          }}
        >
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Farms;
