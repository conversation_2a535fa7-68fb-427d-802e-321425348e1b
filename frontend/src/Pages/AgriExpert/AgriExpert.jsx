import React, { useState, useEffect } from "react";
import {
  allKVKs,
  allAgriUniversities,
  allResearchInstitutes,
  allAgriExperts,
  indianStates,
  getKVKsByState,
  getAgriUniversitiesByState,
  getResearchInstitutesByState,
  getAgriExpertsByState,
  agriculturalTopics,
} from "../../data/agriExpertData";
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemIcon,
  Avatar,
  TextField,
  IconButton,
  Tabs,
  Tab,
  AppBar,
  Toolbar,
  Chip,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import {
  School as SchoolIcon,
  Science as ScienceIcon,
  Agriculture as AgricultureIcon,
  ArrowBack as ArrowBackIcon,
  Search as SearchIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Star as StarIcon,
  Person as PersonIcon,
  ExpandMore as ExpandMoreIcon,
  FilterList as FilterListIcon,
  Language as LanguageIcon,
  ViewList as ViewListIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

// Tab Panel Component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`agri-expert-tabpanel-${index}`}
      aria-labelledby={`agri-expert-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const AgriExpert = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedState, setSelectedState] = useState("All");
  const [selectedDistrict, setSelectedDistrict] = useState("All");
  const [selectedTopic, setSelectedTopic] = useState("All");
  const [filteredKVKs, setFilteredKVKs] = useState([]);
  const [filteredUniversities, setFilteredUniversities] = useState([]);
  const [filteredInstitutes, setFilteredInstitutes] = useState([]);
  const [filteredExperts, setFilteredExperts] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [showFilters, setShowFilters] = useState(false);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle state change
  const handleStateChange = (event) => {
    const state = event.target.value;
    setSelectedState(state);
    setSelectedDistrict("All");

    // Get districts for the selected state
    if (state !== "All") {
      const stateKVKs = getKVKsByState(state);
      const stateDistricts = [...new Set(stateKVKs.map((kvk) => kvk.district))];
      setDistricts(stateDistricts.sort());
    } else {
      setDistricts([]);
    }
  };

  // Handle district change
  const handleDistrictChange = (event) => {
    setSelectedDistrict(event.target.value);
  };

  // Handle topic change
  const handleTopicChange = (event) => {
    setSelectedTopic(event.target.value);
  };

  // Filter data based on search query and filters
  useEffect(() => {
    // Filter KVKs
    let kvks = allKVKs;
    if (selectedState !== "All") {
      kvks = getKVKsByState(selectedState);
    }
    if (selectedDistrict !== "All") {
      kvks = kvks.filter((kvk) => kvk.district === selectedDistrict);
    }
    if (searchQuery) {
      kvks = kvks.filter(
        (kvk) =>
          kvk.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          kvk.district.toLowerCase().includes(searchQuery.toLowerCase()) ||
          kvk.state.toLowerCase().includes(searchQuery.toLowerCase()) ||
          kvk.services.some((service) =>
            service.toLowerCase().includes(searchQuery.toLowerCase())
          )
      );
    }
    setFilteredKVKs(kvks);

    // Filter Universities
    let universities = allAgriUniversities;
    if (selectedState !== "All") {
      universities = getAgriUniversitiesByState(selectedState);
    }
    if (searchQuery) {
      universities = universities.filter(
        (uni) =>
          uni.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          uni.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
          uni.departments.some((dept) =>
            dept.toLowerCase().includes(searchQuery.toLowerCase())
          ) ||
          uni.researchAreas.some((area) =>
            area.toLowerCase().includes(searchQuery.toLowerCase())
          )
      );
    }
    setFilteredUniversities(universities);

    // Filter Research Institutes
    let institutes = allResearchInstitutes;
    if (selectedState !== "All") {
      institutes = getResearchInstitutesByState(selectedState);
    }
    if (searchQuery) {
      institutes = institutes.filter(
        (inst) =>
          inst.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          inst.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
          inst.focus.some((focus) =>
            focus.toLowerCase().includes(searchQuery.toLowerCase())
          )
      );
    }
    setFilteredInstitutes(institutes);

    // Filter Experts
    let experts = allAgriExperts;
    if (selectedState !== "All") {
      experts = getAgriExpertsByState(selectedState);
    }
    if (selectedTopic !== "All") {
      // This is a simplified filter - in a real app, you'd have topic tags for each expert
      experts = experts.filter((expert) =>
        expert.specialization
          .toLowerCase()
          .includes(selectedTopic.toLowerCase())
      );
    }
    if (searchQuery) {
      experts = experts.filter(
        (expert) =>
          expert.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          expert.designation
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          expert.specialization
            .toLowerCase()
            .includes(searchQuery.toLowerCase())
      );
    }
    setFilteredExperts(experts);
  }, [searchQuery, selectedState, selectedDistrict, selectedTopic]);

  // Initialize data on component mount
  useEffect(() => {
    setFilteredKVKs(allKVKs);
    setFilteredUniversities(allAgriUniversities);
    setFilteredInstitutes(allResearchInstitutes);
    setFilteredExperts(allAgriExperts);
  }, []);

  return (
    <Box sx={{ position: "relative", pb: 4 }}>
      <AppBar
        position="static"
        color="transparent"
        elevation={0}
        sx={{
          borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
          bgcolor: "background.paper",
          mb: 3,
        }}
      >
        <Toolbar variant="dense" sx={{ minHeight: 48 }}>
          <Button
            onClick={() => navigate("/dashboard")}
            startIcon={<ArrowBackIcon fontSize="small" />}
            size="small"
            sx={{
              mr: 2,
              color: "primary.main",
              "&:hover": {
                bgcolor: "primary.light",
                color: "primary.contrastText",
              },
              fontSize: "0.75rem",
              py: 0.5,
              minWidth: "auto",
            }}
          >
            {t("back_to_dash_msg")}
          </Button>
          <Typography variant="h6" component="h1" sx={{ fontWeight: "medium" }}>
            {t("agri_expert_msg")}
          </Typography>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg">
        <Paper sx={{ mb: 3, p: 2 }}>
          <Typography variant="h6" gutterBottom>
            AgriXpert Connect
          </Typography>
          <Typography variant="body1" paragraph>
            Connect with agricultural experts and institutions for advice on
            farming practices, crop management, soil health, and more. Get
            personalized recommendations and stay updated with the latest
            agricultural research and technologies.
          </Typography>

          <Box
            sx={{
              display: "flex",
              mb: 2,
              flexDirection: { xs: "column", sm: "row" },
              gap: 1,
            }}
          >
            <TextField
              fullWidth
              placeholder="Search experts, specializations, or institutions..."
              variant="outlined"
              size="small"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Button
              variant="contained"
              startIcon={<SearchIcon />}
              sx={{
                minWidth: "auto",
                px: 2,
                alignSelf: { xs: "stretch", sm: "auto" },
              }}
            >
              Search
            </Button>
            <Button
              variant="outlined"
              startIcon={<FilterListIcon />}
              onClick={() => setShowFilters(!showFilters)}
              sx={{
                minWidth: "auto",
                px: 2,
                alignSelf: { xs: "stretch", sm: "auto" },
              }}
            >
              Filters
            </Button>
          </Box>

          {showFilters && (
            <Box
              sx={{
                mt: 2,
                mb: 1,
                display: "flex",
                flexDirection: { xs: "column", md: "row" },
                gap: 2,
              }}
            >
              <FormControl size="small" fullWidth>
                <InputLabel id="state-select-label">State</InputLabel>
                <Select
                  labelId="state-select-label"
                  value={selectedState}
                  label="State"
                  onChange={handleStateChange}
                >
                  <MenuItem value="All">All States</MenuItem>
                  {indianStates.map((state) => (
                    <MenuItem key={state.code} value={state.name}>
                      {state.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl
                size="small"
                fullWidth
                disabled={selectedState === "All" || districts.length === 0}
              >
                <InputLabel id="district-select-label">District</InputLabel>
                <Select
                  labelId="district-select-label"
                  value={selectedDistrict}
                  label="District"
                  onChange={handleDistrictChange}
                >
                  <MenuItem value="All">All Districts</MenuItem>
                  {districts.map((district) => (
                    <MenuItem key={district} value={district}>
                      {district}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl size="small" fullWidth>
                <InputLabel id="topic-select-label">Topic</InputLabel>
                <Select
                  labelId="topic-select-label"
                  value={selectedTopic}
                  label="Topic"
                  onChange={handleTopicChange}
                >
                  <MenuItem value="All">All Topics</MenuItem>
                  {agriculturalTopics.map((topic) => (
                    <MenuItem key={topic.id} value={topic.name}>
                      {topic.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          )}

          <Box
            sx={{
              mt: 2,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Typography variant="body2" color="text.secondary">
              {activeTab === 0 && `Showing ${filteredKVKs.length} KVKs`}
              {activeTab === 1 &&
                `Showing ${filteredUniversities.length} institutions`}
              {activeTab === 2 &&
                `Showing ${filteredInstitutes.length} research institutes`}
              {activeTab === 3 && `Showing ${filteredExperts.length} experts`}
            </Typography>

            <Box sx={{ display: "flex", alignItems: "center" }}>
              <LanguageIcon
                fontSize="small"
                sx={{ mr: 1, color: "primary.main" }}
              />
              <Typography variant="body2" color="primary.main">
                All India Coverage
              </Typography>
            </Box>
          </Box>
        </Paper>

        <Paper sx={{ mb: 3 }}>
          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              aria-label="agri expert tabs"
              variant="scrollable"
              scrollButtons="auto"
              allowScrollButtonsMobile
              sx={{
                "& .MuiTab-root": {
                  fontSize: { xs: "0.7rem", sm: "0.8rem", md: "0.875rem" },
                  minWidth: { xs: "auto", sm: 80 },
                  px: { xs: 1, sm: 2 },
                },
              }}
            >
              <Tab icon={<PersonIcon />} label="Experts" />
              <Tab icon={<SchoolIcon />} label="Institutions" />
              <Tab icon={<ScienceIcon />} label="Research" />
              <Tab icon={<AgricultureIcon />} label="Advisory" />
            </Tabs>
          </Box>

          {/* Experts Tab */}
          <TabPanel value={activeTab} index={0}>
            <Typography variant="h6" gutterBottom>
              Agricultural Experts
            </Typography>
            <Grid container spacing={3}>
              {filteredExperts.length > 0 ? (
                filteredExperts.map((expert) => (
                  <Grid item xs={12} sm={6} lg={4} key={expert.id}>
                    <Card
                      variant="outlined"
                      sx={{
                        height: "100%",
                        display: "flex",
                        flexDirection: "column",
                        transition: "transform 0.3s, box-shadow 0.3s",
                        "&:hover": {
                          transform: "translateY(-5px)",
                          boxShadow: "0 8px 16px rgba(0,0,0,0.1)",
                        },
                      }}
                    >
                      <CardContent sx={{ p: 2, pb: 1 }}>
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: { xs: "column", sm: "row" },
                            alignItems: { xs: "center", sm: "flex-start" },
                            mb: 2,
                          }}
                        >
                          <Avatar
                            alt={expert.name}
                            sx={{
                              width: 64,
                              height: 64,
                              mr: { xs: 0, sm: 2 },
                              mb: { xs: 1, sm: 0 },
                              bgcolor: expert.id.includes("1")
                                ? "#4caf50"
                                : expert.id.includes("2")
                                ? "#2196f3"
                                : expert.id.includes("3")
                                ? "#ff9800"
                                : expert.id.includes("4")
                                ? "#9c27b0"
                                : expert.id.includes("5")
                                ? "#f44336"
                                : "#3f51b5",
                            }}
                          >
                            {expert.name.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="h6" component="h3">
                              {expert.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {expert.designation}
                            </Typography>
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                mt: 0.5,
                              }}
                            >
                              <Chip
                                label={expert.specialization}
                                size="small"
                                color="primary"
                                variant="outlined"
                                sx={{ fontSize: "0.7rem" }}
                              />
                            </Box>
                          </Box>
                        </Box>

                        <Divider sx={{ my: 1 }} />

                        <Typography variant="subtitle2" gutterBottom>
                          Achievements
                        </Typography>
                        <List dense disablePadding>
                          {expert.achievements.map((achievement, index) => (
                            <ListItem
                              key={index}
                              disablePadding
                              sx={{ py: 0.5 }}
                            >
                              <ListItemIcon sx={{ minWidth: 30 }}>
                                <StarIcon
                                  sx={{
                                    color: "warning.main",
                                    fontSize: "0.9rem",
                                  }}
                                />
                              </ListItemIcon>
                              <ListItemText
                                primary={achievement}
                                primaryTypographyProps={{ variant: "body2" }}
                              />
                            </ListItem>
                          ))}
                        </List>

                        <Typography
                          variant="subtitle2"
                          gutterBottom
                          sx={{ mt: 1 }}
                        >
                          Contact Information
                        </Typography>
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: 0.5,
                          }}
                        >
                          <PhoneIcon
                            fontSize="small"
                            sx={{ mr: 1, color: "text.secondary" }}
                          />
                          <Typography variant="body2">
                            {expert.contact.phone}
                          </Typography>
                        </Box>
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: 0.5,
                          }}
                        >
                          <EmailIcon
                            fontSize="small"
                            sx={{ mr: 1, color: "text.secondary" }}
                          />
                          <Typography variant="body2">
                            {expert.contact.email}
                          </Typography>
                        </Box>

                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            mt: 2,
                          }}
                        >
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={() => {
                              // Open a modal or dialog with expert details
                              alert(
                                `${expert.name}\n\nSpecialization: ${
                                  expert.specialization
                                }\n\nAchievements:\n- ${expert.achievements.join(
                                  "\n- "
                                )}\n\nContact:\nEmail: ${
                                  expert.contact.email
                                }\nPhone: ${expert.contact.phone}`
                              );
                            }}
                            startIcon={<PersonIcon />}
                          >
                            View Profile
                          </Button>
                          <Button
                            variant="contained"
                            size="small"
                            startIcon={<PhoneIcon />}
                            onClick={() => {
                              alert(
                                `Contact ${expert.name}:\nEmail: ${expert.contact.email}\nPhone: ${expert.contact.phone}`
                              );
                            }}
                          >
                            Contact
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))
              ) : (
                <Grid item xs={12}>
                  <Paper sx={{ p: 3, textAlign: "center" }}>
                    <Typography variant="body1">
                      No experts found matching your search criteria.
                    </Typography>
                  </Paper>
                </Grid>
              )}
            </Grid>
          </TabPanel>

          {/* Institutions Tab */}
          <TabPanel value={activeTab} index={1}>
            <Typography variant="h6" gutterBottom>
              Agricultural Universities & Institutions
            </Typography>
            <Grid container spacing={3}>
              {filteredUniversities.length > 0 ? (
                filteredUniversities.map((institution) => (
                  <Grid item xs={12} sm={6} key={institution.id}>
                    <Card
                      variant="outlined"
                      sx={{
                        height: "100%",
                        display: "flex",
                        flexDirection: "column",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: { xs: "column", sm: "row" },
                            alignItems: { xs: "center", sm: "flex-start" },
                            mb: 2,
                          }}
                        >
                          <Avatar
                            alt={institution.name}
                            sx={{
                              width: 100,
                              height: 100,
                              mr: { xs: 0, sm: 2 },
                              mb: { xs: 1, sm: 0 },
                              bgcolor: institution.id.includes("univ")
                                ? "#4caf50"
                                : institution.id.includes("college")
                                ? "#2196f3"
                                : institution.id.includes("institute")
                                ? "#ff9800"
                                : "#3f51b5",
                              fontSize: "2rem",
                            }}
                          >
                            {institution.name.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="h6" component="h3">
                              {institution.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Established: {institution.established}
                            </Typography>
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                mt: 0.5,
                              }}
                            >
                              <LocationIcon
                                fontSize="small"
                                sx={{ color: "text.secondary", mr: 0.5 }}
                              />
                              <Typography variant="body2">
                                {institution.location}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>

                        <Divider sx={{ my: 1 }} />

                        <Accordion
                          sx={{
                            boxShadow: "none",
                            "&:before": { display: "none" },
                          }}
                        >
                          <AccordionSummary
                            expandIcon={<ExpandMoreIcon />}
                            sx={{ p: 0, minHeight: "auto" }}
                          >
                            <Typography variant="subtitle2">
                              Departments & Research Areas
                            </Typography>
                          </AccordionSummary>
                          <AccordionDetails sx={{ p: 0, pt: 1 }}>
                            <Typography variant="subtitle2" gutterBottom>
                              Departments
                            </Typography>
                            <Box
                              sx={{
                                display: "flex",
                                flexWrap: "wrap",
                                gap: 0.5,
                                mb: 2,
                              }}
                            >
                              {institution.departments.map((dept, index) => (
                                <Chip
                                  key={index}
                                  label={dept}
                                  size="small"
                                  variant="outlined"
                                />
                              ))}
                            </Box>

                            <Typography variant="subtitle2" gutterBottom>
                              Research Areas
                            </Typography>
                            <Box
                              sx={{
                                display: "flex",
                                flexWrap: "wrap",
                                gap: 0.5,
                                mb: 2,
                              }}
                            >
                              {institution.researchAreas.map((area, index) => (
                                <Chip
                                  key={index}
                                  label={area}
                                  size="small"
                                  variant="outlined"
                                />
                              ))}
                            </Box>
                          </AccordionDetails>
                        </Accordion>

                        <Typography variant="subtitle2" gutterBottom>
                          Contact Information
                        </Typography>
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: 0.5,
                          }}
                        >
                          <PhoneIcon
                            fontSize="small"
                            sx={{ mr: 1, color: "text.secondary" }}
                          />
                          <Typography variant="body2">
                            {institution.contact.phone}
                          </Typography>
                        </Box>
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: 0.5,
                          }}
                        >
                          <EmailIcon
                            fontSize="small"
                            sx={{ mr: 1, color: "text.secondary" }}
                          />
                          <Typography variant="body2">
                            {institution.contact.email}
                          </Typography>
                        </Box>

                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            mt: 2,
                          }}
                        >
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<LanguageIcon />}
                            onClick={() => {
                              window.open(institution.website, "_blank");
                            }}
                          >
                            Visit Website
                          </Button>
                          <Button
                            variant="contained"
                            size="small"
                            startIcon={<PhoneIcon />}
                            onClick={() => {
                              alert(
                                `Contact: ${institution.contact.phone}\nEmail: ${institution.contact.email}`
                              );
                            }}
                          >
                            Contact
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))
              ) : (
                <Grid item xs={12}>
                  <Paper sx={{ p: 3, textAlign: "center" }}>
                    <Typography variant="body1">
                      No institutions found matching your search criteria.
                    </Typography>
                  </Paper>
                </Grid>
              )}
            </Grid>
          </TabPanel>

          {/* Research Tab */}
          <TabPanel value={activeTab} index={2}>
            <Typography variant="h6" gutterBottom>
              Agricultural Research Institutes
            </Typography>
            <Grid container spacing={3}>
              {filteredInstitutes.length > 0 ? (
                filteredInstitutes.map((institute) => (
                  <Grid item xs={12} sm={6} key={institute.id}>
                    <Card
                      variant="outlined"
                      sx={{
                        height: "100%",
                        display: "flex",
                        flexDirection: "column",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: { xs: "column", sm: "row" },
                            alignItems: { xs: "center", sm: "flex-start" },
                            mb: 2,
                          }}
                        >
                          <Avatar
                            alt={institute.name}
                            sx={{
                              width: 100,
                              height: 100,
                              mr: { xs: 0, sm: 2 },
                              mb: { xs: 1, sm: 0 },
                              bgcolor: institute.id.includes("icar")
                                ? "#4caf50"
                                : institute.id.includes("csir")
                                ? "#2196f3"
                                : institute.id.includes("icfre")
                                ? "#ff9800"
                                : "#3f51b5",
                              fontSize: "2rem",
                            }}
                          >
                            {institute.name.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="h6" component="h3">
                              {institute.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Established: {institute.established}
                            </Typography>
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                mt: 0.5,
                              }}
                            >
                              <LocationIcon
                                fontSize="small"
                                sx={{ color: "text.secondary", mr: 0.5 }}
                              />
                              <Typography variant="body2">
                                {institute.location}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>

                        <Divider sx={{ my: 1 }} />

                        <Typography variant="subtitle2" gutterBottom>
                          Research Focus Areas
                        </Typography>
                        <Box
                          sx={{
                            display: "flex",
                            flexWrap: "wrap",
                            gap: 0.5,
                            mb: 2,
                          }}
                        >
                          {institute.focus.map((focus, index) => (
                            <Chip
                              key={index}
                              label={focus}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>

                        {institute.achievements && (
                          <>
                            <Typography variant="subtitle2" gutterBottom>
                              Key Achievements
                            </Typography>
                            <List dense disablePadding>
                              {institute.achievements.map(
                                (achievement, index) => (
                                  <ListItem
                                    key={index}
                                    disablePadding
                                    sx={{ py: 0.5 }}
                                  >
                                    <ListItemIcon sx={{ minWidth: 30 }}>
                                      <StarIcon
                                        sx={{
                                          color: "warning.main",
                                          fontSize: "0.9rem",
                                        }}
                                      />
                                    </ListItemIcon>
                                    <ListItemText
                                      primary={achievement}
                                      primaryTypographyProps={{
                                        variant: "body2",
                                      }}
                                    />
                                  </ListItem>
                                )
                              )}
                            </List>
                          </>
                        )}

                        <Typography
                          variant="subtitle2"
                          gutterBottom
                          sx={{ mt: 1 }}
                        >
                          Contact Information
                        </Typography>
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: 0.5,
                          }}
                        >
                          <PhoneIcon
                            fontSize="small"
                            sx={{ mr: 1, color: "text.secondary" }}
                          />
                          <Typography variant="body2">
                            {institute.contact.phone}
                          </Typography>
                        </Box>
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: 0.5,
                          }}
                        >
                          <EmailIcon
                            fontSize="small"
                            sx={{ mr: 1, color: "text.secondary" }}
                          />
                          <Typography variant="body2">
                            {institute.contact.email}
                          </Typography>
                        </Box>

                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            mt: 2,
                          }}
                        >
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<LanguageIcon />}
                            onClick={() => {
                              window.open(institute.website, "_blank");
                            }}
                          >
                            Visit Website
                          </Button>
                          <Button
                            variant="contained"
                            size="small"
                            startIcon={<PhoneIcon />}
                            onClick={() => {
                              alert(
                                `Contact: ${institute.contact.phone}\nEmail: ${institute.contact.email}`
                              );
                            }}
                          >
                            Contact
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))
              ) : (
                <Grid item xs={12}>
                  <Paper sx={{ p: 3, textAlign: "center" }}>
                    <Typography variant="body1">
                      No research institutes found matching your search
                      criteria.
                    </Typography>
                  </Paper>
                </Grid>
              )}
            </Grid>
          </TabPanel>

          {/* Advisory Tab */}
          <TabPanel value={activeTab} index={3}>
            <Typography variant="h6" gutterBottom>
              Agricultural Advisory Topics
            </Typography>
            <Typography variant="body1" paragraph>
              Browse agricultural topics and connect with experts for
              personalized advice on farming practices, crop management, soil
              health, and more.
            </Typography>

            <Grid container spacing={3}>
              {agriculturalTopics.map((topic) => (
                <Grid item xs={12} md={6} key={topic.id}>
                  <Accordion>
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      aria-controls={`${topic.id}-content`}
                      id={`${topic.id}-header`}
                    >
                      <Typography variant="subtitle1">{topic.name}</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Box sx={{ pl: 2 }}>
                        {topic.subtopics.map((subtopic, index) => (
                          <Box
                            key={index}
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              mb: 1,
                            }}
                          >
                            <AgricultureIcon
                              color="primary"
                              fontSize="small"
                              sx={{ mr: 1 }}
                            />
                            <Typography variant="body2">{subtopic}</Typography>
                          </Box>
                        ))}
                      </Box>
                      <Box
                        sx={{
                          mt: 2,
                          display: "flex",
                          justifyContent: "flex-end",
                        }}
                      >
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={() => {
                            // Switch to the Experts tab and set appropriate filters
                            setActiveTab(0);

                            // Map topic names to relevant specialization keywords
                            const topicToSpecialization = {
                              "Crop Management": "Agronomy",
                              "Soil Health": "Soil Science",
                              "Pest Control": "Entomology",
                              "Disease Management": "Plant Pathology",
                              "Water Management": "Irrigation",
                              "Organic Farming": "Organic",
                              "Livestock Management": "Animal Science",
                              "Farm Mechanization": "Agricultural Engineering",
                              "Market Access": "Agricultural Economics",
                              "Climate Resilience": "Climate",
                            };

                            // Set the search query to the mapped specialization or the topic name
                            const searchTerm =
                              topicToSpecialization[topic.name] || topic.name;
                            setSearchQuery(searchTerm);
                          }}
                          startIcon={<PersonIcon />}
                        >
                          Find Experts
                        </Button>
                      </Box>
                    </AccordionDetails>
                  </Accordion>
                </Grid>
              ))}
            </Grid>

            <Box sx={{ mt: 4 }}>
              <Typography variant="h6" gutterBottom>
                Krishi Vigyan Kendras (KVKs)
              </Typography>
              <Typography variant="body1" paragraph>
                Krishi Vigyan Kendras (KVKs) are agricultural extension centers
                created by the Indian Council of Agricultural Research (ICAR) to
                provide various types of farm support to farmers. They serve as
                the ultimate link between the Indian Council of Agricultural
                Research and farmers, and aim to apply agricultural research in
                a practical, localized setting.
              </Typography>

              <Grid container spacing={3}>
                {filteredKVKs.slice(0, 6).map((kvk) => (
                  <Grid item xs={12} sm={6} lg={4} key={kvk.id}>
                    <Card
                      variant="outlined"
                      sx={{
                        transition: "transform 0.3s, box-shadow 0.3s",
                        "&:hover": {
                          transform: "translateY(-5px)",
                          boxShadow: "0 8px 16px rgba(0,0,0,0.1)",
                        },
                      }}
                    >
                      <CardContent>
                        <Typography variant="h6" component="h3" gutterBottom>
                          {kvk.name}
                        </Typography>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 1 }}
                        >
                          <LocationIcon
                            fontSize="small"
                            sx={{ color: "text.secondary", mr: 0.5 }}
                          />
                          <Typography variant="body2">
                            {kvk.district}, {kvk.state}
                          </Typography>
                        </Box>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          gutterBottom
                        >
                          Host: {kvk.hostInstitution}
                        </Typography>
                        <Box
                          sx={{
                            mt: 2,
                            display: "flex",
                            justifyContent: "space-between",
                          }}
                        >
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<PhoneIcon />}
                            onClick={() => {
                              alert(
                                `Contact: ${kvk.contact.phone}\nEmail: ${kvk.contact.email}`
                              );
                            }}
                          >
                            Contact
                          </Button>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<LanguageIcon />}
                            onClick={() => {
                              window.open(kvk.contact.website, "_blank");
                            }}
                          >
                            Website
                          </Button>
                        </Box>

                        <Divider sx={{ my: 1 }} />

                        <Typography variant="subtitle2" gutterBottom>
                          Services
                        </Typography>
                        <Box
                          sx={{
                            display: "flex",
                            flexWrap: "wrap",
                            gap: 0.5,
                            mb: 1,
                          }}
                        >
                          {kvk.services.slice(0, 3).map((service, index) => (
                            <Chip
                              key={index}
                              label={service}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                          {kvk.services.length > 3 && (
                            <Chip
                              label={`+${kvk.services.length - 3} more`}
                              size="small"
                              variant="outlined"
                              color="primary"
                            />
                          )}
                        </Box>

                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "flex-end",
                            mt: 2,
                          }}
                        >
                          <Button variant="outlined" size="small">
                            View Details
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {filteredKVKs.length > 6 && (
                <Box sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
                  <Button
                    variant="contained"
                    onClick={() => {
                      // Scroll to top and show all KVKs
                      window.scrollTo({ top: 0, behavior: "smooth" });
                      // Reset filters but don't show alert to avoid interrupting the user experience
                      setSearchQuery("");
                      setSelectedState("");
                      setSelectedDistrict("");
                    }}
                    startIcon={<ViewListIcon />}
                  >
                    View All {allKVKs.length} KVKs
                  </Button>
                </Box>
              )}
            </Box>
          </TabPanel>
        </Paper>
      </Container>
    </Box>
  );
};

export default AgriExpert;
