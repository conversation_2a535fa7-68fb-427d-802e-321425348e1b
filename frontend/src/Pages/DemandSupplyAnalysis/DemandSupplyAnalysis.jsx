import React, { useState, useEffect } from "react";
import {
  Container,
  Paper,
  Typography,
  Grid,
  Box,
  Card,
  CardContent,
  CircularProgress,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Divider,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  Tooltip,
  IconButton,
  Autocomplete,
} from "@mui/material";
import {
  ArrowBack as ArrowBackIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Info as InfoIcon,
  LocationOn as LocationIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  BarElement,
} from "chart.js";
import { Line, Bar } from "react-chartjs-2";
import {
  getCropDemandSupply,
  getAvailableCrops,
  getAvailableStates,
  getTopCropsByDemand,
  getCropsWithHighestGap,
} from "../../services/demandSupplyService";
import { useTranslation } from "react-i18next";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend
);

const DemandSupplyAnalysis = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [crops, setCrops] = useState([]);
  const [states, setStates] = useState([]);
  const [selectedCrop, setSelectedCrop] = useState("");
  const [selectedState, setSelectedState] = useState("");
  const [analysisData, setAnalysisData] = useState(null);
  const [topCrops, setTopCrops] = useState([]);
  const [gapCrops, setGapCrops] = useState([]);
  const [searchRadius, setSearchRadius] = useState(100);

  // Fetch initial data
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoading(true);
        const [cropsData, statesData] = await Promise.all([
          getAvailableCrops(),
          getAvailableStates(),
        ]);

        setCrops(cropsData);
        setStates(statesData);

        // Set default selections
        setSelectedCrop(cropsData[0]);
        setSelectedState(statesData[12]); // Default to Maharashtra

        setLoading(false);
      } catch (err) {
        setError("Failed to load initial data. Please try again.");
        setLoading(false);
      }
    };

    fetchInitialData();
  }, []);

  // Fetch analysis data when crop or state changes
  useEffect(() => {
    if (selectedCrop && selectedState) {
      fetchAnalysisData();
    }
  }, [selectedCrop, selectedState]);

  // Fetch analysis data
  const fetchAnalysisData = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);

      console.log(`Fetching analysis data with forceRefresh=${forceRefresh}`);

      const [analysisResult, topCropsData, gapCropsData] = await Promise.all([
        getCropDemandSupply(selectedCrop, selectedState, forceRefresh),
        getTopCropsByDemand(selectedState, forceRefresh),
        getCropsWithHighestGap(selectedState, forceRefresh),
      ]);

      setAnalysisData(analysisResult);
      setTopCrops(topCropsData);
      setGapCrops(gapCropsData);

      setLoading(false);

      console.log("Analysis data refreshed successfully");
    } catch (err) {
      console.error("Error fetching analysis data:", err);
      setError("Failed to fetch analysis data. Please try again.");
      setLoading(false);
    }
  };

  const handleCropChange = (event) => {
    setSelectedCrop(event.target.value);
  };

  const handleStateChange = (event) => {
    setSelectedState(event.target.value);
  };

  const handleBack = () => {
    navigate("/dashboard");
  };

  const handleRefresh = () => {
    console.log("Refresh button clicked, forcing data refresh");
    fetchAnalysisData(true);
  };

  // Prepare chart data for historical and prediction data
  const prepareChartData = () => {
    if (!analysisData) return null;

    const combinedData = [
      ...analysisData.historicalData,
      ...analysisData.predictions,
    ];

    return {
      labels: combinedData.map((item) => item.year.toString()),
      datasets: [
        {
          label: "Demand (MT)",
          data: combinedData.map((item) => item.demand),
          borderColor: "rgb(75, 192, 192)",
          backgroundColor: "rgba(75, 192, 192, 0.5)",
          borderWidth: 2,
          tension: 0.1,
          pointStyle: (ctx) => {
            const index = ctx.dataIndex;
            return combinedData[index].isPrediction ? "triangle" : "circle";
          },
          pointRadius: (ctx) => {
            const index = ctx.dataIndex;
            return combinedData[index].isPrediction ? 6 : 4;
          },
        },
        {
          label: "Supply (MT)",
          data: combinedData.map((item) => item.supply),
          borderColor: "rgb(255, 99, 132)",
          backgroundColor: "rgba(255, 99, 132, 0.5)",
          borderWidth: 2,
          tension: 0.1,
          pointStyle: (ctx) => {
            const index = ctx.dataIndex;
            return combinedData[index].isPrediction ? "triangle" : "circle";
          },
          pointRadius: (ctx) => {
            const index = ctx.dataIndex;
            return combinedData[index].isPrediction ? 6 : 4;
          },
        },
      ],
    };
  };

  // Prepare price chart data
  const preparePriceChartData = () => {
    if (!analysisData) return null;

    const combinedData = [
      ...analysisData.historicalData,
      ...analysisData.predictions,
    ];

    return {
      labels: combinedData.map((item) => item.year.toString()),
      datasets: [
        {
          label: "Price (₹/kg)",
          data: combinedData.map((item) => item.price),
          borderColor: "rgb(153, 102, 255)",
          backgroundColor: "rgba(153, 102, 255, 0.5)",
          borderWidth: 2,
          tension: 0.1,
          yAxisID: "y",
          pointStyle: (ctx) => {
            const index = ctx.dataIndex;
            return combinedData[index].isPrediction ? "triangle" : "circle";
          },
          pointRadius: (ctx) => {
            const index = ctx.dataIndex;
            return combinedData[index].isPrediction ? 6 : 4;
          },
        },
        {
          label: "Supply/Demand Ratio",
          data: combinedData.map((item) => parseFloat(item.supplyDemandRatio)),
          borderColor: "rgb(255, 159, 64)",
          backgroundColor: "rgba(255, 159, 64, 0.5)",
          borderWidth: 2,
          tension: 0.1,
          yAxisID: "y1",
          pointStyle: (ctx) => {
            const index = ctx.dataIndex;
            return combinedData[index].isPrediction ? "triangle" : "circle";
          },
          pointRadius: (ctx) => {
            const index = ctx.dataIndex;
            return combinedData[index].isPrediction ? 6 : 4;
          },
        },
      ],
    };
  };

  // Prepare regional comparison chart data
  const prepareRegionalChartData = () => {
    if (!analysisData || !analysisData.regionalData) return null;

    return {
      labels: analysisData.regionalData.map((item) => item.state),
      datasets: [
        {
          label: "Demand (MT)",
          data: analysisData.regionalData.map((item) => item.demand),
          backgroundColor: "rgba(75, 192, 192, 0.7)",
        },
        {
          label: "Supply (MT)",
          data: analysisData.regionalData.map((item) => item.supply),
          backgroundColor: "rgba(255, 99, 132, 0.7)",
        },
      ],
    };
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Demand and Supply Trends",
        font: {
          size: 16,
        },
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            const label = context.dataset.label || "";
            const value = context.parsed.y;
            const dataIndex = context.dataIndex;
            const datasetIndex = context.datasetIndex;

            const combinedData = [
              ...analysisData.historicalData,
              ...analysisData.predictions,
            ];
            const isPrediction = combinedData[dataIndex].isPrediction;

            return isPrediction
              ? `${label}: ${value.toLocaleString()} (Predicted)`
              : `${label}: ${value.toLocaleString()}`;
          },
        },
      },
    },
    scales: {
      x: {
        title: {
          display: true,
          text: "Year",
        },
      },
      y: {
        title: {
          display: true,
          text: "Metric Tons (MT)",
        },
        beginAtZero: false,
      },
    },
  };

  // Price chart options
  const priceChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Price Trends and Supply/Demand Ratio",
        font: {
          size: 16,
        },
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            const label = context.dataset.label || "";
            const value = context.parsed.y;
            const dataIndex = context.dataIndex;

            const combinedData = [
              ...analysisData.historicalData,
              ...analysisData.predictions,
            ];
            const isPrediction = combinedData[dataIndex].isPrediction;

            return isPrediction
              ? `${label}: ${value} (Predicted)`
              : `${label}: ${value}`;
          },
        },
      },
    },
    scales: {
      x: {
        title: {
          display: true,
          text: "Year",
        },
      },
      y: {
        type: "linear",
        display: true,
        position: "left",
        title: {
          display: true,
          text: "Price (₹/kg)",
        },
      },
      y1: {
        type: "linear",
        display: true,
        position: "right",
        title: {
          display: true,
          text: "Supply/Demand Ratio",
        },
        grid: {
          drawOnChartArea: false,
        },
        min: 0,
        max: 1.5,
      },
    },
  };

  // Regional chart options
  const regionalChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: `Regional Comparison (${searchRadius}km Radius)`,
        font: {
          size: 16,
        },
      },
    },
    scales: {
      x: {
        title: {
          display: true,
          text: "States",
        },
      },
      y: {
        title: {
          display: true,
          text: "Metric Tons (MT)",
        },
        beginAtZero: false,
      },
    },
  };

  // Format large numbers
  const formatNumber = (num) => {
    return new Intl.NumberFormat("en-IN").format(num);
  };

  // Get trend icon and color
  const getTrendIcon = (value) => {
    if (value > 0) {
      return <TrendingUpIcon sx={{ color: "success.main" }} />;
    } else if (value < 0) {
      return <TrendingDownIcon sx={{ color: "error.main" }} />;
    }
    return null;
  };

  // Loading state
  if (loading && !analysisData) {
    return (
      <Container
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "80vh",
        }}
      >
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{
            mb: 3,
            alignSelf: "flex-start",
            bgcolor: "#2e7d32",
            "&:hover": {
              bgcolor: "#1b5e20",
            },
          }}
        >
          Back to Dashboard
        </Button>
        <CircularProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading demand and supply data...
        </Typography>
      </Container>
    );
  }

  // Error state
  if (error && !analysisData) {
    return (
      <Container>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{
            mt: 2,
            mb: 2,
            bgcolor: "#2e7d32",
            "&:hover": {
              bgcolor: "#1b5e20",
            },
          }}
        >
          Back to Dashboard
        </Button>
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
        <Button
          variant="outlined"
          onClick={() => fetchAnalysisData(true)}
          startIcon={<RefreshIcon />}
          sx={{ mt: 2 }}
        >
          Retry
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{
            mr: 2,
            bgcolor: "#2e7d32",
            "&:hover": {
              bgcolor: "#1b5e20",
            },
          }}
        >
          {t("back_to_dash_msg")}
        </Button>
        <Typography variant="h4" component="h1">
          {t("crop_demand_msg")}
        </Typography>
      </Box>

      {/* Selection Controls */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel id="crop-select-label">Crop</InputLabel>
              <Select
                labelId="crop-select-label"
                id="crop-select"
                value={selectedCrop}
                label="Crop"
                onChange={handleCropChange}
              >
                {crops.map((crop) => (
                  <MenuItem key={crop} value={crop}>
                    {crop}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel id="state-select-label">State</InputLabel>
              <Select
                labelId="state-select-label"
                id="state-select"
                value={selectedState}
                label="State"
                onChange={handleStateChange}
              >
                {states.map((state) => (
                  <MenuItem key={state} value={state}>
                    {state}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
                disabled={loading}
              >
                {loading ? "Updating..." : "Refresh Data"}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {loading && (
        <Box sx={{ display: "flex", justifyContent: "center", my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {analysisData && (
        <>
          {/* Summary Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Current Demand
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography variant="h5" component="div">
                      {formatNumber(
                        analysisData.historicalData[
                          analysisData.historicalData.length - 1
                        ].demand
                      )}{" "}
                      MT
                    </Typography>
                    <Tooltip
                      title={`${analysisData.marketInsights.demandGrowth}% projected growth`}
                    >
                      <Box
                        sx={{ ml: 1, display: "flex", alignItems: "center" }}
                      >
                        {getTrendIcon(analysisData.marketInsights.demandGrowth)}
                        <Typography
                          variant="body2"
                          color={
                            analysisData.marketInsights.demandGrowth > 0
                              ? "success.main"
                              : "error.main"
                          }
                        >
                          {analysisData.marketInsights.demandGrowth}%
                        </Typography>
                      </Box>
                    </Tooltip>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Current Supply
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography variant="h5" component="div">
                      {formatNumber(
                        analysisData.historicalData[
                          analysisData.historicalData.length - 1
                        ].supply
                      )}{" "}
                      MT
                    </Typography>
                    <Tooltip
                      title={`${analysisData.marketInsights.supplyGrowth}% projected growth`}
                    >
                      <Box
                        sx={{ ml: 1, display: "flex", alignItems: "center" }}
                      >
                        {getTrendIcon(analysisData.marketInsights.supplyGrowth)}
                        <Typography
                          variant="body2"
                          color={
                            analysisData.marketInsights.supplyGrowth > 0
                              ? "success.main"
                              : "error.main"
                          }
                        >
                          {analysisData.marketInsights.supplyGrowth}%
                        </Typography>
                      </Box>
                    </Tooltip>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Current Price
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography variant="h5" component="div">
                      ₹
                      {
                        analysisData.historicalData[
                          analysisData.historicalData.length - 1
                        ].price
                      }
                      /kg
                    </Typography>
                    <Tooltip
                      title={`${analysisData.marketInsights.priceGrowth}% projected change`}
                    >
                      <Box
                        sx={{ ml: 1, display: "flex", alignItems: "center" }}
                      >
                        {getTrendIcon(analysisData.marketInsights.priceGrowth)}
                        <Typography
                          variant="body2"
                          color={
                            analysisData.marketInsights.priceGrowth > 0
                              ? "success.main"
                              : "error.main"
                          }
                        >
                          {analysisData.marketInsights.priceGrowth}%
                        </Typography>
                      </Box>
                    </Tooltip>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Market Potential
                  </Typography>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography variant="h5" component="div">
                      {analysisData.marketInsights.futurePotential}
                    </Typography>
                    <Tooltip title="Based on demand-supply gap and price trends">
                      <IconButton size="small">
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mt: 1 }}
                  >
                    Gap: {formatNumber(analysisData.marketInsights.marketGap)}{" "}
                    MT
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Main Charts */}
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Paper sx={{ p: 3, mb: 4 }}>
                <Line data={prepareChartData()} options={chartOptions} />
                <Box
                  sx={{
                    mt: 2,
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Chip
                    icon={<InfoIcon />}
                    label="Historical Data"
                    variant="outlined"
                    size="small"
                    sx={{ mr: 1 }}
                  />
                  <Box
                    sx={{
                      width: 20,
                      height: 2,
                      backgroundColor: "#ccc",
                      mx: 1,
                    }}
                  />
                  <Chip
                    icon={<InfoIcon />}
                    label="Predicted Data"
                    variant="outlined"
                    size="small"
                    sx={{ ml: 1 }}
                  />
                </Box>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, mb: 4, height: "100%" }}>
                <Line
                  data={preparePriceChartData()}
                  options={priceChartOptions}
                />
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, mb: 4, height: "100%" }}>
                <Bar
                  data={prepareRegionalChartData()}
                  options={regionalChartOptions}
                />
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ display: "block", mt: 2, textAlign: "center" }}
                >
                  Comparison of {selectedCrop} demand and supply in{" "}
                  {selectedState} and nearby states
                </Typography>
              </Paper>
            </Grid>
          </Grid>

          {/* Market Insights */}
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h5" gutterBottom>
              Market Insights & Recommendations
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Market Analysis
                </Typography>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body1" paragraph>
                    <strong>Demand-Supply Gap:</strong> There is currently a gap
                    of {formatNumber(analysisData.marketInsights.marketGap)}{" "}
                    metric tons between demand and supply of {selectedCrop} in{" "}
                    {selectedState}.
                  </Typography>

                  <Typography variant="body1" paragraph>
                    <strong>Price Trend:</strong> Prices are projected to
                    {analysisData.marketInsights.priceGrowth > 0
                      ? " increase"
                      : " decrease"}{" "}
                    by
                    {Math.abs(analysisData.marketInsights.priceGrowth)}% over
                    the next 3 years.
                  </Typography>

                  <Typography variant="body1">
                    <strong>Regional Context:</strong> {selectedState} ranks
                    {analysisData.regionalData
                      .sort((a, b) => b.demand - a.demand)
                      .findIndex((item) => item.state === selectedState) + 1}
                    in demand for {selectedCrop} among nearby states.
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Recommendation
                </Typography>

                <Alert
                  severity={
                    analysisData.marketInsights.futurePotential === "High"
                      ? "success"
                      : analysisData.marketInsights.futurePotential ===
                        "Moderate"
                      ? "info"
                      : "warning"
                  }
                  sx={{ mb: 2 }}
                >
                  <Typography variant="body1">
                    {analysisData.marketInsights.recommendation}
                  </Typography>
                </Alert>

                <Box sx={{ mt: 3 }}>
                  <Typography variant="body2" color="text.secondary">
                    Data Source: {analysisData.dataSource}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Last updated: {new Date().toLocaleDateString()}{" "}
                    {new Date().toLocaleTimeString()}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>

          {/* Additional Data Tables */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, mb: 4 }}>
                <Typography variant="h6" gutterBottom>
                  Top Crops by Demand in {selectedState}
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Crop</TableCell>
                        <TableCell align="right">Demand (MT)</TableCell>
                        <TableCell align="right">Supply (MT)</TableCell>
                        <TableCell align="right">Gap (MT)</TableCell>
                        <TableCell align="right">Price (₹/kg)</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {topCrops.map((crop) => (
                        <TableRow
                          key={crop.crop}
                          sx={{
                            cursor: "pointer",
                            "&:hover": {
                              backgroundColor: "rgba(0, 0, 0, 0.04)",
                            },
                            backgroundColor:
                              crop.crop === selectedCrop
                                ? "rgba(46, 125, 50, 0.1)"
                                : "inherit",
                          }}
                          onClick={() => setSelectedCrop(crop.crop)}
                        >
                          <TableCell component="th" scope="row">
                            {crop.crop}
                          </TableCell>
                          <TableCell align="right">
                            {formatNumber(crop.demand)}
                          </TableCell>
                          <TableCell align="right">
                            {formatNumber(crop.supply)}
                          </TableCell>
                          <TableCell align="right">
                            {formatNumber(crop.gap)}
                          </TableCell>
                          <TableCell align="right">₹{crop.price}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, mb: 4 }}>
                <Typography variant="h6" gutterBottom>
                  Crops with Highest Demand-Supply Gap
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Crop</TableCell>
                        <TableCell align="right">Demand (MT)</TableCell>
                        <TableCell align="right">Supply (MT)</TableCell>
                        <TableCell align="right">Gap (MT)</TableCell>
                        <TableCell align="right">Gap %</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {gapCrops.map((crop) => (
                        <TableRow
                          key={crop.crop}
                          sx={{
                            cursor: "pointer",
                            "&:hover": {
                              backgroundColor: "rgba(0, 0, 0, 0.04)",
                            },
                            backgroundColor:
                              crop.crop === selectedCrop
                                ? "rgba(46, 125, 50, 0.1)"
                                : "inherit",
                          }}
                          onClick={() => setSelectedCrop(crop.crop)}
                        >
                          <TableCell component="th" scope="row">
                            {crop.crop}
                          </TableCell>
                          <TableCell align="right">
                            {formatNumber(crop.demand)}
                          </TableCell>
                          <TableCell align="right">
                            {formatNumber(crop.supply)}
                          </TableCell>
                          <TableCell align="right">
                            {formatNumber(crop.gap)}
                          </TableCell>
                          <TableCell align="right">
                            {crop.gapPercentage}%
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Grid>
          </Grid>
        </>
      )}
    </Container>
  );
};

export default DemandSupplyAnalysis;
