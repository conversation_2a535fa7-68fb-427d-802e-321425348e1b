import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardActions,
  Chip,
  Grid,
  Typography,
  <PERSON>vider,
  <PERSON>ton,
  Collapse,
  IconButton,
  Tooltip,
  LinearProgress
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Info as InfoIcon,
  Agriculture as AgricultureIcon,
  LocalShipping as LocalShippingIcon,
  Inventory as InventoryIcon,
  AttachMoney as AttachMoneyIcon,
  Public as PublicIcon,
  CalendarMonth as CalendarMonthIcon,
  Grass as GrassIcon,
  Spa as SpaIcon,
  Grain as GrainIcon,
  LocalFlorist as LocalFloristIcon,
  Nature as EcoIcon,
  Opacity as OpacityIcon,
  FilterVintage as FilterVintageIcon
} from '@mui/icons-material';

/**
 * ExternalDataCard component for displaying external data in the Demand and Supply Analysis
 *
 * @param {Object} props - Component props
 * @param {Object} props.item - The data item to display
 * @returns {JSX.Element} - Rendered component
 */
const ExternalDataCard = ({ item }) => {
  // State for expanded view
  const [expanded, setExpanded] = useState(false);

  // Toggle expanded view
  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  // Calculate gap between demand and supply
  const gap = item.demandQuantity && item.supplyQuantity
    ? item.demandQuantity - item.supplyQuantity
    : null;

  // Determine if there's a deficit or surplus
  const hasDeficit = gap > 0;

  // Calculate demand-supply ratio
  const demandSupplyRatio = item.demandQuantity && item.supplyQuantity
    ? (item.demandQuantity / item.supplyQuantity).toFixed(2)
    : null;

  // Format large numbers
  const formatNumber = (num) => {
    if (!num && num !== 0) return 'N/A';

    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(2)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(2)}K`;
    }

    return num.toLocaleString();
  };

  // Get crop-specific icon
  const getCropIcon = (cropName, category) => {
    if (!cropName) return <AgricultureIcon />;

    const name = cropName.toLowerCase();

    // Grain crops
    if (name.includes('rice') || name.includes('wheat') || name.includes('barley') ||
        name.includes('oat') || name.includes('maize') || name.includes('corn')) {
      return <GrainIcon />;
    }

    // Vegetables
    if (name.includes('potato') || name.includes('tomato') || name.includes('onion') ||
        name.includes('carrot') || name.includes('cabbage') || name.includes('cauliflower') ||
        category?.toLowerCase() === 'vegetables') {
      return <LocalFloristIcon />;
    }

    // Fruits
    if (name.includes('apple') || name.includes('orange') || name.includes('banana') ||
        name.includes('mango') || name.includes('grape') || name.includes('watermelon') ||
        category?.toLowerCase() === 'fruits') {
      return <FilterVintageIcon />;
    }

    // Pulses
    if (name.includes('pulse') || name.includes('lentil') || name.includes('bean') ||
        name.includes('pea') || name.includes('gram') || name.includes('dal')) {
      return <EcoIcon />;
    }

    // Oilseeds
    if (name.includes('oil') || name.includes('seed') || name.includes('mustard') ||
        name.includes('sunflower') || name.includes('groundnut') || name.includes('soybean')) {
      return <OpacityIcon />;
    }

    // Spices
    if (name.includes('spice') || name.includes('turmeric') || name.includes('chilli') ||
        name.includes('pepper') || name.includes('cardamom') || name.includes('cinnamon')) {
      return <SpaIcon />;
    }

    // Cash crops
    if (name.includes('cotton') || name.includes('sugarcane') || name.includes('jute') ||
        name.includes('tobacco')) {
      return <GrassIcon />;
    }

    // Default
    return <AgricultureIcon />;
  };

  // Get market trend color and icon
  const getTrendInfo = (trend) => {
    if (!trend) return { icon: <TrendingFlatIcon color="info" />, color: 'info.main', text: 'Unknown' };

    switch (trend.toLowerCase()) {
      case 'rising':
        return { icon: <TrendingUpIcon color="error" />, color: 'error.main', text: 'Rising' };
      case 'falling':
        return { icon: <TrendingDownIcon color="success" />, color: 'success.main', text: 'Falling' };
      default:
        return { icon: <TrendingFlatIcon color="info" />, color: 'info.main', text: 'Stable' };
    }
  };

  // Get trend info
  const trendInfo = getTrendInfo(item.marketTrend);

  // Get crop icon
  const cropIcon = getCropIcon(item.name, item.category);

  // Calculate market balance
  const getMarketBalance = () => {
    if (!demandSupplyRatio) return { text: 'Unknown', color: 'text.secondary' };

    const ratio = parseFloat(demandSupplyRatio);

    if (ratio > 1.1) return { text: 'High Demand', color: 'error.main' };
    if (ratio < 0.9) return { text: 'Oversupply', color: 'success.main' };
    return { text: 'Balanced', color: 'info.main' };
  };

  const marketBalance = getMarketBalance();

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        {/* Header with Title and Icon */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'rgba(46, 125, 50, 0.1)',
              borderRadius: '50%',
              width: 40,
              height: 40,
              mr: 1
            }}>
              {React.cloneElement(cropIcon, { color: "primary", sx: { fontSize: 24 } })}
            </Box>
            <Typography variant="h6" component="div">
              {item.name}
            </Typography>
          </Box>

          {/* Market Trend Indicator */}
          {item.marketTrend && (
            <Tooltip title={`Market Trend: ${trendInfo.text}`}>
              <Chip
                icon={trendInfo.icon}
                label={trendInfo.text}
                size="small"
                sx={{
                  color: trendInfo.color,
                  borderColor: trendInfo.color,
                  fontWeight: 'bold'
                }}
                variant="outlined"
              />
            </Tooltip>
          )}
        </Box>

        {/* Description */}
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {item.description || 'No description available'}
        </Typography>

        {/* Tags */}
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          {item.region && (
            <Chip
              icon={<PublicIcon fontSize="small" />}
              label={item.region}
              size="small"
              color="primary"
              variant="outlined"
            />
          )}

          {item.category && (
            <Chip
              icon={<InventoryIcon fontSize="small" />}
              label={item.category}
              size="small"
              color="secondary"
              variant="outlined"
            />
          )}

          {item.season && (
            <Chip
              icon={<CalendarMonthIcon fontSize="small" />}
              label={item.season}
              size="small"
              color="info"
              variant="outlined"
            />
          )}
        </Box>

        {/* Key Metrics */}
        <Grid container spacing={2}>
          {/* Price */}
          {item.price && (
            <Grid item xs={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                <AttachMoneyIcon fontSize="small" color="primary" sx={{ mr: 0.5 }} />
                <Typography variant="body2" color="text.secondary">Current Price</Typography>
              </Box>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                ₹{item.price.toFixed(2)}/kg
              </Typography>

              {/* Price Projection */}
              {item.priceProjection && (
                <Typography variant="caption" sx={{ color: trendInfo.color }}>
                  Projected: ₹{item.priceProjection.toFixed(2)}/kg
                </Typography>
              )}
            </Grid>
          )}

          {/* Market Balance */}
          {demandSupplyRatio && (
            <Grid item xs={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                <InfoIcon fontSize="small" color="primary" sx={{ mr: 0.5 }} />
                <Typography variant="body2" color="text.secondary">Market Balance</Typography>
              </Box>
              <Typography variant="h6" sx={{ fontWeight: 'bold', color: marketBalance.color }}>
                {marketBalance.text}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                D/S Ratio: {demandSupplyRatio}
              </Typography>
            </Grid>
          )}

          {/* Demand */}
          {item.demandQuantity && (
            <Grid item xs={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                <LocalShippingIcon fontSize="small" color="primary" sx={{ mr: 0.5 }} />
                <Typography variant="body2" color="text.secondary">Demand</Typography>
              </Box>
              <Typography variant="body1" fontWeight="bold">
                {formatNumber(item.demandQuantity)} MT
              </Typography>
            </Grid>
          )}

          {/* Supply */}
          {item.supplyQuantity && (
            <Grid item xs={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                <InventoryIcon fontSize="small" color="primary" sx={{ mr: 0.5 }} />
                <Typography variant="body2" color="text.secondary">Supply</Typography>
              </Box>
              <Typography variant="body1" fontWeight="bold">
                {formatNumber(item.supplyQuantity)} MT
              </Typography>
            </Grid>
          )}

          {/* Gap Visualization */}
          {gap !== null && (
            <Grid item xs={12}>
              <Box sx={{ mb: 0.5, display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2" color="text.secondary">
                  Supply-Demand Gap:
                </Typography>
                <Typography
                  variant="body2"
                  fontWeight="bold"
                  color={hasDeficit ? 'error.main' : 'success.main'}
                >
                  {hasDeficit ? 'Deficit' : 'Surplus'}: {formatNumber(Math.abs(gap))} MT
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={50 + (hasDeficit ? -1 : 1) * Math.min(Math.abs(gap) / Math.max(item.demandQuantity, item.supplyQuantity) * 50, 50)}
                color={hasDeficit ? "error" : "success"}
                sx={{ height: 8, borderRadius: 4 }}
              />
            </Grid>
          )}
        </Grid>
      </CardContent>

      {/* Expandable Section */}
      <CardActions sx={{ justifyContent: 'space-between', px: 2, pt: 0 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {item.source && (
            <Typography variant="caption" color="text.secondary">
              Source: {item.source}
            </Typography>
          )}
        </Box>
        <Button
          size="small"
          onClick={handleExpandClick}
          endIcon={expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        >
          {expanded ? 'Less' : 'More'} Details
        </Button>
      </CardActions>

      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <CardContent sx={{ pt: 0 }}>
          <Divider sx={{ mb: 2 }} />

          <Grid container spacing={2}>
            {/* Production Details */}
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold' }}>
                Production Details
              </Typography>
            </Grid>

            {item.yieldPerHectare && (
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Yield per Hectare:</Typography>
                <Typography variant="body1" fontWeight="medium">{item.yieldPerHectare}</Typography>
              </Grid>
            )}

            {item.totalCultivationArea && (
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Cultivation Area:</Typography>
                <Typography variant="body1" fontWeight="medium">{item.totalCultivationArea}</Typography>
              </Grid>
            )}

            {item.majorVarieties && (
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">Major Varieties:</Typography>
                <Typography variant="body1" fontWeight="medium">{item.majorVarieties}</Typography>
              </Grid>
            )}

            {/* Market Details */}
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold', mt: 1 }}>
                Market Details
              </Typography>
            </Grid>

            {item.exportQuantity && (
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Export Quantity:</Typography>
                <Typography variant="body1" fontWeight="medium">{formatNumber(item.exportQuantity)} MT</Typography>
              </Grid>
            )}

            {item.domesticConsumption && (
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Domestic Consumption:</Typography>
                <Typography variant="body1" fontWeight="medium">{formatNumber(item.domesticConsumption)} MT</Typography>
              </Grid>
            )}

            {/* Price History */}
            {item.priceHistory && item.priceHistory.length > 0 && (
              <>
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary">Price History:</Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1, mb: 1 }}>
                    {item.priceHistory.map((historyItem, index) => (
                      <Box key={index} sx={{ textAlign: 'center' }}>
                        <Typography variant="caption" color="text.secondary">
                          {historyItem.date.split('-')[1]}/{historyItem.date.split('-')[0].substring(2)}
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          ₹{historyItem.price}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Grid>
              </>
            )}

            {/* Price Forecast */}
            {item.priceForecast && item.priceForecast.length > 0 && (
              <>
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary">Price Forecast:</Typography>
                  <Box sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    mt: 1,
                    mb: 1,
                    backgroundColor: 'rgba(46, 125, 50, 0.05)',
                    p: 1,
                    borderRadius: 1
                  }}>
                    {item.priceForecast.map((forecastItem, index) => (
                      <Box key={index} sx={{ textAlign: 'center' }}>
                        <Typography variant="caption" color="text.secondary">
                          {forecastItem.date.split('-')[1]}/{forecastItem.date.split('-')[0].substring(2)}
                        </Typography>
                        <Typography variant="body2" fontWeight="medium" color="primary.main">
                          ₹{forecastItem.price}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Grid>
              </>
            )}

            {/* Nutritional Value */}
            {item.nutritionalValue && (
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">Nutritional Value:</Typography>
                <Typography variant="body1" fontWeight="medium">{item.nutritionalValue}</Typography>
              </Grid>
            )}

            {/* Quality Parameters */}
            {item.qualityParameters && (
              <>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold', mt: 1 }}>
                    Quality Parameters
                  </Typography>
                </Grid>

                {item.qualityParameters.moisture && (
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">Moisture:</Typography>
                    <Typography variant="body1" fontWeight="medium">{item.qualityParameters.moisture}</Typography>
                  </Grid>
                )}

                {item.qualityParameters.foreignMatter && (
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">Foreign Matter:</Typography>
                    <Typography variant="body1" fontWeight="medium">{item.qualityParameters.foreignMatter}</Typography>
                  </Grid>
                )}

                {item.qualityParameters.brokenGrains && (
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">Broken Grains:</Typography>
                    <Typography variant="body1" fontWeight="medium">{item.qualityParameters.brokenGrains}</Typography>
                  </Grid>
                )}

                {item.qualityParameters.grade && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">Grade:</Typography>
                    <Typography variant="body1" fontWeight="medium">{item.qualityParameters.grade}</Typography>
                  </Grid>
                )}
              </>
            )}

            {/* Weather Conditions */}
            {item.weatherConditions && (
              <>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold', mt: 1 }}>
                    Weather Conditions
                  </Typography>
                </Grid>

                {item.weatherConditions.temperature && (
                  <Grid item xs={3}>
                    <Typography variant="body2" color="text.secondary">Temperature:</Typography>
                    <Typography variant="body1" fontWeight="medium">{item.weatherConditions.temperature}°C</Typography>
                  </Grid>
                )}

                {item.weatherConditions.humidity && (
                  <Grid item xs={3}>
                    <Typography variant="body2" color="text.secondary">Humidity:</Typography>
                    <Typography variant="body1" fontWeight="medium">{item.weatherConditions.humidity}%</Typography>
                  </Grid>
                )}

                {item.weatherConditions.rainfall && (
                  <Grid item xs={3}>
                    <Typography variant="body2" color="text.secondary">Rainfall:</Typography>
                    <Typography variant="body1" fontWeight="medium">{item.weatherConditions.rainfall} mm</Typography>
                  </Grid>
                )}

                {item.weatherConditions.soilMoisture && (
                  <Grid item xs={3}>
                    <Typography variant="body2" color="text.secondary">Soil Moisture:</Typography>
                    <Typography variant="body1" fontWeight="medium">{item.weatherConditions.soilMoisture}%</Typography>
                  </Grid>
                )}
              </>
            )}

            {/* State Agricultural Data */}
            {item.stateAgriData && Object.values(item.stateAgriData).some(value => value) && (
              <>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold', mt: 1 }}>
                    {item.region} Agricultural Data
                  </Typography>
                </Grid>

                {item.stateAgriData.majorCrops && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">Major Crops:</Typography>
                    <Typography variant="body1" fontWeight="medium">{item.stateAgriData.majorCrops}</Typography>
                  </Grid>
                )}

                {item.stateAgriData.totalAgriculturalArea && (
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Total Agricultural Area:</Typography>
                    <Typography variant="body1" fontWeight="medium">{item.stateAgriData.totalAgriculturalArea}</Typography>
                  </Grid>
                )}

                {item.stateAgriData.irrigatedArea && (
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Irrigated Area:</Typography>
                    <Typography variant="body1" fontWeight="medium">{item.stateAgriData.irrigatedArea}</Typography>
                  </Grid>
                )}

                {item.stateAgriData.majorMarkets && (
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Major Markets:</Typography>
                    <Typography variant="body1" fontWeight="medium">{item.stateAgriData.majorMarkets}</Typography>
                  </Grid>
                )}

                {item.stateAgriData.farmersPopulation && (
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Farmers Population:</Typography>
                    <Typography variant="body1" fontWeight="medium">{item.stateAgriData.farmersPopulation}</Typography>
                  </Grid>
                )}
              </>
            )}

            {/* Last Updated */}
            {item.lastUpdated && (
              <Grid item xs={12}>
                <Divider sx={{ my: 1 }} />
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                  Last updated: {item.lastUpdated}
                </Typography>
              </Grid>
            )}
          </Grid>
        </CardContent>
      </Collapse>
    </Card>
  );
};

export default ExternalDataCard;
