# Frontend Environment Variables
# Copy this file to .env and update the values

# API Configuration
REACT_APP_API_URL=http://localhost:8000/api

# Weather API Configuration
REACT_APP_WEATHER_API_KEY=your_weather_api_key_here

# Feature Flags
REACT_APP_ENABLE_AI_ANALYSIS=true
REACT_APP_ENABLE_WEATHER_FORECAST=true
REACT_APP_ENABLE_SOIL_ANALYSIS=true

# Analytics (Optional)
REACT_APP_GOOGLE_ANALYTICS_ID=your_ga_id_here

# Theme Configuration
REACT_APP_THEME=dark
REACT_APP_PRIMARY_COLOR=#4CAF50

# Cache Configuration
REACT_APP_CACHE_DURATION=3600 # in seconds 

VITE_AZURE_TRANSLATION_API_KEY=9DGIQrRiywiKFxqm1utRrYcDY7lSMtLDv35VKWGjkoZeztk0yHWIJQQJ99BEACGhslBXJ3w3AAAbACOGB8ph
VITE_AZURE_TRANSLATION_REGION=centralindia

REACT_APP_MONGODB_URI=**************************************************************************************************************************************************************************************************************************