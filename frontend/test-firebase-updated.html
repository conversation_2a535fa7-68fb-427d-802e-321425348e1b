<!DOCTYPE html>
<html>
<head>
  <title>Firebase Phone Authentication Test (Updated)</title>
  <script src="https://www.gstatic.com/firebasejs/9.6.10/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.10/firebase-auth-compat.js"></script>
  <style>
    body { font-family: Arial; max-width: 600px; margin: 0 auto; padding: 20px; }
    .container { border: 1px solid #ccc; padding: 20px; border-radius: 5px; }
    .form-group { margin-bottom: 15px; }
    label { display: block; margin-bottom: 5px; }
    input { width: 100%; padding: 8px; box-sizing: border-box; }
    button { background-color: #4CAF50; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; }
    button:disabled { background-color: #cccccc; }
    .result { margin-top: 20px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; background-color: #f9f9f9; }
    .logs { margin-top: 20px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; background-color: #f9f9f9; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    .log-entry { margin-bottom: 5px; padding: 3px; border-bottom: 1px solid #eee; }
    .log-error { color: #cc0000; }
    .log-success { color: #4CAF50; }
    .log-info { color: #0066cc; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Firebase Phone Authentication Test (Updated)</h1>
    
    <div class="form-group">
      <label for="phone">Phone Number (with country code):</label>
      <input type="tel" id="phone" placeholder="+919611966747" value="+919611966747">
    </div>
    
    <div id="recaptcha-container"></div>
    
    <div class="form-group">
      <button id="send-otp">Send OTP</button>
      <button id="reset-captcha">Reset reCAPTCHA</button>
    </div>
    
    <div class="form-group" id="otp-section" style="display: none;">
      <label for="otp">Enter OTP:</label>
      <input type="text" id="otp" placeholder="123456">
      <button id="verify-otp">Verify OTP</button>
    </div>
    
    <div class="result" id="result">Ready to test. Click "Send OTP" to begin.</div>
    
    <div class="logs" id="logs"></div>
  </div>

  <script>
    // Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyA3ex2Lw-RTMetcAB7-Sf0dkEHNX9GTMKA",
      authDomain: "quamin-agricare.firebaseapp.com",
      projectId: "quamin-agricare",
      storageBucket: "quamin-agricare.firebasestorage.app",
      messagingSenderId: "744194487251",
      appId: "1:744194487251:web:e3d0ccee5ad58b6ddef526",
      measurementId: "G-HTCNBBQYJD"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
    const auth = firebase.auth();
    
    // Get elements
    const phoneInput = document.getElementById('phone');
    const sendOtpButton = document.getElementById('send-otp');
    const resetCaptchaButton = document.getElementById('reset-captcha');
    const otpSection = document.getElementById('otp-section');
    const otpInput = document.getElementById('otp');
    const verifyOtpButton = document.getElementById('verify-otp');
    const resultDiv = document.getElementById('result');
    const logsDiv = document.getElementById('logs');
    
    // Logging function
    function log(message, type = 'info') {
      const logEntry = document.createElement('div');
      logEntry.className = `log-entry log-${type}`;
      logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      logsDiv.appendChild(logEntry);
      logsDiv.scrollTop = logsDiv.scrollHeight;
      console.log(`[${type.toUpperCase()}] ${message}`);
    }
    
    // Initialize reCAPTCHA
    let recaptchaVerifier = null;
    let recaptchaWidgetId = null;
    
    function setupRecaptcha() {
      // Clear previous container
      const recaptchaContainer = document.getElementById('recaptcha-container');
      recaptchaContainer.innerHTML = '';
      
      // Create new reCAPTCHA verifier with invisible size
      recaptchaVerifier = new firebase.auth.RecaptchaVerifier('recaptcha-container', {
        'size': 'invisible',
        'callback': (response) => {
          log("reCAPTCHA verified successfully", "success");
          sendOtpButton.disabled = false;
        },
        'expired-callback': () => {
          log("reCAPTCHA expired", "error");
          sendOtpButton.disabled = true;
        }
      });
      
      // Render the reCAPTCHA
      recaptchaVerifier.render().then(function(widgetId) {
        recaptchaWidgetId = widgetId;
        log("reCAPTCHA initialized (invisible mode)", "info");
      });
    }
    
    // Setup initial reCAPTCHA
    setupRecaptcha();
    
    // Reset reCAPTCHA
    resetCaptchaButton.addEventListener('click', () => {
      log("Resetting reCAPTCHA...", "info");
      setupRecaptcha();
    });
    
    // Send OTP
    sendOtpButton.addEventListener('click', () => {
      const phoneNumber = phoneInput.value.trim();
      
      if (!phoneNumber) {
        log("Phone number is empty", "error");
        resultDiv.textContent = "Please enter a phone number";
        return;
      }
      
      resultDiv.textContent = `Sending OTP to ${phoneNumber}...`;
      log(`Attempting to send OTP to ${phoneNumber}`, "info");
      
      // Disable button to prevent multiple clicks
      sendOtpButton.disabled = true;
      
      auth.signInWithPhoneNumber(phoneNumber, recaptchaVerifier)
        .then((confirmationResult) => {
          // SMS sent. Prompt user to type the code.
          window.confirmationResult = confirmationResult;
          resultDiv.textContent = `OTP sent successfully to ${phoneNumber}! Please check your phone.`;
          otpSection.style.display = 'block';
          log("OTP sent successfully", "success");
        })
        .catch((error) => {
          // Error; SMS not sent
          resultDiv.textContent = `Error sending OTP: ${error.message}`;
          log(`Error sending OTP: ${error.code} - ${error.message}`, "error");
          
          // Additional error details
          if (error.code === 'auth/invalid-phone-number') {
            log("Phone number format is invalid. Use E.164 format: +[country code][phone number]", "error");
          } else if (error.code === 'auth/captcha-check-failed') {
            log("reCAPTCHA verification failed. Try resetting the reCAPTCHA.", "error");
          } else if (error.code === 'auth/quota-exceeded') {
            log("SMS quota exceeded. Try again later.", "error");
          } else if (error.code === 'auth/invalid-app-credential') {
            log("Invalid app credential. This usually means the reCAPTCHA verification failed or the domain is not authorized.", "error");
            log("Try using the 'invisible' reCAPTCHA or check domain verification in Firebase Console.", "info");
          }
          
          // Reset reCAPTCHA and enable button
          setupRecaptcha();
          sendOtpButton.disabled = false;
        });
    });
    
    // Verify OTP
    verifyOtpButton.addEventListener('click', () => {
      const code = otpInput.value.trim();
      
      if (!code) {
        log("OTP is empty", "error");
        resultDiv.textContent = "Please enter the OTP";
        return;
      }
      
      if (!window.confirmationResult) {
        log("No confirmation result found", "error");
        resultDiv.textContent = "No OTP was sent. Please send OTP first.";
        return;
      }
      
      resultDiv.textContent = `Verifying OTP...`;
      log(`Attempting to verify OTP: ${code}`, "info");
      
      window.confirmationResult.confirm(code)
        .then((result) => {
          // User signed in successfully.
          const user = result.user;
          resultDiv.textContent = `OTP verified successfully! User signed in: ${user.phoneNumber}`;
          log(`OTP verified successfully. User signed in: ${user.phoneNumber}`, "success");
          
          // Reset UI
          otpSection.style.display = 'none';
          sendOtpButton.disabled = false;
          otpInput.value = '';
        })
        .catch((error) => {
          // User couldn't sign in (bad verification code?)
          resultDiv.textContent = `Error verifying OTP: ${error.message}`;
          log(`Error verifying OTP: ${error.code} - ${error.message}`, "error");
        });
    });
    
    // Log initial state
    log("Page loaded. Firebase initialized.", "info");
  </script>
</body>
</html>
