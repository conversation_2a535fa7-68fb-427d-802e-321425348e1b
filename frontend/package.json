{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 127.0.0.1", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@base-ui-components/react": "^1.0.0-alpha.8", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@firebasegen/default-connector": "file:dataconnect-generated/js/default-connector", "@hookform/resolvers": "^3.10.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.11.16", "@react-google-maps/api": "^2.20.6", "@tensorflow/tfjs": "^4.22.0", "axios": "^1.8.2", "chart.js": "^4.4.8", "date-fns": "^4.1.0", "firebase": "^11.4.0", "framer-motion": "^12.0.6", "html2canvas": "^1.4.1", "i18next": "^25.2.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "leaflet": "^1.9.4", "lodash.isequal": "^4.5.0", "microsoft-cognitiveservices-speech-sdk": "^1.43.0", "react": "^18.3.1", "react-calendar": "^5.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-i18next": "^15.5.3", "react-leaflet": "^4.2.1", "react-markdown": "^10.1.0", "react-router-dom": "^7.1.3", "recharts": "^2.15.1", "remark-gfm": "^4.0.1", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "vite": "^6.3.4"}}