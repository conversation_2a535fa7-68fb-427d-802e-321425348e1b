<!DOCTYPE html>
<html>
<head>
  <title>Firebase Phone Auth (Modular SDK)</title>
  <script type="module">
    // Import the functions you need from the SDKs
    import { initializeApp } from "https://www.gstatic.com/firebasejs/9.6.10/firebase-app.js";
    import { 
      getAuth, 
      RecaptchaVerifier, 
      signInWithPhoneNumber 
    } from "https://www.gstatic.com/firebasejs/9.6.10/firebase-auth.js";

    // Your web app's Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyA3ex2Lw-RTMetcAB7-Sf0dkEHNX9GTMKA",
      authDomain: "quamin-agricare.firebaseapp.com",
      projectId: "quamin-agricare",
      storageBucket: "quamin-agricare.firebasestorage.app",
      messagingSenderId: "744194487251",
      appId: "1:744194487251:web:e3d0ccee5ad58b6ddef526",
      measurementId: "G-HTCNBBQYJD"
    };

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const auth = getAuth(app);
    
    // DOM elements
    const phoneInput = document.getElementById('phone');
    const sendOtpButton = document.getElementById('send-otp');
    const resetCaptchaButton = document.getElementById('reset-captcha');
    const otpSection = document.getElementById('otp-section');
    const otpInput = document.getElementById('otp');
    const verifyOtpButton = document.getElementById('verify-otp');
    const resultDiv = document.getElementById('result');
    const logsDiv = document.getElementById('logs');
    
    // Logging function
    function log(message, type = 'info') {
      const logEntry = document.createElement('div');
      logEntry.className = `log-entry log-${type}`;
      logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      logsDiv.appendChild(logEntry);
      logsDiv.scrollTop = logsDiv.scrollHeight;
      console.log(`[${type.toUpperCase()}] ${message}`);
    }
    
    // Initialize reCAPTCHA
    let recaptchaVerifier = null;
    
    function setupRecaptcha() {
      try {
        // Clear previous container
        const recaptchaContainer = document.getElementById('recaptcha-container');
        recaptchaContainer.innerHTML = '';
        
        // Create new reCAPTCHA verifier with invisible size
        recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
          'size': 'invisible',
          'callback': (response) => {
            log("reCAPTCHA verified successfully", "success");
          },
          'expired-callback': () => {
            log("reCAPTCHA expired", "error");
          }
        });
        
        log("reCAPTCHA initialized (invisible mode)", "info");
        return true;
      } catch (error) {
        log(`Error setting up reCAPTCHA: ${error.message}`, "error");
        return false;
      }
    }
    
    // Setup initial reCAPTCHA
    document.addEventListener('DOMContentLoaded', () => {
      log("Page loaded. Firebase initialized.", "info");
      setupRecaptcha();
    });
    
    // Reset reCAPTCHA
    resetCaptchaButton.addEventListener('click', () => {
      log("Resetting reCAPTCHA...", "info");
      setupRecaptcha();
    });
    
    // Send OTP
    sendOtpButton.addEventListener('click', async () => {
      const phoneNumber = phoneInput.value.trim();
      
      if (!phoneNumber) {
        log("Phone number is empty", "error");
        resultDiv.textContent = "Please enter a phone number";
        return;
      }
      
      resultDiv.textContent = `Sending OTP to ${phoneNumber}...`;
      log(`Attempting to send OTP to ${phoneNumber}`, "info");
      
      // Disable button to prevent multiple clicks
      sendOtpButton.disabled = true;
      
      try {
        // Make sure recaptchaVerifier is initialized
        if (!recaptchaVerifier) {
          setupRecaptcha();
        }
        
        // Get verification ID
        const confirmationResult = await signInWithPhoneNumber(auth, phoneNumber, recaptchaVerifier);
        window.confirmationResult = confirmationResult;
        
        // Success
        resultDiv.textContent = `OTP sent successfully to ${phoneNumber}! Please check your phone.`;
        otpSection.style.display = 'block';
        log("OTP sent successfully", "success");
      } catch (error) {
        // Error handling
        resultDiv.textContent = `Error sending OTP: ${error.message}`;
        log(`Error sending OTP: ${error.code} - ${error.message}`, "error");
        
        // Additional error details
        if (error.code === 'auth/invalid-phone-number') {
          log("Phone number format is invalid. Use E.164 format: +[country code][phone number]", "error");
        } else if (error.code === 'auth/captcha-check-failed') {
          log("reCAPTCHA verification failed. Try resetting the reCAPTCHA.", "error");
        } else if (error.code === 'auth/quota-exceeded') {
          log("SMS quota exceeded. Try again later.", "error");
        } else if (error.code === 'auth/invalid-app-credential') {
          log("Invalid app credential. This usually means the reCAPTCHA verification failed or the domain is not authorized.", "error");
          log("Try checking domain verification in Firebase Console.", "info");
        }
        
        // Reset reCAPTCHA and enable button
        setupRecaptcha();
        sendOtpButton.disabled = false;
      }
    });
    
    // Verify OTP
    verifyOtpButton.addEventListener('click', async () => {
      const code = otpInput.value.trim();
      
      if (!code) {
        log("OTP is empty", "error");
        resultDiv.textContent = "Please enter the OTP";
        return;
      }
      
      if (!window.confirmationResult) {
        log("No confirmation result found", "error");
        resultDiv.textContent = "No OTP was sent. Please send OTP first.";
        return;
      }
      
      resultDiv.textContent = `Verifying OTP...`;
      log(`Attempting to verify OTP: ${code}`, "info");
      
      try {
        const result = await window.confirmationResult.confirm(code);
        const user = result.user;
        
        resultDiv.textContent = `OTP verified successfully! User signed in: ${user.phoneNumber}`;
        log(`OTP verified successfully. User signed in: ${user.phoneNumber}`, "success");
        
        // Reset UI
        otpSection.style.display = 'none';
        sendOtpButton.disabled = false;
        otpInput.value = '';
      } catch (error) {
        resultDiv.textContent = `Error verifying OTP: ${error.message}`;
        log(`Error verifying OTP: ${error.code} - ${error.message}`, "error");
      }
    });
  </script>
  <style>
    body { font-family: Arial; max-width: 600px; margin: 0 auto; padding: 20px; }
    .container { border: 1px solid #ccc; padding: 20px; border-radius: 5px; }
    .form-group { margin-bottom: 15px; }
    label { display: block; margin-bottom: 5px; }
    input { width: 100%; padding: 8px; box-sizing: border-box; }
    button { background-color: #4CAF50; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; }
    button:disabled { background-color: #cccccc; }
    .result { margin-top: 20px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; background-color: #f9f9f9; }
    .logs { margin-top: 20px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; background-color: #f9f9f9; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    .log-entry { margin-bottom: 5px; padding: 3px; border-bottom: 1px solid #eee; }
    .log-error { color: #cc0000; }
    .log-success { color: #4CAF50; }
    .log-info { color: #0066cc; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Firebase Phone Auth (Modular SDK)</h1>
    
    <div class="form-group">
      <label for="phone">Phone Number (with country code):</label>
      <input type="tel" id="phone" placeholder="+919611966747" value="+919611966747">
    </div>
    
    <div id="recaptcha-container"></div>
    
    <div class="form-group">
      <button id="send-otp">Send OTP</button>
      <button id="reset-captcha">Reset reCAPTCHA</button>
    </div>
    
    <div class="form-group" id="otp-section" style="display: none;">
      <label for="otp">Enter OTP:</label>
      <input type="text" id="otp" placeholder="123456">
      <button id="verify-otp">Verify OTP</button>
    </div>
    
    <div class="result" id="result">Ready to test. Click "Send OTP" to begin.</div>
    
    <div class="logs" id="logs"></div>
  </div>
</body>
</html>
