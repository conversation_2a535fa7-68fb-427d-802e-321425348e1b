# Azure App Service Deployment Guide for AgriCare

This guide provides instructions for deploying the AgriCare application to Azure App Service using Azure DevOps Pipelines.

## Prerequisites

1. Azure subscription
2. Azure DevOps organization and project
3. Azure App Service plan
4. MongoDB database (Azure CosmosDB with MongoDB API or other MongoDB provider)

## Azure Resources Information

- **App Name**: AgrocareProd
- **App URL**: https://agrocareprod-b7f0bqe6c0f5cadt.centralindia-01.azurewebsites.net/
- **Subscription ID**: eb126322-f0a5-441d-aa95-369f4388f676
- **Resource Group**: AgrocareProd-rg
- **Region**: Central India

## Deployment Options

### Option 1: Using Azure DevOps Pipeline

1. **Set up Azure DevOps Pipeline**:
   - Navigate to your Azure DevOps project
   - Go to Pipelines > Create Pipeline
   - Select "Azure Repos Git" as the source
   - Select your repository
   - Configure the pipeline to use the `azure-pipelines.yml` file

2. **Configure Pipeline Variables**:
   - In Azure DevOps, go to Pipelines > Edit > Variables
   - Add the following variables:
     - `MONGODB_URI`: Your MongoDB connection string
     - `JWT_SECRET`: Secret key for JWT authentication
     - `FIREBASE_PROJECT_ID`: Your Firebase project ID
     - `AZURE_OPENAI_KEY`: Your Azure OpenAI API key
     - `AZURE_OPENAI_ENDPOINT`: Your Azure OpenAI endpoint
     - `AZURE_SPEECH_KEY`: Your Azure Speech Services key
     - `AZURE_SPEECH_REGION`: Your Azure Speech Services region
     - `OPENWEATHER_API_KEY`: Your OpenWeather API key
     - `APPINSIGHTS_KEY`: Your Application Insights instrumentation key
     - `APPINSIGHTS_CONNECTION_STRING`: Your Application Insights connection string

3. **Run the Pipeline**:
   - Manually trigger the pipeline or push to the main/production branch
   - The pipeline will:
     - Build the frontend and backend
     - Deploy MongoDB data to CosmosDB (if configured)
     - Deploy the application to Azure App Service
     - Perform health checks

### Option 2: Manual Deployment

You can also deploy manually using the provided script:

```bash
# Make the script executable
chmod +x deploy-to-azure.sh

# Run the script
./deploy-to-azure.sh
```

## MongoDB Setup

The application requires a MongoDB database. You can use Azure CosmosDB with MongoDB API:

1. **Create CosmosDB Account**:
   - Go to Azure Portal > Create a resource > Azure Cosmos DB
   - Select "Azure Cosmos DB for MongoDB" API
   - Create a new account in the same region as your App Service

2. **Configure Connection String**:
   - Get the connection string from the Azure Portal
   - Add `?retryWrites=false&w=majority` to the connection string for CosmosDB compatibility
   - Set this as the `MONGODB_URI` variable in your pipeline

3. **Import Data**:
   - Use the MongoDB backup in `backend/mongodb_backup.tar.gz`
   - The pipeline will automatically restore this data to CosmosDB

## Troubleshooting

### Common Issues

1. **Deployment Fails**:
   - Check the pipeline logs for specific errors
   - Verify that all required variables are set
   - Ensure the Azure service connection has proper permissions

2. **App Starts but Shows Errors**:
   - Check the App Service logs in Azure Portal
   - Verify MongoDB connection string is correct
   - Check if all environment variables are set correctly

3. **MongoDB Connection Issues**:
   - Verify the connection string format is correct for CosmosDB
   - Check if the IP address of the App Service is allowed in MongoDB firewall rules
   - Ensure the database and collections exist

### Health Check Endpoint

The application provides a health check endpoint at `/api/health` that returns:
- Database connection status
- System information
- Overall health status

Use this endpoint to verify the application is running correctly.

## Monitoring

1. **Application Insights**:
   - The deployment automatically sets up Application Insights
   - View logs, performance data, and errors in Azure Portal

2. **Azure App Service Monitoring**:
   - Go to Azure Portal > Your App Service > Monitoring
   - View metrics, logs, and alerts

3. **MongoDB Monitoring**:
   - If using CosmosDB, go to Azure Portal > Your CosmosDB Account > Metrics
   - Monitor request units, storage, and throughput

## Scaling

1. **App Service Scaling**:
   - Go to Azure Portal > Your App Service > Scale up (for better hardware)
   - Go to Azure Portal > Your App Service > Scale out (for more instances)

2. **MongoDB Scaling**:
   - If using CosmosDB, go to Azure Portal > Your CosmosDB Account > Scale
   - Increase RU/s (Request Units per second) for better performance

## Backup and Restore

1. **App Service Backup**:
   - Go to Azure Portal > Your App Service > Backups
   - Configure regular backups of your application

2. **MongoDB Backup**:
   - Use the provided scripts in `backend/scripts/` to create and restore MongoDB backups
   - For CosmosDB, use Azure Portal > Your CosmosDB Account > Backup & Restore

## Security

1. **App Service Security**:
   - Enable HTTPS only
   - Configure authentication if needed
   - Set up IP restrictions

2. **MongoDB Security**:
   - Use strong passwords
   - Enable IP filtering
   - Use SSL for connections

## Additional Resources

- [Azure App Service Documentation](https://docs.microsoft.com/en-us/azure/app-service/)
- [Azure CosmosDB Documentation](https://docs.microsoft.com/en-us/azure/cosmos-db/)
- [Azure DevOps Pipelines Documentation](https://docs.microsoft.com/en-us/azure/devops/pipelines/)
