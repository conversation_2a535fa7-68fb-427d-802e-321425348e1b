# Azure DevOps Pipeline for AgriCare App Service Deployment
# This pipeline builds and deploys the AgriCare application to Azure App Service

trigger:
  branches:
    include:
    - main
    - production
    - feature/weather-integration

# Enable scheduled deployments (daily at midnight UTC)
schedules:
- cron: "0 0 * * *"
  displayName: Daily midnight build
  branches:
    include:
    - production
  always: false  # only run if there are code changes

# Enable manual triggers
parameters:
- name: deploymentType
  displayName: Deployment Type
  type: string
  default: Full
  values:
  - Full
  - AppOnly
  - DatabaseOnly

- name: environment
  displayName: Environment
  type: string
  default: production
  values:
  - production
  - staging

variables:
  # Azure App Service name
  appName: 'myagricare'
  # Azure App Service full domain
  appDomain: 'myagricare.azurewebsites.net'
  # Azure Subscription ID
  subscriptionId: 'eb126322-f0a5-441d-aa95-369f4388f676'
  # Azure App Service environment (dev, test, prod)
  environment: '${{ parameters.environment }}'
  # Azure Service Connection
  azureServiceConnection: '51140511-aa6a-4f19-83a8-4b6159e0c35a'
  # Node.js version
  nodeVersion: '18.x'
  # MongoDB connection string variable group
  mongoDBConnString: '$(MONGODB_URI)'
  # Azure region
  azureRegion: 'centralindia'
  # MongoDB backup file path
  mongoBackupPath: 'backend/mongodb_backup.tar.gz'
  # MongoDB backup extraction path
  mongoBackupExtractPath: '$(System.ArtifactsDirectory)/mongodb_backup'
  # MongoDB database name
  mongoDBName: 'agricare'
  # Resource group name
  resourceGroupName: 'myagricare-rg'

stages:
- stage: Build
  displayName: 'Build Stage'
  jobs:
  - job: BuildJob
    displayName: 'Build Job'
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '$(nodeVersion)'
      displayName: 'Install Node.js'

    # Install dependencies for backend
    - script: |
        cd backend
        npm ci
      displayName: 'Install Backend Dependencies'

    # Install dependencies for frontend
    - script: |
        cd frontend
        npm ci
      displayName: 'Install Frontend Dependencies'

    # Build frontend for production
    - script: |
        cd frontend
        npm run build
      displayName: 'Build Frontend'

    # Copy frontend build to backend public folder
    - script: |
        mkdir -p backend/public
        cp -r frontend/dist/* backend/public/
      displayName: 'Copy Frontend Build to Backend'

    # Create production .env file
    - task: Bash@3
      inputs:
        targetType: 'inline'
        script: |
          cat > backend/.env << EOL
          NODE_ENV=production
          PORT=8080
          MONGODB_URI=$(MONGODB_URI)
          JWT_SECRET=$(JWT_SECRET)
          FIREBASE_PROJECT_ID=$(FIREBASE_PROJECT_ID)
          AZURE_OPENAI_KEY=$(AZURE_OPENAI_KEY)
          AZURE_OPENAI_ENDPOINT=$(AZURE_OPENAI_ENDPOINT)
          AZURE_SPEECH_KEY=$(AZURE_SPEECH_KEY)
          AZURE_SPEECH_REGION=$(AZURE_SPEECH_REGION)
          OPENWEATHER_API_KEY=$(OPENWEATHER_API_KEY)
          EOL
      displayName: 'Create Production Environment File'
      env:
        MONGODB_URI: $(MONGODB_URI)
        JWT_SECRET: $(JWT_SECRET)
        FIREBASE_PROJECT_ID: $(FIREBASE_PROJECT_ID)
        AZURE_OPENAI_KEY: $(AZURE_OPENAI_KEY)
        AZURE_OPENAI_ENDPOINT: $(AZURE_OPENAI_ENDPOINT)
        AZURE_SPEECH_KEY: $(AZURE_SPEECH_KEY)
        AZURE_SPEECH_REGION: $(AZURE_SPEECH_REGION)
        OPENWEATHER_API_KEY: $(OPENWEATHER_API_KEY)

    # Copy Firebase credentials file
    - task: CopyFiles@2
      inputs:
        sourceFolder: '$(Pipeline.Workspace)/firebase-credentials'
        contents: 'firebase-credentials.json'
        targetFolder: 'backend/config'
      displayName: 'Copy Firebase Credentials'

    # Archive the backend folder for deployment
    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: 'backend'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/backend.zip'
        replaceExistingArchive: true
      displayName: 'Archive Backend Files'

    # Publish the artifacts
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)/backend.zip'
        artifactName: 'backend'
      displayName: 'Publish Backend Artifact'

    # Verify and publish MongoDB backup
    - task: Bash@3
      inputs:
        targetType: 'inline'
        script: |
          # Check if MongoDB backup file exists
          if [ ! -f "$(mongoBackupPath)" ]; then
            echo "##vso[task.logissue type=error]MongoDB backup file not found at $(mongoBackupPath)"
            echo "##vso[task.complete result=Failed;]MongoDB backup file not found"
            exit 1
          fi

          # Verify the backup file is a valid tar.gz file
          if ! tar -tzf "$(mongoBackupPath)" > /dev/null 2>&1; then
            echo "##vso[task.logissue type=error]MongoDB backup file is not a valid tar.gz file"
            echo "##vso[task.complete result=Failed;]Invalid backup file"
            exit 1
          fi

          # Check the size of the backup file
          BACKUP_SIZE=$(du -h "$(mongoBackupPath)" | cut -f1)
          echo "MongoDB backup file size: $BACKUP_SIZE"

          # Create a backup of the current backup file with timestamp
          TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
          cp "$(mongoBackupPath)" "$(mongoBackupPath).$TIMESTAMP.bak"
          echo "Created backup of MongoDB backup file: $(mongoBackupPath).$TIMESTAMP.bak"
      displayName: 'Verify MongoDB Backup'

    # Publish MongoDB backup
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: '$(mongoBackupPath)'
        artifactName: 'mongodb-backup'
      displayName: 'Publish MongoDB Backup'
      condition: succeeded()

- stage: DeployMongoDB
  displayName: 'Deploy MongoDB'
  dependsOn: Build
  condition: or(eq('${{ parameters.deploymentType }}', 'Full'), eq('${{ parameters.deploymentType }}', 'DatabaseOnly'))
  jobs:
  - deployment: DeployMongoDB
    displayName: 'Deploy MongoDB to Azure CosmosDB'
    environment: '$(environment)'
    pool:
      vmImage: 'ubuntu-latest'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: 'current'
              downloadType: 'single'
              artifactName: 'mongodb-backup'
              downloadPath: '$(System.ArtifactsDirectory)'
            displayName: 'Download MongoDB Backup'

          - task: AzureCLI@2
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                # Set error handling
                set -e
                trap 'echo "##vso[task.logissue type=error]An error occurred during MongoDB deployment"; exit 1' ERR

                # Create extraction directory
                mkdir -p $(mongoBackupExtractPath)

                # Extract the MongoDB backup with error handling
                echo "Extracting MongoDB backup..."
                if ! tar -xzvf $(System.ArtifactsDirectory)/mongodb-backup/mongodb_backup.tar.gz -C $(mongoBackupExtractPath); then
                  echo "##vso[task.logissue type=error]Failed to extract MongoDB backup"
                  exit 1
                fi

                # Verify extraction was successful
                if [ ! -d "$(mongoBackupExtractPath)/exports/$(mongoDBName)" ]; then
                  echo "##vso[task.logissue type=error]Expected directory structure not found in backup"
                  echo "Contents of extraction directory:"
                  find $(mongoBackupExtractPath) -type d | sort
                  exit 1
                fi

                # Install MongoDB tools
                echo "Installing MongoDB tools..."
                wget -qO - https://www.mongodb.org/static/pgp/server-5.0.asc | sudo apt-key add -
                echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/5.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-5.0.list
                sudo apt-get update
                sudo apt-get install -y mongodb-database-tools

                # Backup existing data before restoration (if any)
                echo "Creating backup of existing data (if any)..."
                TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
                BACKUP_DIR="$(System.ArtifactsDirectory)/existing_data_backup_$TIMESTAMP"
                mkdir -p $BACKUP_DIR

                # Try to backup existing data, but don't fail if it doesn't exist
                mongodump --uri="$(MONGODB_URI)" --out="$BACKUP_DIR" || echo "No existing data to backup or backup failed"

                # Prepare CosmosDB connection string with proper parameters
                # CosmosDB requires retryWrites=false and w=majority for compatibility
                COSMOS_CONN_STRING="$(MONGODB_URI)?retryWrites=false&w=majority"

                # Restore to CosmosDB with MongoDB API with retry mechanism
                echo "Restoring data to CosmosDB..."
                MAX_RETRIES=3
                RETRY_COUNT=0

                while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
                  if mongorestore --uri="$COSMOS_CONN_STRING" --drop --noIndexRestore $(mongoBackupExtractPath)/exports/$(mongoDBName); then
                    echo "MongoDB restoration successful!"
                    break
                  else
                    RETRY_COUNT=$((RETRY_COUNT+1))
                    if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
                      echo "Restoration failed, retrying in 30 seconds (Attempt $RETRY_COUNT of $MAX_RETRIES)..."
                      sleep 30
                    else
                      echo "##vso[task.logissue type=error]Failed to restore MongoDB data after $MAX_RETRIES attempts"
                      exit 1
                    fi
                  fi
                done

                # Create indexes separately (CosmosDB has different indexing requirements)
                echo "Creating indexes..."
                for COLLECTION in $(find $(mongoBackupExtractPath)/exports/$(mongoDBName) -name "*.metadata.json" | sed 's/\.metadata\.json$//');
                do
                  COLLECTION_NAME=$(basename $COLLECTION)
                  echo "Processing indexes for collection: $COLLECTION_NAME"

                  # Extract indexes from metadata and apply them with lower key limit for CosmosDB
                  if [ -f "$COLLECTION.metadata.json" ]; then
                    # CosmosDB has a limit on index keys, so we'll create only essential indexes
                    mongorestore --uri="$COSMOS_CONN_STRING" --nsInclude="$(mongoDBName).$COLLECTION_NAME" --noCollectionRestore --convertLegacyIndexes $(mongoBackupExtractPath)/exports || echo "Index creation for $COLLECTION_NAME failed, continuing..."
                  fi
                done

                echo "MongoDB deployment completed successfully"
            displayName: 'Restore MongoDB Data to CosmosDB'

- stage: DeployApp
  displayName: 'Deploy App'
  dependsOn: DeployMongoDB
  condition: or(eq('${{ parameters.deploymentType }}', 'Full'), eq('${{ parameters.deploymentType }}', 'AppOnly'))
  jobs:
  - deployment: DeployApp
    displayName: 'Deploy to Azure App Service'
    environment: '$(environment)'
    pool:
      vmImage: 'ubuntu-latest'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: DownloadBuildArtifacts@0
            inputs:
              buildType: 'current'
              downloadType: 'single'
              artifactName: 'backend'
              downloadPath: '$(System.ArtifactsDirectory)'
            displayName: 'Download Backend Artifact'

          # Prepare for deployment with pre-deployment checks
          - task: AzureCLI@2
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                # Check if the App Service exists
                echo "Checking if App Service $(appName) exists..."
                APP_EXISTS=$(az webapp show --name $(appName) --resource-group $(resourceGroupName) --subscription $(subscriptionId) 2>/dev/null)

                if [ -z "$APP_EXISTS" ]; then
                  echo "##vso[task.logissue type=warning]App Service $(appName) does not exist. It will be created during deployment."
                else
                  echo "App Service $(appName) exists. Proceeding with deployment."

                  # Get current app status
                  APP_STATUS=$(az webapp show --name $(appName) --resource-group $(resourceGroupName) --subscription $(subscriptionId) --query state -o tsv)
                  echo "Current App Service status: $APP_STATUS"

                  # Check MongoDB connection
                  echo "Verifying MongoDB connection..."
                  MONGO_CHECK=$(az webapp config appsettings list --name $(appName) --resource-group $(resourceGroupName) --subscription $(subscriptionId) --query "[?name=='MONGODB_URI'].value" -o tsv)

                  if [ -z "$MONGO_CHECK" ]; then
                    echo "##vso[task.logissue type=warning]MongoDB connection string not found in app settings."
                  else
                    echo "MongoDB connection string is configured."
                  fi
                fi
            displayName: 'Pre-deployment Checks'

          # Deploy to Azure App Service
          - task: AzureWebApp@1
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              appType: 'webAppLinux'
              appName: '$(appName)'
              deployToSlotOrASE: true
              resourceGroupName: '$(resourceGroupName)'
              slotName: 'production'
              package: '$(System.ArtifactsDirectory)/backend/backend.zip'
              runtimeStack: 'NODE|18-lts'
              startupCommand: 'node server.js'
            displayName: 'Deploy to Azure App Service'

          # Configure app settings
          - task: AzureCLI@2
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                # Set environment variables
                echo "Setting up environment variables..."
                az webapp config appsettings set --name $(appName) --resource-group $(resourceGroupName) --subscription $(subscriptionId) --settings \
                  MONGODB_URI="$(MONGODB_URI)?retryWrites=false&w=majority" \
                  JWT_SECRET="$(JWT_SECRET)" \
                  FIREBASE_PROJECT_ID="$(FIREBASE_PROJECT_ID)" \
                  AZURE_OPENAI_KEY="$(AZURE_OPENAI_KEY)" \
                  AZURE_OPENAI_ENDPOINT="$(AZURE_OPENAI_ENDPOINT)" \
                  AZURE_SPEECH_KEY="$(AZURE_SPEECH_KEY)" \
                  AZURE_SPEECH_REGION="$(AZURE_SPEECH_REGION)" \
                  OPENWEATHER_API_KEY="$(OPENWEATHER_API_KEY)" \
                  NODE_ENV="production" \
                  WEBSITE_NODE_DEFAULT_VERSION="18.14.0" \
                  WEBSITE_RUN_FROM_PACKAGE="1" \
                  WEBSITE_HEALTHCHECK_MAXPINGFAILURES="5" \
                  WEBSITE_HEALTHCHECK_PATH="/api/health"

                # Restart the app to apply settings
                echo "Restarting the app..."
                az webapp restart --name $(appName) --resource-group $(resourceGroupName) --subscription $(subscriptionId)
            displayName: 'Configure App Settings'

          # Post-deployment health check
          - task: AzureCLI@2
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                # Wait for app to start
                echo "Waiting for app to start..."
                sleep 30

                # Use the specific app domain
                APP_URL="https://$(appDomain)"
                echo "App URL: $APP_URL"

                # Check if the app is responding
                echo "Checking if app is responding..."
                MAX_RETRIES=5
                RETRY_COUNT=0

                while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
                  HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $APP_URL)

                  if [ "$HTTP_STATUS" == "200" ] || [ "$HTTP_STATUS" == "302" ]; then
                    echo "App is responding with HTTP status: $HTTP_STATUS"
                    break
                  else
                    RETRY_COUNT=$((RETRY_COUNT+1))
                    if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
                      echo "App not responding (HTTP status: $HTTP_STATUS), retrying in 30 seconds (Attempt $RETRY_COUNT of $MAX_RETRIES)..."
                      sleep 30
                    else
                      echo "##vso[task.logissue type=warning]App not responding properly after $MAX_RETRIES attempts. HTTP status: $HTTP_STATUS"
                    fi
                  fi
                done

                # Check health endpoint
                echo "Checking health endpoint..."
                HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $APP_URL/api/health)

                if [ "$HEALTH_STATUS" == "200" ]; then
                  echo "Health endpoint is responding correctly."
                else
                  echo "##vso[task.logissue type=warning]Health endpoint returned HTTP status: $HEALTH_STATUS"
                fi

                # Enable Application Insights if not already enabled
                echo "Setting up Application Insights..."
                az webapp config appsettings set --name $(appName) --resource-group $(resourceGroupName) --subscription $(subscriptionId) --settings APPINSIGHTS_INSTRUMENTATIONKEY="$(APPINSIGHTS_KEY)" APPLICATIONINSIGHTS_CONNECTION_STRING="$(APPINSIGHTS_CONNECTION_STRING)" ApplicationInsightsAgent_EXTENSION_VERSION=~2

                echo "Deployment verification completed."
            displayName: 'Post-deployment Health Check'
            continueOnError: true
