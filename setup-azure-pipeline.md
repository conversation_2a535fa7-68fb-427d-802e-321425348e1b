# Setting Up the Correct Azure DevOps Pipeline

## Problem

The current pipeline is trying to use the `AzureStaticWebApp` task, which is causing the error:

```
The content server has rejected the request with: BadRequest
Reason: No matching Static Web App was found or the api key was invalid.
```

This is happening because:
1. Your application is configured for a regular Azure App Service (AgrocareProd)
2. The pipeline is trying to use a Static Web App deployment task

## Solution

### Step 1: Disable the Current Pipeline

1. Go to Azure DevOps > Pipelines
2. Find the pipeline that's currently failing (likely named something with "witty-coast-02b4f0a00")
3. Click on the pipeline > More actions (three dots) > Disable

### Step 2: Create a New Pipeline

1. Go to Azure DevOps > Pipelines > New Pipeline
2. Select Azure Repos Git as the source
3. Select your repository
4. Choose "Existing Azure Pipelines YAML file"
5. Select `/azure-devops-pipeline.yml`
6. Click "Continue"

### Step 3: Configure Service Connection

1. Go to Azure DevOps > Project Settings > Service connections
2. Click "New service connection"
3. Select "Azure Resource Manager"
4. Choose "Service principal (automatic)" authentication
5. Select your subscription
6. Give the connection a name (e.g., "AgrocareProdConnection")
7. Check "Grant access permission to all pipelines"
8. Click "Save"

### Step 4: Configure Pipeline Variables

Before running the pipeline, you need to set up the following variables:

1. Click on "Variables" in the pipeline editor
2. Add the following variables:
   - `azureServiceConnection`: The name of the service connection you created in Step 3
   - `MONGODB_URI`: Your MongoDB connection string
   - `JWT_SECRET`: Secret key for JWT authentication
   - `FIREBASE_PROJECT_ID`: Your Firebase project ID
   - `AZURE_OPENAI_KEY`: Your Azure OpenAI API key
   - `AZURE_OPENAI_ENDPOINT`: Your Azure OpenAI endpoint
   - `AZURE_SPEECH_KEY`: Your Azure Speech Services key
   - `AZURE_SPEECH_REGION`: Your Azure Speech Services region
   - `OPENWEATHER_API_KEY`: Your OpenWeather API key
   - `APPINSIGHTS_KEY`: Your Application Insights instrumentation key
   - `APPINSIGHTS_CONNECTION_STRING`: Your Application Insights connection string

### Step 5: Run the Pipeline

1. Click "Save and run"
2. The pipeline will:
   - Build the frontend and backend
   - Deploy MongoDB data to CosmosDB (if configured)
   - Deploy the application to AgrocareProd
   - Perform health checks

## Verifying Deployment

After the pipeline runs successfully:

1. Go to https://agrocareprod-b7f0bqe6c0f5cadt.centralindia-01.azurewebsites.net/
2. Verify that your application is running correctly
3. Check the health endpoint at https://agrocareprod-b7f0bqe6c0f5cadt.centralindia-01.azurewebsites.net/api/health

## Troubleshooting

If you encounter issues:

1. Check the pipeline logs for specific errors
2. Verify that all required variables are set
3. Ensure the Azure service connection has proper permissions
4. Check if the MongoDB connection string is correct
5. Verify that the resource group and app service exist
