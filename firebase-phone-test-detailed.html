<!DOCTYPE html>
<html>
<head>
  <title>Firebase Phone Authentication Test (Detailed)</title>
  <script src="https://www.gstatic.com/firebasejs/9.6.10/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.10/firebase-auth-compat.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .container {
      border: 1px solid #ccc;
      padding: 20px;
      border-radius: 5px;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
    }
    input {
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    button:disabled {
      background-color: #cccccc;
    }
    .result {
      margin-top: 20px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #f9f9f9;
    }
    .logs {
      margin-top: 20px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #f9f9f9;
      max-height: 300px;
      overflow-y: auto;
      font-family: monospace;
      font-size: 12px;
    }
    .log-entry {
      margin-bottom: 5px;
      padding: 5px;
      border-bottom: 1px solid #eee;
    }
    .log-info {
      color: #0066cc;
    }
    .log-success {
      color: #4CAF50;
    }
    .log-error {
      color: #cc0000;
    }
    .log-warning {
      color: #ff9900;
    }
    .tab {
      overflow: hidden;
      border: 1px solid #ccc;
      background-color: #f1f1f1;
      margin-bottom: 20px;
    }
    .tab button {
      background-color: inherit;
      float: left;
      border: none;
      outline: none;
      cursor: pointer;
      padding: 14px 16px;
      transition: 0.3s;
      color: black;
    }
    .tab button:hover {
      background-color: #ddd;
    }
    .tab button.active {
      background-color: #ccc;
    }
    .tabcontent {
      display: none;
      padding: 6px 12px;
      border: 1px solid #ccc;
      border-top: none;
    }
    .visible {
      display: block;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Firebase Phone Authentication Test (Detailed)</h1>
    
    <div class="tab">
      <button class="tablinks active" onclick="openTab(event, 'TestTab')">Test OTP</button>
      <button class="tablinks" onclick="openTab(event, 'ConfigTab')">Configuration</button>
      <button class="tablinks" onclick="openTab(event, 'DiagnosticsTab')">Diagnostics</button>
    </div>
    
    <div id="TestTab" class="tabcontent visible">
      <div class="form-group">
        <label for="phone">Phone Number (with country code):</label>
        <input type="tel" id="phone" placeholder="+919611966747" value="+919611966747">
      </div>
      
      <div id="recaptcha-container"></div>
      
      <div class="form-group">
        <button id="send-otp">Send OTP</button>
        <button id="reset-captcha">Reset reCAPTCHA</button>
      </div>
      
      <div class="form-group" id="otp-section" style="display: none;">
        <label for="otp">Enter OTP:</label>
        <input type="text" id="otp" placeholder="123456">
        <button id="verify-otp">Verify OTP</button>
      </div>
      
      <div class="result" id="result">Ready to test. Click "Send OTP" to begin.</div>
    </div>
    
    <div id="ConfigTab" class="tabcontent">
      <h2>Firebase Configuration</h2>
      <div class="form-group">
        <label for="api-key">API Key:</label>
        <input type="text" id="api-key" value="AIzaSyA3ex2Lw-RTMetcAB7-Sf0dkEHNX9GTMKA">
      </div>
      <div class="form-group">
        <label for="auth-domain">Auth Domain:</label>
        <input type="text" id="auth-domain" value="quamin-agricare.firebaseapp.com">
      </div>
      <div class="form-group">
        <label for="project-id">Project ID:</label>
        <input type="text" id="project-id" value="quamin-agricare">
      </div>
      <div class="form-group">
        <button id="update-config">Update Configuration</button>
      </div>
    </div>
    
    <div id="DiagnosticsTab" class="tabcontent">
      <h2>Diagnostics</h2>
      <div class="form-group">
        <button id="check-auth">Check Auth Status</button>
        <button id="check-recaptcha">Check reCAPTCHA</button>
        <button id="clear-logs">Clear Logs</button>
      </div>
      <div class="logs" id="logs"></div>
    </div>
  </div>

  <script>
    // Initialize variables
    let firebaseConfig = {
      apiKey: "AIzaSyA3ex2Lw-RTMetcAB7-Sf0dkEHNX9GTMKA",
      authDomain: "quamin-agricare.firebaseapp.com",
      projectId: "quamin-agricare",
      storageBucket: "quamin-agricare.firebasestorage.app",
      messagingSenderId: "744194487251",
      appId: "1:744194487251:web:e3d0ccee5ad58b6ddef526",
      measurementId: "G-HTCNBBQYJD"
    };
    
    let app;
    let auth;
    let recaptchaVerifier;
    let recaptchaWidgetId;
    
    // Get elements
    const phoneInput = document.getElementById('phone');
    const sendOtpButton = document.getElementById('send-otp');
    const resetCaptchaButton = document.getElementById('reset-captcha');
    const otpSection = document.getElementById('otp-section');
    const otpInput = document.getElementById('otp');
    const verifyOtpButton = document.getElementById('verify-otp');
    const resultDiv = document.getElementById('result');
    const logsDiv = document.getElementById('logs');
    const apiKeyInput = document.getElementById('api-key');
    const authDomainInput = document.getElementById('auth-domain');
    const projectIdInput = document.getElementById('project-id');
    const updateConfigButton = document.getElementById('update-config');
    const checkAuthButton = document.getElementById('check-auth');
    const checkRecaptchaButton = document.getElementById('check-recaptcha');
    const clearLogsButton = document.getElementById('clear-logs');
    
    // Logging functions
    function log(message, type = 'info') {
      const logEntry = document.createElement('div');
      logEntry.className = `log-entry log-${type}`;
      logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      logsDiv.appendChild(logEntry);
      logsDiv.scrollTop = logsDiv.scrollHeight;
      console.log(`[${type.toUpperCase()}] ${message}`);
    }
    
    // Tab functions
    function openTab(evt, tabName) {
      const tabcontent = document.getElementsByClassName("tabcontent");
      for (let i = 0; i < tabcontent.length; i++) {
        tabcontent[i].classList.remove("visible");
      }
      
      const tablinks = document.getElementsByClassName("tablinks");
      for (let i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
      }
      
      document.getElementById(tabName).classList.add("visible");
      evt.currentTarget.className += " active";
    }
    
    // Initialize Firebase
    function initializeFirebase() {
      try {
        // If Firebase is already initialized, clean up
        if (app) {
          log("Cleaning up previous Firebase instance", "info");
          app.delete().catch(error => {
            log(`Error cleaning up Firebase: ${error.message}`, "error");
          });
        }
        
        // Initialize Firebase with config
        app = firebase.initializeApp(firebaseConfig);
        auth = firebase.auth();
        
        log("Firebase initialized successfully", "success");
        log(`API Key: ${firebaseConfig.apiKey.substring(0, 10)}...`, "info");
        log(`Auth Domain: ${firebaseConfig.authDomain}`, "info");
        log(`Project ID: ${firebaseConfig.projectId}`, "info");
        
        // Setup reCAPTCHA
        setupRecaptcha();
        
        return true;
      } catch (error) {
        log(`Firebase initialization error: ${error.message}`, "error");
        resultDiv.textContent = `Error initializing Firebase: ${error.message}`;
        return false;
      }
    }
    
    // Setup reCAPTCHA verifier
    function setupRecaptcha() {
      try {
        // Clear previous reCAPTCHA if exists
        const recaptchaContainer = document.getElementById('recaptcha-container');
        recaptchaContainer.innerHTML = '';
        
        // Create new reCAPTCHA verifier
        recaptchaVerifier = new firebase.auth.RecaptchaVerifier('recaptcha-container', {
          'size': 'normal',
          'callback': (response) => {
            log("reCAPTCHA verified successfully", "success");
            sendOtpButton.disabled = false;
          },
          'expired-callback': () => {
            log("reCAPTCHA expired", "warning");
            sendOtpButton.disabled = true;
          }
        });
        
        // Render the reCAPTCHA
        recaptchaVerifier.render().then(function(widgetId) {
          recaptchaWidgetId = widgetId;
          log("reCAPTCHA rendered successfully", "success");
        });
        
        return true;
      } catch (error) {
        log(`reCAPTCHA setup error: ${error.message}`, "error");
        resultDiv.textContent = `Error setting up reCAPTCHA: ${error.message}`;
        return false;
      }
    }
    
    // Send OTP
    function sendOTP() {
      const phoneNumber = phoneInput.value.trim();
      
      if (!phoneNumber) {
        log("Phone number is empty", "error");
        resultDiv.textContent = "Please enter a phone number";
        return;
      }
      
      log(`Attempting to send OTP to ${phoneNumber}`, "info");
      resultDiv.textContent = `Sending OTP to ${phoneNumber}...`;
      
      auth.signInWithPhoneNumber(phoneNumber, recaptchaVerifier)
        .then((confirmationResult) => {
          // SMS sent. Prompt user to type the code.
          window.confirmationResult = confirmationResult;
          log("OTP sent successfully", "success");
          resultDiv.textContent = `OTP sent successfully to ${phoneNumber}! Please check your phone.`;
          otpSection.style.display = 'block';
          sendOtpButton.disabled = true;
        })
        .catch((error) => {
          // Error; SMS not sent
          log(`Error sending OTP: ${error.code} - ${error.message}`, "error");
          resultDiv.textContent = `Error sending OTP: ${error.message}`;
          
          // Detailed error logging
          if (error.code === 'auth/invalid-phone-number') {
            log("Phone number format is invalid. Use E.164 format: +[country code][phone number]", "error");
          } else if (error.code === 'auth/captcha-check-failed') {
            log("reCAPTCHA verification failed. Try resetting the reCAPTCHA.", "error");
          } else if (error.code === 'auth/quota-exceeded') {
            log("SMS quota exceeded. Try again later.", "error");
          } else if (error.code === 'auth/too-many-requests') {
            log("Too many requests. Try again later.", "error");
          }
          
          // Reset reCAPTCHA
          if (recaptchaWidgetId) {
            grecaptcha.reset(recaptchaWidgetId);
          }
          sendOtpButton.disabled = false;
        });
    }
    
    // Verify OTP
    function verifyOTP() {
      const code = otpInput.value.trim();
      
      if (!code) {
        log("OTP is empty", "error");
        resultDiv.textContent = "Please enter the OTP";
        return;
      }
      
      if (!window.confirmationResult) {
        log("No confirmation result found", "error");
        resultDiv.textContent = "No OTP was sent. Please send OTP first.";
        return;
      }
      
      log(`Attempting to verify OTP: ${code}`, "info");
      resultDiv.textContent = `Verifying OTP...`;
      
      window.confirmationResult.confirm(code)
        .then((result) => {
          // User signed in successfully.
          const user = result.user;
          log(`OTP verified successfully. User signed in: ${user.phoneNumber}`, "success");
          resultDiv.textContent = `OTP verified successfully! User signed in: ${user.phoneNumber}`;
          
          // Log user details
          log(`User UID: ${user.uid}`, "info");
          log(`Provider ID: ${user.providerId}`, "info");
          log(`Is Anonymous: ${user.isAnonymous}`, "info");
          
          // Reset UI
          otpSection.style.display = 'none';
          sendOtpButton.disabled = false;
          otpInput.value = '';
        })
        .catch((error) => {
          // User couldn't sign in (bad verification code?)
          log(`Error verifying OTP: ${error.code} - ${error.message}`, "error");
          resultDiv.textContent = `Error verifying OTP: ${error.message}`;
          
          // Detailed error logging
          if (error.code === 'auth/invalid-verification-code') {
            log("The verification code is invalid. Please check and try again.", "error");
          } else if (error.code === 'auth/code-expired') {
            log("The verification code has expired. Please request a new OTP.", "error");
          }
        });
    }
    
    // Update Firebase configuration
    function updateConfig() {
      firebaseConfig.apiKey = apiKeyInput.value.trim();
      firebaseConfig.authDomain = authDomainInput.value.trim();
      firebaseConfig.projectId = projectIdInput.value.trim();
      
      log("Updating Firebase configuration...", "info");
      
      // Re-initialize Firebase with new config
      if (initializeFirebase()) {
        log("Firebase configuration updated successfully", "success");
      }
    }
    
    // Check authentication status
    function checkAuth() {
      log("Checking authentication status...", "info");
      
      auth.onAuthStateChanged((user) => {
        if (user) {
          log(`User is signed in: ${user.phoneNumber}`, "success");
          log(`User UID: ${user.uid}`, "info");
        } else {
          log("No user is signed in", "info");
        }
      });
    }
    
    // Check reCAPTCHA status
    function checkRecaptcha() {
      log("Checking reCAPTCHA status...", "info");
      
      if (recaptchaVerifier) {
        log("reCAPTCHA verifier exists", "success");
        if (recaptchaWidgetId) {
          log(`reCAPTCHA widget ID: ${recaptchaWidgetId}`, "info");
        } else {
          log("reCAPTCHA widget ID not found", "warning");
        }
      } else {
        log("reCAPTCHA verifier not found", "error");
        setupRecaptcha();
      }
    }
    
    // Clear logs
    function clearLogs() {
      logsDiv.innerHTML = '';
      log("Logs cleared", "info");
    }
    
    // Event listeners
    sendOtpButton.addEventListener('click', sendOTP);
    resetCaptchaButton.addEventListener('click', () => {
      if (recaptchaWidgetId) {
        grecaptcha.reset(recaptchaWidgetId);
        log("reCAPTCHA reset", "info");
      } else {
        setupRecaptcha();
      }
    });
    verifyOtpButton.addEventListener('click', verifyOTP);
    updateConfigButton.addEventListener('click', updateConfig);
    checkAuthButton.addEventListener('click', checkAuth);
    checkRecaptchaButton.addEventListener('click', checkRecaptcha);
    clearLogsButton.addEventListener('click', clearLogs);
    
    // Initialize on load
    window.onload = function() {
      log("Page loaded", "info");
      initializeFirebase();
    };
  </script>
</body>
</html>
