#!/bin/bash
# Azure App Service Deployment Script for AgriCare
# This script deploys the AgriCare application to Azure App Service

# Exit on error
set -e

# Configuration
APP_NAME="AgrocareProd"
RESOURCE_GROUP="AgrocareProd-rg"
LOCATION="centralindia"
SKU="P1V2"
RUNTIME="NODE:18-lts"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== AgriCare Azure App Service Deployment ===${NC}"
echo "App Name: $APP_NAME"
echo "Resource Group: $RESOURCE_GROUP"
echo "Location: $LOCATION"

# Check if Azure CLI is installed
if ! command -v az &> /dev/null; then
    echo -e "${RED}Azure CLI is not installed. Please install it first.${NC}"
    echo "Visit: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    exit 1
fi

# Check if logged in to Azure
echo -e "${YELLOW}Checking Azure login...${NC}"
az account show &> /dev/null || {
    echo -e "${YELLOW}Not logged in to Azure. Please login:${NC}"
    az login
}

# Create resource group if it doesn't exist
echo -e "${YELLOW}Checking if resource group exists...${NC}"
if ! az group show --name "$RESOURCE_GROUP" &> /dev/null; then
    echo -e "${YELLOW}Creating resource group $RESOURCE_GROUP...${NC}"
    az group create --name "$RESOURCE_GROUP" --location "$LOCATION"
else
    echo -e "${GREEN}Resource group $RESOURCE_GROUP already exists.${NC}"
fi

# Create App Service Plan if it doesn't exist
PLAN_NAME="${APP_NAME}-plan"
echo -e "${YELLOW}Checking if App Service Plan exists...${NC}"
if ! az appservice plan show --name "$PLAN_NAME" --resource-group "$RESOURCE_GROUP" &> /dev/null; then
    echo -e "${YELLOW}Creating App Service Plan $PLAN_NAME...${NC}"
    az appservice plan create --name "$PLAN_NAME" --resource-group "$RESOURCE_GROUP" \
        --location "$LOCATION" --sku "$SKU" --is-linux
else
    echo -e "${GREEN}App Service Plan $PLAN_NAME already exists.${NC}"
fi

# Create Web App if it doesn't exist
echo -e "${YELLOW}Checking if Web App exists...${NC}"
if ! az webapp show --name "$APP_NAME" --resource-group "$RESOURCE_GROUP" &> /dev/null; then
    echo -e "${YELLOW}Creating Web App $APP_NAME...${NC}"
    az webapp create --name "$APP_NAME" --resource-group "$RESOURCE_GROUP" \
        --plan "$PLAN_NAME" --runtime "$RUNTIME"
else
    echo -e "${GREEN}Web App $APP_NAME already exists.${NC}"
fi

# Build the frontend
echo -e "${YELLOW}Building frontend...${NC}"
cd frontend
npm ci
npm run build
cd ..

# Prepare backend
echo -e "${YELLOW}Preparing backend...${NC}"
cd backend
npm ci

# Copy frontend build to backend public folder
echo -e "${YELLOW}Copying frontend build to backend...${NC}"
mkdir -p public
cp -r ../frontend/dist/* public/

# Create a zip file for deployment
echo -e "${YELLOW}Creating deployment package...${NC}"
cd ..
zip -r deployment.zip backend

# Deploy to Azure
echo -e "${YELLOW}Deploying to Azure App Service...${NC}"
az webapp deployment source config-zip --resource-group "$RESOURCE_GROUP" \
    --name "$APP_NAME" --src deployment.zip

# Configure app settings
echo -e "${YELLOW}Configuring app settings...${NC}"
az webapp config appsettings set --resource-group "$RESOURCE_GROUP" --name "$APP_NAME" \
    --settings \
    WEBSITE_NODE_DEFAULT_VERSION=18.14.0 \
    NODE_ENV=production \
    WEBSITE_RUN_FROM_PACKAGE=1 \
    WEBSITE_HEALTHCHECK_MAXPINGFAILURES=5 \
    WEBSITE_HEALTHCHECK_PATH=/api/health

echo -e "${GREEN}Deployment completed successfully!${NC}"
echo -e "Your app is available at: ${YELLOW}https://$APP_NAME.azurewebsites.net${NC}"

# Clean up
echo -e "${YELLOW}Cleaning up...${NC}"
rm deployment.zip

echo -e "${GREEN}Done!${NC}"
